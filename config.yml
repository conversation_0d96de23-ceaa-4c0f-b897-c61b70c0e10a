app:
  name: mantis
  port: 8080
  maxroutine: 30
  svc: http://svc-cube-mantis.product:8080
domain:
  cube: http://cube-front.product.poc.za-tech.net
  deckjob: http://cube-ship-deckjob.product.poc.za-tech.net
db:
  dsn: host=************ port=5432 user=postgres dbname=mantis password=Cubeinfo_2024 sslmode=disable
  max_idle: 10
  max_open: 200
  max_life_time: 60
  migrate: false
redis:
  mode: direct  # direct/sentinel
  addr: r-tj7q69p7yevt6lny65.redis.rds.aliyuncs.com:6379
  password: XEzO&7yx@GKG#%mn
  db: 98
  ssl: false
nexus:
  url: http://************:8081
  username: admin
  password: Cubeinfo_2024
auth:
  auth_type: cube
  ua_url: http://cube-usercenter.product.poc.za-tech.net
  ua_suffix: uangateway
  auth_url: http://cube-base.product.poc.za-tech.net
  token: 0dfc3be2b2c54dd2
  service: za-cube
cmdb:
  open_api_url: http://cube-ship-deckjob.product.poc.za-tech.net
  license: hGY+rgogPbHjIRVzmPSr7bMotvhkfKOh/VvrZ8dqcEc=
  version: v4
log:
  path: ./logs
  level: debug
store:
  endpoint: http://************:9006
  bucketName: magiandship
  accessId: admin
  accessKey: admin1234
notification:
  enable: true
  address: http://cube-base.product.poc.za-tech.net
k8s:
  config_path: ./k8sconfig
  sync_resource_disable: false
  sync_resource_namespace: product
  sync_workers_num: 1
  kube_qps: 200
  kube_burst: 250
pipeline:
  driver: tekton
  namespace: product
  taskNodeSelector:
    com.zhonganinfo.cube: mantis
  taskHostNetwork: true
  image_pull_secret: ""
auditLog:
  endpoint: http://cube-base.product.poc.za-tech.net
  timeout: 200
modules:
  base:
    endpoint: http://svc-cube-magic-base.product:8081/base
  jupiter:
    endpoint: http://svc-cube-magic-base.product:8081/apt
  mars:
    enable: true
    projectRemoteApi:
      type: tm
      endpoint: http://cube-base.product.poc.za-tech.net
  mercury:
    enable: true
    single_task: false
  venus:
    enable: true
    executeTask:
      image: harbor.zhonganinfo.com/devcube/ui-executor:v9.12.0
      cpu: 1000
      memory: 2048
    stepReportCallback: http://svc-cube-mantis:8080/ui/openapi/v1/agent/callback
    fileDownloadUrl: http://svc-cube-mantis:8080/common/api/v1/biz/file/download
    variableUrl: http://svc-cube-mantis:8080/common/api/v1/biz/variable/getVars
  neptune:
    enable: true
    sonar:
      domain: http://************:9000
      token: squ_61527a9392566bbb117d8161bcf1cc06fda349bb
    maven:
      repo: http://mvnrepos.zhonganonline.com/nexus/content/groups/public
      username:
      password:
    trivyTask:
      image: harbor.zhonganinfo.com/devcube/trivy-scanner:v9.12.0-0.63.0-20250601
      cpu: 1000
      memory: 2048
    sonarTask:
      image: harbor.zhonganinfo.com/devcube/esonar-scanner:v9.12.0
      cpu: 1000
      memory: 4096
    jacocoTask:
      image: harbor.zhonganinfo.com/devcube/jacoco-coverage:v9.12.0
      cpu: 1000
      memory: 2048
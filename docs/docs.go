// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/neptune/openapi/v1/pipeline/trigger/product": {
            "post": {
                "description": "制品扫描",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "openapi"
                ],
                "summary": "制品扫描",
                "parameters": [
                    {
                        "description": "流水线制品扫描请求对象",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.PipelineProductScanRequestDto"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/controller.Response"
                        }
                    }
                }
            }
        },
        "/neptune/openapi/v1/result/:type/:id/form": {
            "post": {
                "description": "表单类型结果上报",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "openapi"
                ],
                "summary": "表单类型结果上报",
                "parameters": [
                    {
                        "type": "string",
                        "description": "结果类型",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "表单参数以约定为准",
                        "name": "k1",
                        "in": "formData"
                    },
                    {
                        "type": "file",
                        "description": "file",
                        "name": "file",
                        "in": "formData"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/neptune/v1/execHis/:id/detail": {
            "get": {
                "description": "查看制品执行结果",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "执行结果"
                ],
                "summary": "查看制品执行结果",
                "parameters": [
                    {
                        "type": "string",
                        "description": "执行记录id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "name": "requestId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/execHis/:id/detail/log": {
            "get": {
                "description": "查看制品执行日志",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "执行结果"
                ],
                "summary": "查看制品执行日志",
                "parameters": [
                    {
                        "type": "string",
                        "description": "执行记录id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        },
        "/neptune/v1/execHis/:id/list": {
            "get": {
                "description": "查看执行历史",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "执行结果"
                ],
                "summary": "查看执行历史",
                "parameters": [
                    {
                        "type": "string",
                        "description": "task id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "name": "requestId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/plan": {
            "post": {
                "description": "新增或修改方案",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "新增或修改方案",
                "parameters": [
                    {
                        "description": "方案",
                        "name": "plan",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ScanPlan"
                        }
                    }
                ],
                "responses": {}
            }
        },
        "/neptune/v1/plan/:id": {
            "delete": {
                "description": "删除方案",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "删除方案",
                "parameters": [
                    {
                        "type": "string",
                        "description": "方案id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/neptune/v1/plan/:id/bindTask": {
            "get": {
                "description": "查看方案绑定的任务",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "查看方案绑定的任务",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索框",
                        "name": "searchKey",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "q=status=(0 1) 查询类型 0 待执行 1 运行中",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "方案id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "页数",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "条数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "za-cube",
                        "name": "X-Service-Name",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "xxx",
                        "name": "X-Usercenter-Session",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/plan/:id/default/:v": {
            "put": {
                "description": "方案设置为默认",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "方案设置为默认",
                "parameters": [
                    {
                        "type": "string",
                        "description": "方案id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "1|0",
                        "name": "v",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/neptune/v1/plan/list": {
            "get": {
                "description": "查看方案列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "查看方案列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索框",
                        "name": "searchKey",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "q=planType=1 查询类型",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "页数",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "条数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "排序字段",
                        "name": "orderField",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "1:正序 2:倒叙",
                        "name": "orderType",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "za-cube",
                        "name": "X-Service-Name",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "xxx",
                        "name": "X-Usercenter-Session",
                        "in": "header",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/plan/{id}": {
            "get": {
                "description": "查看方案详情",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "plan"
                ],
                "summary": "查看方案详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "方案id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.ScanPlan"
                        }
                    }
                }
            }
        },
        "/neptune/v1/repo/instance/:id/latest/:repoId/:repoName": {
            "get": {
                "description": "仓库下最新的制品",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repo"
                ],
                "summary": "仓库下最新的制品",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "仓库实例id",
                        "name": "id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "仓库id",
                        "name": "repoId",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "制品名",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ViewProductInfo"
                            }
                        }
                    }
                }
            }
        },
        "/neptune/v1/repo/instance/:id/products/:repoId/:repoName": {
            "get": {
                "description": "查询指定仓库下的制品",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repo"
                ],
                "summary": "查询指定仓库下的制品",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "仓库实例id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "仓库id",
                        "name": "repoId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名",
                        "name": "repoName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "制品名",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.MinProductDto"
                            }
                        }
                    }
                }
            }
        },
        "/neptune/v1/repo/instance/:id/repoNames": {
            "get": {
                "description": "仓库信息列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repo"
                ],
                "summary": "仓库信息列表",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "仓库实例id",
                        "name": "id",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.RepoDto"
                            }
                        }
                    }
                }
            }
        },
        "/neptune/v1/repo/instance/:id/versions/:repoName/:prodId/:prodName": {
            "get": {
                "description": "查询制品的所有版本",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repo"
                ],
                "summary": "查询制品的所有版本",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "仓库实例id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "仓库名",
                        "name": "repoName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "制品id",
                        "name": "prodId",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "制品名",
                        "name": "prodName",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "版本名",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.ProductVersionDto"
                            }
                        }
                    }
                }
            }
        },
        "/neptune/v1/repo/instance/list": {
            "get": {
                "description": "仓库实例列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "repo"
                ],
                "summary": "仓库实例列表",
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/neptune/v1/report/:id/detail": {
            "get": {
                "description": "指定制品的报告",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "报告"
                ],
                "summary": "指定制品的报告",
                "parameters": [
                    {
                        "type": "string",
                        "description": "详情id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "name": "requestId",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "name": "pageSize",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "name": "searchKey",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/report/:id/execHis/export": {
            "post": {
                "description": "按执行记录id导出报告",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "报告"
                ],
                "summary": "按执行记录id导出报告",
                "parameters": [
                    {
                        "type": "string",
                        "description": "执行记录id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "导出条件对象",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ProductExportDto"
                        }
                    }
                ],
                "responses": {}
            }
        },
        "/neptune/v1/report/:id/execHis/list": {
            "get": {
                "description": "查看报告列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "报告"
                ],
                "summary": "查看报告列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "执行记录id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {}
            }
        },
        "/neptune/v1/task": {
            "post": {
                "description": "新增或修改任务",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "task"
                ],
                "summary": "新增或修改任务",
                "parameters": [
                    {
                        "description": "请求参数",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.ScanTask"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/controller.Response"
                        }
                    }
                }
            }
        },
        "/neptune/v1/task/:id": {
            "delete": {
                "description": "删除任务",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "task"
                ],
                "summary": "删除任务",
                "parameters": [
                    {
                        "type": "string",
                        "description": "任务id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/controller.Response"
                        }
                    }
                }
            }
        },
        "/neptune/v1/task/:id/:execId": {
            "get": {
                "description": "查看任务详情",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "task"
                ],
                "summary": "查看任务详情",
                "parameters": [
                    {
                        "type": "string",
                        "description": "任务id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "执行记录id",
                        "name": "execId",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/models.ScanTask"
                        }
                    }
                }
            }
        },
        "/neptune/v1/task/:id/abort": {
            "post": {
                "description": "任务中断",
                "tags": [
                    "task"
                ],
                "summary": "任务中断",
                "parameters": [
                    {
                        "type": "string",
                        "description": "任务id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/controller.Response"
                        }
                    }
                }
            }
        },
        "/neptune/v1/task/:id/exec": {
            "post": {
                "description": "任务执行",
                "tags": [
                    "task"
                ],
                "summary": "任务执行",
                "parameters": [
                    {
                        "type": "string",
                        "description": "任务id",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/controller.Response"
                        }
                    }
                }
            }
        },
        "/neptune/v1/task/list": {
            "get": {
                "description": "查看任务列表",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "task"
                ],
                "summary": "查看任务列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "搜索框",
                        "name": "searchKey",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "q=planType=1 查询类型",
                        "name": "q",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "页数",
                        "name": "page",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "条数",
                        "name": "pageSize",
                        "in": "query",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "排序字段",
                        "name": "orderField",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "1:正序 2:倒叙",
                        "name": "orderType",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "$ref": "#/definitions/gormx.PageResult"
                        }
                    }
                }
            }
        },
        "/neptune/v1/template/{type}/download": {
            "get": {
                "description": "下载模板",
                "tags": [
                    "template 模板"
                ],
                "summary": "下载模板",
                "parameters": [
                    {
                        "type": "string",
                        "description": "模板类型:cve",
                        "name": "type",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "string"
                        }
                    }
                }
            }
        },
        "/neptune/v1/template/{type}/format": {
            "post": {
                "description": "格式化模板",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "template 模板"
                ],
                "summary": "格式化模板",
                "parameters": [
                    {
                        "type": "string",
                        "description": "模板类型:cve",
                        "name": "type",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "文件",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    },
                    {
                        "type": "array",
                        "items": {
                            "type": "string"
                        },
                        "collectionFormat": "csv",
                        "description": "字符串数组",
                        "name": "arr",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "成功",
                        "schema": {
                            "type": "array",
                            "items": {
                                "type": "string"
                            }
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "gormx.PageResult": {
            "type": "object",
            "properties": {
                "currentPage": {
                    "description": "当前页",
                    "type": "integer"
                },
                "list": {
                    "description": "数据"
                },
                "pageSize": {
                    "description": "一页条数",
                    "type": "integer"
                },
                "pages": {
                    "description": "总页数",
                    "type": "integer"
                },
                "total": {
                    "description": "总条数",
                    "type": "integer"
                }
            }
        },
        "models.MinProductDto": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "制品id",
                    "type": "integer"
                },
                "name": {
                    "description": "制品名称",
                    "type": "string"
                }
            }
        },
        "models.PipelineProductScanRequestDto": {
            "type": "object",
            "required": [
                "appName",
                "callBackUrl",
                "companyID",
                "instanceName",
                "productName",
                "productVersion",
                "repoName",
                "taskName"
            ],
            "properties": {
                "appName": {
                    "description": "应用名称",
                    "type": "string"
                },
                "callBackUrl": {
                    "description": "回调地址",
                    "type": "string"
                },
                "companyID": {
                    "description": "公司id",
                    "type": "string"
                },
                "instanceName": {
                    "description": "实例名称,artifact(一般制品仓库),cmdb上的仓库名称(harbor)",
                    "type": "string"
                },
                "productName": {
                    "description": "制品名称  or repoName(harbor)",
                    "type": "string"
                },
                "productType": {
                    "description": "制品类型  Generic|Docker",
                    "type": "string",
                    "enum": [
                        "Generic",
                        "Docker"
                    ]
                },
                "productVersion": {
                    "description": "制品版本  or tagName(harbor)",
                    "type": "string"
                },
                "repoName": {
                    "description": "仓库名称  or projectName(harbor)",
                    "type": "string"
                },
                "taskName": {
                    "description": "任务名称 同一个 发布单+应用名+执行节点为一个任务，没有就创建，有就直接执行",
                    "type": "string"
                }
            }
        },
        "models.ProductExportDto": {
            "type": "object",
            "properties": {
                "cves": {
                    "description": "漏洞列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "exportType": {
                    "description": "导出类型",
                    "type": "integer"
                }
            }
        },
        "models.ProductVersionDto": {
            "type": "object",
            "properties": {
                "version": {
                    "description": "版本名称",
                    "type": "string"
                }
            }
        },
        "models.QualityGateDto": {
            "type": "object",
            "properties": {
                "critical": {
                    "description": "严重",
                    "type": "integer"
                },
                "high": {
                    "description": "高危",
                    "type": "integer"
                },
                "low": {
                    "description": "低危",
                    "type": "integer"
                },
                "medium": {
                    "description": "中危",
                    "type": "integer"
                },
                "none": {
                    "description": "未定级",
                    "type": "integer"
                }
            }
        },
        "models.RepoDto": {
            "type": "object",
            "properties": {
                "id": {
                    "description": "仓库id",
                    "type": "integer"
                },
                "name": {
                    "description": "仓库名称",
                    "type": "string"
                },
                "repoType": {
                    "description": "仓库类型",
                    "type": "string"
                }
            }
        },
        "models.ScanPlan": {
            "type": "object",
            "required": [
                "planName"
            ],
            "properties": {
                "companyID": {
                    "description": "公司id",
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "description": {
                    "description": "方案描述",
                    "type": "string"
                },
                "gmtCreated": {
                    "type": "string"
                },
                "gmtModified": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "isDefault": {
                    "description": "是否默认 1-是,0-否",
                    "type": "integer",
                    "enum": [
                        1,
                        0
                    ]
                },
                "isDeleted": {
                    "type": "string"
                },
                "modifier": {
                    "type": "string"
                },
                "planName": {
                    "description": "方案名称",
                    "type": "string"
                },
                "planType": {
                    "description": "方案类型 1-安全漏洞扫描,2-代码规约扫描,3-单测测试覆盖率扫描",
                    "type": "integer",
                    "enum": [
                        1,
                        2,
                        3
                    ]
                },
                "whiteCVEInfos": {
                    "description": "非数据库对象",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.WhiteCVEInfo"
                    }
                }
            }
        },
        "models.ScanTask": {
            "type": "object",
            "required": [
                "planId",
                "repoInstanceId",
                "taskName"
            ],
            "properties": {
                "LastExecUserName": {
                    "description": "最新执行人",
                    "type": "string"
                },
                "LastTriggerTime": {
                    "description": "最新执行时间",
                    "type": "string"
                },
                "callBackUrl": {
                    "description": "回调地址",
                    "type": "string"
                },
                "companyID": {
                    "description": "公司id",
                    "type": "string"
                },
                "creator": {
                    "type": "string"
                },
                "gmtCreated": {
                    "type": "string"
                },
                "gmtModified": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "isDeleted": {
                    "type": "string"
                },
                "lastExecId": {
                    "type": "integer"
                },
                "lastReportSwitch": {
                    "description": "最新一次报告开关",
                    "type": "integer"
                },
                "lastReportUrl": {
                    "description": "最新一次报告地址",
                    "type": "string"
                },
                "lastTaskStatus": {
                    "description": "最新执行状态",
                    "type": "string"
                },
                "lastTaskStatusCode": {
                    "description": "最新执行状态",
                    "type": "integer"
                },
                "modifier": {
                    "type": "string"
                },
                "planId": {
                    "type": "integer"
                },
                "planName": {
                    "description": "计划名称",
                    "type": "string"
                },
                "planType": {
                    "description": "冗余字段，用于查询,分类",
                    "type": "integer"
                },
                "productList": {
                    "description": "制品列表",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.ViewProductInfo"
                    }
                },
                "productType": {
                    "description": "制品类型",
                    "type": "string"
                },
                "qualityGateInfo": {
                    "description": "非数据库字段",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.QualityGateDto"
                        }
                    ]
                },
                "qualityGateSwitch": {
                    "description": "质量门控开关 1-开 0-关闭",
                    "type": "integer",
                    "enum": [
                        1,
                        0
                    ]
                },
                "repoInfo": {
                    "description": "仓库信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.RepoDto"
                    }
                },
                "repoInstanceId": {
                    "description": "仓库实例id",
                    "type": "integer"
                },
                "scanCount": {
                    "description": "扫描次数",
                    "type": "integer"
                },
                "scanProductCount": {
                    "description": "前端需要展示字段",
                    "type": "integer"
                },
                "scanRange": {
                    "description": "扫描范围 1-指定制品 2-最新制品 3-规则扫描",
                    "type": "integer",
                    "enum": [
                        1,
                        2,
                        3
                    ]
                },
                "scanResultInfo": {
                    "description": "任务结果汇总",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewScanResultInfo"
                        }
                    ]
                },
                "taskFrom": {
                    "description": "任务来源 web or pipeline",
                    "type": "string",
                    "enum": [
                        "web",
                        "pipeline"
                    ]
                },
                "taskName": {
                    "description": "任务名称",
                    "type": "string"
                },
                "timerCorn": {
                    "description": "定时corn表达式",
                    "type": "string"
                },
                "timerSwitch": {
                    "description": "定时开关 1-开 0-关闭",
                    "type": "integer",
                    "enum": [
                        1,
                        0
                    ]
                }
            }
        },
        "models.ViewNumTag": {
            "type": "object",
            "properties": {
                "num": {
                    "description": "个数",
                    "type": "integer"
                },
                "tag": {
                    "description": "1 红色 0 不显示",
                    "type": "integer"
                }
            }
        },
        "models.ViewProductInfo": {
            "type": "object",
            "properties": {
                "productInfo": {
                    "$ref": "#/definitions/models.MinProductDto"
                },
                "repoInfo": {
                    "$ref": "#/definitions/models.RepoDto"
                },
                "versionInfo": {
                    "$ref": "#/definitions/models.ProductVersionDto"
                }
            }
        },
        "models.ViewScanResultInfo": {
            "type": "object",
            "properties": {
                "critical": {
                    "description": "严重",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                },
                "high": {
                    "description": "高危",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                },
                "low": {
                    "description": "低危",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                },
                "medium": {
                    "description": "中危",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                },
                "none": {
                    "description": "未定级",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                },
                "vulnerabilityCount": {
                    "description": "漏洞总数量",
                    "allOf": [
                        {
                            "$ref": "#/definitions/models.ViewNumTag"
                        }
                    ]
                }
            }
        },
        "models.WhiteCVEInfo": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                }
            }
        },
        "controller.Response": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {},
                "message": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "v8.13.0",
	Host:             "",
	BasePath:         "",
	Schemes:          []string{},
	Title:            "Mantis",
	Description:      "DevCube质量与测试模块",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}

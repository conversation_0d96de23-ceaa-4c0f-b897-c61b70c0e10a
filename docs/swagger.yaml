definitions:
  gormx.PageResult:
    properties:
      currentPage:
        description: 当前页
        type: integer
      list:
        description: 数据
      pageSize:
        description: 一页条数
        type: integer
      pages:
        description: 总页数
        type: integer
      total:
        description: 总条数
        type: integer
    type: object
  models.MinProductDto:
    properties:
      id:
        description: 制品id
        type: integer
      name:
        description: 制品名称
        type: string
    type: object
  models.PipelineProductScanRequestDto:
    properties:
      appName:
        description: 应用名称
        type: string
      callBackUrl:
        description: 回调地址
        type: string
      companyID:
        description: 公司id
        type: string
      instanceName:
        description: 实例名称,artifact(一般制品仓库),cmdb上的仓库名称(harbor)
        type: string
      productName:
        description: 制品名称  or repoName(harbor)
        type: string
      productType:
        description: 制品类型  Generic|Docker
        enum:
        - Generic
        - Docker
        type: string
      productVersion:
        description: 制品版本  or tagName(harbor)
        type: string
      repoName:
        description: 仓库名称  or projectName(harbor)
        type: string
      taskName:
        description: 任务名称 同一个 发布单+应用名+执行节点为一个任务，没有就创建，有就直接执行
        type: string
    required:
    - appName
    - callBackUrl
    - companyID
    - instanceName
    - productName
    - productVersion
    - repoName
    - taskName
    type: object
  models.ProductExportDto:
    properties:
      cves:
        description: 漏洞列表
        items:
          type: string
        type: array
      exportType:
        description: 导出类型
        type: integer
    type: object
  models.ProductVersionDto:
    properties:
      version:
        description: 版本名称
        type: string
    type: object
  models.QualityGateDto:
    properties:
      critical:
        description: 严重
        type: integer
      high:
        description: 高危
        type: integer
      low:
        description: 低危
        type: integer
      medium:
        description: 中危
        type: integer
      none:
        description: 未定级
        type: integer
    type: object
  models.RepoDto:
    properties:
      id:
        description: 仓库id
        type: integer
      name:
        description: 仓库名称
        type: string
      repoType:
        description: 仓库类型
        type: string
    type: object
  models.ScanPlan:
    properties:
      companyID:
        description: 公司id
        type: string
      creator:
        type: string
      description:
        description: 方案描述
        type: string
      gmtCreated:
        type: string
      gmtModified:
        type: string
      id:
        type: integer
      isDefault:
        description: 是否默认 1-是,0-否
        enum:
        - 1
        - 0
        type: integer
      isDeleted:
        type: string
      modifier:
        type: string
      planName:
        description: 方案名称
        type: string
      planType:
        description: 方案类型 1-安全漏洞扫描,2-代码规约扫描,3-单测测试覆盖率扫描
        enum:
        - 1
        - 2
        - 3
        type: integer
      whiteCVEInfos:
        description: 非数据库对象
        items:
          $ref: '#/definitions/models.WhiteCVEInfo'
        type: array
    required:
    - planName
    type: object
  models.ScanTask:
    properties:
      LastExecUserName:
        description: 最新执行人
        type: string
      LastTriggerTime:
        description: 最新执行时间
        type: string
      callBackUrl:
        description: 回调地址
        type: string
      companyID:
        description: 公司id
        type: string
      creator:
        type: string
      gmtCreated:
        type: string
      gmtModified:
        type: string
      id:
        type: integer
      isDeleted:
        type: string
      lastExecId:
        type: integer
      lastReportSwitch:
        description: 最新一次报告开关
        type: integer
      lastReportUrl:
        description: 最新一次报告地址
        type: string
      lastTaskStatus:
        description: 最新执行状态
        type: string
      lastTaskStatusCode:
        description: 最新执行状态
        type: integer
      modifier:
        type: string
      planId:
        type: integer
      planName:
        description: 计划名称
        type: string
      planType:
        description: 冗余字段，用于查询,分类
        type: integer
      productList:
        description: 制品列表
        items:
          $ref: '#/definitions/models.ViewProductInfo'
        type: array
      productType:
        description: 制品类型
        type: string
      qualityGateInfo:
        allOf:
        - $ref: '#/definitions/models.QualityGateDto'
        description: 非数据库字段
      qualityGateSwitch:
        description: 质量门控开关 1-开 0-关闭
        enum:
        - 1
        - 0
        type: integer
      repoInfo:
        description: 仓库信息
        items:
          $ref: '#/definitions/models.RepoDto'
        type: array
      repoInstanceId:
        description: 仓库实例id
        type: integer
      scanCount:
        description: 扫描次数
        type: integer
      scanProductCount:
        description: 前端需要展示字段
        type: integer
      scanRange:
        description: 扫描范围 1-指定制品 2-最新制品 3-规则扫描
        enum:
        - 1
        - 2
        - 3
        type: integer
      scanResultInfo:
        allOf:
        - $ref: '#/definitions/models.ViewScanResultInfo'
        description: 任务结果汇总
      taskFrom:
        description: 任务来源 web or pipeline
        enum:
        - web
        - pipeline
        type: string
      taskName:
        description: 任务名称
        type: string
      timerCorn:
        description: 定时corn表达式
        type: string
      timerSwitch:
        description: 定时开关 1-开 0-关闭
        enum:
        - 1
        - 0
        type: integer
    required:
    - planId
    - repoInstanceId
    - taskName
    type: object
  models.ViewNumTag:
    properties:
      num:
        description: 个数
        type: integer
      tag:
        description: 1 红色 0 不显示
        type: integer
    type: object
  models.ViewProductInfo:
    properties:
      productInfo:
        $ref: '#/definitions/models.MinProductDto'
      repoInfo:
        $ref: '#/definitions/models.RepoDto'
      versionInfo:
        $ref: '#/definitions/models.ProductVersionDto'
    type: object
  models.ViewScanResultInfo:
    properties:
      critical:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 严重
      high:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 高危
      low:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 低危
      medium:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 中危
      none:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 未定级
      vulnerabilityCount:
        allOf:
        - $ref: '#/definitions/models.ViewNumTag'
        description: 漏洞总数量
    type: object
  models.WhiteCVEInfo:
    properties:
      code:
        type: string
    type: object
  response.Response:
    properties:
      code:
        type: string
      data: {}
      message:
        type: string
    type: object
info:
  contact: {}
  description: DevCube质量与测试模块
  title: Mantis
  version: v8.13.0
paths:
  /neptune/openapi/v1/pipeline/trigger/product:
    post:
      description: 制品扫描
      parameters:
      - description: 流水线制品扫描请求对象
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.PipelineProductScanRequestDto'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
      summary: 制品扫描
      tags:
      - openapi
  /neptune/openapi/v1/result/:type/:id/form:
    post:
      description: 表单类型结果上报
      parameters:
      - description: 结果类型
        in: path
        name: type
        required: true
        type: string
      - description: 表单参数以约定为准
        in: formData
        name: k1
        type: string
      - description: file
        in: formData
        name: file
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            additionalProperties: true
            type: object
      summary: 表单类型结果上报
      tags:
      - openapi
  /neptune/v1/execHis/:id/detail:
    get:
      description: 查看制品执行结果
      parameters:
      - description: 执行记录id
        in: path
        name: id
        required: true
        type: string
      - in: query
        name: requestId
        type: string
      - in: query
        name: page
        type: integer
      - in: query
        name: pageSize
        type: integer
      - in: query
        name: q
        type: string
      - in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 查看制品执行结果
      tags:
      - 执行结果
  /neptune/v1/execHis/:id/detail/log:
    get:
      description: 查看制品执行日志
      parameters:
      - description: 执行记录id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            additionalProperties:
              type: string
            type: object
      summary: 查看制品执行日志
      tags:
      - 执行结果
  /neptune/v1/execHis/:id/list:
    get:
      description: 查看执行历史
      parameters:
      - description: task id
        in: path
        name: id
        required: true
        type: string
      - in: query
        name: requestId
        type: string
      - in: query
        name: page
        type: integer
      - in: query
        name: pageSize
        type: integer
      - in: query
        name: q
        type: string
      - in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 查看执行历史
      tags:
      - 执行结果
  /neptune/v1/plan:
    post:
      description: 新增或修改方案
      parameters:
      - description: 方案
        in: body
        name: plan
        required: true
        schema:
          $ref: '#/definitions/models.ScanPlan'
      produces:
      - application/json
      responses: {}
      summary: 新增或修改方案
      tags:
      - plan
  /neptune/v1/plan/:id:
    delete:
      description: 删除方案
      parameters:
      - description: 方案id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 删除方案
      tags:
      - plan
  /neptune/v1/plan/:id/bindTask:
    get:
      description: 查看方案绑定的任务
      parameters:
      - description: 搜索框
        in: query
        name: searchKey
        type: string
      - description: q=status=(0 1) 查询类型 0 待执行 1 运行中
        in: query
        name: q
        type: string
      - description: 方案id
        in: path
        name: id
        required: true
        type: string
      - description: 页数
        in: query
        name: page
        required: true
        type: string
      - description: 条数
        in: query
        name: pageSize
        required: true
        type: string
      - description: za-cube
        in: header
        name: X-Service-Name
        required: true
        type: string
      - description: xxx
        in: header
        name: X-Usercenter-Session
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 查看方案绑定的任务
      tags:
      - plan
  /neptune/v1/plan/:id/default/:v:
    put:
      description: 方案设置为默认
      parameters:
      - description: 方案id
        in: path
        name: id
        required: true
        type: string
      - description: 1|0
        in: path
        name: v
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 方案设置为默认
      tags:
      - plan
  /neptune/v1/plan/{id}:
    get:
      description: 查看方案详情
      parameters:
      - description: 方案id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.ScanPlan'
      summary: 查看方案详情
      tags:
      - plan
  /neptune/v1/plan/list:
    get:
      description: 查看方案列表
      parameters:
      - description: 搜索框
        in: query
        name: searchKey
        type: string
      - description: q=planType=1 查询类型
        in: query
        name: q
        type: string
      - description: 页数
        in: query
        name: page
        required: true
        type: string
      - description: 条数
        in: query
        name: pageSize
        required: true
        type: string
      - description: 排序字段
        in: query
        name: orderField
        type: string
      - description: 1:正序 2:倒叙
        in: query
        name: orderType
        type: string
      - description: za-cube
        in: header
        name: X-Service-Name
        required: true
        type: string
      - description: xxx
        in: header
        name: X-Usercenter-Session
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 查看方案列表
      tags:
      - plan
  /neptune/v1/repo/instance/:id/latest/:repoId/:repoName:
    get:
      description: 仓库下最新的制品
      parameters:
      - description: 仓库实例id
        in: query
        name: id
        required: true
        type: integer
      - description: 仓库id
        in: query
        name: repoId
        required: true
        type: integer
      - description: 制品名
        in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              $ref: '#/definitions/models.ViewProductInfo'
            type: array
      summary: 仓库下最新的制品
      tags:
      - repo
  /neptune/v1/repo/instance/:id/products/:repoId/:repoName:
    get:
      description: 查询指定仓库下的制品
      parameters:
      - description: 仓库实例id
        in: path
        name: id
        required: true
        type: integer
      - description: 仓库id
        in: path
        name: repoId
        required: true
        type: integer
      - description: 仓库名
        in: path
        name: repoName
        required: true
        type: string
      - description: 制品名
        in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              $ref: '#/definitions/models.MinProductDto'
            type: array
      summary: 查询指定仓库下的制品
      tags:
      - repo
  /neptune/v1/repo/instance/:id/repoNames:
    get:
      description: 仓库信息列表
      parameters:
      - description: 仓库实例id
        in: query
        name: id
        required: true
        type: integer
      - description: 仓库名
        in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              $ref: '#/definitions/models.RepoDto'
            type: array
      summary: 仓库信息列表
      tags:
      - repo
  /neptune/v1/repo/instance/:id/versions/:repoName/:prodId/:prodName:
    get:
      description: 查询制品的所有版本
      parameters:
      - description: 仓库实例id
        in: path
        name: id
        required: true
        type: integer
      - description: 仓库名
        in: path
        name: repoName
        required: true
        type: string
      - description: 制品id
        in: path
        name: prodId
        required: true
        type: integer
      - description: 制品名
        in: path
        name: prodName
        required: true
        type: string
      - description: 版本名
        in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              $ref: '#/definitions/models.ProductVersionDto'
            type: array
      summary: 查询制品的所有版本
      tags:
      - repo
  /neptune/v1/repo/instance/list:
    get:
      description: 仓库实例列表
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            additionalProperties: true
            type: object
      summary: 仓库实例列表
      tags:
      - repo
  /neptune/v1/report/:id/detail:
    get:
      description: 指定制品的报告
      parameters:
      - description: 详情id
        in: path
        name: id
        required: true
        type: string
      - in: query
        name: requestId
        type: string
      - in: query
        name: page
        type: integer
      - in: query
        name: pageSize
        type: integer
      - in: query
        name: q
        type: string
      - in: query
        name: searchKey
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 指定制品的报告
      tags:
      - 报告
  /neptune/v1/report/:id/execHis/export:
    post:
      description: 按执行记录id导出报告
      parameters:
      - description: 执行记录id
        in: path
        name: id
        required: true
        type: string
      - description: 导出条件对象
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ProductExportDto'
      produces:
      - application/json
      responses: {}
      summary: 按执行记录id导出报告
      tags:
      - 报告
  /neptune/v1/report/:id/execHis/list:
    get:
      description: 查看报告列表
      parameters:
      - description: 执行记录id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses: {}
      summary: 查看报告列表
      tags:
      - 报告
  /neptune/v1/task:
    post:
      description: 新增或修改任务
      parameters:
      - description: 请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/models.ScanTask'
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
      summary: 新增或修改任务
      tags:
      - task
  /neptune/v1/task/:id:
    delete:
      description: 删除任务
      parameters:
      - description: 任务id
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
      summary: 删除任务
      tags:
      - task
  /neptune/v1/task/:id/:execId:
    get:
      description: 查看任务详情
      parameters:
      - description: 任务id
        in: path
        name: id
        required: true
        type: string
      - description: 执行记录id
        in: path
        name: execId
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/models.ScanTask'
      summary: 查看任务详情
      tags:
      - task
  /neptune/v1/task/:id/abort:
    post:
      description: 任务中断
      parameters:
      - description: 任务id
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
      summary: 任务中断
      tags:
      - task
  /neptune/v1/task/:id/exec:
    post:
      description: 任务执行
      parameters:
      - description: 任务id
        in: path
        name: id
        required: true
        type: string
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/response.Response'
      summary: 任务执行
      tags:
      - task
  /neptune/v1/task/list:
    get:
      description: 查看任务列表
      parameters:
      - description: 搜索框
        in: query
        name: searchKey
        type: string
      - description: q=planType=1 查询类型
        in: query
        name: q
        type: string
      - description: 页数
        in: query
        name: page
        required: true
        type: string
      - description: 条数
        in: query
        name: pageSize
        required: true
        type: string
      - description: 排序字段
        in: query
        name: orderField
        type: string
      - description: 1:正序 2:倒叙
        in: query
        name: orderType
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            $ref: '#/definitions/gormx.PageResult'
      summary: 查看任务列表
      tags:
      - task
  /neptune/v1/template/{type}/download:
    get:
      description: 下载模板
      parameters:
      - description: 模板类型:cve
        in: path
        name: type
        required: true
        type: string
      responses:
        "200":
          description: 成功
          schema:
            type: string
      summary: 下载模板
      tags:
      - template 模板
  /neptune/v1/template/{type}/format:
    post:
      description: 格式化模板
      parameters:
      - description: 模板类型:cve
        in: path
        name: type
        required: true
        type: string
      - description: 文件
        in: formData
        name: file
        required: true
        type: string
      - collectionFormat: csv
        description: 字符串数组
        in: formData
        items:
          type: string
        name: arr
        required: true
        type: array
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            items:
              type: string
            type: array
      summary: 格式化模板
      tags:
      - template 模板
swagger: "2.0"

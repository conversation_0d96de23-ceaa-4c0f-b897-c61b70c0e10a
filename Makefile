.PHONY: help pre_step setup vet build run install test format generate doc trivy-cli trivy-db trivy-scanner esonar-cli esonar-scanner jacoco-cli jacoco-coverage ui-executor-cli ui-executor

BIN_FILE := mantis
MAIN_FILE := main.go
CGO_ENABLED ?= 0
INSTALL_DIR ?= .

MANTIS_VERSION := $(shell git rev-parse --abbrev-ref HEAD)
TRIVY_VERSION := 0.63.0
TRIVY_DB_VERSION := 20250601

help:
	@echo "make setup                   安装工具"
	@echo "make vet                     静态分析工具"
	@echo "make build                   编译程序"
	@echo "make run                     执行程序"
	@echo "make install                 安装程序"
	@echo "make test                    执行测试用例"
	@echo "make format                  格式化"
	@echo "make generate                codegen生成代码"
	@echo "make doc                     生成API文档"
	@echo "make trivy-cli               安装trivy-cli命令行"
	@echo "make trivy-db                构建trivy-db镜像"
	@echo "make trivy-scanner           构建trivy-scanner镜像"
	@echo "make esonar-cli              安装esonar-cli命令行"
	@echo "make esonar-scanner          构建esonar-scanner镜像"
	@echo "make jacoco-cli              安装jacoco-cli命令行"
	@echo "make jacoco-coverage         构建jacoco-coverage镜像"
	@echo "make ui-executor-cli         安装ui-executor-cli命令行"
	@echo "make ui-executor             构建ui-executor镜像"

pre_step:
	mkdir -p ${INSTALL_DIR}
	mkdir -p ${INSTALL_DIR}/logs

# init setup
setup:
	go install -mod=mod mvdan.cc/gofumpt@latest                  #代码格式化工具
	go install -mod=mod github.com/swaggo/swag/cmd/swag@latest   #API文档生成工具

# vendor vet
vet:
	go vet -mod=vendor ./...

# build mantis
build:
	CGO_ENABLED=${CGO_ENABLED} go build -mod=vendor -o ${INSTALL_DIR}/${BIN_FILE} ./cmd/mantis/${MAIN_FILE}

# run mantis
run: build
	${INSTALL_DIR}/${BIN_FILE}

# install mantis
install: pre_step build

# unit test
test:
	go test ./...

# format code
format:
	find . -name "*.go" | grep -v "generated/" | grep -v "vendor/" | grep -v "assets/" | xargs gofumpt -l -w

# generate code
generate:
	go generate ./...

# generate swagger file
doc:
	swag init -g ./cmd/mantis/${MAIN_FILE} --parseDependency=false

# install trivy-cli
trivy-cli: pre_step
	CGO_ENABLED=${CGO_ENABLED} go build -mod=vendor -o ${INSTALL_DIR}/trivy-scanner ./cmd/trivy-scanner

# build trivy db image
trivy-db:
	docker build -t harbor.zhonganinfo.com/devcube/trivy-db:${TRIVY_VERSION}-${TRIVY_DB_VERSION} \
		-f app/neptune/build/Dockerfile-trivy-db . --load

# build trivy-scanner image
trivy-scanner:
	docker build -t harbor.zhonganinfo.com/devcube/trivy-scanner:${MANTIS_VERSION}-${TRIVY_VERSION}-${TRIVY_DB_VERSION} \
		-f app/neptune/build/Dockerfile-trivy-scanner . --load

# install esonar-cli
esonar-cli: pre_step
	CGO_ENABLED=${CGO_ENABLED} go build -mod=vendor -o ${INSTALL_DIR}/esonar-scanner ./cmd/esonar-scanner

# build esonar-scanner image
esonar-scanner:
	docker build -t harbor.zhonganinfo.com/devcube/esonar-scanner:${MANTIS_VERSION} \
		-f app/neptune/build/Dockerfile-esonar-scanner . --load

# install jacoco-cli
jacoco-cli: pre_step
	CGO_ENABLED=${CGO_ENABLED} go build -mod=vendor -o ${INSTALL_DIR}/jacoco-coverage ./cmd/jacoco-coverage

# build jacoco-coverage image
jacoco-coverage:
	docker build -t harbor.zhonganinfo.com/devcube/jacoco-coverage:${MANTIS_VERSION} \
		-f app/neptune/build/Dockerfile-jacoco-coverage . --load

# install ui-executor-cli
ui-executor-cli: pre_step
	CGO_ENABLED=${CGO_ENABLED} go build -mod=vendor -o ${INSTALL_DIR}/ui-executor ./cmd/ui-executor

# build ui-executor image
ui-executor:
	docker build -t harbor.zhonganinfo.com/devcube/ui-executor:${MANTIS_VERSION} \
		-f app/venus/build/Dockerfile-ui-executor . --load
package configs

import (
	"encoding/base64"
	"os"
	"time"

	configtypes "git.zhonganinfo.com/zainfo/shiplib/pkgs/config/types"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/cors"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/message/cube"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/redis"
	"gopkg.in/yaml.v2"
)

// Configs 应用配置结构体
type Configs struct {
	App struct {
		Name       string `yaml:"name"`       // 应用名称
		Port       string `yaml:"port"`       // 监听端口
		MaxRoutine int    `yaml:"maxroutine"` // 最大协程数
		Svc        string `yaml:"svc"`        // 服务名
	} `yaml:"app"`
	Domain struct {
		Cube    string `yaml:"cube"`    // Cube域名
		Deckjob string `yaml:"deckjob"` // Deckjob域名
	} `yaml:"domain"`
	Db struct {
		Dsn         string `yaml:"dsn"`           // 数据库连接字符串
		MaxIdle     int    `yaml:"max_idle"`      // 最大空闲连接数
		MaxOpen     int    `yaml:"max_open"`      // 最大打开连接数
		MaxLifeTime int    `yaml:"max_life_time"` // 连接最大生存时间
		Migrate     bool   `yaml:"migrate"`       // 是否执行迁移
	} `yaml:"db"`
	Redis redis.Config `yaml:"redis"` // Redis配置
	Nexus struct {
		Enable   bool                   `yaml:"enable"`   // 是否启用Nexus
		Url      string                 `yaml:"url"`      // Nexus地址
		Username string                 `yaml:"username"` // 用户名
		Password configtypes.Credential `yaml:"password"` // 密码
	} `yaml:"nexus"`
	ElasticSearch struct {
		Enable   bool                   `yaml:"enable"`   // 是否启用ElasticSearch
		Url      []string               `yaml:"url"`      // ES地址列表
		Username string                 `yaml:"username"` // 用户名
		Password configtypes.Credential `yaml:"password"` // 密码
	} `yaml:"elasticsearch"`
	Auth struct {
		AuthType string `yaml:"auth_type"` // 认证类型
		UaUrl    string `yaml:"ua_url"`    // UA地址
		UaSuffix string `yaml:"ua_suffix"` // UA后缀
		AuthUrl  string `yaml:"auth_url"`  // 认证地址
		Token    string `yaml:"token"`     // 令牌
		Service  string `yaml:"service"`   // 服务名称
	} `yaml:"auth"`
	Cmdb cmdb.Config `yaml:"cmdb"` // CMDB配置
	Cors cors.Config `yaml:"cors"` // CORS配置
	Log  struct {
		Path  string `yaml:"path"`  // 日志路径
		Level string `yaml:"level"` // 日志级别
	} `yaml:"log"`
	Store struct {
		Endpoint   string `yaml:"endpoint"`   // 存储端点
		BucketName string `yaml:"bucketName"` // 桶名称
		AccessId   string `yaml:"accessId"`   // 访问ID
		AccessKey  string `yaml:"accessKey"`  // 访问密钥
	} `yaml:"store"`
	Notification cube.Config `yaml:"notification"` // 通知配置
	K8S          struct {
		ConfigPath            string `yaml:"config_path"`             // Kubernetes配置路径
		SyncResourceDisable   bool   `yaml:"sync_resource_disable"`   // 是否禁用资源同步
		SyncResourceNamespace string `yaml:"sync_resource_namespace"` // 同步资源命名空间
		SyncWorkersNum        int    `yaml:"sync_workers_num"`        // 同步工作线程数
		KubeQPS               int    `yaml:"kube_qps"`                // Kubernetes QPS
		KubeBurst             int    `yaml:"kube_burst"`              // Kubernetes突发QPS
	} `yaml:"k8s"`
	Pipeline struct {
		Driver           string            `yaml:"driver"`            // 流水线驱动
		Namespace        string            `yaml:"namespace"`         // 命名空间
		TaskNodeSelector map[string]string `yaml:"taskNodeSelector"`  // 任务节点选择器
		TaskHostNetwork  bool              `yaml:"taskHostNetwork"`   // 任务主机网络
		TaskTimeOut      time.Duration     `yaml:"taskTimeOut"`       // 任务超时时间
		ImagePullSecret  string            `yaml:"image_pull_secret"` // 镜像拉取密钥
	} `yaml:"pipeline"`
	AuditLog struct {
		Endpoint string `yaml:"endpoint"` // 审计日志端点
		Timeout  int64  `yaml:"timeout"`  // 超时时间
	} `yaml:"auditLog"`
	Modules struct {
		Base struct {
			Endpoint string `yaml:"endpoint"` // 基础服务端点
		} `yaml:"base"`
		Jupiter struct {
			Endpoint string `yaml:"endpoint"` // Jupiter服务端点
		} `yaml:"jupiter"`
		Mercury struct {
			Enable     bool `yaml:"enable"`      // 是否启用Mercury模块
			SingleTask bool `yaml:"single_task"` // 是否单任务模式
		} `yaml:"mercury"`
		Venus struct {
			Enable             bool   `yaml:"enable"`             // 是否启用Venus模块
			StepReportCallback string `yaml:"stepReportCallback"` // 步骤报告回调
			FileDownloadUrl    string `yaml:"fileDownloadUrl"`    // 文件下载URL
			VariableUrl        string `yaml:"variableUrl"`        // 变量URL
			ExecuteTask        struct {
				Image  string `yaml:"image"`  // 执行任务镜像
				Cpu    int    `yaml:"cpu"`    // CPU资源
				Memory int    `yaml:"memory"` // 内存资源
			} `yaml:"executeTask"`
		} `yaml:"venus"`
		Neptune struct {
			Enable bool      `yaml:"enable"` // 是否启用Neptune模块
			Sonar  SonarInfo `yaml:"sonar"`  // Sonar配置
			Maven  struct {
				Repo     string                 `yaml:"repo"`     // Maven仓库地址
				UserName string                 `yaml:"username"` // 用户名
				Password configtypes.Credential `yaml:"password"` // 密码
			} `yaml:"maven"`
			TrivyTask struct {
				Image  string `yaml:"image"`  // Trivy任务镜像
				Cpu    int    `yaml:"cpu"`    // CPU资源
				Memory int    `yaml:"memory"` // 内存资源
			} `yaml:"trivyTask"`
			SonarTask struct {
				Image  string `yaml:"image"`  // Sonar任务镜像
				Cpu    int    `yaml:"cpu"`    // CPU资源
				Memory int    `yaml:"memory"` // 内存资源
			} `yaml:"sonarTask"`
			JacocoTask struct {
				Image  string `yaml:"image"`  // Jacoco任务镜像
				Cpu    int    `yaml:"cpu"`    // CPU资源
				Memory int    `yaml:"memory"` // 内存资源
			} `yaml:"jacocoTask"`
		} `yaml:"neptune"`
	} `yaml:"modules"`
}

// RepoInstance 仓库实例对象
type RepoInstance struct {
	ID                 string                 `yaml:"id"`                 // ID
	SupportProductList []GenField             `yaml:"supportProductList"` // 支持的产品列表
	Type               string                 `yaml:"type"`               // 类型
	Token              string                 `yaml:"token"`              // 令牌
	Username           string                 `yaml:"username"`           // 用户名
	PassWord           configtypes.Credential `yaml:"password"`           // 密码
	Url                string                 `yaml:"url"`                // URL
	InstanceName       string                 `yaml:"instanceName"`       // 实例名称
	CompanyId          string                 `yaml:"companyId"`          // 公司ID
}

// GenField 通用字段
type GenField struct {
	Label string `json:"label"` // 标签
	Value string `json:"value"` // 值
	Icon  string `json:"icon"`  // 图标
}

// SonarInfo Sonar信息
type SonarInfo struct {
	Domain string `yaml:"domain"` // 域名
	Token  string `yaml:"token"`  // 令牌
}

// GetBasicToken 获取基本认证令牌
func (s SonarInfo) GetBasicToken() string {
	return base64.StdEncoding.EncodeToString([]byte(s.Token + ":"))
}

// GitInfo Git信息
type GitInfo struct {
	Domain string `yaml:"domain"` // 域名
	User   string `yaml:"user"`   // 用户
	Token  string `yaml:"token"`  // 令牌
}

// Config 全局配置实例
var Config *Configs

// Init 初始化配置
func Init() {
	file, err := os.ReadFile("config.yml")
	if err != nil {
		panic(err)
	}
	err = yaml.Unmarshal(file, &Config)
	if err != nil {
		panic(err)
	}
}

package main

import (
	"os"

	uiagent "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"github.com/spf13/cobra"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "ui-executor",
		Short: "ui-executor command-line interface",
		Long:  `ui-executor command-line interface`,
		RunE:  run,
	}
	fs := rootCmd.Flags()
	fs.StringP("input", "", "", "用例json")
	fs.StringP("callback", "", "", "步骤执行结果回调地址")
	fs.StringP("reportid", "", "", "当前执行的报告id")
	fs.StringP("filedownloadurl", "", "", "下载文件url")
	fs.StringP("variableurl", "", "", "请求变量地址")
	fs.StringP("env", "", "", "环境")
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func run(cmd *cobra.Command, args []string) error {
	cf := cmd.Flags()
	caseJSON, err := cf.GetString("input")
	if err != nil {
		panic(err)
	}
	callback, err := cf.GetString("callback")
	if err != nil {
		panic(err)
	}
	reportId, err := cf.GetString("reportid")
	if err != nil {
		panic(err)
	}
	filedownloadurl, err := cf.GetString("filedownloadurl")
	if err != nil {
		panic(err)
	}
	variableurl, err := cf.GetString("variableurl")
	if err != nil {
		panic(err)
	}
	env, err := cf.GetString("env")
	if err != nil {
		panic(err)
	}
	snowflake.Init()
	return uiagent.Run(caseJSON, callback, reportId, filedownloadurl, variableurl, env)
}

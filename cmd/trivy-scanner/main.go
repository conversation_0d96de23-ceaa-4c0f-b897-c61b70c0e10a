package main

import (
	"fmt"
	"log"
	"os"
	"strings"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner/docker"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner/generic"
	"github.com/spf13/cobra"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "trivy-scanner",
		Short: "trivy-scanner command-line interface",
		Long:  `trivy-scanner command-line interface`,
		RunE:  run,
	}

	fs := rootCmd.Flags()
	fs.StringP("type", "", "", "制品类型可选: Generic、Docker")
	fs.StringP("image", "", "", "镜像名称")
	fs.StringP("docker-addr", "", "", "镜像仓库地址")
	fs.StringP("docker-user", "", "", "镜像仓库用户")
	fs.StringP("docker-password", "", "", "镜像仓库密码")
	fs.StringP("upload-result-url", "", "", "上传扫描报告接口URL")
	fs.StringP("cube-mantis-url", "", "", "cube-mantis服务地址")
	fs.StringP("filename", "", "", "制品文件名称")
	fs.StringP("artifact-download-url", "", "", "制品下载URL")
	fs.StringP("artifact-download-token", "", "", "制品下载Token")
	fs.StringP("artifact-download-authorization", "", "", "制品下载Authorization")
	fs.StringP("white-cve", "", "", "CVE白名单")
	fs.StringP("non-ssl", "", "", "镜像仓库是否为http")

	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func run(cmd *cobra.Command, args []string) error {
	log.Println("当前主机IP: ", os.Getenv("TASK_HOST_IP"))
	cf := cmd.Flags()
	artifactType, err := cf.GetString("type")
	if err != nil {
		return err
	}
	log.Println("制品扫描类型: ", artifactType)

	var sc trivyscanner.Scanner
	switch strings.ToLower(artifactType) {
	case "generic":
		sc, err = generic.NewGenericScanner(cf)
	case "docker":
		sc, err = docker.NewDockerScanner(cf)
	default:
		return fmt.Errorf("不支持的制品类型: %s", artifactType)
	}
	if err != nil {
		return err
	}

	// 执行Trivy扫描
	log.Println("执行Trivy扫描")
	err = sc.Exec()
	if err != nil {
		return err
	}

	// 上传扫描报告
	log.Println("上传扫描报告")
	err = sc.UploadResult()
	if err != nil {
		return err
	}

	return nil
}

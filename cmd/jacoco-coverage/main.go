package main

import (
	"log"
	"os"

	jacococoverage "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/jacoco-coverage"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/jacoco-coverage/params"
	"github.com/spf13/cobra"
)

func main() {
	rootCmd := &cobra.Command{
		Use:   "jacoco-coverage",
		Short: "jacoco-coverage command-line interface",
		Long:  `jacoco-coverage command-line interface`,
		RunE:  run,
	}
	fs := rootCmd.Flags()
	fs.StringP("mode", "", "", "模式: increment(增量)/total(全量)")
	fs.StringP("app-name", "", "", "应用名")
	fs.StringP("ip", "", "", "ip")
	fs.StringP("port", "", "", "port")
	fs.StringP("packages", "", "", "comma separated packages, such as com.zhongan.a.*,com.zhongan.b.*")
	fs.StringP("git-url", "", "", "svn branch urls for diff lists")
	fs.StringP("git-user", "", "", "git user")
	fs.StringP("git-token", "", "", "git token")
	fs.StringP("exclude", "", "", "excluding some class for Coverage")
	fs.StringP("module-name", "", "", "module name")
	fs.StringP("branch", "", "", "branch")
	fs.StringP("oss-path", "", "", "oss-path")
	fs.StringP("oss-endpoint", "", "", "oss endpoint")
	fs.StringP("oss-bucket-name", "", "", "oss bucket name")
	fs.StringP("oss-access-id", "", "", "oss access id")
	fs.StringP("oss-access-key", "", "", "oss access key")
	fs.BoolP("oss-path-style", "", false, "是否启用反响代理")
	fs.StringP("task-no", "", "", "task no")
	fs.StringP("base-commit-id", "", "", "base commit id")
	fs.StringP("compare-commit-id", "", "", "compare commit id")
	fs.StringP("mantis-call-back", "", "", "mantis 回调地址")
	fs.StringP("old-exec-path", "", "", "上一次执行的exec文件在oss中的路径")
	if err := rootCmd.Execute(); err != nil {
		os.Exit(1)
	}
}

func run(cmd *cobra.Command, args []string) error {
	cf := cmd.Flags()
	p, err := params.InitParams(cf)
	if err != nil {
		return err
	}
	log.Printf("初始化参数完成: %+v", p)
	if err := jacococoverage.Exec(p); err != nil {
		return err
	}
	return nil
}

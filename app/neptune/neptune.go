package neptune

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/consumer"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/tekton/tasks"
	"knative.dev/pkg/apis"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/migration"
)

func Init() {
	// 初始化数据库迁移
	migration.Init()

	tasks.AddTaskRunDealers(map[string]func(condition apis.Condition, labels map[string]string) error{
		"trivy-scanner":   artifact.ArtifactStatusAndResultLinkUpdate,
		"sonar-scanner":   quality.SonarStatusAndResultLinkUpdate,
		"jacoco-coverage": quality.JacocoStatusAndResultLinkUpdate,
	})

	// 初始化消费者客户端
	consumer.Init()

	// 初始化sonar
	help.InitSonar()

	// 初始化质量阀
	quality.InitSonarQualityGates()

	// 初始化规则集模版
	quality.InitDefaultProfileToTemplate()
}

{{- /* Template based on https://docs.gitlab.com/ee/user/application_security/container_scanning/#reports-json-format */ -}}
{
  "version": "15.0.7",
  "vulnerabilities": [
  {{- $t_first := true }}
  {{- range . }}
  {{- $target := .Target }}
    {{- $image := $target | regexFind "[^\\s]+" }}
    {{- range .Vulnerabilities -}}
    {{- if $t_first -}}
      {{- $t_first = false -}}
    {{ else -}}
      ,
    {{- end }}
    {
      "vulnerabilityID": "{{ .VulnerabilityID }}",
      "title": {{ .Title | printf "%q" }},
      "pkgName": "{{ .PkgName }}",
      "pkgID": "{{ .PkgID }}",
      "pkgPath": "{{ .PkgPath }}",
      "installedVersion": "{{ .InstalledVersion }}",
      "fixedVersion": "{{ .FixedVersion }}",
      "status": "{{ .Status }}",
      "description": {{ .Description | printf "%q" }},
      "severity": {{ if eq .Severity "UNKNOWN" -}}
                    "Unknown"
                  {{- else if eq .Severity "LOW" -}}
                    "Low"
                  {{- else if eq .Severity "MEDIUM" -}}
                    "Medium"
                  {{- else if eq .Severity "HIGH" -}}
                    "High"
                  {{- else if eq .Severity "CRITICAL" -}}
                    "Critical"
                  {{-  else -}}
                    "{{ .Severity }}"
                  {{- end }},
      "solution": {{ if .FixedVersion -}}
                    "Upgrade {{ .PkgName }} to {{ .FixedVersion }}"
                  {{- else -}}
                    "No solution provided"
                  {{- end }}
    }
    {{- end -}}
  {{- end }}
  ],
  "remediations": []
}

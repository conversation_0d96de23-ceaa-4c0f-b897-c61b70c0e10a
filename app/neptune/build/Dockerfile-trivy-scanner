# Build mantis
FROM harbor.zhonganinfo.com/devcube/golang:1.22 AS builder
WORKDIR $GOPATH/src/cube-mantis
COPY . .
RUN INSTALL_DIR=/cube-mantis make trivy-cli


# Trivy database
FROM harbor.zhonganinfo.com/devcube/trivy-db:0.63.0-20250601 AS trivy


# Install base tools
FROM harbor.zhonganinfo.com/devcube/ubuntu:24.04

# Install trivy
COPY --from=builder /cube-mantis/trivy-scanner /usr/local/bin/trivy-scanner
COPY --from=trivy /usr/local/bin/trivy /usr/local/bin/trivy
COPY --from=trivy /root/.cache/trivy /root/.cache/trivy

RUN chmod a+x /usr/local/bin/trivy-scanner
COPY app/neptune/build/gitlab.tpl /root/gitlab.tpl

CMD ["/bin/bash"]
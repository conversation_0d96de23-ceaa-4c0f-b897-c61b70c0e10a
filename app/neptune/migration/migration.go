package migration

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"github.com/duke-git/lancet/v2/xerror"
)

// Init 初始化数据
func Init() {
	ctx := &commoncontext.MantisContext{}
	if !configs.Config.Db.Migrate {
		return
	}
	err := gormx.GetDB(ctx).AutoMigrate(
		&artifact.ScanPlan{},
		&artifact.ScanTask{},
		&artifact.ScanTaskDetail{},
		&artifact.ScanTaskExecHis{},
		&artifact.ScanReport{},
		&quality.ScanLanguage{},
		&quality.ScanProfileTemplate{},
		&quality.ScanProgramme{},
		&quality.ScanProgrammeProfile{},
		&quality.ScanQualityGates{},
		&quality.ScanTaskFilterConfig{},
		&quality.ScanTestExecHis{},
		&quality.ScanTestTask{},
		&quality.JacocoCoverageExecHis{})
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "error in auto migrate table"))
	}
	InitScanLanguages()
}

func InitScanLanguages() {
	ctx := &commoncontext.MantisContext{}
	// 清空预制数据
	gormx.DeleteAllRecords(ctx, &quality.ScanLanguage{})
	languages := []quality.ScanLanguage{
		{Addons: models.NewAddons(), Name: "CSS", Key: "css"},
		{Addons: models.NewAddons(), Name: "Go", Key: "go"},
		{Addons: models.NewAddons(), Name: "JSP", Key: "jsp"},
		{Addons: models.NewAddons(), Name: "Java", Key: "java"},
		{Addons: models.NewAddons(), Name: "JavaScript", Key: "js"},
		{Addons: models.NewAddons(), Name: "PHP", Key: "php"},
		{Addons: models.NewAddons(), Name: "Python", Key: "py"},
		{Addons: models.NewAddons(), Name: "Scala", Key: "scala"},
		{Addons: models.NewAddons(), Name: "XML", Key: "xml"},
		{Addons: models.NewAddons(), Name: "C#", Key: "cs"},
	}
	gormx.InsertBatchX(ctx, &languages)
}

package convert

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	common_models "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

func ScanProgrammeModelToDTO(programme quality.ScanProgramme) dto.ScanProgrammeDTO {
	return dto.ScanProgrammeDTO{
		Id:        programme.Id,
		Name:      programme.Name,
		Describe:  programme.Describe,
		IsDefault: programme.IsDefault,
	}
}

func ScanProgrammeDTOToModel(programmeDto dto.ScanProgrammeDTO) quality.ScanProgramme {
	return quality.ScanProgramme{
		Addons: common_models.Addons{
			Id: programmeDto.Id,
		},
		Name:      programmeDto.Name,
		Describe:  programmeDto.Describe,
		IsDefault: programmeDto.IsDefault,
	}
}

func ScanTestExecHisDTOToModel(execHisDto dto.ScanTestExecHisDTO) quality.ScanTestExecHis {
	execHis := execHisDto.ScanTestExecHis
	return execHis
}

func ScanTestExecHisModelToDTO(execHis quality.ScanTestExecHis) dto.ScanTestExecHisDTO {
	return dto.ScanTestExecHisDTO{
		ScanTestExecHis: execHis,
	}
}

package quality

import (
	"fmt"
	"mime/multipart"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/xmlx"
)

type ScanProfileTemplateService struct{}

func (ScanProfileTemplateService) SaveScanProfileTemplate(ctx *commoncontext.MantisContext, template quality.ScanProfileTemplate, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper
	// 判断是否重名
	profiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{QualityProfile: template.Name})
	if len(profiles) != 0 {
		logger.Logger.Panic("规则集名称重复，请重新命名！")
	}
	if template.Id != 0 {
		oldTemplate := quality.ScanProfileTemplate{}
		oldTemplate.Id = template.Id
		gormx.SelectOneByConditionX(ctx, &oldTemplate)
		// 修改库
		template.GmtModified = times.Now()
		template.Modifier = user.AdAccount
		gormx.UpdateOneByConditionX(ctx, &template)
		// 修改sonar
		sonarHelper.RenameProfile(oldTemplate.Key, template.Name)
	} else {
		// 新增sonar
		key := sonarHelper.CreateProfile(template.Name, template.Language)
		// 新增库
		template.Creator = user.AdAccount
		template.Modifier = user.AdAccount
		template.GmtCreated = times.Now()
		template.GmtModified = times.Now()
		template.IsDeleted = commonconstants.DeleteNo
		template.Key = key
		gormx.InsertUpdateOneX(ctx, &template)
	}
	return template.Id
}

func (ScanProfileTemplateService) CopyTemplate(ctx *commoncontext.MantisContext, id int64, name string, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper
	// 查询被复制的template
	template := quality.ScanProfileTemplate{}
	template.Id = id
	template.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &template)
	// 操作sonar
	newKey := sonarHelper.CopyProfile(template.Key, name)
	// 写库
	template.Id = 0
	template.Name = name
	template.Key = newKey
	template.Creator = user.AdAccount
	template.Modifier = user.AdAccount
	template.GmtCreated = times.Now()
	template.GmtModified = times.Now()
	gormx.InsertUpdateOneX(ctx, &template)
	return template.Id
}

func (ScanProfileTemplateService) DeleteTemplate(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	sonarHelper := help.DefaultSonarHelper
	template := quality.ScanProfileTemplate{}
	template.Id = id
	template.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &template)
	// 操作sonar
	sonarHelper.DeleteProfile(template.Language, template.Name)
	// 操作数据库
	template.IsDeleted = commonconstants.DeleteYes
	template.Modifier = user.AdAccount
	template.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &template)
}

func (ScanProfileTemplateService) GetTemplatesByLanguage(ctx *commoncontext.MantisContext, language string) []quality.ScanProfileTemplate {
	sonarHelper := help.DefaultSonarHelper
	// 搜索sonar里该语言的所有profile
	profilesInSonar := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{Language: language})
	profileKeyMap := make(map[string]quality.ScanProfileTemplate)
	for _, profile := range profilesInSonar {
		profileKeyMap[profile.Key] = profile
	}
	// 搜索数据库中该语言的模版
	profilesInDB := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
			Eq("language", language).Eq("is_deleted", commonconstants.DeleteNo),
		&profilesInDB)
	for i, template := range profilesInDB {
		scanProfileTemplate := profileKeyMap[template.Key]
		profilesInDB[i].LanguageName = scanProfileTemplate.LanguageName
		profilesInDB[i].ActiveRuleCount = scanProfileTemplate.ActiveRuleCount
		profilesInDB[i].ActiveDeprecatedRuleCount = scanProfileTemplate.ActiveDeprecatedRuleCount
		profilesInDB[i].IsDefault = scanProfileTemplate.IsDefault
	}
	return profilesInDB
}

func (ScanProfileTemplateService) ImportProfileToTemplate(ctx *commoncontext.MantisContext, file *multipart.FileHeader, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper
	fileToString := common_util.ReadFileToString(file)
	name, err := xmlx.GetDataByXpath(fileToString, "/profile/name")
	if err != nil {
		logger.Logger.Panicf("解析xml失败, err=%v", err)
	}
	language, err := xmlx.GetDataByXpath(fileToString, "/profile/language")
	if err != nil {
		logger.Logger.Panicf("解析xml失败, err=%v", err)
	}
	// 去sonar查询是否存在同名规则集
	logger.Logger.Infof("import profiles...name=%s,language=%s", name, language)
	sonarQualityProfiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{
		QualityProfile: fmt.Sprintf("%s", name),
		Language:       fmt.Sprintf("%s", language),
	})
	if len(sonarQualityProfiles) != 0 {
		logger.Logger.Panicf("规则集已经存在！name=%s,language=%s", name, language)
	}
	stream, err := file.Open()
	if err != nil {
		logger.Logger.Panicf("解析xml失败, err=%v", err)
	}
	profileKey := sonarHelper.ImportProfiles(stream, file.Filename)
	err1 := recover()
	if err1 != nil {
		logger.Logger.Panicf("规则集导入sonar失败！")
	}
	logger.Logger.Infof("规则集模版%s入库", name)
	insertTemplate := quality.ScanProfileTemplate{
		Key:       profileKey,
		Name:      fmt.Sprintf("%s", name),
		Language:  fmt.Sprintf("%s", language),
		IsBuiltIn: false,
	}
	insertTemplate.GmtCreated = times.Now()
	insertTemplate.GmtModified = times.Now()
	insertTemplate.Creator = user.AdAccount
	insertTemplate.Modifier = user.AdAccount
	gormx.InsertUpdateOneX(ctx, &insertTemplate)
	return insertTemplate.Id
}

func (ScanProfileTemplateService) ExportProfileTemplate(ctx *commoncontext.MantisContext, templateId int64) (string, string) {
	sonarHelper := help.DefaultSonarHelper
	template := quality.ScanProfileTemplate{}
	template.Id = templateId
	gormx.SelectOneByConditionX(ctx, &template)
	return template.Name + "_" + template.LanguageName + ".xmlx", sonarHelper.DownloadProfiles(template.Name, template.Language)
}

func (ScanProfileTemplateService) SearchRulesInTemplate(req help.SonarRuleSearchReq) *gormx.PageResult {
	sonarHelper := help.DefaultSonarHelper
	rules, total := sonarHelper.SearchRules(req)
	return &gormx.PageResult{
		Pages:       total / req.PageSize,
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        rules,
	}
}

func InitDefaultProfileToTemplate() {
	sonarHelper := help.DefaultSonarHelper
	defer func() {
		err := recover()
		if err != nil {
			logger.Logger.Errorf("初始化规则集模版失败, err = %+v", err)
		}
	}()
	templates := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(&commoncontext.MantisContext{}, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
		Eq("is_deleted", commonconstants.DeleteNo).Eq("is_built_in", true), &templates)
	// 有数据，不初始化
	if len(templates) != 0 {
		return
	}
	// 无数据，初始化
	profiles := sonarHelper.SearchQualityProfiles(help.SonarQualityProfilesSearchReq{})
	profilesWrite := make([]quality.ScanProfileTemplate, 0)
	for i := range profiles {
		if profiles[i].IsBuiltIn {
			profiles[i].Creator = ""
			profiles[i].Modifier = ""
			profiles[i].GmtCreated = times.Now()
			profiles[i].GmtModified = times.Now()
			profilesWrite = append(profilesWrite, profiles[i])
		}
	}
	if len(profilesWrite) != 0 {
		gormx.InsertBatchX(&commoncontext.MantisContext{}, profilesWrite)
	}
}

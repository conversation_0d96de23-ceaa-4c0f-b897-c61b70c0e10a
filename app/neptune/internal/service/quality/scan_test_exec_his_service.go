package quality

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/convert"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTestExecHisService struct{}

type updateSonarCallJsonObject struct {
	Properties struct {
		IsHookUseful string `json:"sonar.analysis.isHookUseful"`
	} `json:"properties"`
	Project struct {
		Key string `json:"key"`
		Url string `json:"url"`
	} `json:"project"`
	Status      string `json:"status"`
	QualityGate struct {
		Conditions []struct {
			Metric string `json:"metric"`
			Value  string `json:"value"`
		} `json:"conditions"`
	} `json:"qualityGate"`
}

var (
	taskDao          dao.ScanTestTaskDao
	shipInvokeHelper help.ShipInvokeHelper
)

func (s ScanTestExecHisService) InitRecord(ctx *commoncontext.MantisContext, execHisDto dto.ScanTestExecHisDTO) int64 {
	execHis := convert.ScanTestExecHisDTOToModel(execHisDto)
	gormx.InsertUpdateOneX(ctx, &execHis)
	return execHisDto.Id
}

func (s ScanTestExecHisService) GetScanTestExecHis(ctx *commoncontext.MantisContext, id int64) *dto.ScanTestExecHisDTO {
	scanTestExecHis := quality.ScanTestExecHis{}
	scanTestExecHis.Id = id
	gormx.SelectOneByConditionX(ctx, &scanTestExecHis)
	execHisDTO := convert.ScanTestExecHisModelToDTO(scanTestExecHis)
	return &execHisDTO
}

func (ScanTestExecHisService) UpdateStatusById(ctx *commoncontext.MantisContext, status int8, id int64) {
	scanTestExecHis := quality.ScanTestExecHis{}
	scanTestExecHis.Id = id
	scanTestExecHis.Status = status
	scanTestExecHis.StatusMsg = task.StatusCodeToMemo(status)
	scanTestExecHis.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &scanTestExecHis)
}

func (s ScanTestExecHisService) SonarUpdateCallBack(ctx *commoncontext.MantisContext, id int64, jsonStr string) {
	logger.Logger.Info("start scanTestExecHisService.SonarUpdateCallBack...")
	scanTestTask := taskDao.SelectTaskByLastExecHisId(ctx, id)
	s.updateCallBackBySonar(ctx, id, jsonStr, scanTestTask.PdfGen)
	if scanTestTask.From == constants.SourcePipeline {
		logger.Logger.Info("scanTestExecHisService.SonarUpdateCallBack...is ship will invoke ship")
		hisDTO := s.GetScanTestExecHis(ctx, id)
		shipInvokeHelper.InvokeStaticPublicShip(ctx, *hisDTO)
	} else {
		logger.Logger.Info("scanTestExecHisService.SonarUpdateCallBack...is normal user, do nothing")
	}
	logger.Logger.Info("end scanTestExecHisService.SonarUpdateCallBack...")
	return
}

func (s ScanTestExecHisService) updateCallBackBySonar(ctx *commoncontext.MantisContext, id int64, jsonStr string, pdfGen bool) {
	jsonObject := updateSonarCallJsonObject{}
	jsonx.UnMarshal([]byte(jsonStr), &jsonObject)
	properties := jsonObject.Properties
	isHookUseful := properties.IsHookUseful
	if isHookUseful == "false" {
		return
	}
	scanTestExecHisDto := s._updateCallBackBySonar(ctx, id, jsonObject)
	model := convert.ScanTestExecHisDTOToModel(scanTestExecHisDto)
	if !pdfGen {
		model.PdfUrl = ""
	}
	model.GmtModified = times.Now()
	gormx.InsertUpdateOneX(ctx, &model)
	err := recover()
	if err != nil {
		s.UpdateStatusById(ctx, task.TaskStatusFail, id)
	}
}

func (s ScanTestExecHisService) _updateCallBackBySonar(ctx *commoncontext.MantisContext, id int64, jsonObject updateSonarCallJsonObject) dto.ScanTestExecHisDTO {
	status := jsonObject.Status
	reportUrl := jsonObject.Project.Url
	scanTestExecHisDto := dto.ScanTestExecHisDTO{}
	scanTestExecHis := quality.ScanTestExecHis{}
	scanTestExecHis.Id = id
	gormx.SelectOneByConditionX(ctx, &scanTestExecHis)
	scanTestExecHisDto.ScanTestExecHis = scanTestExecHis
	scanTestExecHisDto.SonarUrl = reportUrl
	if status != "SUCCESS" {
		logger.Logger.Warnf("sonar 分析失败，exec_his_id=%d,status=%s", id, status)
		scanTestExecHisDto.Status = task.TaskStatusFail
		scanTestExecHisDto.StatusMsg = task.StatusCodeToMemo(task.TaskStatusFail)
		return scanTestExecHisDto
	}
	conditions := jsonObject.QualityGate.Conditions
	for _, condition := range conditions {
		key := condition.Metric
		value, err := strconv.ParseFloat(condition.Value, 64)
		if err != nil {
			continue
		}
		if condition.Value != "" {
			switch key {
			case "lines":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.Lines = int32(value)
			case "comment_lines":
				scanTestExecHisDto.CommentLines = int32(value)
			case "complexity":
				scanTestExecHisDto.Complexity = int32(value)
			case "ncloc":
				scanTestExecHisDto.Ncloc = int32(value)
			case "lines_to_cover":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.LinesToCover = int32(value)
			case "line_coverage":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.LineCoverage = value
			case "blocker_violations":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.BlockerViolations = int32(value)
			case "critical_violations":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.CriticalViolations = int32(value)
			case "major_violations":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.MajorViolations = int32(value)
			case "minor_violations":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.MinorViolations = int32(value)
			case "info_violations":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.InfoViolations = int32(value)
			case "comment_lines_density":
				scanTestExecHisDto.CommentLinesDensity = value
			case "duplicated_lines_density":
				if scanTestExecHisDto.Mode == constants.StandardIncrement {
					continue
				}
				scanTestExecHisDto.DuplicatedLinesDensity = value
			case "test_success_density":
				scanTestExecHisDto.TestSuccessDensity = value
			case "branch_coverage":
				scanTestExecHisDto.BranchCoverage = value
			case "vulnerabilities":
				scanTestExecHisDto.Vulnerabilities = int32(value)
			case "new_lines_to_cover":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.LinesToCover = int32(value)
			case "new_lines":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.Lines = int32(value)
			case "new_blocker_violations":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.BlockerViolations = int32(value)
			case "new_critical_violations":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.CriticalViolations = int32(value)
			case "new_major_violations":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.MajorViolations = int32(value)
			case "new_minor_violations":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.MinorViolations = int32(value)
			case "new_info_violations":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.InfoViolations = int32(value)
			case "new_line_coverage":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.LineCoverage = value
			case "new_duplicated_lines_density":
				if scanTestExecHisDto.Mode == constants.StandardTotal {
					continue
				}
				scanTestExecHisDto.DuplicatedLinesDensity = value
			}
		}
	}
	scanTestExecHisDto.Status = task.TaskStatusSucc
	scanTestExecHisDto.StatusMsg = task.StatusCodeToMemo(scanTestExecHisDto.Status)
	scanTestExecHisDto.SonarProjectKey = jsonObject.Project.Key
	scanTestExecHisDto.PdfUrl = configs.Config.Modules.Neptune.Sonar.Domain + "/api/pdfreport/get?componentKey=" + jsonObject.Project.Key
	return scanTestExecHisDto
}

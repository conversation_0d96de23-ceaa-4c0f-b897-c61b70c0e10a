package quality

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	"github.com/avast/retry-go"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"knative.dev/pkg/apis"
)

type JacocoCoverageService struct{}

var shipInvoke remote.ShipInvoke

// PipelineRegister 流水线注册代码覆盖率任务
func (JacocoCoverageService) PipelineRegister(ctx *commoncontext.MantisContext, req dto.PipelineCollectCoverageReqDTO) {
	app, err := cmdb.Client.GetApplication(ctx, url.Values{"app_name": []string{req.AppName}})
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	if app.AppId == 0 {
		logger.Logger.Panicf("未查询到应用:%s", req.AppName)
	}
	req.AppId = utils.IDString(app.AppId)
	his := quality.JacocoCoverageExecHis{
		Addons: models.NewAddons(),
		AppId:  req.AppId,
		FromId: req.Pid,
		Env:    req.Env,
		Stage:  strings.ToLower(req.Stage),
		From:   constants.SourcePipeline,
		Status: task.TaskStatusWait,
		Mode:   req.Mode,
		Branch: req.Branch,
	}
	if his.Stage == "" {
		his.Stage = "cd"
	}
	reqStr := string(jsonx.Marshal(&req))
	his.Request = reqStr
	// 查询上次历史
	lastHises := make([]quality.JacocoCoverageExecHis, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&quality.JacocoCoverageExecHis{}).
			Eq("from_id", req.Pid).Eq("app_id", req.AppId).Eq("env", req.Env).
			Eq("stage", strings.ToLower(req.Stage)).Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("id"),
		&lastHises)
	if len(lastHises) != 0 {
		his.OldExecPath = lastHises[0].ExecPath
	}
	gormx.InsertUpdateOneX(ctx, &his)
}

// PipelineExec 流水线执行代码覆盖率任务
func (JacocoCoverageService) PipelineExec(ctx *commoncontext.MantisContext, req dto.PipelineCollectCoverageReqDTO) {
	// 查询记录
	app, err := cmdb.Client.GetApplication(ctx, url.Values{"app_name": []string{req.AppName}})
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	lastHises := make([]quality.JacocoCoverageExecHis, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&quality.JacocoCoverageExecHis{}).
			Eq("from_id", req.Pid).Eq("app_id", app.AppId).Eq("env", req.Env).
			Eq("stage", strings.ToLower(req.Stage)).Eq("is_deleted", commonconstants.DeleteNo).Eq("from", constants.SourcePipeline).
			OrderByDesc("id"),
		&lastHises)
	if len(lastHises) == 0 {
		logger.Logger.Panicf("未注册代码覆盖率任务, pid: %d, appId: %d, Env: %s, Stage: %s", req.Pid, app.AppId, req.Env, req.Stage)
	}
	his := lastHises[0]
	hisReq := dto.PipelineCollectCoverageReqDTO{}
	jsonx.UnMarshal([]byte(his.Request), &hisReq)
	hisReq.CallBackAddr = req.CallBackAddr
	hisReq.BaseCommitId = req.BaseCommitId
	hisReq.CompareCommitId = req.CompareCommitId
	hisReq.Mode = req.Mode
	his.Request = string(jsonx.Marshal(&hisReq))
	his.Status = task.TaskStatusRun
	his.Mode = req.Mode
	gormx.UpdateOneByConditionX(ctx, &his)
	gitUrl := app.VcsFullPath
	gitInfo := gitHelper.GetGitInfoByUrl(gitUrl)
	// 向队列内发送消息以发起代码覆盖率任务
	payload := payload.JacocoCoveragePayload{
		Mode:    his.Mode,
		AppName: app.Name,
		Ip:      hisReq.Ip,
		Port:    hisReq.Ports,
		Git: struct {
			GitUrl string `json:"gitUrl"`
			User   string `json:"user"`
			Token  string `json:"token"`
			Branch string `json:"branch"`
		}{
			GitUrl: app.VcsFullPath,
			User:   gitInfo.User,
			Token:  gitInfo.Token,
			Branch: his.Branch,
		},
		Exclude:    "",
		ModuleName: "",
		Oss: struct {
			Path      string `json:"path"`
			Endpoint  string `json:"endpoint"`
			Bucket    string `json:"bucket"`
			AccessId  string `json:"accessId"`
			AccessKey string `json:"accessKey"`
			PathStyle bool   `json:"pathStyle"`
		}{
			Path:      hisReq.MountedJarPath,
			Endpoint:  configs.Config.Store.Endpoint,
			Bucket:    configs.Config.Store.BucketName,
			AccessId:  configs.Config.Store.AccessId,
			AccessKey: configs.Config.Store.AccessKey,
			PathStyle: false,
		},
		TaskNo:          strconv.FormatInt(his.Id, 10),
		BaseCommitId:    hisReq.BaseCommitId,
		CompareCommitId: hisReq.CompareCommitId,
		HisId:           his.Id,
		MantisCallBack:  configs.Config.App.Svc + "/rick/openapi/coverage/callBack",
		OldExecPath:     his.OldExecPath,
	}
	pubsub.SendToRedisQueue(payload, his.From)
}

// AptRegister API清理并注册任务
func (j JacocoCoverageService) AptRegister(ctx *commoncontext.MantisContext, req []dto.AptResetAndRegisterCoverageReqDTO) []map[string]any {
	// 清理代码覆盖率任务
	tasks := make([]func(), 0)
	for _, reqDto := range req {
		tasks = append(tasks, func() {
			j.cleanJacoco(reqDto.Ip, strconv.FormatInt(reqDto.Ports, 10))
		})
	}
	_ = goroutine.RunTasksWithoutLimit(tasks)
	// 注册任务
	res := make([]map[string]any, 0)
	for _, reqDto := range req {
		// 查询bid是否已经被占用
		existList := make([]quality.JacocoCoverageExecHis, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&quality.JacocoCoverageExecHis{}).Eq("from_id", reqDto.Bid).Eq("is_deleted", commonconstants.DeleteNo),
			&existList)
		if len(existList) != 0 {
			logger.Logger.Panicf("存在重复bid, bid=%d", reqDto.Bid)
		}
		his := quality.JacocoCoverageExecHis{
			Addons: models.NewAddons(),
			AppId:  reqDto.AppId,
			FromId: reqDto.Bid,
			Env:    reqDto.Env,
			Stage:  reqDto.Stage,
			From:   constants.SourceWeb,
			Status: task.TaskStatusWait,
			Mode:   constants.StandardTotal,
		}
		// 查是不是存在ship之前注册的记录
		shipHisList := make([]quality.JacocoCoverageExecHis, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&quality.JacocoCoverageExecHis{}).
				Eq("app_id", reqDto.AppId).Eq("env", reqDto.Env).Eq("from", constants.SourcePipeline).OrderByDesc("gmt_created"),
			&shipHisList)
		if len(shipHisList) == 0 {
			res = append(res, map[string]any{
				"appId":  reqDto.AppId,
				"taskNo": strconv.FormatInt(his.Id, 10),
				"bid":    reqDto.Bid,
				"status": "F",
			})
			continue
		}
		shipHis := shipHisList[0]
		his.Request = shipHis.Request
		if shipHis.ExecPath == "" {
			his.OldExecPath = shipHis.OldExecPath
		} else {
			his.OldExecPath = shipHis.ExecPath
		}
		gormx.InsertUpdateOneX(ctx, &his)
		res = append(res, map[string]any{
			"appId":  reqDto.AppId,
			"taskNo": strconv.FormatInt(his.Id, 10),
			"bid":    reqDto.Bid,
			"status": "S",
		})
	}
	return res
}

// AptExec jupiter执行任务
func (JacocoCoverageService) AptExec(ctx *commoncontext.MantisContext, req []dto.AptExecCoverageReqDTO) any {
	res := make([]string, 0)
	for _, reqDto := range req {
		// 查询任务
		lastHises := make([]quality.JacocoCoverageExecHis, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&quality.JacocoCoverageExecHis{}).
				Eq("from_id", reqDto.Bid).Eq("app_id", reqDto.AppId).
				Eq("from", constants.SourceWeb).Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("id"),
			&lastHises)
		if len(lastHises) == 0 {
			logger.Logger.Warnf("未查询到Jupiter注册的任务, bid:%d, appId:%d", reqDto.Bid, reqDto.AppId)
			continue
		}
		his := lastHises[0]
		res = append(res, strconv.FormatInt(his.Id, 10))
		req1 := dto.PipelineCollectCoverageReqDTO{}
		jsonx.UnMarshal([]byte(his.Request), &req1)
		app, err := cmdb.Client.GetAppById(ctx, his.AppId)
		if err != nil {
			logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
		}
		gitUrl := app.VcsFullPath
		gitInfo := gitHelper.GetGitInfoByUrl(gitUrl)
		// 向队列内发送消息以发起功能覆盖率任务
		payload := payload.JacocoCoveragePayload{
			Mode:    his.Mode,
			AppName: app.Name,
			Ip:      req1.Ip,
			Port:    req1.Ports,
			Git: struct {
				GitUrl string `json:"gitUrl"`
				User   string `json:"user"`
				Token  string `json:"token"`
				Branch string `json:"branch"`
			}{
				GitUrl: app.VcsFullPath,
				User:   gitInfo.User,
				Token:  gitInfo.Token,
				Branch: his.Branch,
			},
			Exclude:    "",
			ModuleName: "",
			Oss: struct {
				Path      string `json:"path"`
				Endpoint  string `json:"endpoint"`
				Bucket    string `json:"bucket"`
				AccessId  string `json:"accessId"`
				AccessKey string `json:"accessKey"`
				PathStyle bool   `json:"pathStyle"`
			}{
				Path:      req1.MountedJarPath,
				Endpoint:  configs.Config.Store.Endpoint,
				Bucket:    configs.Config.Store.BucketName,
				AccessId:  configs.Config.Store.AccessId,
				AccessKey: configs.Config.Store.AccessKey,
				PathStyle: false,
			},
			TaskNo:          strconv.FormatInt(his.Id, 10),
			BaseCommitId:    "",
			CompareCommitId: "",
			HisId:           his.Id,
			MantisCallBack:  configs.Config.App.Svc + "/rick/openapi/coverage/callBack",
			OldExecPath:     his.OldExecPath,
		}
		pubsub.SendToRedisQueue(payload, his.From)
	}
	return res
}

func JacocoStatusAndResultLinkUpdate(condition apis.Condition, labels map[string]string) error {
	var status int8
	switch condition.Status {
	case corev1.ConditionTrue:
		status = task.ProductStatusSucc
	case corev1.ConditionFalse:
		if condition.Reason == v1beta1.TaskRunSpecStatusCancelled {
			status = task.ProductStatusFail
		} else {
			status = task.ProductStatusFail
		}
	case corev1.ConditionUnknown:
		if condition.Reason == "Running" || condition.Reason == "Pending" {
			status = task.TaskStatusRun
		}
		if condition.Reason == v1beta1.TaskRunReasonTimedOut.String() {
			status = task.ProductStatusFail
		}
	default:
		return fmt.Errorf("syncTaskRunRecord invaild taskrun status")
	}
	execIdStr, ok := labels[commonconstants.ExecIdLabel]
	if !ok {
		return fmt.Errorf("error in getting execId")
	}
	execId, err := strconv.ParseInt(execIdStr, 10, 64)
	if err != nil {
		return err
	}
	ctx := &commoncontext.MantisContext{}
	his := quality.JacocoCoverageExecHis{}
	his.Id = execId
	his.IsDeleted = commonconstants.DeleteNo
	if err := gormx.SelectOneByCondition(ctx, &his); err != nil {
		return err
	}
	if his.Status == status || his.Status == task.TaskStatusSucc || his.Status == task.TaskStatusFail {
		return nil
	}
	his.Status = status
	_, err = gormx.UpdateOneByCondition(ctx, &his)
	if err != nil {
		return err
	}
	// 如果失败，回调ship
	if status == task.TaskStatusFail {
		if his.From == constants.SourcePipeline {
			q := dto.PipelineCollectCoverageReqDTO{}
			jsonx.UnMarshal([]byte(his.Request), &q)
			JacocoCoverageService{}.invokeShip(his, q)
		} else {
			JacocoCoverageService{}.invokeMagic(his)
		}
	}
	return nil
}

func (j JacocoCoverageService) CallBack(ctx *commoncontext.MantisContext, req dto.CallBackReqDTO) {
	// 查询任务记录
	his := quality.JacocoCoverageExecHis{}
	his.Id = req.Id
	his.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &his)
	// 修改任务记录
	his.Status = task.TaskStatusSucc
	his.LineCoverage = req.LinePercentage
	his.ClassCoverage = req.ClassPercentage
	his.BranchCoverage = req.BranchPercentage
	his.ReportUrl = req.ReportUrl
	his.OldExecPath = req.OldExecPath
	gormx.UpdateOneByConditionX(ctx, &his)
	// 如果是ship来源，回调ship
	if his.From == constants.SourcePipeline {
		q := dto.PipelineCollectCoverageReqDTO{}
		jsonx.UnMarshal([]byte(his.Request), &q)
		j.invokeShip(his, q)
	} else {
		j.invokeMagic(his)
	}
}

func (JacocoCoverageService) invokeShip(his quality.JacocoCoverageExecHis, req dto.PipelineCollectCoverageReqDTO) {
	if req.CallBackAddr == "" {
		logger.Logger.Panic("回调ship失败, 无回调url")
	}
	res := make(map[string]any)
	res["pid"] = his.FromId
	app, err := cmdb.Client.GetAppById(context.Background(), his.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	res["appId"] = his.AppId
	res["appName"] = app.Name
	res["callBackAddr"] = req.CallBackAddr
	res["zaccReportUrl"] = his.ReportUrl
	if his.Status == task.TaskStatusSucc {
		res["status"] = constants.SuccessStatus
	} else if his.Status == task.TaskStatusFail {
		res["status"] = constants.FailureStatus
	} else {
		res["status"] = constants.AbortedStatus
	}
	res["branchPercentage"] = his.BranchCoverage
	res["classPercentage"] = his.ClassCoverage
	res["linePercentage"] = his.LineCoverage
	res["mode"] = his.Mode
	shipInvoke.ShipCallBack(req.CallBackAddr, res)
}

func (JacocoCoverageService) invokeMagic(his quality.JacocoCoverageExecHis) {
	res := make(map[string]any)
	res["pid"] = his.FromId
	app, err := cmdb.Client.GetAppById(context.Background(), his.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	res["appId"] = his.AppId
	res["appName"] = app.Name
	res["bid"] = his.FromId
	res["jacocoDetailUrl"] = his.ReportUrl
	if his.Status == task.TaskStatusSucc {
		res["status"] = constants.SuccessStatus
	} else if his.Status == task.TaskStatusFail {
		res["status"] = constants.FailureStatus
	} else {
		res["status"] = constants.AbortedStatus
	}
	res["branchPercentage"] = his.BranchCoverage
	res["classPercentage"] = his.ClassCoverage
	res["linePercentage"] = his.LineCoverage
	resultUrl := configs.Config.Modules.Base.Endpoint + "/openapi/api/full/auto/receive/result"
	err = retry.Do(func() error {
		request := &netutil.HttpRequest{
			RawURL: resultUrl,
			Method: http.MethodPost,
			Body:   jsonx.Marshal(&res),
		}
		httpClient := netutil.NewHttpClient()
		resp, err := httpClient.SendRequest(request)
		if err != nil || resp.StatusCode != 200 {
			return xerror.Wrap(err, "error in send request to magic base")
		}
		return nil
	}, retry.Attempts(3), retry.LastErrorOnly(true), retry.OnRetry(func(n uint, err error) {
		logger.Logger.Infof("第%d次重试：%v", n, err)
	}))
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "调用接口[%v]失败:", resultUrl))
	}
}

func (JacocoCoverageService) cleanJacoco(ip, port string) {
	// 服务器地址和端口
	serverAddr := fmt.Sprintf("%s:%s", ip, port)
	// 连接到 TCP 服务器
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		logger.Logger.Panicf("执行jacoco clean 错误, err=%s", err.Error())
	}
	defer conn.Close()
	// 要发送的原始数据
	data := []byte{0x01, 0xc0, 0xc0, 0x10, 0x07, 0x40, 0x00, 0x01}

	// 发送数据，一次发送一个字节
	for _, b := range data {
		_, err = conn.Write([]byte{b})
		if err != nil {
			logger.Logger.Panicf("执行jacoco clean 错误, err=%s", err.Error())
		}
		// 可选：在发送每个字节后等待一段时间
		time.Sleep(100 * time.Millisecond)
	}
}

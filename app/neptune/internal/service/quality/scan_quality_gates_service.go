package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanQualityGatesService struct{}

func (ScanQualityGatesService) InsertTaskConfig(ctx *commoncontext.MantisContext, gatesDto dto.ScanQualityGatesDTO, user commondto.UserInfo) int64 {
	gates := quality.ScanQualityGates{}
	gates.GmtCreated = times.Now()
	gates.GmtModified = times.Now()
	gates.Creator = user.AdAccount
	gates.Modifier = user.AdAccount
	gates.IsDeleted = commonconstants.DeleteNo
	gates.TaskId = gatesDto.TaskId
	gates.IsActive = gatesDto.IsActive
	gates.BlockerViolations = gatesDto.BlockerViolations
	gates.CriticalViolations = gatesDto.CriticalViolations
	gates.MajorViolations = gatesDto.MajorViolations
	gates.MinorViolations = gatesDto.MinorViolations
	queryGates := quality.ScanQualityGates{}
	queryGates.TaskId = gatesDto.TaskId
	gormx.SelectOneByConditionX(ctx, &queryGates)
	if queryGates.Id != 0 {
		gates.Id = queryGates.Id
	}
	gormx.InsertUpdateOneX(ctx, &gates)
	return gates.Id
}

func (ScanQualityGatesService) SelectByTaskId(ctx *commoncontext.MantisContext, taskId int64) quality.ScanQualityGates {
	res := quality.ScanQualityGates{}
	res.TaskId = taskId
	res.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &res)
	return res
}

func (ScanQualityGatesService) SelectBatchByTaskIds(ctx *commoncontext.MantisContext, taskIds []int64) []quality.ScanQualityGates {
	res := make([]quality.ScanQualityGates, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&quality.ScanQualityGates{}).In("task_id", taskIds).Eq("is_deleted", commonconstants.DeleteNo),
		&res)
	return res
}

// 比较两个数，若b>a返回1，否则返回2
func (ScanQualityGatesService) compareIntReturnIntResult(a, b int32) int32 {
	if b > a {
		return int32(1)
	} else {
		return int32(2)
	}
}

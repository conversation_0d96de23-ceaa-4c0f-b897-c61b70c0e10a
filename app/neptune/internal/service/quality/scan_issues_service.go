package quality

import (
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanIssuesService struct{}

func (ScanIssuesService) QueryScanIssuesList(ctx *commoncontext.MantisContext, reqDto dto.ScanIssuesReqDTO, request gormx.PageRequest) *gormx.PageResult {
	logger.Logger.Infof("query scan issues request:%v", reqDto)
	if reqDto.TaskId == 0 {
		logger.Logger.Panic("未指定任务!")
	}
	req := help.SearchIssuesReq{
		Severity: reqDto.Severity,
		Page:     request.Page,
		PageSize: request.PageSize,
	}
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = reqDto.TaskId
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	if scanTestTask.LastScanTestExecHis == 0 {
		return &gormx.PageResult{
			Total:       0,
			PageSize:    req.PageSize,
			CurrentPage: req.Page,
			List:        nil,
		}
	}
	his := quality.ScanTestExecHis{}
	his.Id = scanTestTask.LastScanTestExecHis
	his.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &his)
	if his.Status < task.TaskStatusSucc || his.SonarProjectKey == "" {
		logger.Logger.Infof("任务状态异常:%v,sonarkey:%v", his.Status, his.SonarProjectKey)
		return &gormx.PageResult{
			Total:       0,
			PageSize:    req.PageSize,
			CurrentPage: req.Page,
			List:        nil,
		}
	}
	req.ProjectKey = his.SonarProjectKey
	req.Mode = his.Mode
	if reqDto.CreationDate != "" {
		creationDate := make([]*times.Time, 2)
		creationDate[0] = times.GetDataFromCommaString(reqDto.CreationDate, 0)
		creationDate[1] = times.GetDataFromCommaString(reqDto.CreationDate, 1)
		req.CreationDate = creationDate
	}
	sonarHelper := help.DefaultSonarHelper
	list, total := sonarHelper.SearchIssues(req)
	return &gormx.PageResult{
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        list,
	}
}

func (ScanIssuesService) QueryUnitComponentList(ctx *commoncontext.MantisContext, reqDto dto.SearchComponentReqDTO, request gormx.PageRequest) *gormx.PageResult {
	if reqDto.TaskId == 0 {
		logger.Logger.Panic("未指定任务!")
	}
	req := help.SearchComponentReq{
		Page:       request.Page,
		PageSize:   request.PageSize,
		OrderBy:    reqDto.OrderBy,
		MetricSort: reqDto.MetricSort,
	}
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = reqDto.TaskId
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	if scanTestTask.LastScanTestExecHis == 0 {
		return &gormx.PageResult{
			Total:       0,
			PageSize:    req.PageSize,
			CurrentPage: req.Page,
			List:        nil,
		}
	}
	his := quality.ScanTestExecHis{}
	his.Id = scanTestTask.LastScanTestExecHis
	his.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &his)
	if his.Status != task.TaskStatusSucc && his.Status != task.TaskStatusFail {
		return &gormx.PageResult{
			Total:       0,
			PageSize:    req.PageSize,
			CurrentPage: req.Page,
			List:        nil,
		}
	}
	req.Component = his.SonarProjectKey
	sonarHelper := help.DefaultSonarHelper
	list, total := sonarHelper.SearchComponents(req)
	res := make([]dto.SonarComponentRes, 0, len(list))
	for _, component := range list {
		if !strings.Contains(component.Path, reqDto.Search) {
			continue
		}
		componentRes := dto.SonarComponentRes{
			Key:      component.Key,
			Name:     component.Name,
			Path:     component.Path,
			Language: component.Language,
			Url: fmt.Sprintf(
				"%s/component_measures?id=%s&metric=coverage&view=list&selected=%s",
				configs.Config.Modules.Neptune.Sonar.Domain, req.Component, component.Key),
		}
		for _, measure := range component.Measures {
			switch measure.Metric {
			case "uncovered_lines":
				componentRes.UncoveredLines = measure.Value
			case "coverage":
				componentRes.Coverage = measure.Value
			case "uncovered_conditions":
				componentRes.UncoveredConditions = measure.Value
			}
		}
		res = append(res, componentRes)

	}
	return &gormx.PageResult{
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        res,
	}
}

package quality

import (
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type SonarRuleService struct{}

func (SonarRuleService) SearchRulesInTemplate(req help.SonarRuleSearchReq, ctx *commoncontext.MantisContext) *gormx.PageResult {
	if req.ProfileKey == "" {
		logger.Logger.Panic("请选择规则集！")
	}
	if req.IsActive == "" {
		logger.Logger.Panic("启用状态必填！")
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 100
	}
	sonarHelper := help.DefaultSonarHelper
	rules, total := sonarHelper.SearchRules(req)
	isActive, _ := strconv.ParseBool(req.IsActive)
	for i := range rules {
		rules[i].IsActive = isActive
	}
	return &gormx.PageResult{
		Pages:       total / req.PageSize,
		Total:       total,
		PageSize:    req.PageSize,
		CurrentPage: req.Page,
		List:        rules,
	}
}

func (SonarRuleService) ActiveOrDeActiveTemplateRule(req dto.ActiveDeActiveRuleReqDTO, ctx *commoncontext.MantisContext) {
	sonarHelper := help.DefaultSonarHelper
	if req.IsActive {
		sonarHelper.ActiveProfileRule(req.ProfileKey, req.RuleKey, req.Severity)
	} else {
		sonarHelper.DeActiveProfileRule(req.ProfileKey, req.RuleKey)
	}
}

func (SonarRuleService) GetLanguages(ctx *commoncontext.MantisContext) []dto.ScanLanguageDTO {
	languages := make([]quality.ScanLanguage, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanLanguage{}).
		Eq("is_deleted", commonconstants.DeleteNo).OrderByAsc("id"), &languages)
	res := make([]dto.ScanLanguageDTO, 0, len(languages))
	for _, language := range languages {
		res = append(res, dto.ScanLanguageDTO{
			Key:  language.Key,
			Name: language.Name,
		})
	}
	return res
}

func (SonarRuleService) ShowRuleDetail(key string) dto.SonarRuleDTO {
	sonarHelper := help.DefaultSonarHelper
	return sonarHelper.ShowRule(key)
}

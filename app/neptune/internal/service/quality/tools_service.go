package quality

import (
	"slices"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

var conditions = map[string]string{
	"new_branch_coverage":          "LT",
	"new_duplicated_lines_density": "GT",
	"new_line_coverage":            "LT",
	"new_lines_to_cover":           "GT",
	"new_blocker_violations":       "GT",
	"new_critical_violations":      "GT",
	"new_lines":                    "GT",
	"new_major_violations":         "GT",
	"new_minor_violations":         "GT",
	"blocker_violations":           "GT",
	"comment_lines":                "LT",
	"comment_lines_density":        "LT",
	"branch_coverage":              "LT",
	"critical_violations":          "GT",
	"complexity":                   "GT",
	"duplicated_lines_density":     "GT",
	"generated_ncloc":              "GT",
	"line_coverage":                "LT",
	"lines":                        "GT",
	"ncloc":                        "GT",
	"lines_to_cover":               "GT",
	"major_violations":             "GT",
	"minor_violations":             "GT",
	"test_success_density":         "LT",
}

func InitSonarQualityGates() {
	sonarHelper := help.DefaultSonarHelper
	defer func() {
		err := recover()
		if err != nil {
			logger.Logger.Errorf("对%s的sonar质量阀初始化失败, err = %v", sonarHelper.Domain, err)
		}
	}()
	if gates := sonarHelper.GetQualityGates(); slices.Contains(gates, "online") {
		return
	}
	qualityGateId := sonarHelper.CreateQualityGate()
	for k, v := range conditions {
		sonarHelper.AddCondition(qualityGateId, k, v)
	}
	if sonarHelper.SetDefault(qualityGateId) {
		logger.Logger.Infof("对%s的sonar质量阀初始化成功", sonarHelper.Domain)
	} else {
		logger.Logger.Errorf("对%s的sonar质量阀初始化失败", sonarHelper.Domain)
	}
}

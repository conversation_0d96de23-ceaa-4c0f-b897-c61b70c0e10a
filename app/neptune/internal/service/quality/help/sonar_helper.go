package help

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
)

type SonarHelper struct {
	Domain string
	Token  string
}

type SonarQualityProfilesSearchReq struct {
	Defaults       bool
	Language       string
	Project        string
	QualityProfile string
}

type SonarRuleSearchReq struct {
	IsActive       string   `schema:"isActive"`       // 是否启用
	Severity       string   `schema:"severity"`       // 默认严重性
	AvailableSince string   `schema:"availableSince"` // 查询自此日期以后增加的rule, yyyy-MM-dd
	Page           int64    `schema:"page"`           // 页码
	PageSize       int64    `schema:"pageSize"`       // 一页显示数
	RuleName       string   `schema:"ruleName"`       // rule 名模糊查询
	ProfileKey     string   `schema:"profileKey"`     // 规则集key
	Statuses       []string `schema:"statuses"`       // 状态
	Types          []string `schema:"types"`          // 类型
}

type SearchIssuesReq struct {
	Severity     string
	CreationDate []*times.Time
	ProjectKey   string
	Page         int64
	PageSize     int64
	Mode         string
}

type SearchComponentReq struct {
	Page       int64
	PageSize   int64
	OrderBy    string // 排序方式，asc or desc
	MetricSort string
	Search     string
	Component  string // sonar 项目 key
}

var DefaultSonarHelper = SonarHelper{}

func (s SonarHelper) DeleteSonarProject(key string) {
	path := "/api/projects/delete"
	formData := url.Values{}
	formData.Add("project", key)
	_, err := s.httpHeaderRequestWithFormData(path, formData, http.MethodPost, http.Header{})
	if err != nil {
		logger.Logger.Debug("预删除，如果项目不存在，会报错，对流程不影响！")
	}
}

func (s SonarHelper) CreateSonarProject(key string) {
	path := "/api/projects/create"
	formData := url.Values{}
	formData.Add("project", key)
	formData.Add("name", key)
	headers := http.Header{}
	response, err := s.httpHeaderRequestWithFormData(path, formData, http.MethodPost, headers)
	if err != nil || response.StatusCode > 302 {
		logger.Logger.Panicf("sonar 创建项目异常！%+v", response)
	}
}

func (s SonarHelper) BindSonarProfile(key string, profile string, language string) {
	path := "/api/qualityprofiles/add_project"
	formData := url.Values{}
	formData.Add("project", key)
	formData.Add("qualityProfile", profile)
	formData.Add("language", language)
	headers := http.Header{}
	_, err := s.httpHeaderRequestWithFormData(path, formData, http.MethodPost, headers)
	if err != nil {
		logger.Logger.Debug("绑定规则，如果规则不存在，直接使用默认规则，对流程不影响")
	}
}

func (s SonarHelper) BindWebhook(key string, webhook string) {
	path := "/api/webhooks/create"
	formData := url.Values{}
	formData.Add("project", key)
	formData.Add("name", "sonar.webhooks.project")
	formData.Add("url", webhook)
	headers := http.Header{}
	response, err := s.httpHeaderRequestWithFormData(path, formData, http.MethodPost, headers)
	if err != nil || response.StatusCode > 302 {
		logger.Logger.Panicf("sonar 项目绑定webhook失败！%+v", response)
	}
}

func (s SonarHelper) CreateProfile(name string, language string) string {
	uri := "/api/qualityprofiles/create"
	formData := url.Values{}
	formData.Add("name", name)
	formData.Add("language", language)
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("创建sonar规则集失败,msg=%s", body)
	}
	respBody := make(map[string]interface{})
	err = httpClient.DecodeResponse(response, &respBody)
	if err != nil {
		logger.Logger.Panic("创建sonar规则集失败")
	}
	if m, ok := respBody["profile"].(map[string]interface{}); ok {
		key := m["key"].(string)
		logger.Logger.Infof("创建sonar规则集成功 name=%s，language=%s", name, language)
		return key
	} else {
		logger.Logger.Panic("创建sonar规则集失败")
	}
	return ""
}

func (s SonarHelper) CopyProfile(fromKey, name string) string {
	uri := "/api/qualityprofiles/copy"
	formData := url.Values{}
	formData.Add("fromKey", fromKey)
	formData.Add("toName", name)
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("复制sonar规则集失败,msg=%s", body)
	}
	respBody := make(map[string]interface{})
	err = httpClient.DecodeResponse(response, &respBody)
	if err != nil {
		logger.Logger.Panic("复制sonar规则集失败")
	}
	key := respBody["key"].(string)
	logger.Logger.Info("复制sonar规则集成功")
	return key
}

func (s SonarHelper) RenameProfile(key, name string) {
	uri := "/api/qualityprofiles/rename"
	formData := url.Values{}
	formData.Add("key", key)
	formData.Add("name", name)
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		logger.Logger.Panic("重命名sonar规则集失败")
	}
}

func (s SonarHelper) DeleteProfile(language string, profileName string) {
	logger.Logger.Infof("deleteProfile: language:%s, profileName:%s", language, profileName)
	formData := url.Values{}
	formData.Add("language", language)
	formData.Add("qualityProfile", profileName)
	response, err := s.httpHeaderRequestWithFormData("/api/qualityprofiles/delete", formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("deleteProfile...fail,msg=%v", body)
	}
	logger.Logger.Info("deleteProfile...success")
}

func (s SonarHelper) SearchQualityProfiles(req SonarQualityProfilesSearchReq) []quality.ScanProfileTemplate {
	uri := "/api/qualityprofiles/search"
	params := make(map[string]string)
	if req.Defaults == true {
		params["defaults"] = "true"
	}
	if req.Language != "" {
		params["language"] = req.Language
	}
	if req.Project != "" {
		params["project"] = req.Project
	}
	if req.QualityProfile != "" {
		params["qualityProfile"] = req.QualityProfile
	}
	if len(params) != 0 {
		uri += "?"
		for k, v := range params {
			uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
		}
		uri = uri[:len(uri)-1]
	}
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err == nil || response.StatusCode <= http.StatusInternalServerError {
		res := struct {
			Profiles []quality.ScanProfileTemplate `json:"profiles"`
		}{}
		err = httpClient.DecodeResponse(response, &res)
		if err != nil {
			logger.Logger.Panic("查询sonar quality profile 异常")
		}
		return res.Profiles
	} else {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("查询sonar quality profile 异常,msg=%s", body)
		return nil
	}
}

func (s SonarHelper) ImportProfiles(file io.Reader, fileName string) string {
	headers := http.Header{}
	headers.Add("media", fileName)
	headers.Add("Authorization", "Basic "+s.Token)
	data := &bytes.Buffer{}
	writer := multipart.NewWriter(data)
	part, err := writer.CreateFormFile("backup", fileName)
	if err != nil {
		logger.Logger.Panic("导入规则集异常！")
	}
	_, err = io.Copy(part, file)
	if err != nil {
		logger.Logger.Panic("导入规则集异常！")
	}
	headers.Add("Content-Type", writer.FormDataContentType())
	err = writer.Close()
	if err != nil {
		logger.Logger.Panic("导入规则集异常！")
	}
	request := &netutil.HttpRequest{
		RawURL:  s.Domain + "/api/qualityprofiles/restore",
		Method:  http.MethodPost,
		Headers: headers,
		Body:    data.Bytes(),
	}
	response, err := httpClient.SendRequest(request)
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("导入规则集异常,msg=%s", body)
	}
	respBody := make(map[string]interface{})
	err = httpClient.DecodeResponse(response, &respBody)
	if err != nil {
		logger.Logger.Panic("导入规则集异常！")
	}
	if m, ok := respBody["profile"].(map[string]interface{}); ok {
		key := m["key"].(string)
		return key
	} else {
		logger.Logger.Panic("导入规则集异常！")
	}
	return ""
}

func (s SonarHelper) SearchRules(req SonarRuleSearchReq) ([]dto.SonarRuleDTO, int64) {
	uri := "/api/rules/search"
	params := make(map[string]string)
	params["activation"] = req.IsActive
	if req.Severity != "" {
		params["severities"] = req.Severity
	}
	if req.AvailableSince != "" {
		params["available_since"] = req.AvailableSince
	}
	if req.RuleName != "" {
		params["q"] = req.RuleName
	}
	if req.ProfileKey != "" {
		params["qprofile"] = req.ProfileKey
	}
	if len(req.Statuses) != 0 {
		params["statuses"] = strings.Join(req.Statuses, ",")
	}
	if len(req.Types) != 0 {
		params["types"] = strings.Join(req.Types, ",")
	}
	if req.Page != 0 {
		params["p"] = strconv.FormatInt(req.Page, 10)
	}
	if req.PageSize != 0 {
		params["ps"] = strconv.FormatInt(req.PageSize, 10)
	}
	if len(params) != 0 {
		uri += "?"
		for k, v := range params {
			uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
		}
		uri = uri[:len(uri)-1]
	}
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		logger.Logger.Panic("搜索sonar规则异常！")
	}
	res := struct {
		Paging struct {
			Total int64 `json:"total"`
		} `json:"paging"`
		Rules []dto.SonarRuleDTO `json:"rules"`
	}{}
	err = httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panic("搜索sonar规则异常！")
	}
	return res.Rules, res.Paging.Total
}

func (s SonarHelper) ShowRule(key string) dto.SonarRuleDTO {
	uri := "/api/rules/show?key=" + key
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		logger.Logger.Panic("搜索sonar规则异常！")
	}
	res := struct {
		Rule dto.SonarRuleDTO `json:"rule"`
	}{}
	err = httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panic("搜索sonar规则异常！")
	}
	return res.Rule
}

func (s SonarHelper) ActiveProfileRule(profileKey string, ruleKey string, severity string) {
	logger.Logger.Infof("active rule: profile=%s, rule=%s", profileKey, ruleKey)
	uri := "/api/qualityprofiles/activate_rule"
	params := url.Values{}
	params.Add("key", profileKey)
	params.Add("rule", ruleKey)
	if severity != "" {
		params.Add("severity", severity)
	}
	response, err := s.httpHeaderRequestWithFormData(uri, params, http.MethodPost, http.Header{})
	if err == nil && response.StatusCode < http.StatusInternalServerError {
		logger.Logger.Infof("规则绑定规则集成功 profile=%s, rule=%s", profileKey, ruleKey)
	} else {
		logger.Logger.Infof("规则绑定规则集失败 profile=%s, rule=%s", profileKey, ruleKey)
	}
}

func (s SonarHelper) DeActiveProfileRule(profileKey string, ruleKey string) {
	logger.Logger.Infof("activate rule: profile=%s, rule=%s", profileKey, ruleKey)
	uri := "/api/qualityprofiles/deactivate_rule"
	params := url.Values{}
	params.Add("key", profileKey)
	params.Add("rule", ruleKey)
	response, err := s.httpHeaderRequestWithFormData(uri, params, http.MethodPost, http.Header{})
	if err == nil && response.StatusCode < http.StatusInternalServerError {
		logger.Logger.Infof("规则解绑规则集成功 profile=%s, rule=%s", profileKey, ruleKey)
	} else {
		logger.Logger.Infof("规则解绑规则集失败 profile=%s, rule=%s", profileKey, ruleKey)
	}
}

// ActiveRulesFromTemplate 将templateKey规则集下的开启规则复制到targetKey规则集
func (s SonarHelper) ActiveRulesFromTemplate(templateKey string, targetKey string) {
	uri := `/api/qualityprofiles/activate_rules`
	params := url.Values{}
	params.Add("qprofile", templateKey)
	params.Add("targetKey", targetKey)
	params.Add("activation", "true")
	resp, err := s.httpHeaderRequestWithFormData(uri, params, http.MethodPost, http.Header{})
	if err == nil && resp.StatusCode < http.StatusInternalServerError {
		logger.Logger.Infof("ActiveRulesFromTemplate成功 template=%s, target=%s", templateKey, targetKey)
	} else {
		logger.Logger.Panicf("ActiveRulesFromTemplate失败 template=%s, target=%s", templateKey, targetKey)
	}
}

func (s SonarHelper) DeactivateRulesFromTemplate(templateKey string, targetKey string, repo string) {
	logger.Logger.Infof("DeactivateRulesFromTemplate: templateKey:%s, targetKey:%s, repo:%s", templateKey, targetKey, repo)
	formData := url.Values{}
	formData.Add("activation", "true")
	formData.Add("qprofile", templateKey)
	if repo != "" {
		formData.Add("repositories", repo)
	}
	formData.Add("targetKey", targetKey)
	uri := "/api/qualityprofiles/deactivate_rules"
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("deactivateRules...fail,msg=%v", body)
	}
	logger.Logger.Info("deactivateRules...success")
}

func (s SonarHelper) DownloadProfiles(name string, language string) string {
	uri := "/api/qualityprofiles/backup?language=%s&qualityProfile=%s"
	response, err := s.httpHeaderRequest(fmt.Sprintf(uri, url.QueryEscape(language), url.QueryEscape(name)), nil, http.MethodGet, http.Header{})
	if err == nil && response.StatusCode < http.StatusInternalServerError {
		body, err := io.ReadAll(response.Body)
		if err != nil {
			logger.Logger.Panic("下载用例配置文件xml失败")
		}
		return string(body)
	} else {
		logger.Logger.Panic("下载用例配置文件xml失败")
		return ""
	}
}

func (s SonarHelper) GetQualityGates() []string {
	uri := "/api/qualitygates/list"
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("get sonar quality gate...fail,msg=%s", body)
	}
	body := struct {
		QualityGates []struct {
			Id   string `json:"id"`
			Name string `json:"name"`
		} `json:"qualityGates"`
	}{}
	err = httpClient.DecodeResponse(response, &body)
	if err != nil {
		logger.Logger.Panicf("error in parsing sonar response, err=%v", err)
	}
	res := make([]string, 0)
	for _, gate := range body.QualityGates {
		res = append(res, gate.Name)
	}
	return res
}

func (s SonarHelper) CreateQualityGate() string {
	logger.Logger.Info("create sonar quality gate")
	formData := url.Values{}
	formData.Add("name", "online")
	uri := "/api/qualitygates/create"
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("create sonar quality gate...fail,msg=%s", body)
	}
	res := make(map[string]interface{})
	err = httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panicf("error in parsing sonar response, err=%v", err)
	}
	return res["id"].(string)
}

func (s SonarHelper) AddCondition(qualityGateId string, key string, direction string) {
	logger.Logger.Info("create sonar quality gate")
	formData := url.Values{}
	formData.Add("gateId", qualityGateId)
	formData.Add("metric", key)
	formData.Add("op", direction)
	formData.Add("error", "0")
	uri := "/api/qualitygates/create_condition"
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil || response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("create sonar quality gare...fail,msg=%v", body)
	}
}

func (s SonarHelper) SetDefault(qualityGateId string) bool {
	logger.Logger.Infof("将质量阀%s设为默认", qualityGateId)
	formData := url.Values{}
	formData.Add("id", qualityGateId)
	uri := "/api/qualitygates/set_as_default"
	response, err := s.httpHeaderRequestWithFormData(uri, formData, http.MethodPost, http.Header{})
	if err != nil {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("create sonar quality gare...fail,msg=%s", body)
	}
	return response.StatusCode == http.StatusOK
}

func (s SonarHelper) SearchIssues(req SearchIssuesReq) (list []dto.SonarIssuesDTO, total int64) {
	uri := `/api/issues/search`
	params := make(map[string]string)
	params["additionalFields"] = "rules"
	if req.ProjectKey != "" {
		params["componentKeys"] = req.ProjectKey
	}
	if req.Severity != "" {
		params["severities"] = req.Severity
	}
	if len(req.CreationDate) == 2 {
		params["createdAfter"] = req.CreationDate[0].Format(times.ISO8601TimeFormat)
		params["createdBefore"] = req.CreationDate[1].Format(times.ISO8601TimeFormat)
	}
	if req.Mode == constants.StandardIncrement {
		params["inNewCodePeriod"] = "true"
	} else {
		params["inNewCodePeriod"] = "false"
	}
	if req.Page != 0 {
		params["p"] = strconv.FormatInt(req.Page, 10)
	}
	if req.PageSize != 0 {
		params["ps"] = strconv.FormatInt(req.PageSize, 10)
	}
	if len(params) != 0 {
		uri += "?"
		for k, v := range params {
			uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
		}
		uri = uri[:len(uri)-1]
	}
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err != nil && response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("search sonar issues...fail,msg=%s", body)
	}
	res := struct {
		Total  int64                `json:"total"`
		Issues []dto.SonarIssuesDTO `json:"issues"`
		Rules  []dto.SonarRuleDTO   `json:"rules"`
	}{}
	err = httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error search sonar issues"))
	}
	ruleKeyDetailMap := make(map[string]dto.SonarRuleDTO, len(res.Rules))
	for _, rule := range res.Rules {
		ruleKeyDetailMap[rule.Key] = rule
	}
	list = make([]dto.SonarIssuesDTO, 0, len(res.Issues))
	for _, issue := range res.Issues {
		issue.CreationTime = times.ParseStringToTime(issue.CreationDate, times.ISO8601TimeFormat)
		issue.UpdateTime = times.ParseStringToTime(issue.UpdateDate, times.ISO8601TimeFormat)
		issue.RuleDetail = ruleKeyDetailMap[issue.Rule]
		issue.SonarUrl = fmt.Sprintf("%s/code?id=%s&line=%d",
			configs.Config.Modules.Neptune.Sonar.Domain, issue.Component, issue.Line)
		list = append(list, issue)
	}
	total = res.Total
	return list, total
}

func (s SonarHelper) SearchComponents(req SearchComponentReq) ([]dto.SonarComponent, int64) {
	uri := `/api/measures/component_tree?`
	params := make(map[string]string)
	params["metricSortFilter"] = "withMeasuresOnly"
	params["metricKeys"] = "coverage,uncovered_lines,uncovered_conditions"
	params["strategy"] = "leaves"
	params["component"] = req.Component
	params["s"] = "metric"
	params["metricSort"] = "coverage"
	if req.Search != "" {
		params["q"] = req.Search
	}
	if req.MetricSort != "" {
		params["metricSort"] = req.MetricSort
	}
	if req.OrderBy == "desc" {
		params["asc"] = "false"
	} else {
		params["asc"] = "true"
	}
	if req.Page != 0 {
		params["p"] = strconv.FormatInt(req.Page, 10)
	}
	if req.PageSize != 0 {
		params["ps"] = strconv.FormatInt(req.PageSize, 10)
	}
	for k, v := range params {
		uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
	}
	uri = uri[:len(uri)-1]
	response, err := s.httpHeaderRequest(uri, nil, http.MethodGet, http.Header{})
	if err != nil && response.StatusCode >= http.StatusInternalServerError {
		body, _ := io.ReadAll(response.Body)
		logger.Logger.Panicf("search sonar components...fail,msg=%s", body)
	}
	res := struct {
		Paging struct {
			PageIndex int64 `json:"pageIndex"`
			PageSize  int64 `json:"pageSize"`
			Total     int64 `json:"total"`
		} `json:"paging"`
		BaseComponent struct {
			Key         string        `json:"key"`
			Name        string        `json:"name"`
			Description string        `json:"description"`
			Qualifier   string        `json:"qualifier"`
			Measures    []interface{} `json:"measures"`
		} `json:"baseComponent"`
		Components []dto.SonarComponent `json:"components"`
	}{}
	err = httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error search sonar components"))
	}
	return res.Components, res.Paging.Total
}

func (s SonarHelper) httpHeaderRequest(path string, requestBody interface{}, method string, headers http.Header) (*http.Response, error) {
	headers.Add("Authorization", "Basic "+s.Token)
	request := &netutil.HttpRequest{
		RawURL:  s.Domain + path,
		Method:  method,
		Headers: headers,
		Body:    jsonx.Marshal(&requestBody),
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		return resp, errors.New("error calling sonar, path=" + request.RawURL)
	} else {
		logger.Logger.Debugf("sonar helper...response=%+v", resp)
		return resp, nil
	}
}

func (s SonarHelper) httpHeaderRequestWithFormData(path string, formData url.Values, method string, headers http.Header) (*http.Response, error) {
	headers.Add("Authorization", "Basic "+s.Token)
	headers.Add("Content-Type", "application/x-www-form-urlencoded")
	request := &netutil.HttpRequest{
		RawURL:   s.Domain + path,
		Method:   method,
		Headers:  headers,
		FormData: formData,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		return resp, errors.New("error calling sonar, path=" + request.RawURL)
	} else {
		logger.Logger.Debugf("sonar helper...response=%+v", resp)
		return resp, nil
	}
}

func InitSonar() {
	DefaultSonarHelper = SonarHelper{
		Domain: configs.Config.Modules.Neptune.Sonar.Domain,
		Token:  configs.Config.Modules.Neptune.Sonar.GetBasicToken(),
	}
}

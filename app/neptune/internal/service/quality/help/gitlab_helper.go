package help

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
)

type GitLabHelper struct {
	Domain string
	Token  string
}

var httpClient = netutil.NewHttpClient()

type GitCommitInfo struct {
	Id            string `json:"id"`
	Message       string `json:"message"`
	AuthorName    string `json:"author_name"`
	CommittedDate string `json:"committed_date"`
}

func (g GitLabHelper) QueryBranchInfoByCodePath(codePath string, page int64, pageSize int64, search string) []dto.GitBranchDTO {
	logger.Logger.Info("start GitlabHelper.queryBranchInfo...")
	start := time.Now().UnixMilli()
	url := "/api/v4/projects/" + codePath + "/repository/branches?sort=updated_desc"
	if search != "" {
		url = url + "&search=" + search
	}
	param := "&page=%d&per_page=" + strconv.FormatInt(pageSize, 10)
	res := make([]dto.GitBranchDTO, 0, 128)
	for i := int64(0); i < page; i++ {
		realPath := fmt.Sprintf(param, i)
		url = url + realPath
		response := g.gitHttpRequest(url, nil, http.MethodGet)
		if response.StatusCode == http.StatusOK {
			gitBranchList := make([]dto.GitBranchDTO, 0)
			err := httpClient.DecodeResponse(response, &gitBranchList)
			if err != nil {
				break
			}
			for j := range gitBranchList {
				gitBranchList[j].CommitId = gitBranchList[j].Commit.Id
			}
			res = append(res, gitBranchList...)
			if int64(len(gitBranchList)) < pageSize {
				break
			}
		}
	}
	logger.Logger.Infof("end gitlabHelper.queryBranchInfo...gitBranchList.size=%d,cost=%d", len(res), time.Now().UnixMilli()-start)
	return res
}

func (g GitLabHelper) QueryProjectCommitInfo(codePath string, branch string, page int64, pageSize int64, since string, until string) []GitCommitInfo {
	url := fmt.Sprintf("/api/v4/projects/%s/repository/commits", codePath)
	params := make(map[string]string)
	if page != 0 {
		params["page"] = strconv.FormatInt(page, 10)
	}
	if pageSize != 0 {
		params["per_page"] = strconv.FormatInt(pageSize, 10)
	}
	if branch != "" {
		params["ref_name"] = branch
	}
	if since != "" {
		params["since"] = since
	}
	if until != "" {
		params["until"] = until
	}
	if len(params) != 0 {
		url += "?"
		for k, v := range params {
			url += fmt.Sprintf("%s=%s&", k, v)
		}
		url = url[:len(url)-1]
	}
	response := g.gitHttpRequest(url, nil, http.MethodGet)
	if response.StatusCode != http.StatusOK {
		logger.Logger.Panicf("error in sending request to gitlab")
	}
	res := make([]GitCommitInfo, 0)
	err := httpClient.DecodeResponse(response, &res)
	if err != nil {
		logger.Logger.Panicf("error in sending request to gitlab")
	}
	return res
}

func (g GitLabHelper) gitHttpRequest(path string, requestBody map[string]interface{}, method string) *http.Response {
	url := g.Domain + path
	if g.Domain == "http://cube.anlink.com/code" {
		url = strings.ReplaceAll(url, "http", "https")
	}
	logger.Logger.Infof("GitlabHelper.gitHttpRequest...url=%s", url)
	headers := http.Header{}
	headers.Set("Content-Type", "application/json; charset=UTF-8")
	headers.Set("PRIVATE-TOKEN", g.Token)
	request := &netutil.HttpRequest{
		RawURL:  url,
		Method:  method,
		Headers: headers,
		Body:    jsonx.Marshal(&requestBody),
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil {
		logger.Logger.Panicf("error in send request to gitlab, err=%+v, url=%s", err, url)
	} else if resp.StatusCode != http.StatusOK {
		logger.Logger.Panicf("error in send request to gitlab, err=%+v, status code = %d, url=%s", err, resp.StatusCode, url)
	}
	return resp
}

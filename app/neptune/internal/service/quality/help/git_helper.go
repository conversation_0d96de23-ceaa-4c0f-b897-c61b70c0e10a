package help

import (
	"context"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
)

type GitHelper struct{}

const cloneCmd = "git clone -b %s %s"

const vcsModel = "vcs_model"

func (h GitHelper) GitCloneCmd(gitUrl string, branch string) string {
	gitInfo := h.GetGitInfoByUrl(gitUrl)
	if gitInfo == nil {
		logger.Logger.Panicf("没有找到对应的git配置，请检查配置文件上的git配置")
	}
	url := strings.ReplaceAll(gitUrl, "//", "//"+gitInfo.User+":"+gitInfo.Token+"@")
	return fmt.Sprintf(cloneCmd, branch, url)
}

func (GitHelper) GetGitInfoByUrl(url string) *configs.GitInfo {
	result := make([]map[string]any, 0)
	err := cmdb.Client.GetModelInstances(context.Background(), vcsModel, &result, nil)
	if err != nil || len(result) == 0 {
		logger.Logger.Panicf("获取git信息错误, err=%s", err.Error())
	}
	for _, v := range result {
		meta := v["meta"]
		metaMap := meta.(map[string]interface{})
		baseUrl := metaMap["address"].(string)

		if strings.HasPrefix(url, baseUrl) {
			user := metaMap["user_name"].(string)
			if user == "" {
				user = "user"
			}
			return &configs.GitInfo{
				Domain: baseUrl,
				User:   user,
				Token:  metaMap["token"].(string),
			}
		}
	}
	logger.Logger.Panicf("%s,===>没有匹配到对应的git仓库", url)
	return nil
}

func (GitHelper) ValidateGitUrl(gitUrl string) string {
	if !strings.HasSuffix(gitUrl, ".git") {
		return gitUrl + ".git"
	} else {
		return gitUrl
	}
}

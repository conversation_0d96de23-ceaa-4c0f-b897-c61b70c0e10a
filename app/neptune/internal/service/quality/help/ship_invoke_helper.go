package help

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
)

type ShipInvokeHelper struct{}

var shipInvoke remote.ShipInvoke

func (s ShipInvokeHelper) InvokeStaticPublicShip(ctx *commoncontext.MantisContext, hisDTO dto.ScanTestExecHisDTO) {
	if hisDTO.CallBackUrl == "" {
		return
	}
	s.invokeShipByScanTestExecHisDTO(ctx, hisDTO)
}

func (s ShipInvokeHelper) invokeShipByScanTestExecHisDTO(ctx *commoncontext.MantisContext, execHisDto dto.ScanTestExecHisDTO) {
	if task.CheckFinalStatusCode(execHisDto.Status) {
		scanTestTask := quality.ScanTestTask{}
		scanTestTask.Id = execHisDto.TaskId
		gormx.SelectOneByConditionX(ctx, &scanTestTask)
		mode := execHisDto.Mode
		execType := scanTestTask.ExecType
		jobType := scanTestTask.JobType
		var resData map[string]interface{}
		if mode == constants.StandardTotal {
			if execType == constants.ExecTypeBack { // 调用后端
				resData = s.invokeTotalAfterStaticForShip(ctx, execHisDto, jobType)
			} else { // 调用前端
				resData = s.invokeTotalFrontStaticForShip(ctx, execHisDto)
			}
		} else {
			if execType == constants.ExecTypeBack { // 调用后端
				resData = s.invokeMixAfterStaticForShip(ctx, execHisDto, jobType)
			} else { // 调用前端
				resData = s.invokeMixFrontStaticForShip(ctx, execHisDto)
			}
		}
		logger.Logger.Infof("ShipInvokeHelper , 回调ship请求params:%+v", resData)
		s.restInvokeShipForStatic(resData, execHisDto.Id)
	} else {
		logger.Logger.Infof("静态扫描任务:hisId:%d的状态为%s，无需回调", execHisDto.Id, execHisDto.StatusMsg)
	}
}

func (s ShipInvokeHelper) invokeTotalAfterStaticForShip(ctx *commoncontext.MantisContext, hisDTO dto.ScanTestExecHisDTO, jobType string) map[string]interface{} {
	res := s.getStaticPublicResult(ctx, hisDTO)
	if hisDTO.Status == task.TaskStatusSucc {
		if jobType == constants.ScanJobType {
			res["blockerViolations"] = hisDTO.BlockerViolations
			res["criticalViolations"] = hisDTO.CriticalViolations
			res["majorViolations"] = hisDTO.MajorViolations
			res["minorViolations"] = hisDTO.MinorViolations
			res["infoViolations"] = hisDTO.InfoViolations
		} else {
			res["coverage"] = hisDTO.LineCoverage
			res["testSuccessDensity"] = hisDTO.TestSuccessDensity
		}
	}
	return res
}

func (s ShipInvokeHelper) invokeTotalFrontStaticForShip(ctx *commoncontext.MantisContext, execHisDto dto.ScanTestExecHisDTO) map[string]interface{} {
	res := s.getStaticPublicResult(ctx, execHisDto)
	if execHisDto.Status == task.TaskStatusSucc {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship前端应用静态扫描全量指标", execHisDto.ShipPid, res["appName"])
		res["blockerViolations"] = execHisDto.BlockerViolations
		res["criticalViolations"] = execHisDto.CriticalViolations
		res["majorViolations"] = execHisDto.MajorViolations
		res["minorViolations"] = execHisDto.MinorViolations
		res["infoViolations"] = execHisDto.InfoViolations
	}
	return res
}

func (s ShipInvokeHelper) invokeMixAfterStaticForShip(ctx *commoncontext.MantisContext, hisDto dto.ScanTestExecHisDTO, jobType string) map[string]interface{} {
	res := s.getStaticPublicResult(ctx, hisDto)
	if hisDto.Status == task.TaskStatusSucc {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship后端应用静态扫描混合指标", hisDto.ShipPid, res["appName"])
		if jobType == constants.ScanJobType {
			res["blockerViolations"] = hisDto.BlockerViolations
			res["criticalViolations"] = hisDto.CriticalViolations
			res["majorViolations"] = hisDto.MajorViolations
			res["minorViolations"] = hisDto.MinorViolations
			res["infoViolations"] = hisDto.InfoViolations
			res["commentLinesDensity"] = hisDto.CommentLinesDensity
			res["duplicatedLinesDensity"] = hisDto.DuplicatedLinesDensity
			res["newBlockerViolations"] = hisDto.BlockerViolations
			res["newCriticalViolations"] = hisDto.CriticalViolations
			res["newMajorViolations"] = hisDto.MajorViolations
			res["newMinorViolations"] = hisDto.MinorViolations
			res["newInfoViolations"] = hisDto.InfoViolations
		} else {
			res["coverage"] = hisDto.LineCoverage
			newLines := hisDto.LinesToCover
			newCoverage := hisDto.LineCoverage
			if newLines == 0 {
				newCoverage = 100
			}
			res["newCoverage"] = newCoverage
			res["testSuccessDensity"] = hisDto.TestSuccessDensity
		}
	}
	return res
}

func (s ShipInvokeHelper) invokeMixFrontStaticForShip(ctx *commoncontext.MantisContext, hisDto dto.ScanTestExecHisDTO) map[string]interface{} {
	res := s.getStaticPublicResult(ctx, hisDto)
	if hisDto.Status == task.TaskStatusSucc {
		logger.Logger.Infof("组装pid:%d、appName:%v回调ship前端应用静态扫描混合指标", hisDto.ShipPid, res["appName"])
		res["blockerViolations"] = hisDto.BlockerViolations
		res["criticalViolations"] = hisDto.CriticalViolations
		res["majorViolations"] = hisDto.MajorViolations
		res["minorViolations"] = hisDto.MinorViolations
		res["infoViolations"] = hisDto.InfoViolations
		res["newBlockerViolations"] = hisDto.BlockerViolations
		res["newCriticalViolations"] = hisDto.CriticalViolations
		res["newMajorViolations"] = hisDto.MajorViolations
		res["newMinorViolations"] = hisDto.MinorViolations
		res["newInfoViolations"] = hisDto.InfoViolations
	}
	return res
}

func (s ShipInvokeHelper) getStaticPublicResult(ctx *commoncontext.MantisContext, execHisDTO dto.ScanTestExecHisDTO) map[string]interface{} {
	res := make(map[string]interface{})
	shipParam := make(map[string]interface{})
	jsonx.UnMarshal([]byte(execHisDTO.ShipParams), &shipParam)
	res["pid"] = execHisDTO.ShipPid
	res["appName"] = shipParam["appName"]
	res["status"] = s.changeShipResultStatus(execHisDTO.Status)
	res["statusMsg"] = execHisDTO.StatusMsg
	scanTestTask := quality.ScanTestTask{}
	scanTestTask.Id = execHisDTO.TaskId
	scanTestTask.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &scanTestTask)
	appInfo, err := cmdb.Client.GetAppById(ctx, scanTestTask.AppId)
	if err != nil {
		logger.Logger.Panicf("查询应用错误, err=%s", err.Error())
	}
	if scanTestTask.JobType == constants.ScanJobType {
		res["zaccReportUrl"] = fmt.Sprintf(constants.QualityScanReportPath, configs.Config.Domain.Cube, execHisDTO.TaskId, scanTestTask.TaskName, appInfo.Name, scanTestTask.AppId)
	} else {
		res["zaccReportUrl"] = fmt.Sprintf(constants.QualityUnitTestReportPath, configs.Config.Domain.Cube, execHisDTO.TaskId, appInfo.Name, scanTestTask.AppId)
	}
	res["callBackAddr"] = execHisDTO.CallBackUrl
	res["mode"] = execHisDTO.Mode
	return res
}

func (ShipInvokeHelper) changeShipResultStatus(status int8) string {
	if status == task.TaskStatusSucc {
		return constants.SuccessStatus
	} else if status == task.TaskStatusFail {
		return constants.FailureStatus
	} else {
		return constants.AbortedStatus
	}
}

func (ShipInvokeHelper) restInvokeShipForStatic(jsonObj map[string]interface{}, hisId int64) {
	logger.Logger.Info("start ShipInvokeHelper.restInvokeShip...")
	callBackUrl := jsonObj["callBackAddr"]
	logger.Logger.Infof("=====中台->Devops：回调url======:%s", callBackUrl)
	invokeResult := shipInvoke.ShipCallBackWithReturn(fmt.Sprintf("%s", callBackUrl), jsonObj)
	logger.Logger.Infof("=====中台->Devops: 回调返回=====:%+v", invokeResult)
	defer func() {
		if err := recover(); err != nil {
			logger.Logger.Infof("任务id:%d，调用ship出现异常，e=%v", hisId, err)
		}
		logger.Logger.Info("end ShipInvokeHelper.restInvokeShip...")
	}()
}

package quality

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/convert"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	common_model "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	shipUtils "git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/slice"
)

type ScanProgrammeService struct{}

var scanProgrammeProfileDao dao.ScanProgrammeProfileDao

func (s ScanProgrammeService) CreateScanProgramme(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo) int64 {
	logger.Logger.Info("start create scan programme")
	programmeName := scanProgrammeDto.Name
	scanProgrammes := make([]quality.ScanProgramme, 0)
	builder := gormx.NewParamBuilder().Model(&quality.ScanProgramme{}).
		Eq("name", programmeName).Eq("is_deleted", common_constants.DeleteNo)
	if user.CompanyID != "" {
		builder.Eq("company_id", user.CompanyID)
	}
	gormx.SelectByParamBuilderX(ctx, builder, &scanProgrammes)
	if len(scanProgrammes) != 0 {
		logger.Logger.Panic("方案名称重复")
	}
	scanProgramme := quality.ScanProgramme{
		Addons: common_model.Addons{
			Creator:     user.AdAccount,
			Modifier:    user.AdAccount,
			GmtCreated:  times.Now(),
			GmtModified: times.Now(),
			IsDeleted:   common_constants.DeleteNo,
		},
		Name:      scanProgrammeDto.Name,
		Describe:  scanProgrammeDto.Describe,
		IsDefault: scanProgrammeDto.IsDefault,
		CompanyId: shipUtils.IDString(user.CompanyID),
	}
	if scanProgramme.IsDefault == constants.IsDefault {
		// 将之前的默认方案设为普通方案
		oldDefaultProgramme := quality.ScanProgramme{}
		oldDefaultProgramme.IsDefault = constants.IsDefault
		oldDefaultProgramme.IsDeleted = common_constants.DeleteNo
		oldDefaultProgramme.CompanyId = shipUtils.IDString(user.CompanyID)
		gormx.SelectOneByConditionX(ctx, &oldDefaultProgramme)
		if oldDefaultProgramme.Id != 0 {
			oldDefaultProgramme.IsDefault = constants.NotDefault
			gormx.InsertUpdateOneX(ctx, &oldDefaultProgramme)
		}
	}
	gormx.InsertUpdateOneX(ctx, &scanProgramme)
	scanProgrammeId := scanProgramme.Id
	scanProgrammeDto.Id = scanProgrammeId
	logger.Logger.Infof("save scanProgramme:%d", scanProgrammeId)
	s.dealRuleProfile(ctx, scanProgrammeDto, user)
	logger.Logger.Info("end create scan programme")
	return scanProgrammeId
}

func (s ScanProgrammeService) QueryScanProgramme(ctx *commoncontext.MantisContext, id int64) *dto.ScanProgrammeDTO {
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)
	if scanProgramme.IsDeleted == "" {
		return nil
	}
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
		Eq("programme_id", id).Eq("is_deleted", common_constants.DeleteNo), &scanProgrammeProfiles)
	scanLanguageProfileDtos := make([]dto.ScanLanguageProfileTemplateDTO, 0)
	languageProfileTemplatesMap := make(map[string][]int64)
	languageKeys := set.New[string]()
	if len(scanProgrammeProfiles) != 0 {
		for _, scanProgrammeProfile := range scanProgrammeProfiles {
			tmpIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(scanProgrammeProfile.ProfileTemplateIdsStr), &tmpIds)
			languageKeys.Add(scanProgrammeProfile.Language)
			if languageProfileTemplatesMap[scanProgrammeProfile.Language] == nil {
				languageProfileTemplatesMap[scanProgrammeProfile.Language] = make([]int64, 0)
			}
			languageProfileTemplatesMap[scanProgrammeProfile.Language] = append(languageProfileTemplatesMap[scanProgrammeProfile.Language], tmpIds...)
		}
	}
	languages := make([]quality.ScanLanguage, 0, languageKeys.Size())
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanLanguage{}).
		In("key", languageKeys.ToSlice()).Eq("is_deleted", common_constants.DeleteNo), &languages)
	languageIdNameMap := make(map[string]string)
	for _, language := range languages {
		languageIdNameMap[language.Key] = language.Name
	}
	for k, v := range languageProfileTemplatesMap {
		scanLanguageProfileDtos = append(scanLanguageProfileDtos, dto.ScanLanguageProfileTemplateDTO{
			Key:                   k,
			Name:                  languageIdNameMap[k],
			SelectProfileTemplate: v,
		})
	}
	res := convert.ScanProgrammeModelToDTO(scanProgramme)
	res.LanguageList = languageKeys.ToSlice()
	res.LanguageProfileTemplate = scanLanguageProfileDtos
	return &res
}

func (s ScanProgrammeService) DeleteScanProgramme(ctx *commoncontext.MantisContext, id int64) bool {
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)
	if scanProgramme.IsDefault == constants.IsDefault {
		logger.Logger.Panic("默认方案不允许删除！")
	}
	_, taskNameList := s.QueryScanTaskNameList(ctx, id, gormx.PageRequest{Page: 1, PageSize: 100})
	if len(taskNameList) > 0 {
		logger.Logger.Panic("关联任务不允许删除")
	}
	scanProgramme.IsDeleted = common_constants.DeleteYes
	gormx.UpdateOneByConditionX(ctx, &scanProgramme)
	goroutine.Run(func() {
		logger.Logger.Infof("start 删除方案%d绑定的规则集！", id)
		profiles := make([]quality.ScanProgrammeProfile, 0)
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", id).Eq("is_deleted", common_constants.DeleteNo), &profiles)
		s.deleteProfileRuleRelation(profiles)
		logger.Logger.Infof("end 删除方案%d绑定的规则集！", id)
	})
	return true
}

func (s ScanProgrammeService) QueryScanProgrammeList(ctx *commoncontext.MantisContext, name string) []dto.ScanProgrammeDTO {
	spaceProgrammes := make([]quality.ScanProgramme, 0)
	builder := gormx.NewParamBuilder().Model(&quality.ScanProgramme{}).
		Eq("is_deleted", common_constants.DeleteNo).
		Like("name", "%"+name+"%").OrderByDesc("is_default").OrderByDesc("gmt_modified")
	if ctx.User.CompanyID != "" {
		builder.Eq("company_id", shipUtils.IDString(ctx.User.CompanyID))
	}
	gormx.SelectByParamBuilderX(ctx, builder, &spaceProgrammes)
	scanProgrammes := make([]quality.ScanProgramme, 0)

	scanProgrammes = append(scanProgrammes, spaceProgrammes...)
	res := make([]dto.ScanProgrammeDTO, 0)
	for _, scanProgramme := range scanProgrammes {
		languageKeys := set.New[string]()
		programmeProfiles := make([]quality.ScanProgrammeProfile, 0)
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", scanProgramme.Id).Eq("is_deleted", common_constants.DeleteNo), &programmeProfiles)
		for _, programmeProfile := range programmeProfiles {
			languageKeys.Add(programmeProfile.Language)
		}
		scanProgrammeDto := convert.ScanProgrammeModelToDTO(scanProgramme)
		scanProgrammeDto.LanguageList = languageKeys.ToSlice()
		res = append(res, scanProgrammeDto)
	}
	return res
}

func (s ScanProgrammeService) QueryScanTaskNameList(ctx *commoncontext.MantisContext, id int64, pageReq gormx.PageRequest) (*gormx.PageResult, []quality.ScanTestTask) {
	res, tasks := taskDao.PageSelectRunningTaskByProgrammeId(ctx, id, pageReq)
	userIdNameMap := make(map[string]string)
	for _, task := range tasks {
		userIdNameMap[task.Operator] = ""
	}
	err := usercenterRemoteApi.GetUserAdAccountNameMap(userIdNameMap, shipUtils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	for i, task := range tasks {
		tasks[i].Operator = userIdNameMap[task.Operator]
	}
	res.List = tasks
	return res, tasks
}

func (s ScanProgrammeService) QueryProgrammeProfileEnum(ctx *commoncontext.MantisContext, programmeId int64) []commondto.CodeEnumDTO {
	programmeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
		Eq("programme_id", programmeId).Eq("is_deleted", common_constants.DeleteNo),
		&programmeProfiles)
	res := make([]commondto.CodeEnumDTO, 0, len(programmeProfiles))
	for _, programmeProfile := range programmeProfiles {
		res = append(res, commondto.CodeEnumDTO{
			Value: programmeProfile.CusKey,
			Label: programmeProfile.CusName,
		})
	}
	return res
}

func (s ScanProgrammeService) ModifyScanProgramme(ctx *commoncontext.MantisContext, programmeDTO dto.ScanProgrammeDTO, user commondto.UserInfo) int64 {
	sonarHelper := help.DefaultSonarHelper
	oldProgramme := quality.ScanProgramme{}
	oldProgramme.Id = programmeDTO.Id
	oldProgramme.IsDeleted = common_constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &oldProgramme)
	programmeName := programmeDTO.Name
	scanProgrammes := make([]quality.ScanProgramme, 0)
	builder := gormx.NewParamBuilder().Model(&quality.ScanProgramme{}).
		Eq("name", programmeName).Eq("is_deleted", common_constants.DeleteNo)
	if user.CompanyID != "" {
		builder.Eq("company_id", user.CompanyID)
	}
	if len(scanProgrammes) != 0 {
		for _, plan := range scanProgrammes {
			if (plan.Name == programmeName) && (plan.Id != programmeDTO.Id) {
				logger.Logger.Panic("方案名称重复")
			}
		}
	}
	scanProgramme := convert.ScanProgrammeDTOToModel(programmeDTO)
	scanProgramme.GmtModified = times.Now()
	scanProgramme.IsDeleted = common_constants.DeleteNo
	scanProgramme.Modifier = user.AdAccount
	if scanProgramme.IsDefault == constants.IsDefault {
		// 将之前的默认方案设为普通方案
		oldDefaultProgramme := quality.ScanProgramme{}
		oldDefaultProgramme.IsDefault = constants.IsDefault
		oldDefaultProgramme.IsDeleted = common_constants.DeleteNo
		oldDefaultProgramme.CompanyId = shipUtils.IDString(user.CompanyID)
		gormx.SelectOneByConditionX(ctx, &oldDefaultProgramme)
		if oldDefaultProgramme.Id != 0 {
			oldDefaultProgramme.IsDefault = constants.NotDefault
			gormx.InsertUpdateOneX(ctx, &oldDefaultProgramme)
		}
	}
	scanProgramme.CompanyId = shipUtils.IDString(user.CompanyID)
	gormx.InsertUpdateOneX(ctx, &scanProgramme)
	oldProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
		Eq("programme_id", programmeDTO.Id).Eq("is_deleted", common_constants.DeleteNo),
		&oldProfiles)
	// 对已有的规则集全部进行行更名
	if len(oldProfiles) != 0 && oldProgramme.Name != scanProgramme.Name {
		for _, profile := range oldProfiles {
			// 先修改sonar
			newProfileName := strings.ReplaceAll(profile.CusName, oldProgramme.Name, scanProgramme.Name)
			sonarHelper.RenameProfile(profile.CusKey, newProfileName)
			// 修改数据库
			profile.CusName = newProfileName
			gormx.UpdateOneByConditionX(ctx, &profile)
		}
	}
	for i, oldProfile := range oldProfiles {
		tmpIds := make([]int64, 0)
		jsonx.UnMarshal([]byte(oldProfile.ProfileTemplateIdsStr), &tmpIds)
		oldProfiles[i].ProfileTemplateIds = tmpIds
	}
	oldLanguageMap := make(map[string][]int64)
	for _, profile := range oldProfiles {
		if _, ok := oldLanguageMap[profile.Language]; !ok {
			oldLanguageMap[profile.Language] = make([]int64, 0)
		}
		oldLanguageMap[profile.Language] = append(oldLanguageMap[profile.Language], profile.ProfileTemplateIds...)
	}
	newLanguageMap := make(map[string]dto.ScanLanguageProfileTemplateDTO)
	for _, languageProfile := range programmeDTO.LanguageProfileTemplate {
		newLanguageMap[languageProfile.Key] = languageProfile
	}
	oldLanguageSet := set.New[string]()
	for k := range oldLanguageMap {
		oldLanguageSet.Add(k)
	}
	oldLanguages := oldLanguageSet.ToSlice()
	newLanguages := programmeDTO.LanguageList
	// 新增语言
	addLanguages := slice.Difference[string](newLanguages, oldLanguages)
	if len(addLanguages) != 0 {
		logger.Logger.Infof("新增的语言：%v", addLanguages)
		languageProfiles := make([]dto.ScanLanguageProfileTemplateDTO, 0)
		for _, addLanguage := range addLanguages {
			languageProfiles = append(languageProfiles, dto.ScanLanguageProfileTemplateDTO{
				Key:                   addLanguage,
				SelectProfileTemplate: newLanguageMap[addLanguage].SelectProfileTemplate,
			})
		}
		addProgrammeDto := dto.ScanProgrammeDTO{
			Id:                      programmeDTO.Id,
			Name:                    programmeName,
			LanguageProfileTemplate: languageProfiles,
		}
		s.dealRuleProfile(ctx, addProgrammeDto, user)
	}
	// 删除语言
	delLanguages := slice.Difference[string](oldLanguages, newLanguages)
	if len(delLanguages) != 0 {
		logger.Logger.Infof("删除的语言：%v", delLanguages)
		profiles := make([]quality.ScanProgrammeProfile, 0)
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			Eq("programme_id", programmeDTO.Id).In("language", delLanguages).
			Eq("is_deleted", common_constants.DeleteNo), &profiles)
		profileIds := make([]int64, 0)
		for _, profile := range profiles {
			profileIds = append(profileIds, profile.Id)
		}
		s.deleteProfileRuleRelation(profiles)
		// 数据库删除
		gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProgrammeProfile{}).
			In("id", profileIds), map[string]any{"is_deleted": common_constants.DeleteYes})
	}
	// 获取没有变动的语言（交集）
	mixLanguages := slice.Intersection[string](oldLanguages, newLanguages)
	if len(mixLanguages) != 0 {
		sonarHelper := help.DefaultSonarHelper
		logger.Logger.Infof("检查语言绑定的内置规则集：%v", mixLanguages)
		for _, lan := range mixLanguages {
			n1 := newLanguageMap[lan].SelectProfileTemplate
			o1 := oldLanguageMap[lan]
			if o1 == nil {
				o1 = make([]int64, 0)
			}
			addExcept := slice.Difference[int64](n1, o1)
			delExcept := slice.Difference[int64](o1, n1)
			// 取删除的模版和增加的模版的并集
			allExcept := slice.Concat[int64](addExcept, delExcept)
			templates := make([]quality.ScanProfileTemplate, 0)
			gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
				In("id", allExcept).Eq("is_deleted", common_constants.DeleteNo), &templates)
			templateIdMap := make(map[int64]quality.ScanProfileTemplate)
			for _, template := range templates {
				templateIdMap[template.Id] = template
			}
			if len(addExcept) != 0 {
				logger.Logger.Infof("绑定新的规则库！规则语言 id=%v", addExcept)
				scanProgrammeProfile := quality.ScanProgrammeProfile{}
				scanProgrammeProfile.ProgrammeId = programmeDTO.Id
				scanProgrammeProfile.Language = lan
				scanProgrammeProfile.IsDeleted = common_constants.DeleteNo
				gormx.SelectOneByConditionX(ctx, &scanProgrammeProfile)
				if scanProgrammeProfile.Id == 0 { // 查不出中间表，跳过
					continue
				}
				tmpIds := make([]int64, 0)
				jsonx.UnMarshal([]byte(scanProgrammeProfile.ProfileTemplateIdsStr), &tmpIds)
				tmpIds = append(tmpIds, addExcept...)
				scanProgrammeProfile.ProfileTemplateIdsStr = string(jsonx.Marshal(&tmpIds))
				gormx.UpdateOneByConditionX(ctx, &scanProgrammeProfile)
				// 操作sonar激活新增的规则模版的规则
				for _, templateId := range addExcept {
					sonarHelper.ActiveRulesFromTemplate(templateIdMap[templateId].Key, scanProgrammeProfile.CusKey)
				}
			}
			if len(delExcept) != 0 {
				logger.Logger.Infof("删除老规则库！规则库id=%v", delExcept)
				programmeProfile := quality.ScanProgrammeProfile{}
				programmeProfile.ProgrammeId = programmeDTO.Id
				programmeProfile.Language = lan
				programmeProfile.IsDeleted = common_constants.DeleteNo
				gormx.SelectOneByConditionX(ctx, &programmeProfile)
				if programmeProfile.Id == 0 { // 查不出中间表，跳过
					continue
				}
				tmpIds := make([]int64, 0)
				jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
				delIds := slice.Difference[int64](tmpIds, delExcept) // 删除了需要删除的模版后的模版id数组
				programmeProfile.ProfileTemplateIdsStr = string(jsonx.Marshal(&delIds))
				gormx.UpdateOneByConditionX(ctx, &programmeProfile)
				// 操作sonar失活删除的规则模版的规则
				for _, templateId := range delExcept {
					sonarHelper.DeactivateRulesFromTemplate(templateIdMap[templateId].Key, programmeProfile.CusKey, "")
				}
			}
		}
	}
	return programmeDTO.Id
}

// SetProfileActive 关闭实例卡片，不操作sonar
func (s ScanProgrammeService) SetProfileActive(ctx *commoncontext.MantisContext, programmeId int64, programmeProfileId int64, active bool) int32 {
	return scanProgrammeProfileDao.SetProfileActive(ctx, programmeProfileId, active)
}

func (s ScanProgrammeService) QueryScanProgrammeProfileCards(ctx *commoncontext.MantisContext, id int64) dto.ScanProgrammeCardDTO {
	scanProgramme := quality.ScanProgramme{}
	scanProgramme.Id = id
	gormx.SelectOneByConditionX(ctx, &scanProgramme)
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().
		Model(&quality.ScanProgrammeProfile{}).Eq("programme_id", id).
		Eq("is_deleted", common_constants.DeleteNo),
		&scanProgrammeProfiles)
	profiles := make([]dto.ScanProgrammeCardProfileDTO, 0)
	if len(scanProgrammeProfiles) != 0 {
		profileTemplateIds := make([]int64, 0)
		for _, programmeProfile := range scanProgrammeProfiles {
			tmpIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
			profileTemplateIds = append(profileTemplateIds, tmpIds...)
		}
		profileTemplates := make([]quality.ScanProfileTemplate, 0, len(profileTemplateIds))
		gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).
			In("id", profileTemplateIds), &profileTemplates)
		templateIdMap := make(map[int64]quality.ScanProfileTemplate)
		for _, template := range profileTemplates {
			templateIdMap[template.Id] = template
		}
		for _, programmeProfile := range scanProgrammeProfiles {
			profileResDto := dto.ScanProgrammeCardProfileDTO{
				IsActive:    programmeProfile.IsActive,
				Languages:   []string{programmeProfile.Language},
				ProfileName: programmeProfile.CusName,
				ProfileKey:  programmeProfile.CusKey,
				ProfileId:   programmeProfile.Id,
			}
			tmpIds := make([]int64, 0)
			jsonx.UnMarshal([]byte(programmeProfile.ProfileTemplateIdsStr), &tmpIds)
			templates := make([]dto.ScanProgrammeCardTemplateDTO, 0)
			for _, templateId := range tmpIds {
				template := templateIdMap[templateId]
				templates = append(templates, dto.ScanProgrammeCardTemplateDTO{
					TemplateId:   templateId,
					TemplateName: template.Name,
				})
			}
			profileResDto.Templates = templates
			profiles = append(profiles, profileResDto)
		}
	}
	cardDto := dto.ScanProgrammeCardDTO{}
	cardDto.Id = scanProgramme.Id
	cardDto.Describe = scanProgramme.Describe
	cardDto.Name = scanProgramme.Name
	cardDto.Profiles = profiles
	cardDto.IsDefault = scanProgramme.IsDefault
	return cardDto
}

func (s ScanProgrammeService) dealRuleProfile(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo) {
	profiles := s.saveScanProgrammeProfile(ctx, scanProgrammeDto, user, scanProgrammeDto.Id)
	s.createProfileAndActiveRule(ctx, scanProgrammeDto.Name, profiles, user)
}

func (ScanProgrammeService) saveScanProgrammeProfile(ctx *commoncontext.MantisContext, scanProgrammeDto dto.ScanProgrammeDTO, user commondto.UserInfo, programmeId int64) []quality.ScanProgrammeProfile {
	scanProgrammeProfiles := make([]quality.ScanProgrammeProfile, 0)
	languageProfile := scanProgrammeDto.LanguageProfileTemplate
	for _, scanLanguageProfile := range languageProfile {
		languageId := scanLanguageProfile.Key
		templateIds := scanLanguageProfile.SelectProfileTemplate
		scanProgrammeProfiles = append(scanProgrammeProfiles, quality.ScanProgrammeProfile{
			Addons: common_model.Addons{
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				Creator:     user.AdAccount,
				Modifier:    user.AdAccount,
				IsDeleted:   common_constants.DeleteNo,
			},
			ProgrammeId:           programmeId,
			ProfileTemplateIds:    templateIds,
			ProfileTemplateIdsStr: string(jsonx.Marshal(&templateIds)),
			Language:              languageId,
			IsActive:              true,
		})
	}
	gormx.InsertBatchX(ctx, &scanProgrammeProfiles)
	logger.Logger.Info("save scanProgrammeProfile success")
	return scanProgrammeProfiles
}

func (s ScanProgrammeService) createProfileAndActiveRule(ctx *commoncontext.MantisContext, programmeName string, programmeProfiles []quality.ScanProgrammeProfile, user commondto.UserInfo) []string {
	profileNames := make([]string, 0)
	languageIds := make([]string, 0, len(programmeProfiles))
	for _, programmeProfile := range programmeProfiles {
		languageIds = append(languageIds, programmeProfile.Language)
	}
	languages := make([]quality.ScanLanguage, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanLanguage{}).In("key", languageIds), &languages)
	languageMap := make(map[string]quality.ScanLanguage)
	for _, language := range languages {
		languageMap[language.Key] = language
	}
	for _, programmeProfile := range programmeProfiles {
		language := languageMap[programmeProfile.Language]
		profileName := shipUtils.IDString(user.CompanyID) + "_" + programmeName + "_" + language.Key
		profileNames = append(profileNames, profileName)
		// sonar新建规则集实例，并且根据模版激活规则
		key := s.createActiveProfile(ctx, programmeProfile.ProfileTemplateIds, language, profileName)
		scanProgrammeProfileDao.UpdateNameAndKeyByIds(ctx, profileName, key, programmeProfile.Id)
	}
	return profileNames
}

// sonar新建规则集实例，并且根据模版激活规则
func (ScanProgrammeService) createActiveProfile(ctx *commoncontext.MantisContext, templateIds []int64, language quality.ScanLanguage, customProfileName string) string {
	sonarHelper := help.DefaultSonarHelper
	customKey := sonarHelper.CreateProfile(customProfileName, language.Key)
	logger.Logger.Infof("rule绑定profile实例，key=%v", customKey)
	templates := make([]quality.ScanProfileTemplate, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&quality.ScanProfileTemplate{}).In("id", templateIds),
		&templates)
	for _, template := range templates {
		sonarHelper.ActiveRulesFromTemplate(template.Key, customKey)
	}
	return customKey
}

// sonar删除规则集实例
func (s ScanProgrammeService) deleteProfileRuleRelation(profiles []quality.ScanProgrammeProfile) {
	sonarHelper := help.DefaultSonarHelper
	if len(profiles) == 0 {
		return
	}
	cusNameLanguageMap := make(map[string]string)
	for _, profile := range profiles {
		cusNameLanguageMap[profile.CusName] = profile.Language
	}
	logger.Logger.Info("删除规则关联关系！")
	for k, v := range cusNameLanguageMap {
		sonarHelper.DeleteProfile(v, k)
	}
	logger.Logger.Info("end 删除规则关联关系！")
}

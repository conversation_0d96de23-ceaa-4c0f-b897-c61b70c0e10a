package jacoco

import (
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
)

func RunJacocoCoverageTask(ctx *commoncontext.MantisContext, payload payload.JacocoCoveragePayload) error {
	req := request.RunRequest{
		UUID: fmt.Sprintf("cube-mantis-jacoco-%s", uuid.NewUUIDSha256()),
		Type: commonconstants.JacocoTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ExecIdLabel:   payload.TaskNo,
			commonconstants.DetailIdLabel: "0",
			commonconstants.IsFirstLabel:  "0",
			commonconstants.TaskFromLabel: "",
		},
		Params: map[string]interface{}{
			"mode":              payload.Mode,
			"app-name":          payload.AppName,
			"ip":                payload.Ip,
			"port":              payload.Port,
			"packages":          payload.Packages,
			"git-url":           payload.Git.GitUrl,
			"git-user":          payload.Git.User,
			"git-token":         payload.Git.Token,
			"exclude":           payload.Exclude,
			"module-name":       payload.ModuleName,
			"oss-path":          payload.Oss.Path,
			"branch":            payload.Git.Branch,
			"oss-endpoint":      payload.Oss.Endpoint,
			"oss-bucket-name":   payload.Oss.Bucket,
			"oss-access-id":     payload.Oss.AccessId,
			"oss-access-key":    payload.Oss.AccessKey,
			"oss-path-style":    strconv.FormatBool(payload.Oss.PathStyle),
			"task-no":           payload.TaskNo,
			"base-commit-id":    payload.BaseCommitId,
			"compare-commit-id": payload.CompareCommitId,
			"mantis-call-back":  payload.MantisCallBack,
			"old-exec-path":     payload.OldExecPath,
		},
	}
	dp, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}
	err = dp.Run(ctx.Context, req)
	if err != nil {
		return err
	}
	// 更新任务记录的TaskRun ID
	execHis := quality.JacocoCoverageExecHis{}
	execHis.Id = payload.HisId
	execHis.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &execHis)
	execHis.K8sTaskId = req.UUID
	gormx.UpdateOneByConditionX(ctx, &execHis)
	return nil
}

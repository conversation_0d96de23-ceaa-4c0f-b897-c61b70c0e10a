package sonar

import (
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
)

func RunSonarScannerTask(ctx *commoncontext.MantisContext, qualityScanTaskPayload payload.QualityScanTaskPayload) error {
	req := request.RunRequest{
		UUID: fmt.Sprintf("cube-mantis-sonar-%s", uuid.NewUUIDSha256()),
		Type: commonconstants.SonarTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ExecIdLabel:   strconv.FormatInt(qualityScanTaskPayload.BizParam.ExecHisId, 10),
			commonconstants.DetailIdLabel: "0",
			commonconstants.IsFirstLabel:  "0",
			commonconstants.TaskFromLabel: "",
		},
		Params: map[string]interface{}{
			"call-back":         qualityScanTaskPayload.BizParam.CallBack,
			"type":              qualityScanTaskPayload.BizParam.Type,
			"mode":              qualityScanTaskPayload.BizParam.Mode,
			"language":          qualityScanTaskPayload.BizParam.Language,
			"java-version":      qualityScanTaskPayload.BizParam.JavaVersion,
			"module-path":       qualityScanTaskPayload.BizParam.ModulePath,
			"app-type":          qualityScanTaskPayload.BizParam.AppType,
			"code-url":          qualityScanTaskPayload.CodeParam.CodeUrl,
			"git-user":          qualityScanTaskPayload.CodeParam.GitUser,
			"git-token":         qualityScanTaskPayload.CodeParam.GitToken,
			"branch":            qualityScanTaskPayload.CodeParam.Branch,
			"base-commit-id":    qualityScanTaskPayload.CodeParam.BaseCommitId,
			"compare-commit-id": qualityScanTaskPayload.CodeParam.CompareCommitId,
			"sonar-url":         qualityScanTaskPayload.SonarParam.SonarUrl,
			"sonar-token":       qualityScanTaskPayload.SonarParam.SonarToken,
			"sonar-pdf":         strconv.FormatBool(qualityScanTaskPayload.SonarParam.SonarPdf),
			"sonar-profile":     qualityScanTaskPayload.SonarParam.SonarProfile,
			"sonar-callback":    qualityScanTaskPayload.SonarParam.SonarCallback,
			"sonar-exclusion":   qualityScanTaskPayload.BizParam.ExclusionPath,
			"sonar-inclusion":   qualityScanTaskPayload.BizParam.InclusionPath,
			"mvn-repo":          qualityScanTaskPayload.MvnParam.MvnRepo,
			"mvn-user":          qualityScanTaskPayload.MvnParam.MvnUser,
			"mvn-pw":            qualityScanTaskPayload.MvnParam.MvnPw,
		},
	}
	dp, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}
	err = dp.Run(ctx.Context, req)
	if err != nil {
		return err
	}
	// 更新任务记录的TaskRun ID
	execHis := quality.ScanTestExecHis{}
	execHis.Id = qualityScanTaskPayload.BizParam.ExecHisId
	execHis.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &execHis)
	execHis.K8sTaskId = req.UUID
	gormx.UpdateOneByConditionX(ctx, &execHis)
	return nil
}

package trivy

import (
	"encoding/base64"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
)

func RunTrivyScannerTask(ctx *commoncontext.MantisContext, producerTask payload.ArtifactScanTaskPayload) error {
	req := request.RunRequest{
		UUID: fmt.Sprintf("cube-mantis-trivy-%s", uuid.NewUUIDSha256()),
		Type: commonconstants.TrivyTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ExecIdLabel:   strconv.FormatInt(producerTask.ExecId, 10),
			commonconstants.DetailIdLabel: strconv.FormatInt(producerTask.DetailId, 10),
			commonconstants.IsFirstLabel:  strconv.Itoa(int(producerTask.IsFirst)),
			commonconstants.TaskFromLabel: producerTask.TaskFrom,
		},
		Params: map[string]interface{}{
			"type":              producerTask.ProductType,
			"cube-mantis-url":   configs.Config.App.Svc,
			"upload-result-url": producerTask.UploadResultUrl,
			"white-cve":         strings.Join(producerTask.WhiteCVE, ","),
		},
	}

	switch strings.ToLower(producerTask.ProductType) {
	case "generic":
		artifactDownloadUrl, err := url.JoinPath(producerTask.InstanceInfo.Url,
			fmt.Sprintf("/api/%s/Generic/%s?version=%s", producerTask.RepoName, producerTask.ProductName, producerTask.ProductVersion))
		if err != nil {
			return err
		}
		if producerTask.DownloadUrl != "" {
			artifactDownloadUrl = producerTask.DownloadUrl
		}
		artifactDownloadUrl, err = url.QueryUnescape(artifactDownloadUrl)
		if err != nil {
			return err
		}
		req.Params["filename"] = producerTask.ProductName
		req.Params["artifact-download-url"] = artifactDownloadUrl
		req.Params["artifact-download-token"] = producerTask.InstanceInfo.Token
		authorization := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", producerTask.InstanceInfo.Username, producerTask.InstanceInfo.PassWord)))
		req.Params["artifact-download-authorization"] = authorization
	case "docker":
		var protocol string
		addr := producerTask.InstanceInfo.Url
		if strings.Contains(producerTask.InstanceInfo.Url, "//") {
			addr = strings.Split(producerTask.InstanceInfo.Url, "//")[1]
			protocol = strings.Split(producerTask.InstanceInfo.Url, "//")[0]
		}
		image := fmt.Sprintf("%s/%s/%s:%s", addr, producerTask.RepoName, producerTask.ProductName, producerTask.ProductVersion)
		// 如果仓库实例来自于nexus,则从downloadUrl中获取地址
		if producerTask.InstanceInfo.Type == "nexus" {
			u, err := url.Parse(producerTask.DownloadUrl)
			if err != nil {
				return fmt.Errorf("nexus:docker解析下载地址失败: %s,%v", producerTask.DownloadUrl, err)
			}
			protocol = u.Scheme
			addr = fmt.Sprintf("%s://%s", protocol, u.Host)
			image = u.Host + u.Path
		}
		req.Params["image"] = image
		req.Params["docker-addr"] = addr
		req.Params["docker-user"] = producerTask.InstanceInfo.Username
		req.Params["docker-password"] = producerTask.InstanceInfo.PassWord.String()
		if protocol == "https:" {
			req.Params["non-ssl"] = "false"
		} else {
			req.Params["non-ssl"] = "true"
		}
	default:
		return fmt.Errorf("不支持的制品类型: %s", producerTask.ProductType)
	}

	dp, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}
	err = dp.Run(ctx.Context, req)
	if err != nil {
		return err
	}

	// 更新任务详情关联的TaskRun ID
	var scanTaskDetail artifact.ScanTaskDetail
	scanTaskDetailBuilder := gormx.NewParamBuilder().Model(&artifact.ScanTaskDetail{}).Eq("id", producerTask.DetailId).Eq("is_deleted", commonconstants.DeleteNo)
	gormx.SelectByParamBuilderX(ctx, scanTaskDetailBuilder, &scanTaskDetail)
	if scanTaskDetail.Id == 0 {
		return fmt.Errorf("未查询到detailId为%v的数据", producerTask.DetailId)
	}
	scanTaskDetail.K8sTaskId = req.UUID
	gormx.UpdateOneByConditionX(ctx, &scanTaskDetail)

	return nil
}

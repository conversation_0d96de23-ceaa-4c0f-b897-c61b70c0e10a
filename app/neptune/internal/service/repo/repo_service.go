package repo

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/TimeBye/go-harbor"
	"github.com/duke-git/lancet/v2/netutil"
)

var (
	repoServiceMap = make(map[string]IRepoService)
	// NameRepoInstanceMap name-repoInstance 映射
	NameRepoInstanceMap = make(map[string]configs.RepoInstance)
)

func addRepoService(repo configs.RepoInstance) {
	NameRepoInstanceMap[repo.InstanceName] = repo
	switch repo.Type {
	case "harbor":
		harborClient, err := harbor.NewClientSet(repo.Url, repo.Username, repo.PassWord.String())
		if err != nil {
			panic(err)
		}
		repoServiceMap[repo.ID] = HarborRepoService{client: harborClient}
		break
	case "normal":
		zaArtifactService := ZaArtifactRepoService{
			endpoint:   repo.Url,
			token:      repo.Token,
			httpClient: netutil.NewHttpClient(),
		}
		repoServiceMap[repo.ID] = zaArtifactService
	case "nexus":
		// ship 提供
		deckjobRepoService := DeckjobRepoService{
			httpClient: netutil.NewHttpClient(),
			instanceId: repo.ID,
		}
		repoServiceMap[repo.ID] = deckjobRepoService
	default:
		logger.Logger.Panicf("不支持的仓库类型[%v]!", repo.Type)
	}
}

func GetServiceById(ctx *commoncontext.MantisContext, companyId, id string) IRepoService {
	service := repoServiceMap[id]
	if service != nil {
		return service
	}
	repo, err := GetRepoInstanceInfo(ctx, companyId, id)
	if err != nil {
		logger.Logger.Panicf("mantis-neptune 从deckjob未获取到 id=%s 的对应的仓库服务! ", id)
	}
	addRepoService(*repo)
	return repoServiceMap[id]
}

// IRepoService 仓库服务接口定义
type IRepoService interface {
	// RepoInfos 根据实例下所有的仓库 参数: 仓库名称 制品类型
	RepoInfos(ctx *commoncontext.MantisContext, repoName string, format string) []dto.RepoDto
	// LatestAllProducts 查询仓库下所有最新的制品
	LatestAllProducts(ctx *commoncontext.MantisContext, repoName string, repoId string, format string) []dto.ViewProductInfo
	// Products 根据仓库名称和仓库id查询制品
	Products(ctx *commoncontext.MantisContext, repoName string, repoId string, productName string, format string) []dto.MinProductDto
	// Versions 查询版本
	Versions(ctx *commoncontext.MantisContext, repoName string, versionName string, productId string, productName string, format string) []dto.ProductVersionDto
	// Path 根据版本id查询制品
	Path(ctx *commoncontext.MantisContext, versionId, format string) []dto.ProductPathDto
}

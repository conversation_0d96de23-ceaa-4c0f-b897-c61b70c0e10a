package repo

import (
	"encoding/json"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
)

type ZaArtifactRepoService struct {
	endpoint   string
	token      string
	httpClient *netutil.HttpClient
}

func (h ZaArtifactRepoService) RepoInfos(ctx *commoncontext.MantisContext, repoName, format string) []dto.RepoDto {
	url := h.endpoint + "/v1/web/repo/findList"
	if repoName != "" {
		url += "?queryName=" + repoName
	}
	request := &netutil.HttpRequest{
		RawURL:  url,
		Method:  "GET",
		Headers: ctx.Header,
	}
	data := zaArtifactSendRequest(h.httpClient, request)
	arr := make([]dto.RepoDto, 0)
	arrI, ok := data.([]interface{})
	if ok {
		for _, v := range arrI {
			vMap := v.(map[string]interface{})
			arr = append(arr, dto.RepoDto{
				Id:       strconv.FormatInt(int64(vMap["id"].(float64)), 10),
				Name:     vMap["name"].(string),
				RepoType: vMap["repoType"].(string),
			})
		}
	}
	return arr
}

func (h ZaArtifactRepoService) LatestAllProducts(ctx *commoncontext.MantisContext, repoName string, repoId string, format string) []dto.ViewProductInfo {
	return h.vProducts(ctx, repoName, repoId, "", 1, 9999)
}

func (h ZaArtifactRepoService) Products(ctx *commoncontext.MantisContext, repoName string, repoId string, productName string, format string) []dto.MinProductDto {
	products := h.vProducts(ctx, repoName, repoId, productName, 1, 999)
	resp := make([]dto.MinProductDto, len(products))
	for i, v := range products {
		resp[i] = dto.MinProductDto{
			Id:   v.ProductInfo.Id,
			Name: v.ProductInfo.Name,
		}
	}
	return resp
}

func (h ZaArtifactRepoService) Path(ctx *commoncontext.MantisContext, versionId, format string) []dto.ProductPathDto {
	return nil
}

func (h ZaArtifactRepoService) vProducts(ctx *commoncontext.MantisContext, repoName string, repoId string, productName string, page, pageSize int64) []dto.ViewProductInfo {
	params := make(map[string]interface{})
	params["id"] = repoId
	params["pageSize"] = pageSize
	params["currentPage"] = page
	params["query"] = productName
	url := h.endpoint + "/v1/web/artifact/findPage/" + repoId + "?" + netutil.ConvertMapToQueryString(params)
	urlRequest := &netutil.HttpRequest{
		RawURL:  url,
		Method:  "GET",
		Headers: ctx.Header,
	}
	data := zaArtifactSendRequest(h.httpClient, urlRequest)
	resp := make([]dto.ViewProductInfo, 0)
	dataX, ok := data.(map[string]interface{})
	if ok {
		arr := dataX["value"].([]interface{})
		respt := make([]dto.ViewProductInfo, len(arr))
		for i, v := range arr {
			byteObj, _ := json.Marshal(v)
			productDto := zaArtifactMinProductDto{}
			json.Unmarshal(byteObj, &productDto)
			vpi := dto.ViewProductInfo{
				RepoInfo:    &dto.RepoDto{Name: repoName, Id: repoId},
				ProductInfo: &dto.MinProductDto{Name: productDto.Name, Id: strconv.FormatInt(productDto.Id, 10)},
				VersionInfo: &productDto.LastVersion,
			}
			// 加到返回对象里
			respt[i] = vpi
		}
		return respt
	}
	return resp
}

func (h ZaArtifactRepoService) Versions(ctx *commoncontext.MantisContext, repoName string, versionName string, productId string, productName string, format string) []dto.ProductVersionDto {
	params := make(map[string]interface{})
	params["fileId"] = productId
	params["pageSize"] = 100
	params["currentPage"] = 1
	params["query"] = versionName
	url := h.endpoint + "/v1/web/version/findPage/" + productId + "?" + netutil.ConvertMapToQueryString(params)
	urlRequest := &netutil.HttpRequest{
		RawURL:  url,
		Method:  "GET",
		Headers: ctx.Header,
	}
	data := zaArtifactSendRequest(h.httpClient, urlRequest)
	resp := make([]dto.ProductVersionDto, 0)
	dataX, ok := data.(map[string]interface{})
	if ok {
		arr := dataX["value"].([]interface{})
		respt := make([]dto.ProductVersionDto, len(arr))
		for i, v := range arr {
			productDto := dto.ProductVersionDto{
				Version: v.(map[string]interface{})["version"].(string),
				// 兼容新页面
				Id: "0",
			}
			respt[i] = productDto
		}
		return respt
	}
	return resp
}

func zaArtifactSendRequest(httpClient *netutil.HttpClient, request *netutil.HttpRequest) interface{} {
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in zaArtifactSendRequest err, err=%+v", err)
	}
	m := zaArtifactRespDto{}
	err = httpClient.DecodeResponse(resp, &m)
	if err != nil {
		logger.Logger.Panicf("error in zaArtifactSendRequest decodeResp, err=%+v", err)
	}
	if m.Code != "0" {
		logger.Logger.Panicf("error in zaArtifactSendRequestt resp fail: mag=[%+v] ", m.Message)
	}
	return m.Data
}

type zaArtifactRespDto struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

type zaArtifactMinProductDto struct {
	Name        string                `json:"name"` // 制品名称
	Id          int64                 `json:"id"`   // 制品id
	LastVersion dto.ProductVersionDto `json:"lastVersion"`
}

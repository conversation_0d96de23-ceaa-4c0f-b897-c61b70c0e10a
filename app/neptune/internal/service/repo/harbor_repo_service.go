package repo

import (
	"strconv"
	"strings"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	client2 "github.com/TimeBye/go-harbor/pkg/client"
	"github.com/TimeBye/go-harbor/pkg/model"
	"github.com/TimeBye/go-harbor/pkg/project/options"
)

type HarborRepoService struct {
	client *client2.Clientset
}

func (h HarborRepoService) RepoInfos(ctx *commoncontext.MantisContext, projectName, format string) []dto.RepoDto {
	query := &options.ProjectsListOptions{
		Query: &model.Query{
			PageSize: 100,
			Page:     1,
			Q:        "",
		},
		Name: projectName,
	}
	result, err := h.client.V2.List(query)
	if err != nil {
		logger.Logger.Errorf("获取项目列表失败:%v", err)
	}
	if result == nil {
		return make([]dto.RepoDto, 0)
	}
	resp := make([]dto.RepoDto, len(*result))
	for i, project := range *result {
		resp[i] = dto.RepoDto{
			Id:       strconv.FormatInt(project.ProjectID, 10),
			Name:     project.Name,
			RepoType: "Docker",
		}
	}
	return resp
}

func (h HarborRepoService) LatestAllProducts(ctx *commoncontext.MantisContext, projectName string, projectId string, format string) []dto.ViewProductInfo {
	// 只能传id
	pInfo, err := h.client.V2.Get(projectId)
	if err != nil {
		logger.Logger.Errorf("查询项目[%v]失败,err=%v", projectName, err)
	}
	if pInfo.RepoCount == 0 {
		logger.Logger.Infof("项目[%v]下无仓库", projectName)
		return make([]dto.ViewProductInfo, 0)
	}
	logger.Logger.Infof("[%v]共有仓库个数:%d", projectName, pInfo.RepoCount)
	// 设置协程数  pInfo.RepoCount / 10 + 1
	var ps int64 = 20
	n := pInfo.RepoCount/ps + 1
	tasks := make([]func(), 0, n)
	taskResultChan := make(chan *dto.ViewProductInfo, 10)
	taskResults := make([]dto.ViewProductInfo, 0)
	var wg sync.WaitGroup
	wg.Add(1)
	goroutine.RunWithoutLimit(func() {
		defer wg.Done()
		for {
			res := <-taskResultChan
			if res == nil {
				break
			}
			taskResults = append(taskResults, *res)
		}
	})
	for i := 0; i < int(n); i++ {
		idx := i
		tasks = append(tasks, func() {
			page := idx + 1
			startTime := time.Now()
			query := &options.RepositoriesListOptions{
				Query: &model.Query{
					PageSize: ps,
					Page:     int64(page),
				},
			}
			result, err := h.client.V2.Repositories(projectName).List(query) // 使用harborClient
			if err != nil {
				logger.Logger.Errorf("获取项目下仓库列表失败:%v", err)
			}
			logger.Logger.Infof("func in ---- get repoNemeList ---- ids=[%d],耗时 %v ms", idx, time.Since(startTime).Milliseconds())
			if result == nil {
				taskResultChan <- &dto.ViewProductInfo{}
			}
			startTime = time.Now()
			for _, repo := range *result {
				nameArr := strings.SplitN(repo.Name, "/", 2)
				list, _ := h.client.V2.Repositories(projectName).Artifacts(nameArr[1]).List(&options.ArtifactsListOptions{Query: &model.Query{
					PageSize: 1,
					Page:     1,
				}})
				// list中只有1条数据
				if len(*list) == 1 {
					art := (*list)[0]
					vpi := dto.ViewProductInfo{
						RepoInfo:    &dto.RepoDto{Name: projectName},
						ProductInfo: &dto.MinProductDto{Name: nameArr[1], Id: strconv.FormatInt(art.ID, 10)},
					}
					if len(art.Tags) > 0 {
						vpi.VersionInfo = &dto.ProductVersionDto{
							Version: art.Tags[0].Name,
						}
					}
					taskResultChan <- &vpi
				}
			}
			logger.Logger.Infof("func in ---- get repoProductList ---- ids=[%d],耗时 %v ms", idx, time.Since(startTime).Milliseconds())
		})
	}
	err = goroutine.RunTasks(tasks)
	if err != nil {
		logger.Logger.Panicf("error in running async tasks, err = %v", err)
	}
	taskResultChan <- nil
	products := make([]dto.ViewProductInfo, 0)
	for _, tr := range taskResults {
		if tr.ProductInfo == nil || tr.ProductInfo.Name == "" {
			// 排除空对象的影响
			continue
		}
		products = append(products, tr)
	}
	return products
}

func (h HarborRepoService) Products(ctx *commoncontext.MantisContext, projectName string, projectId string, productName string, format string) []dto.MinProductDto {
	q := ""
	if productName != "" {
		q = "name=~" + productName
	}
	query := &options.RepositoriesListOptions{
		Query: &model.Query{
			PageSize: 100,
			Page:     1,
			Q:        q,
		},
	}
	result, err := h.client.V2.Repositories(projectName).List(query)
	if err != nil {
		logger.Logger.Errorf("获取项目下仓库列表失败:%v", err)
	}
	products := make([]dto.MinProductDto, len(*result))
	for i, p := range *result {
		products[i] = dto.MinProductDto{
			Name: strings.Replace(p.Name, projectName+"/", "", 1),
			Id:   strconv.FormatInt(p.RepositoryID, 10),
		}
	}
	return products
}

func (h HarborRepoService) Versions(ctx *commoncontext.MantisContext, projectName string, versionName string, productId string, productName string, format string) []dto.ProductVersionDto {
	q := ""
	if versionName != "" {
		q = "tags=~" + versionName
	}
	query := &options.ArtifactsListOptions{
		Query: &model.Query{
			PageSize: 100,
			Page:     1,
			Q:        q,
		},
	}
	result, err := h.client.V2.Repositories(projectName).Artifacts(productName).List(query)
	if err != nil {
		logger.Logger.Errorf("获取Artifacts列表失败:%v", err)
	}
	reps := make([]dto.ProductVersionDto, len(*result))
	for i, p := range *result {
		if len(p.Tags) < 1 {
			continue
		}
		if p.Tags[0].Name == "" {
			continue
		}
		reps[i] = dto.ProductVersionDto{
			Version: p.Tags[0].Name,
		}
	}
	return reps
}

func (h HarborRepoService) Path(ctx *commoncontext.MantisContext, versionId, format string) []dto.ProductPathDto {
	return nil
}

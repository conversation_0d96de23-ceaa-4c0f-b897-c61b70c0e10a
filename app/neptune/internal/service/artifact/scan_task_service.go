package artifact

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/utils"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTaskService struct{}

var (
	taskExecHisService = ScanTaskExecHisService{}
	planService        = ScanPlanService{}
	detailService      = ScanTaskDetailService{}
)

func (s ScanTaskService) List(ctx *commoncontext.MantisContext, request *commondto.QueryWithOrderDTO, user commondto.UserInfo) *gormx.PageResult {
	logger.Logger.Infof("ScanTaskService.List...request: %+v ", request)
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanTask{}).Eq("is_deleted",
		common_constants.DeleteNo).Eq("company_id", user.CompanyID)
	if search := request.SearchKey; search != "" {
		paramBuilder.Or(gormx.NewParamBuilder().ILike("repo_names", "%"+search+"%"), gormx.NewParamBuilder().ILike("task_name", "%"+search+"%"))
	}
	planType := request.GetQueryParam("planType")
	if planType != nil && reflect.TypeOf(planType).Kind() == reflect.String {
		paramBuilder.Eq("plan_type", planType)
	}
	productType := request.GetQueryParam("productType")
	if productType != nil && reflect.TypeOf(productType).Kind() == reflect.String {
		paramBuilder.Eq("product_type", productType)
	}
	taskFrom := request.GetQueryParam("taskFrom")
	if taskFrom != nil && reflect.TypeOf(taskFrom).Kind() == reflect.String {
		paramBuilder.Eq("task_from", taskFrom)
	}
	if request.OrderField != "" {
		paramBuilder.Order(request.OrderField, request.OrderTypeFormat())
	} else {
		paramBuilder.Order("id", "desc")
	}
	list := make([]artifact.ScanTask, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &list, gormx.PageRequest{PageSize: request.PageSize, Page: request.Page})
	if err != nil {
		logger.Logger.Panicf("查询任务列表失败!err=%v", err)
	}
	planIds := make([]int64, len(list))
	execIds := make([]int64, len(list))
	for i, dto := range list {
		planIds[i] = dto.PlanId
		execIds[i] = dto.LastExecId
	}
	planList := make([]*artifact.ScanPlan, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&artifact.ScanPlan{}).In("id", planIds).Eq("is_deleted", common_constants.DeleteNo), &planList)
	planMap := common_util.ListToMap(planList)
	execList := make([]*artifact.ScanTaskExecHis, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&artifact.ScanTaskExecHis{}).In("id", execIds), &execList)
	execMap := common_util.ListToMap(execList)
	for i, dto := range list {
		if plan, ok := planMap[dto.PlanId]; ok {
			dto.PlanName = plan.PlanName
		}
		if dto.QualityGate != "" {
			err := json.Unmarshal([]byte(dto.QualityGate), &dto.QualityGateInfo)
			if err != nil {
				logger.Logger.Panicf("质量阀对象转化失败!err=%v", err)
			}
		}
		if exec, ok := execMap[dto.LastExecId]; ok {
			dto.LastExecUserName = exec.Creator
			dto.LastTriggerTime = exec.TriggerTime
			dto.LastTaskStatus = task2.StatusCodeToMemo(exec.TaskStatus)
			dto.LastTaskStatusCode = exec.TaskStatus
			vsr, _ := utils.CalGateQualityGate(constants.IsFalse, exec.TaskStatus, exec.ScanResult, dto.QualityGateInfo)
			dto.LastScanResultInfo = vsr
			dto.ScanProductCount = &exec.ProductCount
		} else {
			dto.LastExecUserName = "-"
			dto.LastTaskStatus = task2.StatusCodeToMemo(task2.TaskStatusWait)
			dto.LastScanResultInfo = nil
			dto.LastTriggerTime = nil
		}
		if dto.Repo != "" {
			err := json.Unmarshal([]byte(dto.Repo), &dto.RepoInfo)
			if err != nil {
				logger.Logger.Panicf("仓库对象转化失败!err=%v", err)
			}
		}
		list[i] = dto
	}
	return page
}

func (s ScanTaskService) Info(ctx *commoncontext.MantisContext, id, execId int64) artifact.ScanTask {
	logger.Logger.Infof("ScanTaskService.Info...id: %d", id)
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanTask{}).Eq("id",
		id).Eq("is_deleted", common_constants.DeleteNo)
	var res artifact.ScanTask
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
	if res.QualityGate != "" {
		err := json.Unmarshal([]byte(res.QualityGate), &res.QualityGateInfo)
		if err != nil {
			logger.Logger.Panicf("质量阀对象转化失败!err=%v", err)
		}
	}
	if res.Repo != "" {
		err := json.Unmarshal([]byte(res.Repo), &res.RepoInfo)
		if err != nil {
			logger.Logger.Panicf("仓库对象转化失败!err=%v", err)
		}
	}
	if res.ScanRange == task2.ScanRangeCusProduct && res.ScanRangeInfo != "" {
		err := json.Unmarshal([]byte(res.ScanRangeInfo), &res.ProductList)
		if err != nil {
			logger.Logger.Panicf("制品列表对象转化失败!err=%v", err)
		}
	}
	var planName string
	gormx.RawX(ctx, "select plan_name from neptune_artifact_scan_plan where id=?", &planName, res.PlanId)
	res.PlanName = planName
	// 获取执行记录
	if execId != 0 {
		// 设置扫描次数
		if res.Id != execId {
			res.ScanCount = taskExecHisService.GetScanCount(ctx, res.Id)
		} else {
			// 历史详情,无需展示次数
			var sc int64 = 1
			res.ScanCount = &sc
		}
		// 设置最新执行记录信息
		exec := taskExecHisService.GetExecRecord(ctx, execId)
		res.LastExecUserName = exec.Creator
		res.LastTriggerTime = exec.TriggerTime
		res.LastTaskStatus = task2.StatusCodeToMemo(exec.TaskStatus)
		res.LastTaskStatusCode = exec.TaskStatus
		vsr, _ := utils.CalGateQualityGate(res.QualityGateSwitch, exec.TaskStatus, exec.ScanResult, res.QualityGateInfo)
		res.LastScanResultInfo = vsr
		res.ScanProductCount = &exec.ProductCount
	}
	return res
}

func (s ScanTaskService) DeleteTask(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	task := artifact.ScanTask{}
	task.IsDeleted = common_constants.DeleteYes
	task.Id = id
	task.GmtModified = times.Now()
	task.Modifier = user.AdAccount
	gormx.UpdateOneByConditionX(ctx, &task)
}

func (s ScanTaskService) AddOrUpdateTask(ctx *commoncontext.MantisContext, task *artifact.ScanTask, user commondto.UserInfo) int64 {
	logger.Logger.Infof("ScanTaskService.AddOrUpdateTask...plan: %+v ", task)
	task.Modifier = user.AdAccount
	task.GmtModified = times.Now()
	if task.QualityGateSwitch == 1 {
		strByte, _ := json.Marshal(task.QualityGateInfo)
		task.QualityGate = string(strByte)
	}
	if task.ScanRange == task2.ScanRangeCusProduct {
		if task.ProductList == nil {
			logger.Logger.Panicf("选择指定制品扫描时,制品信息必填")
		}
		plByte, _ := json.Marshal(task.ProductList)
		task.ScanRangeInfo = string(plByte)
		// 需要对仓库列表处理
	}

	if task.Id == 0 {
		// 新增方案逻辑
		task.CompanyID = fmt.Sprintf("%v", user.CompanyID)
		task.Creator = user.AdAccount
		task.GmtCreated = times.Now()
	} else {
		// 修改方案逻辑(目前不设置逻辑)
	}
	if task.ScanRange == task2.ScanRangeCusProduct && task.ScanRangeInfo != "" {
		// 选择自定义制品
		rl := distantRepo(task.ProductList)
		repoByte, _ := json.Marshal(rl)
		task.Repo = string(repoByte)
		repoNames := ""
		lastRepo := ""
		for _, repo := range *task.ProductList {
			if lastRepo == repo.RepoInfo.Name {
				continue
			}
			lastRepo = repo.RepoInfo.Name
			repoNames += repo.RepoInfo.Name + "\u0001"
		}
		task.RepoNames = repoNames
	} else if task.ScanRange == task2.ScanRangeLastProduct {
		repoByte, _ := json.Marshal(task.RepoInfo)
		task.Repo = string(repoByte)
		repoNames := ""
		for _, repo := range *task.RepoInfo {
			repoNames += repo.Name + "\u0001"
		}
		task.RepoNames = repoNames
	}
	gormx.InsertUpdateOneX(ctx, task)
	logger.Logger.Infof("ScanTaskService.AddOrUpdateTask...id: %d \n", task.Id)
	return task.Id
}

// 遍历制品列表,获取仓库信息并去重
func distantRepo(pl *[]dto.ViewProductInfo) []dto.RepoDto {
	repoMap := make(map[string]dto.RepoDto)
	for _, p := range *pl {
		if _, ok := repoMap[p.RepoInfo.Name]; !ok {
			repoMap[p.RepoInfo.Name] = *p.RepoInfo
		}
	}
	rl := make([]dto.RepoDto, 0)
	for _, v := range repoMap {
		rl = append(rl, v)
	}
	return rl
}

func (s ScanTaskService) Exec(ctx *commoncontext.MantisContext, taskId int64, user commondto.UserInfo, appName string) int64 {
	logger.Logger.Infof("ScanTaskService.Exec...taskId: %d ", taskId)
	// 查看task
	taskBuilder := gormx.NewParamBuilder().Model(&artifact.ScanTask{}).Eq("id",
		taskId).Eq("is_deleted", common_constants.DeleteNo)
	var task artifact.ScanTask
	gormx.SelectByParamBuilderX(ctx, taskBuilder, &task)
	if task.Id == 0 {
		logger.Logger.Warnf("taskId: %d 不存在!或被删除!", taskId)
		logger.Logger.Panicf("任务不存在,或被删除!")
	}
	// 获取关联的方案,查看SVC白名单
	cveArr := planService.whiteInfos(ctx, task.PlanId)
	// 生成任务历史记录
	execId := taskExecHisService.InitTaskExecHis(ctx, task.Id, task.Repo, user, appName)
	gormx.ExecX(ctx, "update neptune_artifact_scan_task set last_exec_id=? where id=?", execId, taskId)
	// 将所有制品初始化记录写入数据库
	goroutine.Run(func() {
		detailService.GenTaskDetail(ctx, &task, execId, user, cveArr)
	})
	// 依次调用触发接口
	fmt.Println(cveArr, execId)
	return execId
}

func (s ScanTaskService) Abort(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	logger.Logger.Infof("ScanTaskService.Abort...id: %d,user=%v ", id, user.AdAccount)
	var execId int64
	gormx.RawX(ctx, "select last_exec_id from neptune_artifact_scan_task where id=?", &execId, id)
	if execId == 0 {
		logger.Logger.Panicf("id: %d 没有执行记录!", id)
	}
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set task_status=?,gmt_modified=?,modifier=? where id= ?",
		task2.TaskStatusFail, times.Now(), user.AdAccount, execId)
	// k8s 中的任务取消
	var k8sTaskIdArr []string
	gormx.RawX(ctx, "select k8s_task_id from neptune_artifact_scan_task_detail where exec_id=? and status=?", &k8sTaskIdArr,
		execId, task2.TaskStatusRun)
	logger.Logger.Infof("需要取消的k8s task有 %v 个!", len(k8sTaskIdArr))
	goroutine.Run(func() {
		ctx := context.Background()
		for _, k8sTaskId := range k8sTaskIdArr {
			if k8sTaskId != "" {
				provider, _ := driver.NewDriverProvider()
				err := provider.Cancel(ctx, k8sTaskId)
				if err != nil {
					logger.Logger.Errorf("taskId=%v,k8sTaskId: %s,取消任务失败!err=%v", id, k8sTaskId, err)
				}
			}
		}
	})
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_detail set status=?,gmt_modified=? ,modifier=? where exec_id= ? and status in (0,1,4)",
		task2.TaskStatusFail, times.Now(), user.AdAccount, execId)
}

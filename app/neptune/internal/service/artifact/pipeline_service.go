package artifact

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/scan"
	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type PipelineService struct{}

func (s PipelineService) ScanProduct(ctx *commoncontext.MantisContext, req *dto.PipelineProductScanRequestDto) map[string]interface{} {
	logger.Logger.Infof("pipeline-req: %+v", req)
	resp := make(map[string]interface{})
	plan := artifact.ScanPlan{}
	// 获取默认计划
	gormx.RawX(ctx, "select * from neptune_artifact_scan_plan where company_id=? and is_default=1 and plan_type = ? and is_deleted ='N' order by id desc limit 1",
		&plan, req.CompanyID, scan.ScanProductCve)
	if plan.Id == 0 {
		logger.Logger.Panicf("没有找到默认漏洞扫描方案,请先设置默认方案!")
		return resp
	}
	// 流水线默认用户
	user := commondto.UserInfo{CompanyID: req.CompanyID, AdAccount: constants.UserDevops, Username: constants.UserDevops}
	task := artifact.ScanTask{}
	// 检查task是否存在
	gormx.RawX(ctx, "select * from neptune_artifact_scan_task where task_name=? and company_id=? and  plan_type = ? and task_from =? "+
		" order by id desc limit 1",
		&task, req.TaskName, req.CompanyID, scan.ScanProductCve, task2.TaskFromPipeline)
	task.GmtModified = times.Now()
	task.CallBackUrl = req.CallBackUrl
	task.ProductList = &[]dto.ViewProductInfo{{
		RepoInfo:    &dto.RepoDto{Name: req.RepoName},
		ProductInfo: &dto.MinProductDto{Name: req.ProductName},
		VersionInfo: &dto.ProductVersionDto{Version: req.ProductVersion},
		PathInfo:    &dto.ProductPathDto{Path: req.ProductPath, DownloadUrl: req.ProductDownloadUrl},
	}}
	if task.Id == 0 {
		logger.Logger.Infof("创建一个新的task,taskName=%v", req.TaskName)
		// 创建task
		task.TaskName = req.TaskName
		task.ScanRange = task2.ScanRangeCusProduct
		task.CompanyID = req.CompanyID
		task.PlanId = plan.Id
		task.PlanType = scan.ScanProductCve
		task.ProductType = req.ProductType
		task.TaskFrom = task2.TaskFromPipeline
		// pipeline 触发,关闭质量门禁
		task.QualityGateSwitch = constants.IsFalse
		task.RepoInfo = &[]dto.RepoDto{{Name: req.RepoName}}
		task.Creator = constants.UserDevops
		task.Modifier = constants.UserDevops
		task.GmtCreated = times.Now()
		task.RepoInstanceId = req.InstanceId
	} else {
		logger.Logger.Infof("task已经存在,更新制品信息,taskName=%v", req.TaskName)
	}
	taskId := taskService.AddOrUpdateTask(ctx, &task, user)
	logger.Logger.Infof("task id is %d", taskId)
	execId := taskService.Exec(ctx, taskId, user, req.AppName)
	return map[string]interface{}{
		"reportUrl": fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, taskId, execId),
		"appName":   req.AppName,
	}
}

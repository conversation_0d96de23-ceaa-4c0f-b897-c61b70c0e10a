package artifact

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/xuri/excelize/v2"
)

type ReportService struct{}

var (
	// sheet_limit 条数限制
	sheet_limit      = 2000
	cve_excel_titlet = []string{
		"制品名称", "制品版本", "仓库名称", "制品路径", "扫描状态", "扫描结果", "开始时间", "结束时间", "漏洞编号",
		"漏洞所在库", "漏洞等级", "当前使用版本", "漏洞修复版本", "描述", "解决方案",
	}
	downloadDone int8 = 1
	downloadIng  int8 = 0
	downloadErr  int8 = 2
)

func (r ReportService) Detail(ctx *commoncontext.MantisContext, detailID int64, req commondto.QueryDTO) *gormx.PageResult {
	execSql := "select %v from ( " +
		"SELECT v ->> 'vulnerabilityID' AS vulnerabilityID, v ->> 'title' AS title, v ->> 'pkgName' as pkgName,v ->> 'pkgID' AS pkg_id," +
		"v ->> 'pkgPath' AS pkgPath,v ->> 'installedVersion' as installedVersion,v ->> 'fixedVersion' AS fixedVersion," +
		"v ->> 'status' AS status, v ->> 'description' as description,v ->> 'severity' AS severity,v ->> 'solution' AS solution " +
		"FROM (select result from neptune_artifact_scan_task_detail where id=? ) t0 ,jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v ) t1 %v"
	condition := "where 1=1 "
	severityList, ok := req.GetQueryParam("severity").([]string)
	if ok && len(severityList) > 0 {
		ss := ""
		for _, s := range severityList {
			ss += "'" + s + "',"
		}
		condition += "and t1.severity in (" + strings.Trim(ss, ",") + ")"
	}
	key := req.SearchKey
	if key != "" {
		condition += "and (t1.vulnerabilityID ilike '%" + key + "%' or t1.pkgName ilike '%" + key + "%')"
	}
	vuls := make([]dto.RProductVul, 0)
	var total int64 = 0
	gormx.RawX(ctx, fmt.Sprintf(execSql, "count(*)", condition), &total, detailID)
	if total == 0 {
		return &gormx.PageResult{Total: total, List: vuls, CurrentPage: req.Page, PageSize: req.PageSize}
	}
	offset := (req.Page - 1) * req.PageSize
	gormx.RawX(ctx, fmt.Sprintf(execSql, "*", condition+"order by t1.vulnerabilityID offset ? limit ?"),
		&vuls, detailID, offset, req.PageSize)
	return &gormx.PageResult{Total: total, List: vuls, CurrentPage: req.Page, PageSize: req.PageSize}
}

// ExecHisExport 按执行记录id到处报告
func (r ReportService) ExecHisExport(ctx *commoncontext.MantisContext, execId int64, scanType int, req dto.ProductExportDto, user commondto.UserInfo) {
	var scanReport artifact.ScanReport
	// 查询下载中的任务
	gormx.RawX(ctx, "select * from neptune_artifact_scan_report where foreign_id=? and report_type=?  and report_status=? ",
		&scanReport, execId, scanType, downloadIng)
	if scanReport.Id != 0 {
		logger.Logger.Panicf("存在下载中的报告,id=%v,请稍好!", scanReport.Id)
		return
	}
	scanReport.ReportType = scanType
	scanReport.ReportStatus = downloadIng
	scanReport.UName = user.AdAccount
	scanReport.ForeignId = execId
	bytes, _ := json.Marshal(&req)
	scanReport.Conditions = string(bytes)
	scanReport.SetTimeNowAndUser(user.AdAccount)
	gormx.InsertUpdateOneX(ctx, &scanReport)
	go asyncExecHisExport(ctx, execId, scanReport.Id, req.Cves)
}

func (r ReportService) List(ctx *commoncontext.MantisContext, id int64, req commondto.QueryDTO) *gormx.PageResult {
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanReport{}).Eq("is_deleted",
		common_constants.DeleteNo).Eq("foreign_id", id).Order("id", "desc")
	list := make([]artifact.ScanReport, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &list, gormx.PageRequest{Page: req.Page, PageSize: req.PageSize})
	if err != nil {
		logger.Logger.Panicf("查询报告列表失败!err=%v", err)
	}
	for i, v := range list {
		if v.Conditions != "" {
			var productExportDto dto.ProductExportDto
			json.Unmarshal([]byte(v.Conditions), &productExportDto)
			v.ConditionInfo = productExportDto
		}
		list[i] = v
	}
	return page
}

// 异步导出报告
func asyncExecHisExport(ctx *commoncontext.MantisContext, execId, reportId int64, cves []string) {
	// 捕获并处理异常,将下载报告开关重新打开
	defer func() {
		err := recover()
		if err != nil {
			logger.Logger.Error("导出报告异常", err)
			gormx.ExecX(ctx, "update neptune_artifact_scan_report set report_status=? where id=?", downloadErr, reportId)
		}
	}()
	logger.Logger.Info("开始导出报告")
	start := time.Now().UnixMilli()
	// 计算id分布
	cves = formatCves(cves)
	idArrs := calData(ctx, execId, cves)
	prefixField := "id,product_name,product_version,product_path,repo_name,status,scan_result,start_time ,end_time,"
	execSql := "select *, case when severity = 'Critical'  then '严重' when severity = 'High'  then '高危' when severity = 'Medium'  then '中危' when severity = 'Low'  then '低危' when severity = 'Node'  then '未知' end as severity from (" +
		"select %v v ->> 'vulnerabilityID' as  vulnerabilityID,v ->> 'pkgName' as pkgName," +
		" v ->> 'installedVersion' as installedVersion, v ->> 'fixedVersion' AS fixedVersion, v ->> 'severity' AS severity," +
		" v ->> 'solution' AS solution,v ->> 'description' AS description FROM (" +
		"select %v result from neptune_artifact_scan_task_detail where exec_id=? and id in (?) ) t, jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v " +
		") t1 where  1=1 %v order by id "
	if len(cves) == 0 {
		execSql = fmt.Sprintf(execSql, prefixField, prefixField, "")
	} else {
		ss := ""
		for _, s := range cves {
			ss += "'" + s + "',"
		}
		condition := "and t1.vulnerabilityID in (" + strings.Trim(ss, ",") + ")"
		execSql = fmt.Sprintf(execSql, prefixField, prefixField, condition)
	}
	filePath := fmt.Sprintf("/mantis/product-scan/%d", execId)
	filename := fmt.Sprintf("%d.xlsx", time.Now().UnixNano())
	err2 := createExcel(filePath, filename)
	if err2 != nil {
		logger.Logger.Panicf("创建excel文件失败!")
	}
	f, err := excelize.OpenFile(filePath + "/" + filename)
	if err != nil {
		logger.Logger.Panicf("打开excel文件失败!")
	}
	// 获取数据
	for i, ids := range idArrs {
		excels := make([]dto.VulExcel, 0)
		gormx.RawX(ctx, execSql, &excels, execId, ids)
		if len(excels) < 1 {
			break
		}
		saveData(f, excels, i)
	}
	s3store.UploadFile(filePath+"/"+filename, filePath+"/"+filename)
	objKey := strings.TrimPrefix(path.Join(filePath, filename), "/")
	objKeyBase64 := base64.StdEncoding.EncodeToString([]byte(objKey))
	reportDownloadUrl := configs.Config.Domain.Cube + "/magic/api/common/api/v1/biz/file/download?obj_key=" + objKeyBase64
	gormx.ExecX(ctx, "update neptune_artifact_scan_report set report_url=? ,report_status=?, gmt_modified=now()  where id=?",
		reportDownloadUrl, downloadDone, reportId)
	logger.Logger.Infof("导出报告结束,cost=%v ms,downloadUrl=%s", time.Now().UnixMilli()-start, reportDownloadUrl)
}

// createExcel 创建文件
func createExcel(filepath, filename string) error {
	stat, err := os.Stat(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			os.MkdirAll(filepath, 0o755)
		}
	} else {
		fmt.Println("filePath exist！", stat.IsDir())
	}
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	err = f.SaveAs(filepath + "/" + filename)
	return err
}

// 计算数据分布
func calData(ctx *commoncontext.MantisContext, execId int64, cves []string) [][]int64 {
	var idCounts []dto.IdCount
	sql := "select t1.id,count(t1.id) as count from ( SELECT id,v ->> 'vulnerabilityID' as  vulnerabilityID FROM (" +
		"select id,result from neptune_artifact_scan_task_detail where exec_id=?) t,jsonb_array_elements(result::jsonb -> 'vulnerabilities') AS v " +
		") t1 where  1=1 %v group by id "
	if len(cves) == 0 {
		gormx.RawX(ctx, fmt.Sprintf(sql, ""), &idCounts, execId)
	} else {
		condition := "and t1.vulnerabilityID in ?"
		gormx.RawX(ctx, fmt.Sprintf(sql, condition), &idCounts, execId, cves)
	}
	var idLimit [][]int64
	count := 0
	var ids []int64
	for _, v := range idCounts {
		if v.Count > sheet_limit {
			idLimit = append(idLimit, []int64{v.Id})
			continue
		}
		count += v.Count
		ids = append(ids, v.Id)
		if count > sheet_limit {
			idLimit = append(idLimit, ids)
			ids = []int64{}
			count = 0
		}
	}
	if len(ids) > 0 {
		idLimit = append(idLimit, ids)
	}
	return idLimit
}

// cve_excel_titlet = []string{"制品名称", "制品版本", "仓库名称", "扫描状态", "扫描结果", "开始时间", "结束时间", "漏洞编号",
// "漏洞所在库", "漏洞等级", "当前使用版本", "漏洞修复版本", "其他信息"}
func saveData(f *excelize.File, data []dto.VulExcel, i int) {
	sheetName := "sheet" + strconv.Itoa(i+1)
	_, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println("创建sheet 失败", err)
	}
	f.SetSheetRow(sheetName, "A1", &cve_excel_titlet)
	idCount := make(map[int64]int)
	for i, obj := range data {
		i2, exist := idCount[obj.Id]
		if exist {
			idCount[obj.Id] = i2 + 1
		} else {
			idCount[obj.Id] = 1
		}
		if obj.Status == task2.TaskStatusSucc { // 成功
			rs := artifact.QualityGateDto{}
			json.Unmarshal([]byte(obj.ScanResult), &rs)
			startTime := "-"
			if obj.StartTime != nil {
				startTime = obj.StartTime.ToString()
			}
			endTime := "-"
			if obj.EndTime != nil {
				endTime = obj.EndTime.ToString()
			}
			f.SetSheetRow(sheetName, "A"+strconv.Itoa(i+2), &[]string{
				obj.ProductName, obj.ProductVersion, obj.RepoName, obj.ProductPath,
				task2.StatusCodeToMemo(obj.Status), fmt.Sprintf("%d/%d/%d/%d/%d", rs.Critical, rs.High, rs.Medium, rs.Low, rs.None),
				startTime, endTime,
				obj.VulnerabilityID, obj.PkgName, obj.Severity, obj.InstalledVersion, obj.FixedVersion, obj.Description, obj.Solution,
			})
		} else { // 非成功状态
			f.SetSheetRow(sheetName, "A"+strconv.Itoa(i+2), &[]string{
				obj.ProductName, obj.ProductVersion, obj.RepoName,
				task2.StatusCodeToMemo(obj.Status), "-", "-", "-", "-", "-", "-", "-", "-", "-",
			})
		}
	}
	// 声明一个样式
	style, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		fmt.Println("创建sheet 失败", err)
	}
	cellPrePrefix := []string{"A", "B", "C", "D", "E", "F", "G"}
	from := 2
	for _, v := range idCount {
		for _, p := range cellPrePrefix {
			mergeAndSetCell(sheetName, p+strconv.Itoa(from), p+strconv.Itoa(from+v-1), style, f)
		}
		from = from + v
	}
	f.Save()
}

// 合并单元格并设置格式
func mergeAndSetCell(sheetName, topLeftCell, bottomRightCell string, style int, f *excelize.File) {
	err := f.MergeCell(sheetName, topLeftCell, bottomRightCell)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.SetCellStyle(sheetName, topLeftCell, bottomRightCell, style)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
}

// 格式化 formatCves
func formatCves(cves []string) []string {
	if cves == nil {
		return cves
	}
	cevFormat := make([]string, 0)
	for _, str := range cves {
		if strings.Trim(str, " ") != "" {
			cevFormat = append(cevFormat, str)
		}
	}
	return cevFormat
}

package artifact

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ScanPlanService struct{}

var taskService = ScanTaskService{}

// List 查看方案列表
func (s ScanPlanService) List(ctx *commoncontext.MantisContext, request *dto.QueryWithOrderDTO, user dto.UserInfo) *gormx.PageResult {
	logger.Logger.Infof("ScanPlanService.List...request: %+v ", request)
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanPlan{}).Eq("is_deleted",
		common_constants.DeleteNo).Eq("company_id", utils.IDString(user.CompanyID))
	if searchKey := request.SearchKey; searchKey != "" {
		paramBuilder.ILike("plan_name", "%"+searchKey+"%")
	}
	planType := request.GetQueryParam("planType")
	if planType != nil && reflect.TypeOf(planType).Kind() == reflect.String {
		paramBuilder.Eq("plan_type", planType)
	}
	if request.OrderField != "" {
		paramBuilder.Order(request.OrderField, request.OrderTypeFormat())
	} else {
		paramBuilder.Order("gmt_created", "desc")
	}
	list := make([]artifact.ScanPlan, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &list, gormx.PageRequest{Page: request.Page, PageSize: request.PageSize})
	if err != nil {
		logger.Logger.Panicf("查询方案列表失败!err=%v", err)
	}
	for i, dto := range list {
		if dto.WhiteCVE != "" {
			err := json.Unmarshal([]byte(dto.WhiteCVE), &dto.WhiteCVEInfos)
			if err != nil {
				logger.Logger.Panicf("漏洞白名单对象转化失败!err=%v", err)
			}
		}
		list[i] = dto
	}
	return page
}

// Info 查看方案详情
func (s ScanPlanService) Info(ctx *commoncontext.MantisContext, id int64) artifact.ScanPlan {
	logger.Logger.Infof("ScanPlanService.Info...id: %d", id)
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanPlan{}).Eq("id",
		id).Eq("is_deleted", common_constants.DeleteNo)
	var res artifact.ScanPlan
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
	if res.WhiteCVE != "" {
		err := json.Unmarshal([]byte(res.WhiteCVE), &res.WhiteCVEInfos)
		if err != nil {
			logger.Logger.Panicf("漏洞白名单对象转化失败!err=%v", err)
		}
	}
	return res
}

// AddOrUpdatePlan 新增 or 修改 方案
func (s ScanPlanService) AddOrUpdatePlan(ctx *commoncontext.MantisContext, plan *artifact.ScanPlan, user dto.UserInfo) int64 {
	logger.Logger.Infof("ScanPlanService.AddPlan...plan: %+v ", plan)
	checkExist(ctx, plan.PlanName, fmt.Sprintf("%v", user.CompanyID), plan.Id)
	plan.Modifier = user.AdAccount
	plan.GmtModified = times.Now()
	// 序列化漏洞白名单
	strByte, _ := json.Marshal(plan.WhiteCVEInfos)
	plan.WhiteCVE = string(strByte)
	if plan.Id == 0 {
		// 新增方案逻辑
		plan.CompanyID = fmt.Sprintf("%v", user.CompanyID)
		plan.Creator = user.AdAccount
		plan.GmtCreated = times.Now()
	} else {
		// 修改方案逻辑(目前不设置逻辑)
	}
	gormx.InsertUpdateOneX(ctx, plan)
	if plan.IsDefault == constants.IsDefault {
		s.Default(ctx, plan.Id, strconv.Itoa(constants.IsDefault), user)
	}
	logger.Logger.Infof("ScanPlanService.AddPlan...id: %d \n", plan.Id)
	return plan.Id
}

// 判断计划是否存在
func checkExist(ctx *commoncontext.MantisContext, planName string, companyId string, id int64) {
	plan := artifact.ScanPlan{PlanName: planName, CompanyID: companyId}
	plan.IsDeleted = common_constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &plan)
	if id == 0 && plan.Id > 0 {
		logger.Logger.Panicf("新增方案验证:名称[%v]重复,请更换名称！", planName)
	}
	if id > 0 && plan.Id > 0 && plan.Id != id {
		logger.Logger.Panicf("修改方案验证:名称[%v]重复,请更换名称！", planName)
	}
}

func (s ScanPlanService) Default(ctx *commoncontext.MantisContext, id int64, v string, user dto.UserInfo) {
	logger.Logger.Infof("ScanPlanService.Default...id: %d ", id)
	if v == "0" {
		// 取消默认
		gormx.ExecX(ctx, "update neptune_artifact_scan_plan set is_default=?,modifier=?,gmt_modified=? where id=? ",
			constants.IsFalse, user.AdAccount, times.Now(), id)
		return
	}
	companyId := user.CompanyID
	// 在事物中,返回任何失败都会回滚
	gormx.Transaction(ctx, func() error {
		gormx.ExecX(ctx, "update neptune_artifact_scan_plan set is_default=? where is_deleted='N' and is_default=? and company_id=?",
			constants.IsFalse, constants.IsDefault, companyId)
		gormx.ExecX(ctx, "update neptune_artifact_scan_plan set is_default=?,modifier=?,gmt_modified=? where id=? ",
			constants.IsDefault, user.AdAccount, times.Now(), id)
		return nil
	})
}

func (s ScanPlanService) DeletePlan(ctx *commoncontext.MantisContext, id int64, user dto.UserInfo) {
	logger.Logger.Infof("ScanPlanService.DeletePlan...id: %d ", id)
	total := checkBindTask(ctx, id)
	if total > 0 {
		logger.Logger.Panicf("方案[id=%d]下有任务正在执行或等待执行，请先中止任务或等待任务执行完成!", id)
	}
	plan := artifact.ScanPlan{}
	plan.IsDeleted = common_constants.DeleteYes
	plan.Id = id
	plan.GmtModified = times.Now()
	plan.Modifier = user.AdAccount
	gormx.UpdateOneByConditionX(ctx, &plan)
}

func checkBindTask(ctx *commoncontext.MantisContext, planId int64) int64 {
	execSql := "select count(*) from neptune_artifact_scan_task t1 " +
		"left join neptune_artifact_scan_task_exec_his t2 on t1.last_exec_id  =t2.id " +
		" where plan_id =? and t1.is_deleted ='N' and t2.task_status in (0,1) limit 1"
	var total int64 = 0
	gormx.RawX(ctx, execSql, &total, planId)
	return total
}

// whiteInfos 获取所有的漏洞白名单
func (s ScanPlanService) whiteInfos(ctx *commoncontext.MantisContext, planId int64) []artifact.WhiteCVEInfo {
	scanBuilder := gormx.NewParamBuilder().Model(&artifact.ScanPlan{}).Eq("id",
		planId).Eq("is_deleted", common_constants.DeleteNo)
	var plan artifact.ScanPlan
	gormx.SelectByParamBuilderX(ctx, scanBuilder, &plan)
	if plan.Id == 0 {
		logger.Logger.Warnf("planId: %d 不存在!或被删除!", planId)
		logger.Logger.Panicf("方案不存在,或被删除!")
	}
	cveArr := make([]artifact.WhiteCVEInfo, 0)
	if plan.WhiteCVE != "" {
		json.Unmarshal([]byte(plan.WhiteCVE), &cveArr)
	}
	return cveArr
}

// BindTask 查询绑定的task列表
func (s ScanPlanService) BindTask(ctx *commoncontext.MantisContext, req *dto.QueryDTO, planId int64) *gormx.PageResult {
	searchCount := "count(*)"
	searchField := "t1.id,t1.task_name as name,to_char(t2.trigger_time,'YYYY-MM-DD HH24:MI:SS') as time ," +
		"t1.last_exec_id as lastExecId,t2.exec_user as uname ,t2.task_status as status"
	execSql := "select %v from neptune_artifact_scan_task t1 " +
		"left join neptune_artifact_scan_task_exec_his t2 on t1.last_exec_id  =t2.id " +
		" where plan_id =? and t1.is_deleted ='N' and t2.task_status in (?) %v "
	status := req.GetQueryParam("status")
	ints, ok := status.([]string)
	statusList := make([]int, len(ints))
	if ok {
		for i, v := range ints {
			atoi, err := strconv.Atoi(v)
			if err != nil {
				logger.Logger.Panicf("参数格式错误，status 应为int类型数组!")
			}
			statusList[i] = atoi
		}
	} else {
		logger.Logger.Panicf("参数格式错误，status应为数组!")
	}
	tasks := make([]map[string]interface{}, 0)
	var total int64 = 0
	gormx.RawX(ctx, fmt.Sprintf(execSql, searchCount, ""), &total, planId, statusList)
	if total == 0 {
		return &gormx.PageResult{Total: total, List: tasks, CurrentPage: req.Page, PageSize: req.PageSize}
	}
	offset := (req.Page - 1) * req.PageSize
	gormx.RawX(ctx, fmt.Sprintf(execSql, searchField, "order by t1.id offset ? limit ?"), &tasks, planId, statusList, offset, req.PageSize)
	for _, v := range tasks {
		v["lastExecId"] = v["lastexecid"]
		delete(v, "lastexecid")
	}
	return &gormx.PageResult{Total: total, List: tasks, CurrentPage: req.Page, PageSize: req.PageSize}
}

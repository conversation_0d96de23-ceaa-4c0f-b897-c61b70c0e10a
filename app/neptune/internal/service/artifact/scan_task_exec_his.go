package artifact

import (
	"encoding/json"
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/utils"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTaskExecHisService struct{}

// InitTaskExecHis 初始化任务执行历史,返回执行id
func (s *ScanTaskExecHisService) InitTaskExecHis(ctx *commoncontext.MantisContext, taskId int64, repo string, user commondto.UserInfo, appName string) int64 {
	taskExecHis := artifact.ScanTaskExecHis{
		TaskId: taskId, TaskStatus: task.TaskStatusRun, Repo: repo,
		TriggerTime: times.Now(),
	}
	taskExecHis.AppName = appName
	taskExecHis.ExecUser = user.AdAccount
	taskExecHis.Creator = user.AdAccount
	taskExecHis.Modifier = user.AdAccount
	taskExecHis.GmtModified = times.Now()
	taskExecHis.GmtCreated = times.Now()
	gormx.InsertUpdateOneX(ctx, &taskExecHis)
	return taskExecHis.Id
}

// GetProductExecResult 获取制品扫描结果
func (s *ScanTaskExecHisService) GetProductExecResult(ctx *commoncontext.MantisContext, id int64, request commondto.QueryDTO) *gormx.PageResult {
	var scanTask artifact.ScanTask
	gormx.RawX(ctx, "select t2.quality_gate,t2.quality_gate_switch ,t2.id from neptune_artifact_scan_task_exec_his t1 left join neptune_artifact_scan_task t2 "+
		"on t1.task_id =t2.id where t1.id = ? limit 1", &scanTask, id)
	if scanTask.Id == 0 {
		return &gormx.PageResult{Total: 0, List: []artifact.ScanTaskDetail{}, PageSize: request.PageSize, CurrentPage: request.Page}
	}
	var qualityGate artifact.QualityGateDto
	if scanTask.QualityGateSwitch == constants.IsTrue && scanTask.QualityGate != "" {
		err := json.Unmarshal([]byte(scanTask.QualityGate), &qualityGate)
		if err != nil {
			logger.Logger.Panicf("质量阀反序列化失败！%v", err)
			return nil
		}
	}
	condation := "where exec_id = ?"
	if request.SearchKey != "" {
		condation += " and product_name ilike ?"
	}
	sqlTmp := "select id,repo_name ,product_name,product_version,product_path,product_download_url,scan_result,start_time ,end_time,status %v  from neptune_artifact_scan_task_detail %v"
	successField := ",scan_result::jsonb ->> 'critical' as critical,scan_result::jsonb ->> 'high' as high,scan_result::jsonb ->> 'medium' as medium ,scan_result::jsonb ->> 'none' as none ,scan_result::jsonb ->> 'low' as low"
	statusT, err := strconv.Atoi(fmt.Sprintf("%v", request.GetQueryParam("statusCode")))
	if err == nil {
		status := int8(statusT)
		if scanTask.QualityGateSwitch == constants.IsFalse && (status == task.ProductStatusGateNotPass || status == task.ProductStatusGatePass) {
			logger.Logger.Infof("质量阈值未开启，选择了质量门禁，直接返回")
			return &gormx.PageResult{}
		}
		switch status {
		case task.ProductStatusGatePass:
			condation += " and status=" + strconv.Itoa(int(task.ProductStatusSucc))
			sqlTmp = fmt.Sprintf(sqlTmp, successField, condation)
			sqlTmp = fmt.Sprintf("select id,repo_name ,product_name ,product_version,scan_result,start_time ,end_time,status from (%v) t0"+
				" where critical::int <= %d and high::int <= %d and medium::int <= %d and low::int <= %d and none::int <= %d  ", sqlTmp,
				qualityGate.Critical, qualityGate.High, qualityGate.Medium, qualityGate.Low, qualityGate.None)
			break
		case task.ProductStatusGateNotPass:
			condation += " and status=" + strconv.Itoa(int(task.ProductStatusSucc))
			sqlTmp = fmt.Sprintf(sqlTmp, successField, condation)
			sqlTmp = fmt.Sprintf("select id,repo_name ,product_name ,product_version,scan_result,start_time ,end_time,status  from (%v) t0"+
				" where critical::int > %d or high::int > %d or medium::int > %d or low::int > %d or none::int > %d  ", sqlTmp,
				qualityGate.Critical, qualityGate.High, qualityGate.Medium, qualityGate.Low, qualityGate.None)
			break
		default:
			condation += " and status=" + strconv.Itoa(int(status))
			sqlTmp = fmt.Sprintf(sqlTmp, "", condation)
		}
	} else {
		sqlTmp = fmt.Sprintf(sqlTmp, "", condation)
	}
	var total int64
	if request.SearchKey != "" {
		gormx.RawX(ctx, fmt.Sprintf("select count(*) from (%v) t1", sqlTmp), &total, strconv.Itoa(int(id)), "%"+request.SearchKey+"%")
	} else {
		gormx.RawX(ctx, fmt.Sprintf("select count(*) from (%v) t1", sqlTmp), &total, strconv.Itoa(int(id)))
	}
	if total == 0 {
		return &gormx.PageResult{}
	}
	offset := (request.Page - 1) * request.PageSize
	list := make([]artifact.ScanTaskDetail, 0)
	if request.SearchKey != "" {
		gormx.RawX(ctx, sqlTmp+" order by id desc limit ? offset ?", &list, strconv.Itoa(int(id)), "%"+request.SearchKey+"%", request.PageSize, offset)
	} else {
		gormx.RawX(ctx, sqlTmp+" order by id desc limit ? offset ?", &list, strconv.Itoa(int(id)), request.PageSize, offset)
	}
	for i, dto := range list {
		rv, rStatus := utils.CalGateQualityGate(scanTask.QualityGateSwitch, dto.Status, dto.ScanResult, qualityGate)
		dto.StatusCode = rStatus
		dto.ViewScanResultInfo = rv
		list[i] = dto
	}
	return &gormx.PageResult{Total: total, List: list, PageSize: request.PageSize, CurrentPage: request.Page}
}

// GetScanCount 获取扫描次数
func (s *ScanTaskExecHisService) GetScanCount(ctx *commoncontext.MantisContext, taskId int64) *int64 {
	var count int64
	gormx.RawX(ctx, "select count(*) from neptune_artifact_scan_task_exec_his where task_id=?", &count, taskId)
	return &count
}

// GetExecRecord 获取原生执行结果
func (s *ScanTaskExecHisService) GetExecRecord(ctx *commoncontext.MantisContext, execId int64) artifact.ScanTaskExecHis {
	var execRecord artifact.ScanTaskExecHis
	gormx.RawX(ctx, "select * from neptune_artifact_scan_task_exec_his where id=?", &execRecord, execId)
	return execRecord
}

// GetExecHistList 查询执行历史记录
func (s *ScanTaskExecHisService) GetExecHistList(ctx *commoncontext.MantisContext, taskId int64, request commondto.QueryDTO) *gormx.PageResult {
	paramBuilder := gormx.NewParamBuilder().Model(&artifact.ScanTaskExecHis{}).SelectMany("id", "repo", "trigger_time",
		"start_time", "end_time", "exec_user", "task_status").Eq("task_id", taskId).Order("trigger_time", "desc")
	list := make([]artifact.ScanTaskExecHis, 0)
	page, err := gormx.PageSelectByParamBuilder(ctx, paramBuilder, &list, gormx.PageRequest{Page: request.Page, PageSize: request.PageSize})
	if err != nil {
		logger.Logger.Panicf("分页查询任务执行历史失败!%v", err)
	}
	for i, dto := range list {
		dto.StatusMemo = task.StatusCodeToMemo(dto.TaskStatus)
		if dto.Repo != "" {
			json.Unmarshal([]byte(dto.Repo), &dto.RepoInfo)
		}
		list[i] = dto
	}
	return page
}

// CalExecResult 计算执行记录下的所有制品扫描结果的汇总
func (s *ScanTaskExecHisService) CalExecResult(ctx *commoncontext.MantisContext, execId int64) artifact.ScanResultInfo {
	execSQL := "select sum(critical::int) as critical,sum(high::int) as high ,sum(medium::int) as medium,sum(none::int) as none," +
		"sum(low::int) as low ,sum(vulnerabilityCount::int) as vulnerabilityCount from ( " +
		"select r ->> 'critical' as critical,r ->> 'high' as high,r ->> 'medium' as medium ,r ->> 'none' as none ,r ->> 'low' as low ," +
		"r ->> 'vulnerabilityCount' as vulnerabilityCount from (" +
		"select scan_result::jsonb as r from neptune_artifact_scan_task_detail where exec_id=? ) ) t1"
	var result artifact.ScanResultInfo
	gormx.RawX(ctx, execSQL, &result, execId)
	return result
}

// CheckFinality 终态校验
func (s *ScanTaskExecHisService) CheckFinality(ctx *commoncontext.MantisContext, execId int64) bool {
	var status int8
	gormx.RawX(ctx, "select task_status from neptune_artifact_scan_task_exec_his where id=?", &status, execId)
	if status == task.ProductStatusSucc || status == task.ProductStatusFail {
		return true
	}
	return false
}

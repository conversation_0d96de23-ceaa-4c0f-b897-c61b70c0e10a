package artifact

import (
	"encoding/json"
	"fmt"
	"testing"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/xuri/excelize/v2"
)

func TestScanTaskExecHisService_CalExecResult(t *testing.T) {
	s := &ScanTaskExecHisService{}
	result := s.CalExecResult(&commoncontext.MantisContext{}, 7)
	fmt.Println(result)
}

func Test_cveExcel(t *testing.T) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "s1"
	// 创建工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println("创建sheet 失败", err)
	}
	f.SetSheetRow(sheetName, "A1", &[]string{"制品名称", "制品版本", "仓库名称", "扫描结果", "漏洞编号", "漏洞所在库"})
	f.SetSheetRow(sheetName, "A2", &[]string{"p1", "1.0", "magic", "1/2/1/1/0", "CVE-2019-100", "fastjson"})
	f.SetSheetRow(sheetName, "A3", &[]string{"p1", "1.0", "magic", "", "CVE-2019-101", "fastjson"})
	f.SetSheetRow(sheetName, "A4", &[]string{"", "", "", "", "CVE-2019-102", "fastjson"})
	err = f.MergeCell(sheetName, "A2", "A4")
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.MergeCell(sheetName, "B2", "B4")
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.MergeCell(sheetName, "C2", "C4")
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.MergeCell(sheetName, "D2", "D4")
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	style, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.SetCellStyle(sheetName, "A2", "A4", style)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.SetCellStyle(sheetName, "B2", "B4", style)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.SetCellStyle(sheetName, "C2", "C4", style)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	err = f.SetCellStyle(sheetName, "D2", "D4", style)
	if err != nil {
		fmt.Println("合并单元格报错!")
	}
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	if err := f.SaveAs("C:\\Users\\<USER>\\Desktop\\tpre\\cves.xlsx"); err != nil {
		fmt.Println(err)
	}
}

func Test_invoke(t *testing.T) {
	// 初始化配置文件
	configs.Init()

	// 初始化日志
	logger.Init()

	// 初始化数据库
	gormx.Init()

	finality := execHisService.CheckFinality(&commoncontext.MantisContext{}, 178)
	if finality {
		fmt.Println("id end")
		return
	}
	DeckJobHelper(&commoncontext.MantisContext{}, 72, 178)
}

func Test_fmt(t *testing.T) {
	// 初始化配置文件
	configs.Init()
	// 初始化日志
	logger.Init()
	// 初始化数据库
	gormx.Init()
	drawerParam := make(map[string]interface{})
	drawerParam["severity"] = "%v"
	drawerParam["productId"] = 100
	drawerParamStr, _ := json.Marshal(drawerParam)
	fmt.Println(string(drawerParamStr))
	reportUrl := fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, 1, 2, fmt.Sprintf(string(drawerParamStr), "Nono"))
	fmt.Println(reportUrl)
	reportUrl1 := fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, 1, 2, fmt.Sprintf(string(drawerParamStr), "Low"))
	fmt.Println(reportUrl1)
	rs := PipeLineRs{ReportUrl: "HHH"}
	marshal, _ := json.Marshal(rs)
	fmt.Println(string(marshal))
}

func Test_fmt1(t *testing.T) {
	// 初始化配置文件
	configs.Init()
	// 初始化日志
	logger.Init()
	// 初始化数据库
	gormx.Init()

	FinallyStatusDeal(&commoncontext.MantisContext{}, 92866305077765)
}

package artifact

import (
	"encoding/json"
	"mime/multipart"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ResultService struct{}

func (r ResultService) DealResult(ctx *commoncontext.MantisContext, file multipart.File, fileSize int64, params map[string]interface{}) {
	bytes := make([]byte, fileSize)
	_, err := file.Read(bytes)
	if err != nil {
		logger.Logger.Panicf("read file error: %v", err)
	}
	switch params["type"].(string) {
	case enums.ScanTypeProductsScan:
		logger.Logger.Infof("开始处理类型是: %v 的扫描结果文件!", params["type"].(string))
		dealProductResult(ctx, params["id"].(int64), bytes)
		goroutine.Run(func() {
			FinallyStatusDeal(ctx, params["id"].(int64))
		})
	default:
		return
	}
}

// 处理制品扫描结果
func dealProductResult(ctx *commoncontext.MantisContext, id int64, bytes []byte) {
	var x dto.RProduct
	err := json.Unmarshal(bytes, &x)
	if err != nil {
		logger.Logger.Errorf("文件内容序列换为对象异常,打印文件内容!...%v", string(bytes))
		logger.Logger.Panicf("文件内容序列换为对象异常！unmarshal error: %v", err)
	}
	// 漏洞汇总结果
	var scanResultInfo artifact.ScanResultInfo
	scanResultInfo.VulnerabilityCount = int64(len(x.Vulnerabilities))
	for _, v := range x.Vulnerabilities {
		switch strings.ToLower(v.Severity) {
		case enums.GateCritical:
			scanResultInfo.Critical++
		case enums.GateHigh:
			scanResultInfo.High++
		case enums.GateMedium:
			scanResultInfo.Medium++
		case enums.GateLow:
			scanResultInfo.Low++
		case enums.GateNone:
			scanResultInfo.None++
		}
	}
	rInfoBytes, err := json.Marshal(scanResultInfo)
	if err != nil {
		logger.Logger.Errorf("文件内容序列换为对象异常,打印文件内容!...%v", string(bytes))
		logger.Logger.Panicf("文件内容序列换为对象异常！unmarshal error: %v", err)
	}
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_detail set scan_result=? , result=?, status = 2, gmt_modified=?, end_time=? where id=?",
		string(rInfoBytes), string(bytes), times.Now(), times.Now(), id)
}

func FinallyStatusDeal(ctx *commoncontext.MantisContext, detailId int64) {
	logger.Logger.Infof("FinallyStatusDeal...开始处理任务状态! 扫描任务详情id为: %v", detailId)
	// 获取执行id和taskFrom
	var res artifact.ScanTaskDetail
	gormx.RawX(ctx, "select exec_id,task_id from neptune_artifact_scan_task_detail where id=?", &res, &detailId)
	var taskFrom string
	gormx.RawX(ctx, "select task_from from neptune_artifact_scan_task where id=?", &taskFrom, res.TaskId)
	logger.Logger.Infof("FinallyStatusDeal...detailId: %v ,execId: %v ,taskFrom: %v", detailId, res.ExecId, taskFrom)
	ScanTaskDetailService{}.FinallyStatusAndResultUpdate(ctx, detailId, res.ExecId, taskFrom)
	logger.Logger.Infof("FinallyStatusDeal...end ")
}

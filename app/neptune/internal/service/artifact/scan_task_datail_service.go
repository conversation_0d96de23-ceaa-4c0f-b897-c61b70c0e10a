package artifact

import (
	"encoding/json"
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/pubsub"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"knative.dev/pkg/apis"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/remote"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
)

type ScanTaskDetailService struct{}

var (
	resultPath     = "/neptune/openapi/v1/result/%v/%v/form"
	ship           = remote.ShipInvoke{}
	execHisService = ScanTaskExecHisService{}
)

func (s ScanTaskDetailService) GenTaskDetail(ctx *commoncontext.MantisContext,
	task *artifact.ScanTask, execId int64, user commondto.UserInfo, cveArr []artifact.WhiteCVEInfo,
) {
	defer errCallBack(ctx, task, execId)
	ptos := make([]dto.ViewProductInfo, 0)
	switch task.ScanRange {
	// 定制化制品
	case task2.ScanRangeCusProduct:
		err := json.Unmarshal([]byte(task.ScanRangeInfo), &ptos)
		if err != nil {
			logger.Logger.Panicf("定制的制品转换失败！")
		}
		break
	// 最新制品
	case task2.ScanRangeLastProduct:
		if task.Repo != "" {
			repos := make([]dto.RepoDto, 0)
			err := json.Unmarshal([]byte(task.Repo), &repos)
			if err != nil {
				logger.Logger.Panicf("反序列化制品仓库失败！taskId=%v", task.Id)
				return
			}
			companyId := utils.IDString(user.CompanyID)
			repoService := repo.GetServiceById(ctx, companyId, task.RepoInstanceId)
			for _, repoDto := range repos {
				products := repoService.LatestAllProducts(ctx, repoDto.Name, repoDto.Id, repoDto.RepoType)
				logger.Logger.Infof("获取仓库=%v 下所有的最新制品,共计%v个", repoDto.Name, len(products))
				ptos = append(ptos, products...)
			}
		}
	case task2.ScanRangeRuleProduct:
		// todo 规则制品
	}
	logger.Logger.Infof("taskId=%d 下共计有%v个制品", task.Id, len(ptos))
	details, err := insertBatch(ctx, ptos, task, execId, user)
	if err != nil {
		logger.Logger.Panicf("插入数据失败!,err=%v", err)
		return
	}
	companyId := utils.IDString(user.CompanyID)
	instance, err := repo.GetRepoInstanceInfo(ctx, companyId, task.RepoInstanceId)
	if err != nil {
		logger.Logger.Errorf("从 deckjob 获取仓库实例信息失败,err=%v", err)
	}
	sendMsg(&details, *instance, execId, cveArr, task)
	// 更新制品总数
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set product_count=? where id=?", len(details), execId)
}

func sendMsg(details *[]artifact.ScanTaskDetail, repoInstance configs.RepoInstance, execId int64, cveArr []artifact.WhiteCVEInfo, task *artifact.ScanTask) {
	cves := make([]string, len(cveArr))
	for i, cve := range cveArr {
		cves[i] = cve.Code
	}
	for index, detail := range *details {
		payloadX := payload.NewArtifactScanTaskPayload(repoInstance, detail.Id, execId, task.ProductType, detail.RepoName, detail.ProductName,
			detail.ProductVersion, fmt.Sprintf(resultPath, enums.ScanTypeProductsScan, detail.Id), detail.ProductDownloadUrl).WithWhiteCVE(cves).WithTaskFrom(task.TaskFrom)
		if index == 0 {
			payloadX.IsFirst = constants.IsTrue
		}
		pubsub.SendToRedisQueue(*payloadX, task.TaskFrom)
	}
}

// insertBatch 使用数据库事物插入数据
func insertBatch(ctx *commoncontext.MantisContext,
	ptos []dto.ViewProductInfo, task *artifact.ScanTask, execId int64, user commondto.UserInfo,
) ([]artifact.ScanTaskDetail, error) {
	details := make([]artifact.ScanTaskDetail, len(ptos))
	for i, pto := range ptos {
		if pto.PathInfo == nil {
			pto.PathInfo = &dto.ProductPathDto{}
		}
		detail := artifact.ScanTaskDetail{
			TaskId: task.Id, ExecId: execId, ProductName: pto.ProductInfo.Name,
			ProductVersion: pto.VersionInfo.Version, RepoName: pto.RepoInfo.Name, Status: task2.ProductStatusWait, InstanceId: task.RepoInstanceId,
			ProductPath: pto.PathInfo.Path, ProductDownloadUrl: pto.PathInfo.DownloadUrl,
		}
		detail.Creator = user.AdAccount
		detail.Modifier = user.AdAccount
		detail.GmtCreated = times.Now()
		detail.GmtModified = times.Now()
		detail.Result = "{}"
		detail.ScanResult = "{}"
		details[i] = detail
	}
	// 使用事物提交数据
	gormx.Transaction(ctx, func() error {
		if len(details) < 30 {
			gormx.InsertBatchX(ctx, &details)
			return nil
		}
		dc := len(details)
		for i := 0; i < dc; i += 30 {
			var detailsSplit []artifact.ScanTaskDetail
			if (i + 30) > dc {
				detailsSplit = details[i : dc-1]
			} else {
				detailsSplit = details[i : i+30]
			}
			gormx.InsertBatchX(ctx, &detailsSplit)
		}
		return nil
	})
	return details, nil
}

// errCallBack 处理异常回调ship
func errCallBack(ctx *commoncontext.MantisContext, task *artifact.ScanTask, execId int64) {
	err := recover()
	if err == nil {
		return
	}
	logger.Logger.Errorf("生成任务详情失败,err=%v", err)
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set task_status=? ,err_msg=? where id=?", task2.TaskStatusFail, err, execId)
	if task.TaskFrom == task2.TaskFromPipeline {
	}
}

// ArtifactStatusAndResultLinkUpdate 状态及结果联动更新
func ArtifactStatusAndResultLinkUpdate(condition apis.Condition, labels map[string]string) error {
	var status int8
	switch condition.Status {
	case corev1.ConditionTrue:
		status = task.ProductStatusSucc
	case corev1.ConditionFalse:
		if condition.Reason == v1beta1.TaskRunSpecStatusCancelled {
			status = task.ProductStatusFail
		} else {
			status = task.ProductStatusFail
		}
	case corev1.ConditionUnknown:
		if condition.Reason == "Running" || condition.Reason == "Pending" {
			status = task.TaskStatusRun
		}
		if condition.Reason == v1beta1.TaskRunReasonTimedOut.String() {
			status = task.ProductStatusFail
		}
	default:
		return fmt.Errorf("syncTaskRunRecord invaild taskrun status")
	}
	isFirstStr, ok := labels[commonconstants.IsFirstLabel]
	if !ok {
		return fmt.Errorf("error in getting isFirst")
	}
	isFirst, err := strconv.Atoi(isFirstStr)
	if err != nil {
		return fmt.Errorf("error in getting isFirst")
	}
	execIdStr, ok := labels[commonconstants.ExecIdLabel]
	if !ok {
		return fmt.Errorf("error in getting execId")
	}
	execId, err := strconv.ParseInt(execIdStr, 10, 64)
	if err != nil {
		return err
	}
	detailIdStr, ok := labels[commonconstants.DetailIdLabel]
	if !ok {
		return fmt.Errorf("error in getting detailId")
	}
	detailId, err := strconv.ParseInt(detailIdStr, 10, 64)
	if err != nil {
		return err
	}
	ctx := &commoncontext.MantisContext{}
	finality := execHisService.CheckFinality(ctx, execId)
	if finality {
		return nil
	}
	// 处理detail
	if status == task2.ProductStatusRun {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_detail set status=?,start_time=? where id=? and status < ?", status, times.Now(), detailId, task2.ProductStatusSucc)
		if err != nil {
			return err
		}
	} else {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_detail set status=?,end_time=?,err_msg=? where id=? and status < ?", status, times.Now(), err, detailId, task2.ProductStatusSucc)
		if err != nil {
			return err
		}
	}
	// 处理执行历史
	// 1. 查看是否还有执行中的任务
	if isFirst == constants.IsTrue && status == task2.ProductStatusFail {
		_, err := gormx.Exec(ctx, "update neptune_artifact_scan_task_exec_his set start_time=?,task_status=? where id =? ", times.Now(), task2.TaskStatusFail, execId)
		return err
	}
	return err
}

// FinallyStatusAndResultUpdate 任务终态处理
func (s ScanTaskDetailService) FinallyStatusAndResultUpdate(ctx *commoncontext.MantisContext, detailId, execId int64, taskFrom string) {
	finality := execHisService.CheckFinality(ctx, execId)
	if finality {
		return
	}
	// 2. 判断是否还有执行中的任务，如果没有，则表示整个任务结果，需要汇总结构，保存到exechis中
	var eId int64
	gormx.RawX(ctx, "select id from neptune_artifact_scan_task_detail where exec_id =? and status in ? limit 1",
		&eId, execId, []int8{task2.ProductStatusWait, task2.ProductStatusRun})
	if eId > 0 {
		logger.Logger.Infof("execId=%d 下还有未完成的任务,暂不汇总结果！", execId)
		return
	}
	logger.Logger.Infof("execId=%d 的所有制品扫描完成,汇总结果！", execId)
	execStatus := task2.TaskStatusSucc
	var failId int64
	gormx.RawX(ctx, "select id from neptune_artifact_scan_task_detail where exec_id =? and status = ? limit 1",
		&failId, execId, task2.ProductStatusFail)
	if failId > 0 {
		execStatus = task2.TaskStatusFail
	}
	sql := "select sum(critical::int) as critical,sum(high::int) as high ,sum(medium::int) as medium,sum(none::int) as none," +
		"sum(low::int) as low ,sum(vulnerabilityCount::int) as vulnerabilityCount from (" +
		"select scan_result::jsonb ->> 'critical' as critical, scan_result::jsonb ->> 'high' as high,scan_result::jsonb ->> 'medium' as medium ," +
		"scan_result::jsonb ->> 'none' as none ,scan_result::jsonb ->> 'low' as low ,scan_result::jsonb ->> 'vulnerabilityCount' as vulnerabilityCount " +
		"from neptune_artifact_scan_task_detail where exec_id=? and status=? ) t "
	var allResult artifact.ScanResultInfo
	gormx.RawX(ctx, sql, &allResult, execId, task2.ProductStatusSucc)
	hTaskStatus := int8(execStatus)
	bytes, err2 := json.Marshal(allResult)
	err := ""
	if err2 != nil {
		logger.Logger.Errorf("汇总数据转换为json字符串异常！！！execId=%d,err=%v", execId, err2)
		bytes = []byte("{}")
		hTaskStatus = task2.TaskStatusFail
		err = "汇总数据转换为json字符串异常！！！"
	}
	gormx.ExecX(ctx, "update neptune_artifact_scan_task_exec_his set task_status=?,end_time=?,scan_result=?,err_msg=? where id=?",
		hTaskStatus, times.Now(), string(bytes), err, execId)
	if taskFrom == task2.TaskFromPipeline {
		goroutine.Run(func() {
			DeckJobHelper(ctx, execId, detailId)
		})
	}
}

func DeckJobHelper(ctx *commoncontext.MantisContext, execId, detailId int64) {
	list := make([]map[string]interface{}, 0)
	gormx.RawX(ctx, " select t1.task_status,t1.scan_result,t1.app_name,t1.task_id,t2.task_from ,t2.call_back_url from neptune_artifact_scan_task_exec_his t1"+
		" left join neptune_artifact_scan_task t2 on t1.task_id =t2.id where t1.id=? ", &list, execId)
	if len(list) == 0 {
		return
	}
	if list[0]["call_back_url"] == "" {
		logger.Logger.Errorf("pipeline task no call-back-url !!! execId={%v}", execId)
		return
	}
	// 1. 获取回调地址
	url := list[0]["call_back_url"]
	// 2. 获取结果
	result := list[0]["scan_result"]
	status, _ := strconv.ParseInt(fmt.Sprintf("%v", list[0]["task_status"]), 10, 32)
	body := make(map[string]interface{})
	body["appName"] = list[0]["app_name"]
	if status == int64(task2.TaskStatusSucc) {
		body["status"] = constants.IsTrue
		var data artifact.QualityGateDto
		err := json.Unmarshal([]byte(fmt.Sprintf("%v", result)), &data)
		if err != nil {
			logger.Logger.Panicf("转换数据异常,err=%v", err)
		}
		drawerParam := map[string]interface{}{
			"severity":  "%v",
			"productId": detailId,
		}
		drawerParamByte, _ := json.Marshal(drawerParam)
		drawerParamStr := string(drawerParamByte)
		body["low"] = PipeLineRs{
			Num:       data.Low,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId, fmt.Sprintf(drawerParamStr, "Low")),
		}
		body["high"] = PipeLineRs{
			Num:       data.High,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId, fmt.Sprintf(drawerParamStr, "High")),
		}
		body["medium"] = PipeLineRs{
			Num:       data.Medium,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId, fmt.Sprintf(drawerParamStr, "Medium")),
		}
		body["none"] = PipeLineRs{
			Num:       data.Medium,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId, fmt.Sprintf(drawerParamStr, "None")),
		}
		body["critical"] = PipeLineRs{
			Num:       data.Critical,
			ReportUrl: fmt.Sprintf(constants.ProductScanDoneReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId, fmt.Sprintf(drawerParamStr, "Critical")),
		}
	} else {
		body["status"] = constants.IsFalse
		body["low"] = PipeLineRs{
			ReportUrl: fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId),
		}
		body["high"] = PipeLineRs{
			ReportUrl: fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId),
		}
		body["medium"] = PipeLineRs{
			ReportUrl: fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId),
		}
		body["none"] = PipeLineRs{
			ReportUrl: fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId),
		}
		body["critical"] = PipeLineRs{
			ReportUrl: fmt.Sprintf(constants.ProductScanRunningReportPath, configs.Config.Domain.Cube, list[0]["task_id"], execId),
		}
	}
	logger.Logger.Infof("回调 deckjob ,请求url=%v,body=%+v", url, body)
	ship.ShipCallBack(fmt.Sprintf("%v", url), body)
}

type PipeLineRs struct {
	Num       int64  `json:"num"`
	ReportUrl string `json:"reportUrl"`
}

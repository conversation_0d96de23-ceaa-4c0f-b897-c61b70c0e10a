package enums

const (
	// 扫描类型
	ScanTypeQualityScan    = "quality"
	ScanTypeProductsScan   = "products"
	ScanTypeSecurityScan   = "security"
	ScanTypeJacocoCoverage = "jacoco"
	// 质量阀
	GateCritical = "critical"
	GateHigh     = "high"
	GateMedium   = "medium"
	GateLow      = "low"
	GateNone     = "none"
)

func StatusText(code string) string {
	switch code {
	case ScanTypeQualityScan:
		return "质量扫描"
	case ScanTypeProductsScan:
		return "制品扫描"
	case ScanTypeSecurityScan:
		return "安全扫描"
	case GateCritical:
		return "严重"
	case GateHigh:
		return "高危"
	case GateMedium:
		return "中危"
	case GateLow:
		return "低危"
	case GateNone:
		return "未定级"
	default:
		return ""
	}
}

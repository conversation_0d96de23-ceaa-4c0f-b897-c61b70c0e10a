package enums

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"

type RuleStatusEnum struct {
	Code bool
	Name string
}

var (
	On  = RuleStatusEnum{true, "启用"}
	Off = RuleStatusEnum{false, "未启用"}
)

var values = []RuleStatusEnum{On, Off}

func (RuleStatusEnum) GetRuleStatusList() []dto.CodeEnumDTO {
	res := make([]dto.CodeEnumDTO, 0)
	for _, value := range values {
		res = append(res, dto.CodeEnumDTO{
			Value: value.Code,
			Label: value.Name,
		})
	}
	return res
}

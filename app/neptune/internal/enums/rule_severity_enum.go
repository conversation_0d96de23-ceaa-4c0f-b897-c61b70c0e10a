package enums

import (
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type RuleSeverityEnum struct {
	Code string
	Name string
}

var (
	Blocker  = RuleSeverityEnum{"BLOCKER", "阻塞"}
	Critical = RuleSeverityEnum{"CRITICAL", "严重"}
	Major    = RuleSeverityEnum{"MAJOR", "主要"}
	Minor    = RuleSeverityEnum{"MINOR", "次要"}
	Info     = RuleSeverityEnum{"INFO", "提示"}
)

var severitySlice = []RuleSeverityEnum{
	Blocker, Critical, Major, Minor, Info,
}

func (RuleSeverityEnum) GetNameByCode(code string) string {
	for _, severity := range severitySlice {
		if severity.Code == code {
			return severity.Name
		}
	}
	logger.Logger.Panicf("严重级别获取失败")
	return ""
}

func (RuleSeverityEnum) GetSeverityList() []commondto.CodeEnumDTO {
	res := make([]commondto.CodeEnumDTO, 0)
	for _, enum := range severitySlice {
		res = append(res, commondto.CodeEnumDTO{
			Value: enum.Code,
			Label: enum.Name,
		})
	}
	return res
}

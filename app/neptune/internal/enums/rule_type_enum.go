package enums

import (
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type RuleTypeEnum struct {
	Code string
	Name string
}

var (
	CodeSmell       = RuleTypeEnum{"CODE_SMELL", "代码异味"}
	Bug             = RuleTypeEnum{"Bug", "Bug"}
	Vulnerability   = RuleTypeEnum{"VULNERABILITY", "漏洞"}
	SecurityHotSpot = RuleTypeEnum{"SECURITY_HOTSPOT", "安全热点"}
)

var ruleTypesSlice = []RuleTypeEnum{
	CodeSmell, Bug, Vulnerability, SecurityHotSpot,
}

func (RuleTypeEnum) GetNameByCode(code string) string {
	for _, ruleTypeEnum := range ruleTypesSlice {
		if ruleTypeEnum.Code == code {
			return ruleTypeEnum.Name
		}
	}
	logger.Logger.Panicf("严重级别获取失败")
	return ""
}

func (RuleTypeEnum) GetRuleTypeList() []commondto.CodeEnumDTO {
	res := make([]commondto.CodeEnumDTO, 0)
	for _, enum := range ruleTypesSlice {
		res = append(res, commondto.CodeEnumDTO{
			Value: enum.Code,
			Label: enum.Name,
		})
	}
	return res
}

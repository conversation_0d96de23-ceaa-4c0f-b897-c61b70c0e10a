package task

const (
	TaskStatusWait  = 0
	TaskStatusRun   = 1
	TaskStatusSucc  = 2
	TaskStatusFail  = 3
	TaskGatePass    = 4
	TaskGateNotPass = 5
)

const (
	// ProductStatusWait 制品状态-排队中
	ProductStatusWait int8 = iota
	// ProductStatusRun 制品状态-进行中
	ProductStatusRun
	// ProductStatusSucc 制品状态-成功
	ProductStatusSucc
	// ProductStatusFail 制品状态-失败
	ProductStatusFail
	// ProductStatusGatePass 制品状态-质量门禁通过
	ProductStatusGatePass
	// ProductStatusGatePass 制品状态-质量门禁不通过
	ProductStatusGateNotPass
)

const (
	// ScanRangeCusProduct 指定制品
	ScanRangeCusProduct = 1
	// ScanRangeLastProduct 最新制品
	ScanRangeLastProduct = 2
	// ScanRangeRuleProduct 规则扫描匹配的制品
	ScanRangeRuleProduct = 3
)

const (
	TaskFromWeb      = "web"
	TaskFromPipeline = "pipeline"
)

// ScanRangeCodeToMemo 扫描范围码转描述
func ScanRangeCodeToMemo(code int8) string {
	switch code {
	case ScanRangeCusProduct:
		return "定制化制品"
	case ScanRangeLastProduct:
		return "最新制品"
	case ScanRangeRuleProduct:
		return "按规则扫描"
	default:
		return "未知"
	}
}

// StatusCodeToMemo 任务状态码转描述
func StatusCodeToMemo(code int8) string {
	// 0-待执行 1-执行中 2-执行成功 3-执行失败 4-排队中
	switch code {
	case TaskStatusWait:
		return "待执行"
	case TaskStatusRun:
		return "进行中"
	case TaskStatusSucc:
		return "执行成功"
	case TaskStatusFail:
		return "执行失败"
	case TaskGatePass:
		return "质量门禁通过"
	case TaskGateNotPass:
		return "质量门禁未通过"
	default:
		return "未知"
	}
}

func CheckFinalStatusCode(status int8) bool {
	if status == TaskStatusSucc || status == TaskStatusFail {
		return true
	}
	return false
}

func CheckRunning(status int8) bool {
	if status == TaskStatusRun {
		return true
	}
	return false
}

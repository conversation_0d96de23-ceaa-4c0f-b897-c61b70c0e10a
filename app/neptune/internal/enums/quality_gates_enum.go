package enums

type QualityGatesEnum struct {
	Code int32
	Name string
}

var (
	QualityGateOff  = QualityGatesEnum{0, "未开启"}
	QualityGatePass = QualityGatesEnum{1, "通过"}
	QualityGateFail = QualityGatesEnum{2, "未通过"}
)

var qualityEnums = []QualityGatesEnum{
	QualityGateOff, QualityGatePass, QualityGateFail,
}

func (QualityGatesEnum) GetNameByCode(code int32) string {
	for _, enum := range qualityEnums {
		if enum.Code == code {
			return enum.Name
		}
	}
	return ""
}

package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
)

type ScanTestExecHisDTO struct {
	TaskName       string                       `json:"taskName"`
	ShipPid        int64                        `json:"shipPid"`
	CodeUrl        string                       `json:"codeUrl"`
	Language       string                       `json:"language"`
	AppName        string                       `json:"appName"`
	AppId          int64                        `json:"appId"`
	DepartmentName string                       `json:"departmentName"`
	CompanyId      int64                        `json:"companyId"`
	DepartmentId   int64                        `json:"departmentId"`
	ProjectName    string                       `json:"projectName"`
	Type           string                       `json:"type"`
	Owner          string                       `json:"owner"`
	ExecType       string                       `json:"execType"`
	ScanResultInfo map[string]ScanTestResultDto `json:"scanResultInfo"`
	quality.ScanTestExecHis
}

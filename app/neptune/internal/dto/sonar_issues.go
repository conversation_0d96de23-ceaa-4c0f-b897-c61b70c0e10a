package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanIssuesReqDTO struct {
	Severity     string `schema:"severity"`
	CreationDate string `schema:"creationDate"`
	TaskId       int64  `schema:"taskId"`
}

type SonarIssuesDTO struct {
	Key               string       `json:"key"`
	Rule              string       `json:"rule"`
	RuleDetail        SonarRuleDTO `json:"ruleDetail"`
	Severity          string       `json:"severity"`
	Component         string       `json:"component"`
	Project           string       `json:"project"`
	Line              int          `json:"line"`
	Status            string       `json:"status"`
	Message           string       `json:"message"`
	Author            string       `json:"author"`
	CreationDate      string       `json:"creationDate"`
	CreationTime      *times.Time  `json:"creationTime"`
	UpdateDate        string       `json:"updateDate"`
	UpdateTime        *times.Time  `json:"updateTime"`
	Type              string       `json:"type"`
	Scope             string       `json:"scope"`
	QuickFixAvailable bool         `json:"quickFixAvailable"`
	SonarUrl          string       `json:"sonarUrl"`
}

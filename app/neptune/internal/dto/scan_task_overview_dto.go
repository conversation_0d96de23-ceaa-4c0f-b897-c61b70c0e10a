package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTaskOverviewDTO struct {
	HisId                  int64                    `json:"hisId"`
	TaskId                 int64                    `json:"taskId"`
	Mode                   string                   `json:"mode"`
	From                   string                   `json:"from"`
	Status                 int8                     `json:"status"`
	StatusMsg              string                   `json:"statusMsg"`
	BlockerViolations      int32                    `json:"blockerViolations"`
	CriticalViolations     int32                    `json:"criticalViolations"`
	MajorViolations        int32                    `json:"majorViolations"`
	MinorViolations        int32                    `json:"minorViolations"`
	Complexity             int32                    `json:"complexity"`
	DuplicatedLinesDensity float64                  `json:"duplicatedLinesDensity"`
	QualityGates           int32                    `json:"qualityGates"`
	QualityGatesColumn     string                   `json:"qualityGatesColumn"`
	QualityGatesParams     string                   `json:"qualityGatesParams"`
	GmtCreated             *times.Time              `json:"gmtCreated"`
	GmtModified            *times.Time              `json:"gmtModified"`
	Creator                string                   `json:"creator"`
	Operator               string                   `json:"operator"`
	CodeDistribution       string                   `json:"codeDistribution"`
	CodeTrend              string                   `json:"codeTrend"`
	TakeTime               string                   `json:"takeTime"`
	OssDownload            string                   `json:"ossDownload"`
	PdfUrl                 string                   `json:"pdfUrl"`
	SonarUrl               string                   `json:"sonarUrl"`
	LastScanTestExecTime   *times.Time              `json:"lastScanTestExecTime"`
	TaskConfig             quality.ScanQualityGates `json:"taskConfig"`
}

package dto

// ScanTestInfoDTO ship调用dto
type ScanTestInfoDTO struct {
	Branch          string `json:"branch"`
	Source          string `json:"source"`
	CompareCommitId string `json:"compareCommitId"`
	BasicCommitId   string `json:"basicCommitId"`
	JdkVersion      string `json:"jdkVersion"`
	BuildCommand    string `json:"buildCommand"`
	Language        string `json:"language"`
	Mode            string `json:"mode"` // total 全量 increment 增量
	DirPath         string `json:"dirPath"`
	Pid             int64  `json:"pid"`
	CallBackAddr    string `json:"callBackAddr"`
	AppId           string `json:"appId"`
	AppName         string `json:"appName"`
	Stage           string `json:"stage"`
}

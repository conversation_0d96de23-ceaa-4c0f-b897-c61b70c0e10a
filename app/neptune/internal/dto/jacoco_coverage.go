package dto

type PipelineCollectCoverageReqDTO struct {
	Pid             int64  `json:"pid"`
	AppName         string `json:"appName"`
	AppId           string `json:"appId"`
	Stage           string `json:"stage"`
	Env             string `json:"env"`
	Mode            string `json:"mode"`
	Ip              string `json:"ip"`
	Ports           string `json:"ports"`
	MountedJarPath  string `json:"mountedJarPath"`
	CallBackAddr    string `json:"callBackAddr"`
	BaseCommitId    string `json:"baseCommitId"`
	CompareCommitId string `json:"compareCommitId"`
	Branch          string `json:"branch"`
}

type AptResetAndRegisterCoverageReqDTO struct {
	Bid      int64  `json:"bid"`
	AppId    string `json:"appId"`
	Stage    string `json:"stage"`
	Env      string `json:"env"`
	Ip       string `json:"ip"`
	Ports    int64  `json:"ports"`
	Platform string `json:"platform"`
}

type AptExecCoverageReqDTO struct {
	Bid   int64  `json:"bid"`
	AppId string `json:"appId"`
}

type CallBackReqDTO struct {
	Id                    int64   `json:"id"`
	InstructionCovered    float64 `json:"instructionCovered"`
	InstructionTotal      float64 `json:"instructionTotal"`
	InstructionPercentage float64 `json:"instructionPercentage"`
	LineCovered           float64 `json:"lineCovered"`
	LineTotal             float64 `json:"lineTotal"`
	LinePercentage        float64 `json:"linePercentage"`
	ClassCovered          float64 `json:"classCovered"`
	ClassTotal            float64 `json:"classTotal"`
	ClassPercentage       float64 `json:"classPercentage"`
	BranchCovered         float64 `json:"branchCovered"`
	BranchTotal           float64 `json:"branchTotal"`
	BranchPercentage      float64 `json:"branchPercentage"`
	ReportUrl             string  `json:"reportUrl"`
	OldExecPath           string  `json:"oldExecPath"`
}

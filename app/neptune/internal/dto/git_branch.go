package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type GitBranchDTO struct {
	Name     string `json:"name"`
	Commit   Commit `json:"commit"`
	CommitId string `json:"commitId"`
}

type Commit struct {
	Id          string      `json:"id"`
	CreatedAt   *times.Time `json:"createdAt"`
	Title       string      `json:"title"`
	AuthorName  string      `json:"authorName"`
	AuthorEmail string      `json:"authorEmail"`
}

package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// RProduct 制品漏洞报告
type RProduct struct {
	Version         string        `json:"version"`
	Vulnerabilities []RProductVul `json:"vulnerabilities"`
}

// RProductVul 制品漏洞信息
type RProductVul struct {
	VulnerabilityID  string `gorm:"column:vulnerabilityid" json:"vulnerabilityID" `
	Title            string `json:"title" gorm:"title"`
	PkgName          string `gorm:"column:pkgname" json:"pkgName"`
	PkgID            string `gorm:"column:pkgid" json:"pkgID"`
	PkgPath          string `gorm:"column:pkgpath" json:"pkgPath"`
	InstalledVersion string `gorm:"column:installedversion"  json:"installedVersion"`
	FixedVersion     string `gorm:"column:fixedversion" json:"fixedVersion"`
	Status           string `json:"status" gorm:"column:status" `
	Description      string `json:"description" gorm:"column:description"`
	Severity         string `json:"severity" gorm:"column:severity"`
	Solution         string `json:"solution" gorm:"column:solution"`
}

// VulExcel excel 行对象
type VulExcel struct {
	Id             int64       `gorm:"id"`
	RepoName       string      `gorm:"column:repo_name;type:varchar;size:1024;comment:仓库名称" json:"repoName"`                       // 仓库名称
	ProductName    string      `gorm:"column:product_name;type:varchar;size:1024;comment:制品名称" json:"productName"`                 // 制品名称
	ProductVersion string      `gorm:"column:product_version;type:varchar;size:1024;comment:制品版本" json:"productVersion"`           // 制品版本
	Status         int8        `gorm:"column:status;type:int;size:8;comment:任务状态  0-待执行 1-进行中 2-执行成功 3-执行失败 4-排队中 " json:"status"` // 任务状态
	ScanResult     string      `gorm:"column:scan_result;type:varchar;size:1024;comment:汇总结果" json:"scanResult"`                   //  scanResultInfo
	StartTime      *times.Time `gorm:"column:start_time;type:timestamp;size:0;comment:执行时间" json:"startTime"`                      // 开启时间
	EndTime        *times.Time `gorm:"column:end_time;type:timestamp;size:0;comment:执行时间" json:"endTime"`                          // 结束时间
	// 漏洞信息
	VulnerabilityID  string `gorm:"column:vulnerabilityid" json:"vulnerabilityID" `
	PkgName          string `gorm:"column:pkgname" json:"pkgName"`
	Severity         string `gorm:"column:severity" json:"severity"`
	InstalledVersion string `gorm:"column:installedversion"  json:"installedVersion"`
	FixedVersion     string `gorm:"column:fixedversion" json:"fixedVersion"`
	Solution         string `gorm:"column:solution" json:"solution"`
	Description      string `json:"description" gorm:"column:description"`
	ProductPath      string `json:"productPath" gorm:"column:product_path"`
}

// IdCount id 统计
type IdCount struct {
	Id    int64 `gorm:"id"`
	Count int   `gorm:"count"`
}

// ProductExportDto 制品导出对象
type ProductExportDto struct {
	Cves       []string `json:"cves"`       // 漏洞列表
	ExportType int      `json:"exportType"` // 导出类型
}

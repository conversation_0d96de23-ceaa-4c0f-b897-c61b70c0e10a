package dto

// PipelineProductScanRequestDto 流水线制品扫描请求dto
type PipelineProductScanRequestDto struct {
	InstanceName   string `json:"instanceName" form:"instanceName" validate:"required"`           // 实例名称,artifact(一般制品仓库),cmdb上的仓库名称(harbor)
	ProductType    string `json:"productType" form:"productType" validate:"oneof=Generic Docker"` // 制品类型  Generic|Docker
	RepoName       string `json:"repoName" form:"repoName" validate:"required"`                   // 仓库名称  or projectName(harbor)
	ProductName    string `json:"productName" form:"productName" validate:"required"`             // 制品名称  or repoName(harbor)
	ProductVersion string `json:"productVersion" form:"productVersion" validate:"required"`       // 制品版本  or tagName(harbor)
	TaskName       string `json:"taskName" form:"taskName" validate:"required"`                   // 任务名称 同一个 发布单+应用名+执行节点为一个任务，没有就创建，有就直接执行
	CompanyID      string `json:"companyID" form:"companyID" validate:"required" `                // 公司id
	CallBackUrl    string `json:"callBackUrl" form:"callBackUrl" validate:"required"`             // 回调地址
	AppName        string `json:"appName" form:"appName" validate:"required"`                     // 应用名称
	// v2 新增四个字段
	ProductDownloadUrl string `json:"productDownloadUrl" form:"productDownloadUrl"` // 制品下载地址
	ProductPath        string `json:"productPath" form:"productPath"`               // 制品path
	InstanceType       string `json:"instanceType" form:"instanceType"`             // 实例类型 nexus
	InstanceId         string `json:"instanceId" form:"instanceId"`                 // 实例id
}

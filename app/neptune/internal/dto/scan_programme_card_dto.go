package dto

type ScanProgrammeCardDTO struct {
	Id          int64                         `json:"id"`
	Name        string                        `json:"name"`
	ProfileName string                        `json:"profileName"`
	Describe    string                        `json:"describe"`
	Profiles    []ScanProgrammeCardProfileDTO `json:"profiles"`
	IsDefault   int32                         `json:"isDefault"`
}

type ScanProgrammeCardProfileDTO struct {
	Templates   []ScanProgrammeCardTemplateDTO `json:"templates"`
	ProfileName string                         `json:"profileName"`
	ProfileKey  string                         `json:"profileKey"`
	IsActive    bool                           `json:"isActive"`
	Languages   []string                       `json:"languages"`
	ProfileId   int64                          `json:"profileId"`
}

type ScanProgrammeCardTemplateDTO struct {
	TemplateId   int64  `json:"templateId"`
	TemplateName string `json:"templateName"`
}

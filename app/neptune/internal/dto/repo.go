package dto

type RepoDto struct {
	Name     string `json:"name"`     // 仓库名称
	Id       string `json:"id"`       // 仓库id
	RepoType string `json:"repoType"` // 仓库类型
}

type MinProductDto struct {
	Name string `json:"name"` // 制品名称
	Id   string `json:"id"`   // 制品id
	// V2 新增制品path 和 下载url
	PageToken string `json:"pageToken"`
}

type ProductVersionDto struct {
	Version string `json:"version"` // 版本名称
	Id      string `json:"id"`      // 版本名称
}

type ViewProductInfo struct {
	RepoInfo    *RepoDto           `json:"repoInfo"`
	ProductInfo *MinProductDto     `json:"productInfo"`
	VersionInfo *ProductVersionDto `json:"versionInfo"`
	PathInfo    *ProductPathDto    `json:"pathInfo"`
}

type ProductPathDto struct {
	Path        string `json:"path"`        // 制品路径
	DownloadUrl string `json:"downloadUrl"` // 制品路径
}

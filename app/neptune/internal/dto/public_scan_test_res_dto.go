package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type PublicScanTestResDTO struct {
	HisId                     int64       `json:"hisId"`
	DepartmentName            string      `json:"departmentName"`
	AppName                   string      `json:"appName"`
	Mode                      string      `json:"mode"`
	ShipPid                   int64       `json:"shipPid"`
	Branch                    string      `json:"branch"`
	ExecType                  string      `json:"execType"`
	Language                  string      `json:"language"`
	SonarUrl                  string      `json:"sonarUrl"`
	Status                    string      `json:"status"`
	Ncloc                     int32       `json:"ncloc"`
	LinesToCover              int32       `json:"linesToCover"`
	BlockerViolations         int32       `json:"blockerViolations"`
	CriticalViolations        int32       `json:"criticalViolations"`
	MajorViolations           int32       `json:"majorViolations"`
	MinorViolations           int32       `json:"minorViolations"`
	LineCoverage              float64     `json:"lineCoverage"`
	TestSuccessDensity        float64     `json:"testSuccessDensity"`
	CommentLinesDensity       float64     `json:"commentLinesDensity"`
	DuplicatedLinesDensity    float64     `json:"duplicatedLinesDensity"`
	BranchCoverage            float64     `json:"branchCoverage"`
	Vulnerabilities           int32       `json:"vulnerabilities"`
	NewLinesToCover           int32       `json:"newLinesToCover"`
	NewLines                  int32       `json:"newLines"`
	NewBlockerViolations      int32       `json:"newBlockerViolations"`
	NewCriticalViolations     int32       `json:"newCriticalViolations"`
	NewMajorViolations        int32       `json:"newMajorViolations"`
	NewMinorViolations        int32       `json:"newMinorViolations"`
	NewCoverage               float64     `json:"newCoverage"`
	NewTestSuccessDensity     float64     `json:"newTestSuccessDensity"`
	NewCommentLinesDensity    float64     `json:"newCommentLinesDensity"`
	NewDuplicatedLinesDensity float64     `json:"newDuplicatedLinesDensity"`
	TotalWarnings             int32       `json:"totalWarnings"`
	NewWarnings               int32       `json:"newWarnings"`
	FixedWarnings             int32       `json:"fixedWarnings"`
	OssDownloadUrl            string      `json:"ossDownloadUrl"`
	Stage                     string      `json:"stage"`
	ReportUrl                 string      `json:"reportUrl"`
	GmtModified               *times.Time `json:"gmtModified"`
	CompanyName               string      `json:"companyName"`
	CompanyCode               string      `json:"companyCode"`
}

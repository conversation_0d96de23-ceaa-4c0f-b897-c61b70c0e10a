package dto

// ship传递来的扫描或单测信息

type ScanTestInfo struct {
	Branch          string `json:"branch"`
	Source          string `json:"source"`
	CompareCommitId string `json:"compareCommitId"`
	BasicCommitId   string `json:"basicCommitId"`
	JdkVersion      string `json:"jdkVersion"`
	BuildCommand    string `json:"buildCommand"`
	Language        string `json:"language"`
	Mode            string `json:"mode"`
	DirPath         string `json:"dirPath"`
	Pid             int64  `json:"pid"`
	CallBackAddr    string `json:"callBackAddr"`
	AppId           int64  `json:"appId"`
	Stage           string `json:"stage"`
}

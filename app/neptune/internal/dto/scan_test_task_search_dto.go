package dto

import commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"

type ScanTestTaskSearchDTO struct {
	TaskId  int64    `schema:"taskId"`
	Search  string   `schema:"search"`
	AppIds  []string `schema:"appIds"`
	JobType string   `schema:"jobType"`
	From    string   `schema:"from"`
	HubUrl  string   `schema:"hubUrl"`
	Branch  string   `schema:"branch"`
	commondto.AddonTimeDTO
	CompanyId string
}

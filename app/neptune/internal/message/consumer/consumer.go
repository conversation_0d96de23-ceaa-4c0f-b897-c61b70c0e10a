package consumer

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/scanner/jacoco"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/scanner/sonar"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/scanner/trivy"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/factory"
)

func Init() {
	// 处理制品扫描类型的数据
	factory.Sub(HandleArtifactScanTask)
	// 处理质量扫描类型的任务
	factory.Sub(HandleQualityScanTask)
	// 处理功能覆盖率任务
	factory.Sub(HandleJacocoScanTask)
}

// HandleArtifactScanTask 执行制品扫描任务
func HandleArtifactScanTask(ctx context.Context, producerTask payload.ArtifactScanTaskPayload) error {
	logger.Logger.Infof("执行制品扫描任务: %+v", producerTask)
	var status int8
	gormx.RawX(&commoncontext.MantisContext{}, "select status from neptune_artifact_scan_task_detail where id=? ", &status, producerTask.DetailId)
	if status == task.TaskStatusFail {
		logger.Logger.Infof("任务已经取消，不执行！ detailId=%d,execId=%v", producerTask.DetailId, producerTask.ExecId)
		return nil
	}
	err := trivy.RunTrivyScannerTask(&commoncontext.MantisContext{Context: ctx}, producerTask)
	if err != nil {
		logger.Logger.Errorf("run trivy scanner task error: %v, payload: %+v", err, producerTask)
		return err
	}
	return nil
}

// HandleQualityScanTask 执行代码扫描任务
func HandleQualityScanTask(ctx context.Context, qualityTask payload.QualityScanTaskPayload) error {
	// 校验git地址
	qualityTask.CodeParam.CodeUrl = help.GitHelper{}.ValidateGitUrl(qualityTask.CodeParam.CodeUrl)
	logger.Logger.Infof("执行扫描或单测任务：%v", qualityTask)
	his := quality.ScanTestExecHis{}
	his.Id = qualityTask.BizParam.ExecHisId
	his.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(&commoncontext.MantisContext{}, &his)
	if his.Status == task.TaskStatusFail {
		logger.Logger.Infof("任务已经取消，不执行！ execHisId=%v", his.Id)
		return nil
	}
	err := sonar.RunSonarScannerTask(&commoncontext.MantisContext{Context: ctx}, qualityTask)
	if err != nil {
		logger.Logger.Errorf("run sonar scanner task error: %v, payload: %+v", err, qualityTask)
		return err
	}
	return nil
}

// HandleJacocoScanTask 执行代码覆盖率任务
func HandleJacocoScanTask(ctx context.Context, payload payload.JacocoCoveragePayload) error {
	// 校验git地址
	payload.Git.GitUrl = help.GitHelper{}.ValidateGitUrl(payload.Git.GitUrl)
	logger.Logger.Infof("执行代码覆盖率任务: %v", payload)
	his := quality.JacocoCoverageExecHis{}
	his.Id = payload.HisId
	his.IsDeleted = commonconstants.DeleteNo
	if err := gormx.SelectOneByCondition(&commoncontext.MantisContext{}, &his); err != nil {
		return err
	}
	if his.Status == task.TaskStatusFail {
		logger.Logger.Infof("任务已经取消，不执行! hisId=%d", his.Id)
		return nil
	}
	if err := jacoco.RunJacocoCoverageTask(&commoncontext.MantisContext{Context: ctx}, payload); err != nil {
		logger.Logger.Errorf("run jacoco coverage task error: %s, payload: %+v", err.Error(), payload)
		return err
	}
	return nil
}

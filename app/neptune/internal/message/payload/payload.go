package payload

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
)

// ArtifactScanTaskPayload 制品扫描任务载体
type ArtifactScanTaskPayload struct {
	InstanceInfo    configs.RepoInstance `json:"InstanceInfo"`    // 实例信息
	DetailId        int64                `json:"detailId"`        // 任务id
	ExecId          int64                `json:"execId"`          // 执行记录id
	IsFirst         int8                 `json:"isFirst"`         // 是否为本批次任务的第一条 0-否 1-是
	ProductType     string               `json:"productType" `    // 制品类型  Generic|Docker
	RepoName        string               `json:"repoName" `       // 仓库名称  or projectName(harbor)
	ProductName     string               `json:"productName" `    // 制品名称  or repoName(harbor)
	ProductVersion  string               `json:"productVersion" ` // 制品版本  or tagName(harbor)
	UploadResultUrl string               `json:"uploadResultUrl"` // 上传结果文件的url
	WhiteCVE        []string             `json:"whiteCVE"`        // 漏洞白名单
	TaskFrom        string               `json:"taskFrom"`        // 任务来源 web/pipeline
	DownloadUrl     string               `json:"downloadUrl"`     // 制品下载地址
}

// WithWhiteCVE 设置漏洞白名单
func (p *ArtifactScanTaskPayload) WithWhiteCVE(whiteCVE []string) *ArtifactScanTaskPayload {
	p.WhiteCVE = whiteCVE
	return p
}

// WithTaskFrom 设置来源
func (p *ArtifactScanTaskPayload) WithTaskFrom(taskFrom string) *ArtifactScanTaskPayload {
	p.TaskFrom = taskFrom
	return p
}

func (ArtifactScanTaskPayload) Kind() string {
	return "magic-neptune-products"
}

// NewArtifactScanTaskPayload 创建制品扫描任务载体
func NewArtifactScanTaskPayload(instanceInfo configs.RepoInstance, detailId, execId int64, productType, repoName, productName, productVersion, uploadResultUrl string, downloadUrl string) *ArtifactScanTaskPayload {
	dto := &ArtifactScanTaskPayload{
		InstanceInfo:    instanceInfo,
		DetailId:        detailId,
		ExecId:          execId,
		ProductType:     productType,
		RepoName:        repoName,
		ProductName:     productName,
		ProductVersion:  productVersion,
		UploadResultUrl: uploadResultUrl,
		DownloadUrl:     downloadUrl,
	}
	return dto
}

// QualityScanTaskPayload 质量扫描(sonar-scanner)任务载体
type QualityScanTaskPayload struct {
	BizParam   QualityScanBizParam   `json:"bizParam"`
	CodeParam  QualityScanCodeParam  `json:"codeParam"`
	SonarParam QualityScanSonarParam `json:"sonarParam"`
	MvnParam   QualityScanMvnParam   `json:"mvnParam"`
}

func (QualityScanTaskPayload) Kind() string {
	return "magic-neptune-quality"
}

func (q *QualityScanTaskPayload) WithBiz(biz QualityScanBizParam) *QualityScanTaskPayload {
	q.BizParam = biz
	return q
}

func (q *QualityScanTaskPayload) WithCode(code QualityScanCodeParam) *QualityScanTaskPayload {
	q.CodeParam = code
	return q
}

func (q *QualityScanTaskPayload) WithSonar(sonar QualityScanSonarParam) *QualityScanTaskPayload {
	q.SonarParam = sonar
	return q
}

func (q *QualityScanTaskPayload) WithMvn(mvn QualityScanMvnParam) *QualityScanTaskPayload {
	q.MvnParam = mvn
	return q
}

type QualityScanBizParam struct {
	// 业务参数
	ExecHisId     int64  `json:"execHisId"`      // 任务记录id
	CallBack      string `json:"callBack"`       // pod内部直接回调主服务地址
	Type          string `json:"type" `          // 任务类型  unit(单测)/scan(扫描)
	Mode          string `json:"mode" `          // 模式: increment(增量)/total(全量)
	Language      string `json:"language" `      // 语言: java
	JavaVersion   string `json:"javaVersion" `   // java版本: Java8/Java11/Java17
	ExclusionPath string `json:"exclusionPath" ` // 排除的路径: src/test/java/*
	InclusionPath string `json:"inclusionPath"`  // 包含的路径: src/test/java/*
	AppType       string `json:"appType"`        // 前端/后端
	ModulePath    string `json:"modulePath"`     // 模块路径
}

type QualityScanCodeParam struct {
	// 代码相关参数
	CodeUrl         string `json:"codeUrl" `         // 仓库地址: http://xx.xx.git
	GitUser         string `json:"gitUser" `         // 仓库用户名: root
	GitToken        string `json:"gitToken" `        // 仓库密码: 123456
	Branch          string `json:"branch" `          // 分支: master
	BaseCommitId    string `json:"baseCommitId" `    // 基准commitId
	CompareCommitId string `json:"compareCommitId" ` // 比较commitId
}

type QualityScanSonarParam struct {
	// sonar 参数
	SonarUrl      string `json:"sonarUrl" `      // sonar 地址
	SonarToken    string `json:"sonarToken" `    // sonar token
	SonarPdf      bool   `json:"sonarPdf" `      // 是否生成pdf
	SonarProfile  string `json:"sonarProfile" `  // 规则集名称
	SonarCallback string `json:"sonarCallback" ` // 回调地址
}

type QualityScanMvnParam struct {
	// mvn 参数
	MvnRepo string `json:"mvnRepo" ` // mvn私服仓库地址
	MvnUser string `json:"mvnUser" ` // mvn用户,可为空
	MvnPw   string `json:"mvnPw" `   // mvn密码,可为空
}

type JacocoCoveragePayload struct {
	Mode     string `json:"mode"`
	AppName  string `json:"appName"`
	Ip       string `json:"ip"`
	Port     string `json:"port"`
	Packages string `json:"packages"`
	Git      struct {
		GitUrl string `json:"gitUrl"`
		User   string `json:"user"`
		Token  string `json:"token"`
		Branch string `json:"branch"`
	} `json:"git"`
	Exclude    string `json:"exclude"`
	ModuleName string `json:"moduleName"`
	Oss        struct {
		Path      string `json:"path"`
		Endpoint  string `json:"endpoint"`
		Bucket    string `json:"bucket"`
		AccessId  string `json:"accessId"`
		AccessKey string `json:"accessKey"`
		PathStyle bool   `json:"pathStyle"`
	} `json:"oss"`
	TaskNo          string `json:"taskNo"`
	BaseCommitId    string `json:"baseCommitId"`
	CompareCommitId string `json:"compareCommitId"`
	MantisCallBack  string `json:"mantisCallBack"`
	HisId           int64  `json:"hisId"`
	OldExecPath     string `json:"oldExecPath"`
}

func (JacocoCoveragePayload) Kind() string {
	return "magic-neptune-jacoco"
}

package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ScanTestTask struct {
	models.Addons
	TaskName             string      `json:"name" gorm:"column:task_name;type:varchar;size:765;comment:任务名称"` // 任务名称
	AppId                string      `json:"appId" gorm:"column:app_id;type:varchar(128);comment:应用id"`       // 应用id
	AppName              string      `json:"appName" gorm:"column:app_name;type:varchar;size:200;comment:应用名"`
	ProgrammeId          int64       `json:"programmeId" gorm:"column:programme_id;type:int8;comment:方案id"`                                 // 方案id
	Branch               string      `json:"branch" gorm:"column:branch;type:varchar;size:256;comment:代码分支"`                                // 代码分支
	PdfGen               bool        `json:"pdfGen" gorm:"column:pdf_gen;type:bool;comment:是否生成PDF"`                                        // 是否生成PDF
	Operator             string      `json:"operator" gorm:"column:operator;type:varchar;size:256;comment:执行人"`                             // 执行人
	ShipPid              string      `json:"shipPid" gorm:"column:ship_pid;type:varchar;size:100;comment:发布单id"`                            // 发布单id
	Language             string      `json:"language" gorm:"column:language;type:varchar;size:32;comment:语言"`                               // 语言
	ExecType             string      `json:"execType" gorm:"column:exec_type;type:varchar;size:10;comment:应用类型 FRONTEND-前端 BACKEND-后端"`     // 应用类型 FRONTEND-前端 BACKEND-后端
	CodeUrl              string      `json:"codeUrl" gorm:"column:code_url;type:varchar;sie:256;comment:仓库git地址"`                           // 仓库git地址
	JobType              string      `json:"jobType" gorm:"column:job_type;type:varchar;size:20;comment:任务类型 scan-扫描 unit-单测 coverage-覆盖率"` // 任务类型 scan-扫描 unit-单测 coverage-覆盖率
	LastScanTestExecHis  int64       `json:"lastScanTestExecHis" gorm:"column:last_scan_test_exec_his;type:int8;comment:最后一次执行历史id"`        // 最后一次执行历史id
	LastScanTestExecTime *times.Time `json:"lastScanTestExecTime" gorm:"column:last_scan_test_exec_time;type:timestamp;comment:最后一次执行时间"`   // 最后一次执行时间
	CompanyId            string      `json:"companyId" gorm:"column:company_id;type:varchar;size:255;comment:公司id"`                         // 公司id
	From                 string      `json:"from" gorm:"column:from;type:varchar;size:32;comment:任务来源 pipeline-流水线任务 web-离线任务"`             // 来源 pipeline/web
	Mode                 string      `json:"mode" gorm:"column:mode;type:varchar;size:20;comment:扫描模式"`
	CommitTimeFrame      string      `json:"commitTimeFrame" gorm:"column:commit_time_frame;type:varchar;size:100;comment:选取分支的时间范围"`
	CommitBaseId         string      `json:"commitBaseId" gorm:"column:commit_base_id;type:varchar;size:100;comment:基线分支id"`
	CommitCompareId      string      `json:"commitCompareId" gorm:"column:commit_compare_id;type:varchar;size:100;comment:比较分支id"`
}

func (ScanTestTask) TableName() string {
	return "neptune_scan_test_task"
}

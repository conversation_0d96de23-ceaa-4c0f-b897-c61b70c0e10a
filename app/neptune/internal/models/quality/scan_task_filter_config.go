package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

// ScanTaskFilterConfig 过滤配置
type ScanTaskFilterConfig struct {
	models.Addons
	RelationId  string `json:"RelationId" gorm:"column:relation_id;type:text"`
	Type        string `json:"Type" gorm:"column:type;type:varchar;size:30"`
	Description string `json:"Description" gorm:"column:description;type:varchar;size:200"`
	ConfigPaths string `json:"configPaths" gorm:"column:config_paths;type:text"`
	CompanyId   string `json:"companyId" gorm:"column:company_id;type:varchar;size:255;comment:公司id"` // 公司id

}

func (ScanTaskFilterConfig) TableName() string {
	return "neptune_scan_task_filter_config"
}

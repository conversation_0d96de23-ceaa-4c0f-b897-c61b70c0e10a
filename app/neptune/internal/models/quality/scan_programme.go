package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

// ScanProgramme 扫描方案
type ScanProgramme struct {
	models.Addons
	Name      string `json:"name" gorm:"column:name;type:varchar;size:255"`
	Describe  string `json:"describe" gorm:"column:describe;type:varchar;size:500"`
	IsDefault int32  `json:"isDefault" gorm:"column:is_default;type:int4"`
	CompanyId string `json:"companyId" gorm:"column:company_id;type:varchar;size:255;comment:公司id"` // 公司id
}

func (ScanProgramme) TableName() string {
	return "neptune_scan_programme"
}

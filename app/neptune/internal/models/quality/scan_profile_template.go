package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ScanProfileTemplate struct {
	models.Addons
	Key                       string `json:"key" gorm:"column:key;type:varchar;size:255"`
	Name                      string `json:"name" gorm:"column:name;type:varchar;size:100"`
	Language                  string `json:"language" gorm:"column:language;type:varchar;size:20"`
	IsBuiltIn                 bool   `json:"isBuiltIn" gorm:"column:is_built_in;type:bool"`
	LanguageName              string `json:"languageName" gorm:"-"`
	ActiveRuleCount           int32  `json:"activeRuleCount" gorm:"-"`
	ActiveDeprecatedRuleCount int32  `json:"activeDeprecatedRuleCount" gorm:"-"`
	IsDefault                 bool   `json:"isDefault" gorm:"-"`
}

func (ScanProfileTemplate) TableName() string {
	return "neptune_scan_profile_template"
}

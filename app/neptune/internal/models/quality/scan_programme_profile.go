package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ScanProgrammeProfile struct {
	models.Addons
	ProgrammeId           int64   `json:"programmeId" gorm:"column:programme_id;type:int8"`
	ProfileTemplateIdsStr string  `json:"profileIdStr" gorm:"column:profile_template_ids;type:varchar;size:200"`
	ProfileTemplateIds    []int64 `json:"profileIds" gorm:"-"`
	Language              string  `json:"language" gorm:"column:language;type:varchar;size:20"`
	CusName               string  `json:"cusName" gorm:"column:cus_name;type:varchar;size:255"`
	CusKey                string  `json:"cusKey" gorm:"column:cus_key;type:varchar;size:128"`
	IsActive              bool    `json:"isActive" gorm:"column:is_active;type:bool"`
	Name                  string  `json:"name" gorm:"-"`
}

func (ScanProgrammeProfile) TableName() string {
	return "neptune_scan_programme_profile"
}

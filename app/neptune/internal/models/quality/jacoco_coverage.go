package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type JacocoCoverageExecHis struct {
	models.Addons
	AppId          string  `json:"appId" gorm:"column:app_id;type:varchar(128);index:idx_app_id"`
	FromId         int64   `json:"fromId" gorm:"column:from_id;type:int8;index:idx_from_id"`
	Env            string  `json:"env" gorm:"column:env;type:varchar(100);index:idx_env"`
	Stage          string  `json:"stage" gorm:"column:stage;type:varchar(100);index:idx_stage"`
	From           string  `json:"from" gorm:"column:from;type:varchar(20)"`
	ProjectNo      string  `json:"projectNo" gorm:"column:project_no;type:varchar(100)"`
	Request        string  `json:"request" gorm:"column:request;type:text"`
	Status         int8    `json:"status" gorm:"column:status;type:int4"`
	Mode           string  `json:"mode" gorm:"column:mode;type:varchar(20)"`
	Branch         string  `json:"branch" gorm:"column:branch;type:varchar(200)"`
	LineCoverage   float64 `json:"lineCoverage" gorm:"column:line_coverage;type:float4"`
	ClassCoverage  float64 `json:"classCoverage" gorm:"column:class_coverage;type:float4"`
	BranchCoverage float64 `json:"branchCoverage" gorm:"column:branch_coverage;type:float4"`
	ReportUrl      string  `json:"reportUrl" gorm:"column:report_url;type:varchar(200)"`
	K8sTaskId      string  `json:"k8sTaskId" gorm:"column:k8s_task_id;type:varchar(100)"`
	OldExecPath    string  `json:"oldExecPath" gorm:"column:old_exec_path;type:varchar(500);comment:之前执行的exec文件在oss中的路径"`
	ExecPath       string  `json:"execPath" gorm:"column:exec_path;type:varchar(500);comment:本次执行的exec文件在oss中的路径"`
}

func (JacocoCoverageExecHis) TableName() string {
	return "neptune_scan_jacoco_coverage_exec_his"
}

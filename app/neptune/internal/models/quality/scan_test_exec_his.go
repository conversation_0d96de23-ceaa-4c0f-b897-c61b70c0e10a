package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ScanTestExecHis struct {
	models.Addons
	Mode                   string  `json:"mode" gorm:"column:mode;type:varchar;size:32"` // 增量：increment/全量：total
	TaskId                 int64   `json:"taskId" gorm:"column:task_id;type:int8"`
	OssDownloadUrl         string  `json:"ossDownloadUrl" gorm:"column:oss_download_url;type:varchar;size:500"`
	Branch                 string  `json:"branch" gorm:"column:branch;type:varchar;size:128"`
	CommitBaseId           string  `json:"commitBaseId" gorm:"column:commit_base_id;type:varchar;size:64"`
	CommitCompareId        string  `json:"commitCompareId" gorm:"column:commit_compare_id;type:varchar;size:64"`
	CallBackUrl            string  `json:"callBackUrl" gorm:"column:call_back_url;type:varchar;size:256"`
	ShipParams             string  `json:"shipParams" gorm:"column:ship_params;type:varchar;size:4000"`
	SonarUrl               string  `json:"sonarUrl" gorm:"column:sonar_url;type:varchar;size:256"`
	Status                 int8    `json:"status" gorm:"column:status;type:int4"`
	StatusMsg              string  `json:"statusMsg" gorm:"column:status_msg;type:varchar;size:128"`
	PdfUrl                 string  `json:"pdfUrl" gorm:"column:pdf_url;type:varchar;size:255"`
	Lines                  int32   `json:"lines" gorm:"column:lines;type:int4"`
	CommentLines           int32   `json:"commentLines" gorm:"column:comment_lines;type:int4"`
	Complexity             int32   `json:"complexity" gorm:"column:complexity;type:int4"`
	Ncloc                  int32   `json:"ncloc" gorm:"column:ncloc;type:int4"`
	BlockerViolations      int32   `json:"blockerViolations" gorm:"column:blocker_violations;type:int4"`
	CriticalViolations     int32   `json:"criticalViolations" gorm:"column:critical_violations;type:int4"`
	MajorViolations        int32   `json:"majorViolations" gorm:"column:major_violations;type:int4"`
	MinorViolations        int32   `json:"minorViolations" gorm:"column:minor_violations;type:int4"`
	InfoViolations         int32   `json:"infoViolations" gorm:"column:info_violations;type:int4"`
	LineCoverage           float64 `json:"lineCoverage" gorm:"column:line_coverage;type:float4"`
	TestSuccessDensity     float64 `json:"testSuccessDensity" gorm:"column:test_success_density;type:float4"`
	CommentLinesDensity    float64 `json:"commentLinesDensity" gorm:"column:comment_lines_density;type:float4"`
	DuplicatedLinesDensity float64 `json:"duplicatedLinesDensity" gorm:"column:duplicated_lines_density;type:float4"`
	BranchCoverage         float64 `json:"branchCoverage" gorm:"column:branch_coverage;type:float4"`
	Vulnerabilities        int32   `json:"vulnerabilities" gorm:"column:vulnerabilities;type:int4"`
	TotalWarnings          int32   `json:"totalWarnings" gorm:"column:total_warnings;type:int4"`
	FixedWarnings          int32   `json:"fixedWarnings" gorm:"column:fixed_warnings;type:int4"`
	Operator               string  `json:"operator" gorm:"column:operator;type:varchar;size:64"`
	Stage                  string  `json:"stage" gorm:"column:stage;type:varchar;size:128"`
	LinesToCover           int32   `json:"linesToCover" gorm:"column:lines_to_cover;type:int4"`
	K8sTaskId              string  `json:"k8STaskId" gorm:"column:k8s_task_id;type:varchar;size:128"`
	QualityGates           int32   `json:"qualityGates" gorm:"column:quality_gates;type:int2"`
	QualityGatesParams     string  `json:"qualityGatesParams" gorm:"column:quality_gates_params;type:varchar;size:500"`
	SonarProjectKey        string  `json:"sonarProjectKey" gorm:"column:sonar_project_key;type:varchar;size:100"`
}

func (ScanTestExecHis) TableName() string {
	return "neptune_scan_test_exec_his"
}

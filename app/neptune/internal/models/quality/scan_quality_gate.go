package quality

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ScanQualityGates struct {
	models.Addons
	TaskId             int64 `json:"taskId" gorm:"column:task_id;type:int8"`
	IsActive           bool  `json:"isActive" gorm:"column:is_active;type:bool"`
	BlockerViolations  int32 `json:"blockerViolations" gorm:"column:blocker_violations;type:int4"`
	CriticalViolations int32 `json:"criticalViolations" gorm:"column:critical_violations;type:int4"`
	MajorViolations    int32 `json:"majorViolations" gorm:"column:major_violations;type:int4"`
	MinorViolations    int32 `json:"minorViolations" gorm:"column:minor_violations;type:int4"`
}

func (ScanQualityGates) TableName() string {
	return "neptune_scan_quality_gates"
}

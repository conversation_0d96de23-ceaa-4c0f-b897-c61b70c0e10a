package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanTaskExecHis 扫描任务执行历史
type ScanTaskExecHis struct {
	models.Addons
	TaskId       int64       `gorm:"column:task_id;type:int;index:idx_taskId;comment:任务id;not null" json:"taskId" `                      // 任务id" json:"taskId"
	TaskStatus   int8        `gorm:"column:task_status;type:int;size:8;comment:任务状态 0-待执行 4-排队中 1-进行中 2-执行成功 3-执行失败 " json:"taskStatus"` // 任务状态 1-待执行 2-执行中 3-执行成功 4-执行失败 5-执行取消
	ScanResult   string      `gorm:"column:scan_result;type:varchar;size:1024;comment:扫描结果" json:"-"`                                    //  扫描结果jsonStr
	ProductCount int64       `gorm:"column:product_count;type:int;comment:制品数量" json:"productCount"`                                     // 制品数量
	Repo         string      `gorm:"column:repo;type:varchar;size:1024;index:idx_repo;comment:仓库" json:"-" `
	TriggerTime  *times.Time `gorm:"column:trigger_time;type:timestamp;size:0;comment:触发时间" json:"triggerTime"` // 触发时间
	StartTime    *times.Time `gorm:"column:start_time;type:timestamp;size:0;comment:执行时间" json:"startTime"`     // 开启时间
	EndTime      *times.Time `gorm:"column:end_time;type:timestamp;size:0;comment:执行时间" json:"endTime"`         // 结束时间
	ErrMsg       string      `gorm:"column:err_msg;type:varchar;comment:错误信息" json:"errMsg"`                    // 错误信息
	ExecUser     string      `gorm:"column:exec_user;type:varchar;comment:执行人" json:"execUser"`                 // 执行人
	AppName      string      `gorm:"column:app_name;type:varchar;comment:应用名" json:"appName"`
	// 非数据库字段
	StatusMemo     string          `gorm:"-:all" json:"statusMemo"`     // 任务状态描述
	ScanResultInfo *ScanResultInfo `gorm:"-:all" json:"scanResultInfo"` // 扫描结果
	RepoInfo       *[]dto.RepoDto  `gorm:"-:all" json:"repoInfo" `      // 仓库信息数组
}

func (ScanTaskExecHis) TableName() string {
	return "neptune_artifact_scan_task_exec_his"
}

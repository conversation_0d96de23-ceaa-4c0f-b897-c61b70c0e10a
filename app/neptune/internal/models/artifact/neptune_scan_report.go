package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

type ScanReport struct {
	models.Addons
	ReportType   int    `gorm:"column:report_type;type:int;size:8;comment:报告类型" json:"reportType"`                         // 报告类型
	ForeignId    int64  `gorm:"column:foreign_id;type:int;index:idx_foreignId;comment:任务id;not null" json:"foreignId" `    // 外部id
	ReportStatus int8   `gorm:"column:report_status;type:int;size:8;comment:报告开关 1-下载完成 0-下载中 2-下载失败" json:"reportStatus"` // 报告状态
	Conditions   string `gorm:"column:conditions;type:varchar;comment:导出类型" json:"-"`                                      // 导出条件
	ReportUrl    string `gorm:"column:report_url;type:varchar;comment:报告地址" json:"reportUrl"`                              // 报告地址
	UName        string `gorm:"column:u_name;type:varchar;comment:用户名" json:"uName"`                                       // 下载人

	// 非数据库字段
	ConditionInfo interface{} `gorm:"-:all" json:"conditionInfo"` // 下载条件信息
}

func (ScanReport) TableName() string {
	return "neptune_artifact_scan_report"
}

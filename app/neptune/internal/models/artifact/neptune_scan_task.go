package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanTask 扫描任务
type ScanTask struct {
	models.Addons
	RepoInstanceId string `gorm:"column:repo_instance_id;type:varchar;comment:仓库实例id" json:"repoInstanceId" validate:"required"` // 仓库实例id
	PlanId         int64  `gorm:"column:plan_id;type:int;comment:方案id" json:"planId" validate:"required"`                        // 方案id
	// 冗余字段，用于查询,分类
	PlanType          int8   `gorm:"column:plan_type;type:int;size:8;comment:方案类型 1-制品安全漏洞扫描;not null" json:"planType" `                                  // 方案类型 1-安全漏洞扫描
	TaskName          string `gorm:"column:task_name;type:varchar;size:255;index:idx_taskname;comment:任务名称;not null" json:"taskName" validate:"required"` // 任务名称
	ProductType       string `gorm:"column:product_type;type:varchar;size:255;comment:制品类型Generic|Docker" json:"productType"`                             // 制品类型
	TaskFrom          string `gorm:"column:task_from;type:varchar;size:128;comment:任务来源" json:"taskFrom"`                                                 // 任务来源 web or pipeline
	Repo              string `gorm:"column:repo;type:varchar;size:1024;index:idx_repo;comment:仓库" json:"-" `                                              // 仓库json字符传
	ScanRange         int8   `gorm:"column:scan_range;type:int;size:8;comment:扫描范围 1-指定制品 2-最新制品 3-规则扫描" json:"scanRange" validate:"oneof=1 2 3"`         // 扫描范围 1-指定制品 2-最新制品 3-规则扫描
	ScanRangeInfo     string `gorm:"column:scan_range_info;type:text;comment:扫描范围规则" json:"-"`                                                            // 扫描范围信息
	TimerSwitch       int8   `gorm:"column:timer_switch;type:int;size:8;comment:定时开关 1-开 0-关闭" json:"timerSwitch" validate:"oneof=1 0"`                   // 定时开关 1-开 0-关闭
	TimerCorn         string `gorm:"column:timer_corn;type:varchar;size:128;comment:定时corn表达式" json:"timerCorn"`                                          // 定时corn表达式
	QualityGateSwitch int8   `gorm:"column:quality_gate_switch;type:int;size:8;comment:质量门禁开关 1-开 0-关闭" json:"qualityGateSwitch" validate:"oneof=1 0"`    // 质量门禁开关 1-开 0-关闭
	QualityGate       string `gorm:"column:quality_gate;type:text;comment:质量门禁规则" json:"-"`                                                               // 质量门禁规则
	CompanyID         string `gorm:"column:company_id;type:varchar;size:128;comment:公司id" json:"companyID"`                                               // 公司id
	LastExecId        int64  `gorm:"column:last_exec_id;type:int;comment:最近一次执行id" json:"lastExecId"`
	CallBackUrl       string `gorm:"column:call_back_url;type:varchar;size:256;comment:回调地址" json:"callBackUrl"` // 回调地址
	RepoNames         string `gorm:"column:repo_names;type:varchar;" json:"-"`                                   // 仓库名称，只用于模糊查询
	// 非数据库字段
	QualityGateInfo    QualityGateDto         `gorm:"-:all" json:"qualityGateInfo"`    // 质量门禁规则
	RepoInfo           *[]dto.RepoDto         `gorm:"-:all" json:"repoInfo" `          // 仓库信息
	PlanName           string                 `gorm:"-:all" json:"planName"`           // 计划名称
	LastExecUserName   string                 `gorm:"-:all" json:"LastExecUserName"`   // 最新执行人
	LastTaskStatus     string                 `gorm:"-:all" json:"lastTaskStatus"`     // 最新执行状态
	LastTaskStatusCode int8                   `gorm:"-:all" json:"lastTaskStatusCode"` // 最新执行状态
	LastTriggerTime    *times.Time            `gorm:"-:all" json:"LastTriggerTime"`    // 最新执行时间
	LastScanResultInfo *ViewScanResultInfo    `gorm:"-:all" json:"scanResultInfo"`     // 任务结果汇总
	LastReportSwitch   int8                   `gorm:"-:all" json:"lastReportSwitch"`   // 最新一次报告开关
	LastReportUrl      string                 `gorm:"-:all" json:"lastReportUrl"`      // 最新一次报告地址
	ProductList        *[]dto.ViewProductInfo `gorm:"-:all" json:"productList"`        // 制品列表
	// 前端需要展示字段
	ScanProductCount *int64 `gorm:"-:all" json:"scanProductCount"` // 扫描制品数
	ScanCount        *int64 `gorm:"-:all" json:"scanCount"`        // 扫描次数
}

func (ScanTask) TableName() string {
	return "neptune_artifact_scan_task"
}

type QualityGateDto struct {
	Critical int64 `json:"critical" gorm:"column:critical"` // 严重
	High     int64 `json:"high" gorm:"column:high"`         // 高危
	Medium   int64 `json:"medium" gorm:"column:medium"`     // 中危
	Low      int64 `json:"low" gorm:"column:low"`           // 低危
	None     int64 `json:"none" gorm:"column:none"`         // 未定级
}

type ScanResultInfo struct {
	QualityGateDto
	VulnerabilityCount int64 `gorm:"column:vulnerabilitycount" json:"vulnerabilityCount"` // 漏洞总数量
}

// ViewScanResultInfo 带渲染标记的结果
type ViewScanResultInfo struct {
	Critical           ViewNumTag `json:"critical" gorm:"column:critical"`                     // 严重
	High               ViewNumTag `json:"high" gorm:"column:high"`                             // 高危
	Medium             ViewNumTag `json:"medium" gorm:"column:medium"`                         // 中危
	Low                ViewNumTag `json:"low" gorm:"column:low"`                               // 低危
	None               ViewNumTag `json:"none" gorm:"column:none"`                             // 未定级
	VulnerabilityCount ViewNumTag `json:"vulnerabilityCount" gorm:"column:vulnerabilityCount"` // 漏洞总数量
}

type ViewNumTag struct {
	Num int64 `json:"num"` // 个数
	Tag int   `json:"tag"` // 1 红色 0 不显示
}

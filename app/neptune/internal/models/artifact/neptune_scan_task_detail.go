package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

// ScanTaskDetail 扫描任务明细
type ScanTaskDetail struct {
	models.Addons
	TaskId         int64       `gorm:"column:task_id;type:int;index:idx_taskId;comment:任务id;not null" json:"taskId" `    // 任务id" json:"taskId"
	ExecId         int64       `gorm:"column:exec_id;type:int;index:idx_execId;comment:执行id;not null" json:"execId" `    // 执行id" json:"execId"
	Status         int8        `gorm:"column:status;type:int;size:8;comment:任务状态 0-排队中 1-进行中 2-成功 3-失败 " json:"-"`       // 任务状态
	InstanceId     string      `gorm:"column:instance_id;type:varchar;comment:实例id" json:"instanceId"`                   // 仓库实例id
	RepoName       string      `gorm:"column:repo_name;type:varchar;size:1024;comment:仓库名称" json:"repoName"`             // 仓库名称
	ProductName    string      `gorm:"column:product_name;type:varchar;size:1024;comment:制品名称" json:"productName"`       // 制品名称
	ProductVersion string      `gorm:"column:product_version;type:varchar;size:1024;comment:制品版本" json:"productVersion"` // 制品版本
	ScanResult     string      `gorm:"column:scan_result;type:varchar;size:1024;comment:汇总结果" json:"-"`                  //  scanResultInfo
	StartTime      *times.Time `gorm:"column:start_time;type:timestamp;size:0;comment:执行时间" json:"startTime"`            // 开启时间
	EndTime        *times.Time `gorm:"column:end_time;type:timestamp;size:0;comment:执行时间" json:"endTime"`                // 结束时间
	Result         string      `gorm:"column:result;type:jsonb;comment:扫描报告" json:"-"`                                   //  扫描结果 jsonStr
	ErrMsg         string      `gorm:"column:err_msg;type:varchar;comment:错误信息" json:"errMsg"`
	K8sTaskId      string      `gorm:"column:k8s_task_id;type:varchar;size:256;comment:k8s任务id" json:"-"` // k8s task id
	// 非数据库字段
	StatusCode         int8                `gorm:"-:all" json:"statusCode"`         // 任务状态码
	ViewScanResultInfo *ViewScanResultInfo `gorm:"-:all" json:"viewScanResultInfo"` // 任务结果汇总
	// v2 版本,新增字段
	ProductPath        string `gorm:"column:product_path;type:varchar;size:1024;comment:制品路径" json:"productPath"`
	ProductDownloadUrl string `gorm:"column:product_download_url;type:varchar;size:1024;comment:制品下载地址" json:"productDownloadUrl"`
}

func (ScanTaskDetail) TableName() string {
	return "neptune_artifact_scan_task_detail"
}

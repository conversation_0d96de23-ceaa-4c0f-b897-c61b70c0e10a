package artifact

import "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

// ScanPlan 扫描方案
type ScanPlan struct {
	models.Addons
	PlanName    string `gorm:"column:plan_name;type:varchar;size:255;index:idx_planname;comment:方案名称;not null" json:"planName" validate:"required"`         // 方案名称
	PlanType    int8   `gorm:"column:plan_type;type:int;size:8;comment:方案类型 1-安全漏洞扫描,2-代码规约扫描,3-单测测试覆盖率扫描;not null" json:"planType" validate:"oneof=1 2 3"` // 方案类型 1-安全漏洞扫描,2-代码规约扫描,3-单测测试覆盖率扫描
	IsDefault   int8   `gorm:"column:is_default;type:int;size:8;comment:是否默认 1-是,0-否" json:"isDefault" validate:"oneof=1 0"`                                // 是否默认 1-是,0-否
	Description string `gorm:"column:description;type:varchar;size:1024;comment:方案描述" json:"description"`                                                   //	方案描述
	WhiteCVE    string `gorm:"column:white_cve;type:text;comment:漏洞白名单" json:"-"`                                                                           // 漏洞白名单
	CompanyID   string `gorm:"column:company_id;type:varchar;size:128;comment:公司id" json:"companyID"`                                                       // 公司id

	// 非数据库对象
	WhiteCVEInfos []WhiteCVEInfo `gorm:"-:all" json:"whiteCVEInfos"` // 漏洞白名单
}

type WhiteCVEInfo struct {
	Code string `gorm:"-:all" json:"code"`
}

func (ScanPlan) TableName() string {
	return "neptune_artifact_scan_plan"
}

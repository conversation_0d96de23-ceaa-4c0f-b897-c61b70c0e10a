package utils

import (
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
)

func GetGitCodePath(domain string, codeUrl string) string {
	if strings.HasSuffix(codeUrl, constants.GitUrlSuffix) {
		codeUrl = codeUrl[:len(codeUrl)-len(constants.GitUrlSuffix)]
	}
	res := strings.ReplaceAll(codeUrl, domain, "")[1:]
	res = strings.ReplaceAll(res, constants.Slash, constants.SlashCode)
	return res
}

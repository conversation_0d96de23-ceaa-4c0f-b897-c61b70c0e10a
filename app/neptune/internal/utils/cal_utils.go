package utils

import (
	"encoding/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

// CalGateQualityGate 计算质量门禁
func CalGateQualityGate(GateSwitch, status int8, scanResultStr string, qualityGate artifact.QualityGateDto) (*artifact.ViewScanResultInfo, int8) {
	if scanResultStr == "" || scanResultStr == "{}" {
		return nil, status
	}
	rStatus := status
	rV := artifact.ViewScanResultInfo{}
	var scanResult artifact.ScanResultInfo
	err := json.Unmarshal([]byte(scanResultStr), &scanResult)
	if err != nil {
		logger.Logger.Panicf("格式化扫描结果失败,err:%v", err)
	}
	rV.VulnerabilityCount = artifact.ViewNumTag{Num: scanResult.VulnerabilityCount, Tag: constants.IsFalse}
	if GateSwitch == constants.IsFalse {
		rV.Critical = artifact.ViewNumTag{Num: scanResult.Critical, Tag: constants.IsFalse}
		rV.High = artifact.ViewNumTag{Num: scanResult.High, Tag: constants.IsFalse}
		rV.Medium = artifact.ViewNumTag{Num: scanResult.Medium, Tag: constants.IsFalse}
		rV.Low = artifact.ViewNumTag{Num: scanResult.Low, Tag: constants.IsFalse}
		rV.None = artifact.ViewNumTag{Num: scanResult.None, Tag: constants.IsFalse}
	} else if GateSwitch == constants.IsTrue {
		rV.Critical = artifact.ViewNumTag{Num: scanResult.Critical, Tag: compare(scanResult.Critical, qualityGate.Critical)}
		rV.High = artifact.ViewNumTag{Num: scanResult.High, Tag: compare(scanResult.High, qualityGate.High)}
		rV.Medium = artifact.ViewNumTag{Num: scanResult.Medium, Tag: compare(scanResult.Medium, qualityGate.Medium)}
		rV.Low = artifact.ViewNumTag{Num: scanResult.Low, Tag: compare(scanResult.Low, qualityGate.Low)}
		rV.None = artifact.ViewNumTag{Num: scanResult.None, Tag: compare(scanResult.None, qualityGate.None)}
		if rV.Critical.Tag == constants.IsTrue || rV.High.Tag == constants.IsTrue || rV.Medium.Tag ==
			constants.IsTrue || rV.Low.Tag == constants.IsTrue || rV.None.Tag == constants.IsTrue {
			rStatus = task2.ProductStatusGateNotPass
		} else {
			rStatus = task2.ProductStatusGatePass
		}
	}
	return &rV, rStatus
}

func compare(a, b int64) int {
	switch {
	case a > b:
		return constants.IsTrue
	default:
		return constants.IsFalse
	}
}

package remote

import (
	"compress/gzip"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/rest"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/avast/retry-go"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
)

// ShipInvoke 调用ship 接口
type ShipInvoke struct{}

func (s ShipInvoke) ShipCallBack(url string, body interface{}) {
	err := retry.Do(func() error {
		resp := rest.Post(url, body)
		if resp.Err != nil {
			return resp.Err
		}
		resBody := resp.String()
		logger.Logger.Infof("ship resp : %v", resBody)
		return nil
	}, retry.Attempts(3), retry.LastErrorOnly(true), retry.OnRetry(func(n uint, err error) {
		logger.Logger.Infof("第%d次重试：%v", n, err)
	}))
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "调用接口[%v]失败:", url))
	}
}

func (s ShipInvoke) ShipCallBackWithReturn(url string, body interface{}) map[string]interface{} {
	resp := rest.Post(url, body)
	if resp.Err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(resp.Err, "调用接口[%v]失败:", url))
	}
	res := make(map[string]interface{})
	jsonx.UnMarshal(resp.Bytes(), &res)
	return res
}

func (ShipInvoke) GetBranchListByCodePath(ctx *commoncontext.MantisContext, hubUrl, search string, page, pageSize int64) []dto.GitBranchDTO {
	start := time.Now().UnixMilli()
	httpClient := netutil.NewHttpClient()
	hub, err := url.QueryUnescape(hubUrl)
	if err != nil {
		logger.Logger.Panicf("error in decode hub url, hub url = %s, err=%s", hubUrl, err.Error())
	}
	code, hubName, _ := strings.Cut(hub, "/")
	uri := fmt.Sprintf("/ship/api/v1/scm/%s/proxy/api/v4/projects/%s/repository/branches", code, url.QueryEscape(hubName))
	p := url.Values{}
	if search != "" {
		p.Add("search", search)
	}
	if page != 0 {
		p.Add("page", fmt.Sprintf("%d", page))
	}
	if pageSize != 0 {
		p.Add("per_page", fmt.Sprintf("%d", pageSize))
	}
	request := &netutil.HttpRequest{
		RawURL:      configs.Config.Domain.Deckjob + uri,
		Method:      http.MethodGet,
		Headers:     ctx.Header,
		QueryParams: p,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		if resp.StatusCode == http.StatusBadRequest ||
			resp.StatusCode == http.StatusUnauthorized ||
			resp.StatusCode == http.StatusForbidden {
			logger.Logger.Panicf("gitlab未授权, 返回码%d", resp.StatusCode)
		} else {
			logger.Logger.Panicf("error in send request to ship, err=%+v, url=%s", err, uri)
		}
	}
	res := make([]dto.GitBranchDTO, 0)
	gzipFlag := false
	for k, v := range resp.Header {
		if strings.ToLower(k) == "content-encoding" && strings.ToLower(v[0]) == "gzip" {
			gzipFlag = true
		}
	}
	body := resp.Body
	if gzipFlag {
		// 创建 gzip.Reader
		gr, err := gzip.NewReader(resp.Body)
		defer gr.Close()
		if err != nil {
			fmt.Println(err.Error())
		}
		body = gr
	}
	err = json.NewDecoder(body).Decode(&res)
	if err != nil {
		logger.Logger.Panicf("error in send request to ship, err=%+v, url=%s", err, uri)
	}
	logger.Logger.Infof("ship get branch list by code path cost=%d", time.Now().UnixMilli()-start)
	return res
}

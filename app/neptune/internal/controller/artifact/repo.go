package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

type RepoIdInPathReq struct {
	Id string `path:"id"`
}

type RepoLatestProductsReq struct {
	Id       string `path:"id"`
	RepoId   string `path:"repoId"`
	RepoName string `path:"repoName"`
}

type RepoSchemaReq struct {
	RepoType  string `schema:"repoType"`
	SearchKey string `schema:"searchKey"`
}

type RepoVersionsReq struct {
	Id       string `path:"id"`
	ProdId   string `path:"prodId"`
	RepoName string `path:"repoName"`
	ProdName string `path:"prodName"`
}

type RepoVersionPath struct {
	Id        string `path:"id"`
	VersionId string `path:"versionId"`
}

type RepoController struct {
	*controller.BaseController
}

var DefaultRepoController RepoController

func (r *RepoController) InstanceList() {
	instanceList, err := repo.GetAllRepoInstance(r.MantisContext, utils.IDString(r.User.CompanyId))
	if err != nil {
		logger.Logger.Panicf("从 deckjob 获取 repo instance 失败!err=%v", err)
	}
	list := make([]map[string]interface{}, 0)
	for _, repoInstance := range *instanceList {
		m := make(map[string]interface{}, 4)
		m["id"] = repoInstance.ID
		m["instanceName"] = repoInstance.InstanceName
		m["supportProductList"] = repoInstance.SupportProductList
		list = append(list, m)
	}
	r.ResSuccessResult(list)
}

func (r *RepoController) RepoInfos(req RepoIdInPathReq, schema RepoSchemaReq) {
	id := req.Id
	companyId := utils.IDString(r.GetCompany())
	service := repo.GetServiceById(r.MantisContext, companyId, id)
	nameMap := service.RepoInfos(r.MantisContext, schema.SearchKey, schema.RepoType)
	r.ResSuccessResult(nameMap)
}

func (r *RepoController) LatestProducts(req RepoLatestProductsReq, schema RepoSchemaReq) {
	id := req.Id
	repoId := req.RepoId
	repoName := req.RepoName
	companyId := utils.IDString(r.GetCompany())
	service := repo.GetServiceById(r.MantisContext, companyId, id)
	products := service.LatestAllProducts(r.MantisContext, repoName, repoId, schema.RepoType)
	r.ResSuccessResult(products)
}

func (r *RepoController) Products(req RepoLatestProductsReq, schema RepoSchemaReq) {
	id := req.Id
	repoId := req.RepoId
	repoName := req.RepoName
	companyId := utils.IDString(r.GetCompany())
	service := repo.GetServiceById(r.MantisContext, companyId, id)
	products := service.Products(r.MantisContext, repoName, repoId, schema.SearchKey, schema.RepoType)
	r.ResSuccessResult(products)
}

func (r *RepoController) Versions(req RepoVersionsReq, schema RepoSchemaReq) {
	id := req.Id
	prodId := req.ProdId
	repoName := req.RepoName
	companyId := utils.IDString(r.GetCompany())
	service := repo.GetServiceById(r.MantisContext, companyId, id)
	products := service.Versions(r.MantisContext, repoName, schema.SearchKey, prodId, req.ProdName, schema.RepoType)
	r.ResSuccessResult(products)
}

func (r *RepoController) VersionPath(req RepoVersionPath, schema RepoSchemaReq) {
	id := req.Id
	companyId := utils.IDString(r.GetCompany())
	service := repo.GetServiceById(r.MantisContext, companyId, id)
	products := service.Path(r.MantisContext, req.VersionId, schema.RepoType)
	r.ResSuccessResult(products)
}

package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	artifact2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type TaskInfoInPathReq struct {
	Id     int64 `path:"id"`
	ExecId int64 `path:"eid"`
}

type TaskIdInPathReq struct {
	Id int64 `path:"id"`
}

type TaskController struct {
	*controller.BaseController
}

var DefaultTaskController TaskController

var scanTaskService artifact2.ScanTaskService

func (a *TaskController) List(req QueryWithOrderDTOReq) {
	request := dto.QueryWithOrderDTO{
		QueryDTO: dto.QueryDTO{
			PageSize:  req.PageSize,
			Page:      req.Page,
			Q:         req.Q,
			RequestId: req.RequestId,
			SearchKey: req.SearchKey,
		},
		OrderField: req.OrderField,
		OrderType:  req.OrderType,
	}
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	page := scanTaskService.List(a.MantisContext, &request, userInfo)
	a.ResSuccessResult(page)
}

func (a *TaskController) Info(req TaskInfoInPathReq) {
	id := req.Id
	execId := req.ExecId
	info := scanTaskService.Info(a.MantisContext, id, execId)
	if info.Id == 0 {
		logger.Logger.Panicf("error in TaskHandler.Info...查询的数据不存在,id=%d ", id)
	}
	a.ResSuccessResult(info)
}

func (a *TaskController) DeleteTask(req TaskIdInPathReq) {
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	id := req.Id
	scanTaskService.DeleteTask(a.MantisContext, id, userInfo)
	a.ResSuccess()
}

func (a *TaskController) AddTask(task artifact.ScanTask) {
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	task.TaskFrom = task2.TaskFromWeb
	id := scanTaskService.AddOrUpdateTask(a.MantisContext, &task, userInfo)
	a.ResSuccessResult(id)
}

func (a *TaskController) UpdateTask(task artifact.ScanTask) {
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	task.TaskFrom = task2.TaskFromWeb
	id := scanTaskService.AddOrUpdateTask(a.MantisContext, &task, userInfo)
	a.ResSuccessResult(id)
}

func (a *TaskController) Exec(req TaskIdInPathReq) {
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	id := req.Id
	execId := scanTaskService.Exec(a.MantisContext, id, userInfo, "")
	a.ResSuccessResult(execId)
}

func (a *TaskController) Abort(req TaskIdInPathReq) {
	userInfo := dto.UserInfo{
		AdAccount: a.GetUser(),
		CompanyID: utils.IDString(a.User.CompanyId),
	}
	id := req.Id
	scanTaskService.Abort(a.MantisContext, id, userInfo)
	a.ResSuccess()
}

package artifact

import (
	dto2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/scan"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ReportIdInPathReq struct {
	Id int64 `path:"id"`
}

type ReportController struct {
	*controller.BaseController
}

var DefaultReportController ReportController

var reportService artifact.ReportService

func (r *ReportController) Detail(idReq ReportIdInPathReq, req QueryDTOReq) {
	id := idReq.Id
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}
	page := reportService.Detail(r.MantisContext, id, request)
	r.ResSuccessResult(page)
}

func (r *ReportController) ExecHisExport(idReq ReportIdInPathReq, exportDto dto2.ProductExportDto) {
	userInfo := dto.UserInfo{
		AdAccount: r.GetUser(),
		CompanyID: utils.IDString(r.User.CompanyId),
	}
	id := idReq.Id
	reportService.ExecHisExport(r.MantisContext, id, scan.ScanProductCve, exportDto, userInfo)
	r.ResSuccess()
}

func (r *ReportController) List(idReq ReportIdInPathReq, req QueryDTOReq) {
	id := idReq.Id
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}
	page := reportService.List(r.MantisContext, id, request)
	r.ResSuccessResult(page)
}

package artifact

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	artifact2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type QueryWithOrderDTOReq struct {
	QueryDTOReq
	OrderField string `schema:"orderField"`
	// 1 正序 其他都是倒序
	OrderType int8 `schema:"orderType"`
}

type PlanIdInPathReq struct {
	Id int64 `path:"id"`
}

type PlanDefaultPathReq struct {
	Id int64  `path:"id"`
	V  string `path:"v"`
}

type PlanController struct {
	*controller.BaseController
}

var DefaultPlanController PlanController

var scanPlanService artifact2.ScanPlanService

func (p *PlanController) List(req QueryWithOrderDTOReq) {
	request := dto.QueryWithOrderDTO{
		QueryDTO: dto.QueryDTO{
			PageSize:  req.PageSize,
			Page:      req.Page,
			Q:         req.Q,
			RequestId: req.RequestId,
			SearchKey: req.SearchKey,
		},
		OrderField: req.OrderField,
		OrderType:  req.OrderType,
	}
	userInfo := dto.UserInfo{
		AdAccount: p.GetUser(),
		CompanyID: utils.IDString(p.User.CompanyId),
	}
	page := scanPlanService.List(p.MantisContext, &request, userInfo)
	p.ResSuccessResult(page)
}

func (p *PlanController) Info(req PlanIdInPathReq) {
	id := req.Id
	info := scanPlanService.Info(p.MantisContext, id)
	if info.Id == 0 {
		logger.Logger.Warnf("方案不存在,或被删除!id=%d ", id)
		logger.Logger.Panicf("方案不存在,或被删除!")
	}
	p.ResSuccessResult(info)
}

func (p *PlanController) AddPlan(plan artifact.ScanPlan) {
	userInfo := dto.UserInfo{
		AdAccount: p.GetUser(),
		CompanyID: utils.IDString(p.User.CompanyId),
	}
	id := scanPlanService.AddOrUpdatePlan(p.MantisContext, &plan, userInfo)
	p.ResSuccessResult(id)
}

func (p *PlanController) UpdatePlan(plan artifact.ScanPlan) {
	userInfo := dto.UserInfo{
		AdAccount: p.GetUser(),
		CompanyID: utils.IDString(p.User.CompanyId),
	}
	id := scanPlanService.AddOrUpdatePlan(p.MantisContext, &plan, userInfo)
	p.ResSuccessResult(id)
}

func (p *PlanController) Default(req PlanDefaultPathReq) {
	userInfo := dto.UserInfo{
		AdAccount: p.GetUser(),
		CompanyID: utils.IDString(p.User.CompanyId),
	}
	id := req.Id
	scanPlanService.Default(p.MantisContext, id, req.V, userInfo)
	p.ResSuccess()
}

func (p *PlanController) DeletePlan(req PlanIdInPathReq) {
	userInfo := dto.UserInfo{
		AdAccount: p.GetUser(),
		CompanyID: utils.IDString(p.User.CompanyId),
	}
	id := req.Id
	scanPlanService.DeletePlan(p.MantisContext, id, userInfo)
	p.ResSuccess()
}

func (p *PlanController) BindTask(req QueryDTOReq, idReq PlanIdInPathReq) {
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}
	planId := idReq.Id
	page := scanPlanService.BindTask(p.MantisContext, &request, planId)
	p.ResSuccessResult(page)
}

package artifact

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact/openapi"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeExecHisController(rw http.ResponseWriter, req *http.Request) (*ExecHisController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	execHisController := &ExecHisController{
		BaseController: baseController,
	}
	return execHisController, nil
}

func InitializePipelineController(rw http.ResponseWriter, req *http.Request) (*openapi.PipelineController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	pipeLineController := &openapi.PipelineController{
		BaseController: baseController,
	}
	return pipeLineController, nil
}

func InitializePlanController(rw http.ResponseWriter, req *http.Request) (*PlanController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	planController := &PlanController{
		BaseController: baseController,
	}
	return planController, nil
}

func InitializeRepoController(rw http.ResponseWriter, req *http.Request) (*RepoController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	repoController := &RepoController{
		BaseController: baseController,
	}
	return repoController, nil
}

func InitializeReportController(rw http.ResponseWriter, req *http.Request) (*ReportController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	reportController := &ReportController{
		BaseController: baseController,
	}
	return reportController, nil
}

func InitializeResultController(rw http.ResponseWriter, req *http.Request) (*openapi.ResultController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	resultController := &openapi.ResultController{
		BaseController: baseController,
	}
	return resultController, nil
}

func InitializeTaskController(rw http.ResponseWriter, req *http.Request) (*TaskController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	taskController := &TaskController{
		BaseController: baseController,
	}
	return taskController, nil
}

func InitializeTemplateController(rw http.ResponseWriter, req *http.Request) (*TemplateController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	templateController := &TemplateController{
		BaseController: baseController,
	}
	return templateController, nil
}

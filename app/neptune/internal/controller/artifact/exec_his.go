package artifact

import (
	"context"
	"encoding/base64"
	"fmt"
	"path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	artifact2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	task2 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/task"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type ExecHisIdInPathReq struct {
	Id int64 `path:"id"`
}

type QueryDTOReq struct {
	PageSize  int64  `schema:"pageSize"`
	Page      int64  `schema:"page"`
	Q         string `schema:"q"`
	RequestId string `schema:"requestId"`
	SearchKey string `schema:"searchKey"`
}

type ExecHisController struct {
	*controller.BaseController
}

var DefaultExecHisController ExecHisController

var scanTaskExecHisService artifact2.ScanTaskExecHisService

func (e *ExecHisController) GetProductExecResult(idReq ExecHisIdInPathReq, req QueryDTOReq) {
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}
	result := scanTaskExecHisService.GetProductExecResult(e.MantisContext, idReq.Id, request)
	e.ResSuccessResult(result)
}

func (e *ExecHisController) GetExecHistList(idReq ExecHisIdInPathReq, req QueryDTOReq) {
	taskId := idReq.Id
	request := dto.QueryDTO{
		PageSize:  req.PageSize,
		Page:      req.Page,
		Q:         req.Q,
		RequestId: req.RequestId,
		SearchKey: req.SearchKey,
	}
	result := scanTaskExecHisService.GetExecHistList(e.MantisContext, taskId, request)
	e.ResSuccessResult(result)
}

func (e *ExecHisController) GetProductExecLog(idReq ExecHisIdInPathReq) {
	detailId := idReq.Id
	// 获取k8s pod 日志
	dp, err := driver.NewDriverProvider()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in new driver provider"))
	}

	var detail artifact.ScanTaskDetail
	detail.Id = detailId
	detail.IsDeleted = commonconstants.DeleteNo
	err = gormx.SelectOneByCondition(e.MantisContext, &detail)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.New("error in select ScanTaskDetail"))
	}
	var log string
	log, err = dp.GetLog(context.Background(), detail.K8sTaskId)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get log"))
	}
	m := make(map[string]interface{})
	m["id"] = detailId
	m["status"] = detail.Status
	m["log"] = log
	if detail.Status >= task2.ProductStatusSucc {
		oss := base64.StdEncoding.EncodeToString([]byte(path.Join(commonconstants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", detail.K8sTaskId))))
		m["osskey"] = oss
	} else {
		m["osskey"] = ""
	}
	e.ResSuccessResult(m)
}

package openapi

import (
	"fmt"
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums/scan"
	artifactModel "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/repo"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	pkgDto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type PipelineController struct {
	*controller.BaseController
}

var DefaultPipelineControler PipelineController

var pipelineService artifact.PipelineService

const (
	ship_cookie = "zcloud_companyid=%s;zcloud_username=%s"
)

func (p *PipelineController) ScanProduct(req dto.PipelineProductScanRequestDto) {
	plan := artifactModel.ScanPlan{}
	// 获取默认计划
	gormx.RawX(p.MantisContext, "select * from neptune_artifact_scan_plan where company_id=? and is_default=1 and plan_type = ? and is_deleted ='N' order by id desc limit 1",
		&plan, req.CompanyID, scan.ScanProductCve)
	if plan.Id == 0 {
		logger.Logger.Panicf("没有找到默认漏洞扫描方案,请先设置默认方案!")
		return
	}
	if req.InstanceId == "" && req.InstanceName != "" {
		// 兼容老的流水线逻辑，通过名字获取仓库实例
		instance := repo.NameRepoInstanceMap[req.InstanceName]
		req.InstanceId = instance.ID
	}
	logger.Logger.Infof("pipeline-req: %+v", req)
	logger.Logger.Infof("use pipeline-default-plan user=%s & company=%s", plan.Creator, plan.CompanyID)
	header := http.Header{}
	header.Set("Cookie", fmt.Sprintf(ship_cookie, req.CompanyID, plan.Creator))
	p.MantisContext.Header = header
	p.MantisContext.User = pkgDto.UserInfo{
		CompanyID: req.CompanyID,
		AdAccount: plan.Creator,
	}
	service := repo.GetServiceById(p.MantisContext, req.CompanyID, req.InstanceId)
	if service == nil {
		logger.Logger.Panicf("mantis-neptune 没有配置此仓库！id=%v,name=%s", req.InstanceId, req.InstanceName)
	}
	resp := pipelineService.ScanProduct(p.MantisContext, &req)
	p.ResSuccessResult(resp)
}

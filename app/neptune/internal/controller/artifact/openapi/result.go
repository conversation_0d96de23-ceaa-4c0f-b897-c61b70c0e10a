package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ResultUploadFormReq struct {
	Id   int64  `path:"id"`
	Type string `path:"type"`
}

type ResultController struct {
	*controller.BaseController
}

var DefaultResultController ResultController

var resultService artifact.ResultService

func (h *ResultController) UploadFormResult(req ResultUploadFormReq) {
	id := req.Id
	// 获取文件
	_, fileHeader, err := h.Request.FormFile("file")
	if err != nil {
		logger.Logger.Panicf("获取文件失败!")
	}
	logger.Logger.Infof("获取的文件名{%v},size={%v} ", fileHeader.Filename, fileHeader.Size)
	file, _ := fileHeader.Open()
	m := make(map[string]interface{})
	m["type"] = req.Type
	m["id"] = id
	resultService.DealResult(h.MantisContext, file, fileHeader.Size, m)
	h.ResSuccess()
	defer file.Close()
}

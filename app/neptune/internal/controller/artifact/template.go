package artifact

import (
	"encoding/json"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/resources"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"

	"github.com/duke-git/lancet/v2/xerror"
	"github.com/xuri/excelize/v2"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type TemplateTypeInPathReq struct {
	Type string `path:"type"`
}

type TemplateController struct {
	*controller.BaseController
}

var DefaultTemplateController TemplateController

const (
	CveTemplate = "template/CVE-template.xlsx"
)

func (t *TemplateController) Format(req TemplateTypeInPathReq) {
	fType := req.Type
	_, fileHeader, err := t.Request.FormFile("file")
	if err != nil {
		logger.Logger.Panic("获取文件异常！")
	}
	openFile, err := fileHeader.Open()
	if err != nil {
		logger.Logger.Panic("打开文件异常！err=", err)
	}
	defer openFile.Close()
	switch fType {
	case "cve":
		tempMap := make(map[string]string)
		value := t.Request.PostFormValue("whiteCVEInfos")
		if value != "" {
			var infos []artifact.WhiteCVEInfo
			err := json.Unmarshal([]byte(value), &infos)
			if err != nil {
				logger.Logger.Panic("解析字符串数组异常！err=", err)
			}
			for _, info := range infos {
				tempMap[info.Code] = ""
			}
		}
		f, err := excelize.OpenReader(openFile)
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "解析excel文件错误，请检查上传文件的格式！"))
		}
		// 获取 Sheet1 上所有单元格
		contents, err := f.GetRows("Sheet1")
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "解析excel文件错误，请检查上传文件的格式！"))
		}
		tempExec := make([]string, 0)
		for i, line := range contents {
			if i == 0 {
				continue
			}
			for _, str := range line {
				if !strings.HasPrefix(str, "CVE-") {
					continue
				}
				if _, ok := tempMap[str]; !ok {
					tempExec = append(tempExec, str)
				}
			}
		}
		// 对 tempExec 去重
		tempExec = common_util.StrRemoveDuplicates(tempExec)
		resp := make([]artifact.WhiteCVEInfo, len(tempExec))
		for i, str := range tempExec {
			resp[i] = artifact.WhiteCVEInfo{
				Code: str,
			}
		}
		t.ResSuccessResult(resp)
	default:
		logger.Logger.Panic("类型不存在,请输入cve")
	}
}

func (t *TemplateController) Download(req TemplateTypeInPathReq) error {
	fileType := req.Type
	switch fileType {
	case "cve":
		file, err := resources.TemplateFS.ReadFile(CveTemplate)
		if err != nil {
			return fmt.Errorf("获取 %s 文件异常, err= %v", CveTemplate, err)
		}
		t.ResponseWriter.Header().Set("Content-Type", "application/octet-stream")
		t.ResponseWriter.Header().Set("Content-Disposition", "attachment; filename=template.xlsx")
		t.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
		if _, err = t.ResponseWriter.Write(file); err != nil {
			return err
		}
		return nil
	default:
		return fmt.Errorf("类型不存在,请输入cve")
	}
}

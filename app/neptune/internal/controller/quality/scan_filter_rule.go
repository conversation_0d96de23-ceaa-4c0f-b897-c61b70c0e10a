package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type QueryFilterRuleReq struct {
	RelationId string `path:"relationId"`
	Type       string `path:"type"`
}

type QueryAppFilterRuleReq struct {
	AppId string `schema:"appId"`
}

type QueryAppFilterInfoIdReq struct {
	Id int64 `path:"id"`
}

type QueryAppFilterInfoAppIdReq struct {
	Id string `path:"id"`
}

type UpdateAppFilterDescriptionReq struct {
	Id          int64  `json:"id"`
	Description string `json:"description"`
}

type ScanFilterRuleController struct {
	*controller.BaseController
}

var (
	DefaultScanFilterRuleController ScanFilterRuleController
	filterConfigService             quality.ScanTaskFilterConfigService
)

func (c *ScanFilterRuleController) QueryFilterRule(req QueryFilterRuleReq) {
	c.ResSuccessResult(filterConfigService.QueryFilter(c.MantisContext, req.RelationId, req.Type, ""))
}

func (c *ScanFilterRuleController) CreateFilterRule(filterDTO dto.ScanTaskFilterDTO) {
	c.ResSuccessResult(filterConfigService.ModifyFilter(c.MantisContext, filterDTO, commondto.UserInfo{AdAccount: c.GetUser(), CompanyID: c.GetCompany()}))
}

func (c *ScanFilterRuleController) ModifyFilterRule(filterDTO dto.ScanTaskFilterDTO) {
	c.ResSuccessResult(filterConfigService.ModifyFilter(c.MantisContext, filterDTO, commondto.UserInfo{AdAccount: c.GetUser(), CompanyID: c.GetCompany()}))
}

func (c *ScanFilterRuleController) QueryAppFilterRule(req QueryAppFilterRuleReq, request gormx.PageRequest) {
	c.ResSuccessResult(filterConfigService.QueryAppFilter(c.MantisContext, req.AppId, request))
}

func (c *ScanFilterRuleController) QueryAppFilterInfo(req QueryAppFilterInfoIdReq) {
	c.ResSuccessResult(filterConfigService.QueryAppFilterInfo(c.MantisContext, req.Id))
}

func (c *ScanFilterRuleController) QueryAppFilterInfoByAppId(req QueryAppFilterInfoAppIdReq) {
	c.ResSuccessResult(filterConfigService.QueryAppFilterInfoByAppId(c.MantisContext, req.Id))
}

func (c *ScanFilterRuleController) SaveAppFilterRule(filterDto dto.AppScanTaskFilterDTO) {
	c.ResSuccessResult(filterConfigService.SaveAppFilter(c.MantisContext, filterDto))
}

func (c *ScanFilterRuleController) UpdateAppFilterRule(filterDto dto.AppScanTaskFilterDTO) {
	filterConfigService.SaveAppFilter(c.MantisContext, filterDto)
	c.ResSuccess()
}

func (c *ScanFilterRuleController) DeleteAppFilterRule(req QueryAppFilterInfoIdReq) {
	filterConfigService.DeleteAppFilter(c.MantisContext, req.Id)
	c.ResSuccess()
}

func (c *ScanFilterRuleController) QueryFilterExclusionEnum() {
	c.ResSuccessResult([]commondto.CodeEnumDTO{
		{Label: "排除", Value: constants.FilterConfigExclusion},
		// {Label: "包含", Value: constants.FilterConfigInclusion},
	})
}

func (c *ScanFilterRuleController) UpdateDescriptioon(description UpdateAppFilterDescriptionReq) {
	filterConfigService.UpdateAppFilterDescription(c.MantisContext, description.Id, description.Description)
	c.ResSuccess()
}

package quality

import (
	"fmt"
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	qualitymodels "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ProgrammeIdInPathReq struct {
	ProgrammeId int64 `path:"programmeId"`
}

type ScanProfileTemplateIdInPathReq struct {
	Id int64 `path:"id"`
}

type ScanProfileTemplateLanguageInSchemaReq struct {
	Language string `schema:"language"`
}

type ScanProfileProfileIdInPathReq struct {
	ProfileId int64 `path:"profileId"`
}

type ScanProfileTemplateController struct {
	*controller.BaseController
}

var (
	DefaultScanProfileTemplateController ScanProfileTemplateController
	profileService                       quality.ScanProfileTemplateService
)

func (c *ScanProfileTemplateController) QueryProfileTemplateByLanguage(req ScanProfileTemplateLanguageInSchemaReq) {
	c.ResSuccessResult(profileService.GetTemplatesByLanguage(c.MantisContext, req.Language))
}

func (c *ScanProfileTemplateController) SaveScanProfileTemplate(template qualitymodels.ScanProfileTemplate) {
	c.ResSuccessResult(profileService.SaveScanProfileTemplate(c.MantisContext, template, commondto.UserInfo{AdAccount: c.GetUser()}))
}

func (c *ScanProfileTemplateController) CopyScanProfileTemplate(reqDTO dto.CopyProfileTemplateReqDTO) {
	c.ResSuccessResult(profileService.CopyTemplate(c.MantisContext, reqDTO.Id, reqDTO.Name, commondto.UserInfo{AdAccount: c.GetUser()}))
}

func (c *ScanProfileTemplateController) DeleteScanProfileTemplate(req ScanProfileTemplateIdInPathReq) {
	profileService.DeleteTemplate(c.MantisContext, req.Id, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *ScanProfileTemplateController) ImportProfiles() {
	request := c.Request
	_, file, err := request.FormFile("file")
	if err != nil {
		logger.Logger.Panicf("error in getting request param, err=%v", err)
	}
	c.ResSuccessResult(profileService.ImportProfileToTemplate(c.MantisContext, file, commondto.UserInfo{AdAccount: c.GetUser()}))
}

func (c *ScanProfileTemplateController) ExportProfile(req ScanProfileTemplateIdInPathReq) {
	fileName, export := profileService.ExportProfileTemplate(c.MantisContext, req.Id)
	contentType := "application/octet-stream"
	c.ResponseWriter.Header().Set("Content-Type", contentType)
	fileName = url.QueryEscape(fileName)
	c.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	c.ResponseWriter.Write([]byte(export))
}

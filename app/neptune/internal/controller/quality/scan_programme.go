package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ScanProgrammeIdInPathReq struct {
	Id int64 `path:"id"`
}

type ScanProgrammeSearchReq struct {
	Name string `schema:"name"`
}

type ScanTaskListReq struct {
	Id int64 `schema:"id"`
}

type ScanProgrammeSetProfileDefaultSchemaReq struct {
	ProgrammeId int64 `schema:"programmeId"`
	ProfileId   int64 `schema:"profileId"`
	IsActive    bool  `schema:"isActive"`
}

type ScanProgrammeCompileReq struct {
	Id      int64 `path:"id"`
	Compile int32 `path:"compile"`
}

type ScanProgrammeController struct {
	*controller.BaseController
}

var DefaultScanProgrammeController ScanProgrammeController

var scanProgrammeService quality.ScanProgrammeService

func (c *ScanProgrammeController) Add(scanProgrammeDto dto.ScanProgrammeDTO) {
	user := commondto.UserInfo{
		AdAccount: c.GetUser(),
		CompanyID: c.GetCompany(),
	}
	c.ResSuccessResult(scanProgrammeService.CreateScanProgramme(c.MantisContext, scanProgrammeDto, user))
}

func (c *ScanProgrammeController) Query(req ScanProgrammeIdInPathReq) {
	c.ResSuccessResult(scanProgrammeService.QueryScanProgramme(c.MantisContext, req.Id))
}

func (c *ScanProgrammeController) Delete(req ScanProgrammeIdInPathReq) {
	c.ResSuccessResult(scanProgrammeService.DeleteScanProgramme(c.MantisContext, req.Id))
}

func (c *ScanProgrammeController) List(req ScanProgrammeSearchReq) {
	c.ResSuccessResult(scanProgrammeService.QueryScanProgrammeList(c.MantisContext, req.Name))
}

func (c *ScanProgrammeController) Modify(programmeDTO dto.ScanProgrammeDTO) {
	c.ResSuccessResult(scanProgrammeService.ModifyScanProgramme(c.MantisContext, programmeDTO,
		commondto.UserInfo{AdAccount: c.GetUser(), CompanyID: c.GetCompany()}))
}

func (c *ScanProgrammeController) QueryProfileCards(req ScanProgrammeIdInPathReq) {
	c.ResSuccessResult(scanProgrammeService.QueryScanProgrammeProfileCards(c.MantisContext, req.Id))
}

func (c *ScanProgrammeController) SetProfileActive(req ScanProgrammeSetProfileDefaultSchemaReq) {
	c.ResSuccessResult(scanProgrammeService.SetProfileActive(c.MantisContext, req.ProgrammeId, req.ProfileId, req.IsActive))
}

func (c *ScanProgrammeController) QueryTaskNameList(req ScanTaskListReq, pageReq gormx.PageRequest) {
	res, _ := scanProgrammeService.QueryScanTaskNameList(c.MantisContext, req.Id, pageReq)
	c.ResSuccessResult(res)
}

func (c *ScanProgrammeController) QueryProgrammeProfileEnum(req ScanProgrammeIdInPathReq) {
	c.ResSuccessResult(scanProgrammeService.QueryProgrammeProfileEnum(c.MantisContext, req.Id))
}

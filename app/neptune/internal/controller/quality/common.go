package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type QueryBranchListReq struct {
	Search   string `schema:"search"`
	AppId    string `schema:"appId"`
	Page     int64  `schema:"page"`
	PageSize int64  `schema:"pageSize"`
}

type GetBranchReq struct {
	AppId  string `schema:"appId"`
	Branch string `schema:"branch"`
}

type CommonController struct {
	*controller.BaseController
}

var DefaultCommonController CommonController

func (c *CommonController) GetTaskStatusEnumList() {
	c.ResSuccessResult(make(map[string]interface{}))
}

func (c *CommonController) GetLanguages() {
	c.ResSuccessResult(sonarRuleService.GetLanguages(c.MantisContext))
}

func (c *CommonController) GetSeverities() {
	c.ResSuccessResult(enums.RuleSeverityEnum{}.GetSeverityList())
}

func (c *CommonController) GetExecTypes() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "前端", Value: constants.ExecTypeFront},
		{Label: "后端", Value: constants.ExecTypeBack},
	})
}

func (c *CommonController) GetScanModes() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "全量", Value: constants.StandardTotal},
		{Label: "增量", Value: constants.StandardIncrement},
	})
}

func (c *CommonController) GetBranchList(req QueryBranchListReq) {
	c.ResSuccessResult(taskService.GetBranchList(req.AppId, req.Search, req.Page, req.PageSize))
}

func (c *CommonController) GetBranchListByVcsPath(req QueryBranchListReq) {
	c.ResSuccessResult(taskService.GetBranchListByCodePath(c.MantisContext, c.Request.Header.Get(commonconstants.HubUrlKey), req.Search, req.Page, req.PageSize))
}

func (c *CommonController) GetBranch(req GetBranchReq) {
	c.ResSuccessResult(taskService.GetBranchCommit(req.AppId, req.Branch))
}

func (c *CommonController) GetTaskFromEnums() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Value: constants.SourceWeb, Label: "手动创建"},
		{Value: constants.SourcePipeline, Label: "自动创建"},
	})
}

func (c *CommonController) GetTaskTypeEnums() {
	c.ResSuccessResult([]dto.CodeEnumDTO{
		{Label: "代码扫描", Value: constants.ScanJobType},
		{Label: "单元测试", Value: constants.UnitTestJobType},
		{Label: "集成覆盖率", Value: constants.CoverageJobType},
	})
}

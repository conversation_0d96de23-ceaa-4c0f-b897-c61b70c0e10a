package openapi

import (
	"io"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type BackControllerIdInPathReq struct {
	Id int64 `path:"id"`
}

type BackController struct {
	*controller.BaseController
}

var (
	DefaultBackController  BackController
	scanTestExecHisService quality.ScanTestExecHisService
)

func (c *BackController) SonarUpdateBackCall(req BackControllerIdInPathReq) {
	body := c.Request.Body
	jsonStr, err := io.ReadAll(body)
	if err != nil {
		logger.Logger.Panicf("error getting request body, err=%v", err)
	}
	scanTestExecHisService.SonarUpdateCallBack(c.<PERSON>onte<PERSON>, req.Id, string(jsonStr))
	c.ResSuccess()
}

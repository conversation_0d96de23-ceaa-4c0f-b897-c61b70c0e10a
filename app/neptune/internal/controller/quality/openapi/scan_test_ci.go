package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type CiJobTypeInPathReq struct {
	Type string `path:"type"`
}

type CiJobRes struct {
	ReportUrl string `json:"report_url"`
}

type ScanTestCiController struct {
	*controller.BaseController
}

var (
	DefaultScanTestCiController ScanTestCiController
	taskService                 quality.TaskService
)

func (c *ScanTestCiController) ScanTest(infoDto dto.ScanTestInfoDTO, req CiJobTypeInPathReq) {
	reportUrl := taskService.ShipRegisterJob(c.<PERSON>, infoDto, req.Type)
	c.ResSuccessResult(CiJobRes{
		ReportUrl: reportUrl,
	})
}

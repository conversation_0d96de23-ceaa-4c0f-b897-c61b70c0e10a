package openapi

import (
	"encoding/json"
	"io"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type JacocoController struct {
	*controller.BaseController
}

type AptResetAndRegisterReq []dto.AptResetAndRegisterCoverageReqDTO

var (
	DefaultJacocoController JacocoController
	jacocoService           quality.JacocoCoverageService
)

func (c *JacocoController) PipelineRegister(req dto.PipelineCollectCoverageReqDTO) {
	jacocoService.PipelineRegister(c.MantisContext, req)
	c.ResSuccess()
}

func (c *JacocoController) PipelineExec(req dto.PipelineCollectCoverageReqDTO) {
	jacocoService.PipelineExec(c.<PERSON>, req)
	c.ResSuccess()
}

func (c *JacocoController) AptRegister() error {
	req := make([]dto.AptResetAndRegisterCoverageReqDTO, 0)
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, &req); err != nil {
		return err
	}
	c.ResSuccessResult(jacocoService.AptRegister(c.MantisContext, req))
	return nil
}

func (c *JacocoController) AptExec() error {
	req := make([]dto.AptExecCoverageReqDTO, 0)
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return err
	}
	if err := json.Unmarshal(body, &req); err != nil {
		return err
	}
	c.ResSuccessResult(jacocoService.AptExec(c.MantisContext, req))
	return nil
}

func (c *JacocoController) CallBack(req dto.CallBackReqDTO) {
	jacocoService.CallBack(c.MantisContext, req)
	c.ResSuccess()
}

package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ScanIssuesController struct {
	*controller.BaseController
}

var (
	DefaultScanIssuesController ScanIssuesController
	scanIssuesService           quality.ScanIssuesService
)

func (c *ScanIssuesController) List(req dto.ScanIssuesReqDTO, pageRequest gormx.PageRequest) {
	c.ResSuccessResult(scanIssuesService.QueryScanIssuesList(c.<PERSON>onte<PERSON>t, req, pageRequest))
}

func (c *ScanIssuesController) Components(req dto.SearchComponentReqDTO, request gormx.PageRequest) {
	c.ResSuccessResult(scanIssuesService.QueryUnitComponentList(c.Man<PERSON>ontext, req, request))
}

package quality

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeCommonController(rw http.ResponseWriter, req *http.Request) (*CommonController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &CommonController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeScanProgrammeController(rw http.ResponseWriter, req *http.Request) (*ScanProgrammeController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ScanProgrammeController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeScanProfileTemplateController(rw http.ResponseWriter, req *http.Request) (*ScanProfileTemplateController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ScanProfileTemplateController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeSonarRuleController(rw http.ResponseWriter, req *http.Request) (*SonarRuleController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &SonarRuleController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeScanIssuesController(rw http.ResponseWriter, req *http.Request) (*ScanIssuesController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ScanIssuesController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeTaskController(rw http.ResponseWriter, req *http.Request) (*TaskController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &TaskController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeScanFilterRuleController(rw http.ResponseWriter, req *http.Request) (*ScanFilterRuleController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ScanFilterRuleController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeBackController(rw http.ResponseWriter, req *http.Request) (*openapi.BackController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &openapi.BackController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeScanTestCiController(rw http.ResponseWriter, req *http.Request) (*openapi.ScanTestCiController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &openapi.ScanTestCiController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeJacocoController(rw http.ResponseWriter, req *http.Request) (*openapi.JacocoController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &openapi.JacocoController{
		BaseController: baseController,
	}
	return c, nil
}

package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/enums"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality/help"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type SonarRuleKeyInPathReq struct {
	Key string `path:"key"`
}

type SonarRuleController struct {
	*controller.BaseController
}

var (
	DefaultSonarRuleController SonarRuleController
	sonarRuleService           quality.SonarRuleService
)

func (c *SonarRuleController) SearchTemplateRules(search help.SonarRuleSearchReq) {
	c.ResSuccessResult(sonarRuleService.SearchRulesInTemplate(search, c.Mantis<PERSON>ontext))
}

func (c *SonarRuleController) RuleStatusList() {
	c.ResSuccessResult(enums.RuleStatusEnum{}.GetRuleStatusList())
}

func (c *SonarRuleController) RuleTypesList() {
	c.ResSuccessResult(enums.RuleTypeEnum{}.GetRuleTypeList())
}

func (c *SonarRuleController) ActiveOrDeActiveRule(req dto.ActiveDeActiveRuleReqDTO) {
	sonarRuleService.ActiveOrDeActiveTemplateRule(req, c.MantisContext)
	c.ResSuccess()
}

func (c *SonarRuleController) ShowRuleDetail(req SonarRuleKeyInPathReq) {
	c.ResSuccessResult(sonarRuleService.ShowRuleDetail(req.Key))
}

package quality

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/service/quality"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type TaskControllerIdIdInPathReq struct {
	Id int64 `path:"id"`
}

type TaskIdInPathReq struct {
	TaskId int64 `path:"taskId"`
}

type GetCommitInfoSchemaReq struct {
	Id       string `schema:"appId"`
	Branch   string `schema:"branch"`
	Page     int64  `schema:"page"`
	Size     int64  `schema:"size"`
	TimeZone string `schema:"timeZone"`
}

type HisIdInSchemaReq struct {
	HisId int64 `schema:"hisId"`
}

type HisIdInPathReq struct {
	HisId int64 `path:"hisId"`
}

type TaskController struct {
	*controller.BaseController
}

var (
	DefaultTaskController TaskController
	taskService           quality.TaskService
	gatesService          quality.ScanQualityGatesService
)

func (c *TaskController) GetCommitInfo(schemaReq GetCommitInfoSchemaReq) {
	c.ResSuccessResult(taskService.GetCommitInfo(c.MantisContext, schemaReq.Id, schemaReq.Page, schemaReq.Size, schemaReq.TimeZone, schemaReq.Branch))
}

func (c *TaskController) ExecuteScanTask(reqDto dto.ScanTestWebExecReqDTO) {
	taskService.ExecScanTestTask(c.MantisContext, reqDto, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) ExecuteTestTask(reqDto dto.ScanTestWebExecReqDTO) {
	taskService.ExecScanTestTask(c.MantisContext, reqDto, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) StopScanTask(req TaskControllerIdIdInPathReq) {
	taskService.StopScanTestTask(c.MantisContext, req.Id, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) StopTestTask(req TaskControllerIdIdInPathReq) {
	taskService.StopScanTestTask(c.MantisContext, req.Id, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) GetScanTestTask(req TaskIdInPathReq) {
	c.ResSuccessResult(taskService.GetScanTestTask(c.MantisContext, req.TaskId))
}

func (c *TaskController) InsertScanTask(configDTO dto.ScanTestTaskConfigDTO) {
	configDTO.TaskType = constants.ScanJobType
	c.ResSuccessResult(taskService.SaveTaskConfig(c.MantisContext, configDTO, commondto.UserInfo{AdAccount: c.GetUser(), CompanyID: c.GetCompany()}))
}

func (c *TaskController) InsertTestTask(configDTO dto.ScanTestTaskConfigDTO) {
	configDTO.TaskType = constants.UnitTestJobType
	c.ResSuccessResult(taskService.SaveTaskConfig(c.MantisContext, configDTO, commondto.UserInfo{AdAccount: c.GetUser(), CompanyID: c.GetCompany()}))
}

func (c *TaskController) DeleteScanTask(req TaskControllerIdIdInPathReq) {
	taskService.DeleteTask(c.MantisContext, req.Id, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) DeleteTestTask(req TaskControllerIdIdInPathReq) {
	taskService.DeleteTask(c.MantisContext, req.Id, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) UpdateScanTask(configDTO dto.ScanTestTaskConfigDTO) {
	configDTO.TaskType = constants.ScanJobType
	taskService.SaveTaskConfig(c.MantisContext, configDTO, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) UpdateTestTask(configDTO dto.ScanTestTaskConfigDTO) {
	configDTO.TaskType = constants.UnitTestJobType
	taskService.SaveTaskConfig(c.MantisContext, configDTO, commondto.UserInfo{AdAccount: c.GetUser()})
	c.ResSuccess()
}

func (c *TaskController) QueryQualityGate(req TaskIdInPathReq) {
	c.ResSuccessResult(gatesService.SelectByTaskId(c.MantisContext, req.TaskId))
}

func (c *TaskController) QueryTaskOverview(pathReq TaskIdInPathReq, schemaReq HisIdInSchemaReq) {
	c.ResSuccessResult(taskService.GetScanTestTaskOverview(c.MantisContext, pathReq.TaskId, schemaReq.HisId))
}

func (c *TaskController) QueryScanTestTaskPage(search dto.ScanTestTaskSearchDTO, req gormx.PageRequest) {
	if hubUrl := c.Request.Header.Get(commonconstants.GitRepoUrlKey); hubUrl != "" {
		search.HubUrl = hubUrl
		c.ResSuccessResult(taskService.GetScanTestTaskPageListInHub(c.MantisContext, search, req))
	} else {
		c.ResSuccessResult(taskService.GetScanTestTaskPageList(c.MantisContext, search, req))
	}
}

func (c *TaskController) QueryScanTestExecHisPageByTaskId(search dto.ScanTestTaskSearchDTO, req gormx.PageRequest) {
	c.ResSuccessResult(taskService.GetScanTestExecHisPageByTaskId(c.MantisContext, search, gormx.PageRequest{Page: req.Page, PageSize: req.PageSize}))
}

func (c *TaskController) GetLogsByHisId(req HisIdInPathReq) {
	c.ResSuccessResult(taskService.GetLogsByExecHisId(c.MantisContext, req.HisId))
}

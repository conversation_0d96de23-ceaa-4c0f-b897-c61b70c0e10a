package dao

import (
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type ScanTestTaskDao struct{}

func (ScanTestTaskDao) SelectTaskByLastExecHisId(ctx *commoncontext.MantisContext, execHisId int64) quality.ScanTestTask {
	sql := `select * from neptune_scan_test_task where last_scan_test_exec_his = ? and is_deleted = 'N'`
	res := quality.ScanTestTask{}
	gormx.RawX(ctx, sql, &res, execHisId)
	return res
}

func (ScanTestTaskDao) SelectScanHistoryListByTaskIdAndLatestHisId(ctx *commoncontext.MantisContext, taskId int64, hisId int64) []quality.ScanTestExecHis {
	sql := `select * from (select *, ROW_NUMBER() OVER (
      partition by gmt_date
      order by gmt_modified desc
    ) AS rn from (select lines, comment_lines, ncloc, date_trunc('day', gmt_modified) as gmt_date, gmt_modified from neptune_scan_test_exec_his where task_id = ? and id < ?) as his_t limit 20) as rn_t  where rn = 1`
	res := make([]quality.ScanTestExecHis, 0)
	gormx.RawX(ctx, sql, &res, taskId, hisId)
	return res
}

func (ScanTestTaskDao) SelectScanTestTaskList(ctx *commoncontext.MantisContext, search dto.ScanTestTaskSearchDTO, request gormx.PageRequest) (*gormx.PageResult, []dto.ScanTestTaskDetailDTO) {
	sql1 := `select id as ori_task_id, task_name, app_id, app_name, mode as task_mode, programme_id, branch, pdf_gen, operator, ship_pid, language, exec_type, code_url, job_type, "from",
       last_scan_test_exec_his, last_scan_test_exec_time, creator as task_creator, gmt_created as task_gmt_created, commit_time_frame, 
	   commit_base_id as t_commit_base_id, commit_compare_id as t_commit_compare_id
	    from neptune_scan_test_task where is_deleted = ?`
	params := make([]interface{}, 0)
	params = append(params, "N")
	if search.From != "" {
		sql1 += ` and "from" = ?`
		params = append(params, search.From)
	}
	if search.JobType != "" {
		sql1 += ` and job_type = ?`
		params = append(params, search.JobType)
	}
	if search.LastScanTestExecTime != "" {
		sql1 += ` and last_scan_test_exec_time >= ? and last_scan_test_exec_time <= ?`
		params = append(params, search.GetExecuteTimeStart(), search.GetExecuteTimeEnd())
	}
	if search.AppIds != nil {
		sql1 += ` and app_id in ?`
		params = append(params, search.AppIds)
	}
	if search.Branch != "" {
		sql1 += ` and branch in ?`
		params = append(params, strings.Split(search.Branch, ","))
	}
	if search.Search != "" {
		sql1 += ` and ((task_name ilike ?) or (app_name ilike ?))`
		s := "%" + search.Search + "%"
		params = append(params, s, s)
	}
	if search.CompanyId != "" {
		sql1 += ` and company_id = ?`
		params = append(params, search.CompanyId)
	}
	sql := `select * from (%s) t1 left join neptune_scan_test_exec_his t2 on t1.last_scan_test_exec_his = t2.id order by ori_task_id desc`
	tasks := make([]dto.ScanTestTaskDetailDTO, 0)
	pageResult, err := gormx.PageSelectByRaw(ctx, fmt.Sprintf(sql, sql1), &tasks, request, params...)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in select database"))
	}
	execHisIds := make([]int64, 0, len(tasks))
	for _, task := range tasks {
		execHisIds = append(execHisIds, task.LastScanTestExecHis)
	}
	return pageResult, tasks
}

func (ScanTestTaskDao) PageSelectRunningTaskByProgrammeId(ctx *commoncontext.MantisContext,
	programmeId int64, pageReq gormx.PageRequest,
) (*gormx.PageResult, []quality.ScanTestTask) {
	sql := `select "task".*, "his".status from "neptune_scan_test_task" as "task"
		left join "neptune_scan_test_exec_his" as "his" 
		on "task"."last_scan_test_exec_his" = "his"."id" 
		where "task"."programme_id" = ? and "task"."is_deleted" = 'N' and "his"."status" = 1`
	tasks := make([]quality.ScanTestTask, 0)
	res, err := gormx.PageSelectByRaw(ctx, sql, &tasks, pageReq, programmeId)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in select database"))
	}
	return res, tasks
}

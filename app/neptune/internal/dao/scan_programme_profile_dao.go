package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ScanProgrammeProfileDao struct{}

func (ScanProgrammeProfileDao) UpdateNameAndKeyByIds(ctx *commoncontext.MantisContext, cusName string, cusKey string, id int64) {
	sql := `update neptune_scan_programme_profile set cus_name=?,cus_key=? where id = ?`
	gormx.ExecX(ctx, sql, cusName, cusKey, id)
}

func (ScanProgrammeProfileDao) SetProfileActive(ctx *commoncontext.MantisContext, profileId int64, active bool) int32 {
	sql := `update neptune_scan_programme_profile set is_active=? , gmt_modified=now() where id=?`
	return int32(gormx.ExecX(ctx, sql, active, profileId))
}

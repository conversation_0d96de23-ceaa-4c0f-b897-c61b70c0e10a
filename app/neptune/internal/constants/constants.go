package constants

const (

	// 默认是
	NotDefault = 0
	IsDefault  = 1
	IsFalse    = 0
	IsTrue     = 1
	// UserDevops 流水线用户
	UserDevops = "DevOps"
	// UserTimer 定时任务用户
	UserTimer = "Timer"

	// ProductScanDoneReportPath 完成报告地址
	ProductScanDoneReportPath = "%v/magic/productScan/task/%v/%v?drawerId=report&drawerParams=%v"
	// ProductScanRunningReportPath 进行中报告地址
	ProductScanRunningReportPath = "%v/magic/productScan/task/%v/%v"

	// QualityScanReportPath 代码扫描报告地址
	QualityScanReportPath     = "%s/magic/codeScan/task/detail?mode=TaskOverview&taskId=%d&name=%s&appName=%s&appId=%d"
	QualityUnitTestReportPath = "%s/magic/codeScan/unit/detail?mode=ScanHistory&taskId=%d&appName=%s&appId=%d"
	// StandardIncrement 标准增量
	StandardIncrement = "increment"

	// StandardTotal 标准全量
	StandardTotal = "total"

	// StandardMixInc 标准全量+增量
	StandardMixInc = "mix-inc"
	StandardMixTol = "mix-tol"

	// SourcePipeline ship来源
	SourcePipeline = "pipeline"
	SourceWeb      = "web"

	// SuccessStatus 原始状态
	SuccessStatus  = "SUCCESS"
	FailureStatus  = "FAILURE"
	AbortedStatus  = "ABORTED"
	FinalizedParse = "FINALIZED"

	// ScanJobType 业务类型
	ScanJobType     = "scan"
	UnitTestJobType = "unit"
	CoverageJobType = "coverage"

	// GitUrlSuffix git url 后缀
	GitUrlSuffix = ".git"

	Slash     = "/"
	SlashCode = "%2F"

	RuleActive   = true
	RuleDeActive = false

	ExecTypeFront = "FRONTEND"
	ExecTypeBack  = "BACKEND"

	// filter config type
	FilterConfigTypeApp       = "app"
	FilterConfigTypeTask      = "task"
	FilterConfigTypeProgramme = "programme"
	FilterConfigExclusion     = "exclusion"
	FilterConfigInclusion     = "inclusion"

	ScanTestQualityGatePass = "pass"
	ScanTestQualityGateFail = "fail"
)

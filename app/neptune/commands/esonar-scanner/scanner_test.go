package esonar_scanner

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"strings"
	"testing"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
	"golang.org/x/text/encoding/simplifiedchinese"
)

func TestDir(t *testing.T) {
	dir, err := os.ReadDir("C:\\Users\\<USER>\\Desktop\\testdoc")
	if err != nil {
		log.Panic(err)
	}
	for _, en := range dir {
		fmt.Println(en.Name())
	}
}

func TestGenMvnSetting(t *testing.T) {
	err2 := os.MkdirAll("C:\\Users\\<USER>\\Desktop\\tfiles\\x1\\x2", 0o755)
	if err2 != nil {
		fmt.Println(err2)
	}
	/*err := unit.genMvnSetting("http://maven.aliyun.com/nexus/content/groups/public", "", "",
		"C:\\Users\\<USER>\\Desktop\\tfiles\\x1\\x2\\setting.xmlx")
	if err != nil {
		fmt.Println(err)
	}*/
}

func TestExec(t *testing.T) {
	command := exec.Command("cmd.exe", "/c", "dir")
	output, err := command.Output()
	if err != nil {
		fmt.Println("ReadAll error:", err)
		return
	}
	decoder := simplifiedchinese.GBK.NewDecoder()
	bytes, err := decoder.Bytes(output)
	if err != nil {
		fmt.Println("ReadAll error:", err)
		return
	}
	fmt.Println(string(bytes))
}

func TestData(t *testing.T) {
	timestampInSeconds := int64(1723030937)
	timePtr := time.Unix(timestampInSeconds, 0)
	formattedTime := timePtr.Format("2006-01-02T15:04:05+0000")
	fmt.Println(formattedTime)
}

func TestReadFile(t *testing.T) {
	file, err := os.ReadFile("C:\\Users\\<USER>\\Desktop\\tfiles\\java.diff")
	if err != nil {
		fmt.Println(err)
	}
	split := strings.Split(string(file), "\n")
	for _, s := range split {
		sprintf := fmt.Sprintf(s)
		if strings.Contains(sprintf, "cube-magic-base") {
			fmt.Println(sprintf)
		}
	}
}

func TestSonar(t *testing.T) {
	sonarParam := params.QualityScanSonarParam{
		SonarCallback: "http://cube.anlink.com/magic/api/rick/api/magicScan/sonarUpdateBackCall/199",
		SonarToken:    "squ_065f0cbc372225bfd16ea83c3fb6df25a948af0a",
		SonarUrl:      "http://172.28.38.31:9000",
		SonarProfile:  "xxxxx",
		SonarKey:      "scan-" + uuid.NewUUID(),
	}
	createSonarProject(sonarParam, "java")
}

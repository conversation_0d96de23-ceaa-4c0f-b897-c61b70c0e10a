package params

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/spf13/pflag"
)

const (
	TypeScan        = "scan"
	TypeUnit        = "unit"
	ModeIncrement   = "increment"
	ModeTotal       = "total"
	AppTypeFront    = "front"
	LanguageJava    = "java"
	MavenConfigPath = "/usr/share/maven/conf/settings.xml"
	JavaVersion8    = "Java8"
	JavaVersion11   = "Java11"
	JavaVersion17   = "Java17"
	Jdk8Home        = "/tools/jdk8"
	Jdk11Home       = "/usr/lib/jvm/java-11-oracle"
	Jdk17Home       = "/tools/jdk17"
)

type QualityScanBizParam struct {
	// 业务参数
	CallBack    string `json:"callBack"`     // 任务记录id
	Type        string `json:"type" `        // 任务类型  unit(单测)/scan(扫描)
	Mode        string `json:"mode" `        // 模式: increment(增量)/total(全量)
	Language    string `json:"language" `    // 语言: java
	JavaVersion string `json:"javaVersion" ` // java版本: Java8/Java11/Java17
	ModulePath  string `json:"modulePath"`   // 模块路径
	AppType     string `json:"appType"`      // 应用类型: 前端/后端 front/backend
}

type QualityScanCodeParam struct {
	// 代码相关参数
	CodeUrl         string              `json:"codeUrl" `         // 仓库地址: http://xx.xx.git
	GitUser         string              `json:"gitUser" `         // 仓库用户名: root
	GitToken        string              `json:"gitToken" `        // 仓库密码: 123456
	Branch          string              `json:"branch" `          // 分支: master
	BaseCommitId    string              `json:"baseCommitId" `    // 基准commitId
	CompareCommitId string              `json:"compareCommitId" ` // 比较commitId
	MvnParam        QualityScanMvnParam `json:"MvnParam" `        // MvnParam
	// genParam
	ModulePath string `json:"modulePath"` // 模块路径
	JavaHome   string `json:"javaHome"`   // 语言对应的java home
}

type QualityScanSonarParam struct {
	// sonar 参数
	SonarUrl      string `json:"sonarUrl" `      // sonar 地址
	SonarToken    string `json:"sonarToken" `    // sonar token
	SonarPdf      bool   `json:"sonarPdf" `      // 是否生成pdf
	SonarProfile  string `json:"sonarProfile" `  // 规则集名称
	SonarCallback string `json:"sonarCallback" ` // 回调地址
	ExclusionPath string `json:"exclusionPath" ` // 排除的路径: src/test/java/*
	InclusionPath string `json:"inclusionPath"`  // 包含的路径: src/test/java/*
	// genParam
	SonarKey      string
	ExecCommitId  string `json:"execCommitId"`  // 执行的commitId
	ExecDate      string `json:"execDate"`      // 执行的commitId对应的时间
	SonarJavaHome string `json:"sonarJavaHome"` // 语言对应的 sonar home
	HookUseful    bool   `json:"hookUseful"`    // 是否回调
}

type QualityScanMvnParam struct {
	// sonar 参数
	MvnRepo string `json:"mvnRepo" `
	MvnUser string `json:"mvnUser" `
	MvnPw   string `json:"mvnPw" `
}
type Params struct {
	BizParam   QualityScanBizParam
	CodeParam  QualityScanCodeParam
	SonarParam QualityScanSonarParam
}

func paramInit(cf *pflag.FlagSet) (error, *Params) {
	params := &Params{}
	// 初始化业务参数
	callBack, err := cf.GetString("call-back")
	if err != nil {
		return err, params
	}
	params.BizParam.CallBack = callBack
	taskType, err := cf.GetString("type")
	if err != nil {
		return err, params
	}
	params.BizParam.Type = taskType
	mode, err := cf.GetString("mode")
	if err != nil {
		return err, params
	}
	params.BizParam.Mode = mode
	language, err := cf.GetString("language")
	if err != nil {
		return err, params
	}
	params.BizParam.Language = language
	javaVersion, err := cf.GetString("java-version")
	if err != nil {
		return err, params
	}
	params.BizParam.JavaVersion = javaVersion
	appType, err := cf.GetString("app-type")
	if err != nil {
		return err, params
	}
	params.BizParam.AppType = appType
	modulePath, err := cf.GetString("module-path")
	if err != nil {
		return err, params
	}
	params.BizParam.ModulePath = strings.TrimLeft(strings.TrimLeft(modulePath, "./"), "/")
	params.CodeParam.ModulePath = params.BizParam.ModulePath
	// 初始化code参数
	codeUrl, err := cf.GetString("code-url")
	if err != nil {
		return err, params
	}
	params.CodeParam.CodeUrl = codeUrl
	gitUser, err := cf.GetString("git-user")
	if err != nil {
		return err, params
	}
	params.CodeParam.GitUser = gitUser
	gitToken, err := cf.GetString("git-token")
	if err != nil {
		return err, params
	}
	params.CodeParam.GitToken = gitToken
	branch, err := cf.GetString("branch")
	if err != nil {
		return err, params
	}
	params.CodeParam.Branch = branch
	baseCommitId, err := cf.GetString("base-commit-id")
	if err != nil {
		return err, params
	}
	params.CodeParam.BaseCommitId = baseCommitId
	compareCommitId, err := cf.GetString("compare-commit-id")
	if err != nil {
		return err, params
	}
	params.CodeParam.CompareCommitId = compareCommitId
	sonarUrl, err := cf.GetString("sonar-url")
	if err != nil {
		return err, params
	}
	params.SonarParam.SonarUrl = sonarUrl
	sonarToken, err := cf.GetString("sonar-token")
	if err != nil {
		return err, params
	}
	params.SonarParam.SonarToken = sonarToken
	sonarPdf, err := cf.GetBool("sonar-pdf")
	if err != nil {
		return err, params
	}
	params.SonarParam.SonarPdf = sonarPdf
	sonarProfile, err := cf.GetString("sonar-profile")
	if err != nil {
		return err, params
	}
	params.SonarParam.SonarProfile = sonarProfile
	sonarCallback, err := cf.GetString("sonar-callback")
	if err != nil {
		return err, params
	}
	params.SonarParam.SonarCallback = sonarCallback
	sonarExclusion, err := cf.GetString("sonar-exclusion")
	if err != nil {
		return err, params
	}
	params.SonarParam.ExclusionPath = sonarExclusion
	sonarInclusion, err := cf.GetString("sonar-inclusion")
	if err != nil {
		return err, params
	}
	params.SonarParam.InclusionPath = sonarInclusion
	mvnRepo, err := cf.GetString("mvn-repo")
	if err != nil {
		return err, params
	}
	params.CodeParam.MvnParam.MvnRepo = mvnRepo
	mvnUser, err := cf.GetString("mvn-user")
	if err != nil {
		return err, params
	}
	params.CodeParam.MvnParam.MvnUser = mvnUser
	mvnPw, err := cf.GetString("mvn-pw")
	if err != nil {
		return err, params
	}
	params.CodeParam.MvnParam.MvnPw = mvnPw
	return nil, params
}

func CheckAndInit(cf *pflag.FlagSet) (error, *Params) {
	err, params := paramInit(cf)
	if err != nil {
		return fmt.Errorf("初始化参数报错!err=%s", err), params
	}
	switch params.BizParam.Type {
	case TypeUnit:
		if strings.ToLower(params.BizParam.Language) != LanguageJava {
			return fmt.Errorf("单元测试任务目前只支持java语言! language=%s", params.BizParam.Language), params
		}
		params.SonarParam.SonarJavaHome = Jdk11Home
		if params.BizParam.JavaVersion == JavaVersion17 {
			params.CodeParam.JavaHome = Jdk17Home
		} else if params.BizParam.JavaVersion == JavaVersion11 {
			params.CodeParam.JavaHome = Jdk11Home
		} else {
			params.CodeParam.JavaHome = Jdk8Home
		}
		break
	case TypeScan:
		break
	default:
		return fmt.Errorf("任务类型 %s 不合法! ", params.BizParam.Type), params
	}
	params.SonarParam.SonarKey = params.BizParam.Type + "-" + uuid.NewUUID()
	return err, params
}

// NoCodeCheck 不需要代码扫描的场景
func NoCodeCheck(code QualityScanCodeParam, mode, callbackUrl string) bool {
	if mode == ModeTotal {
		return false
	}
	log.Printf("增量模式,commit id 检查! ")
	if code.CompareCommitId != code.BaseCommitId {
		return false
	}
	log.Printf("base commit id (%s)和 compare commit id (%s)相同, 不需要扫描!", code.BaseCommitId, code.CompareCommitId)
	log.Println("流程结束! 不再执行扫描/单测任务!")
	callBackBody, _ := json.Marshal(map[string]any{
		"status": "SUCCESS",
		"qualityGate": map[string]any{
			"conditions": []map[string]any{
				{
					"metric": "new_line_coverage",
					"value":  "100",
				},
			},
		},
	})
	httpClient := netutil.NewHttpClient()
	createRequest := &netutil.HttpRequest{
		RawURL: callbackUrl,
		Method: http.MethodPost,
		Body:   callBackBody,
	}
	resp, err := httpClient.SendRequest(createRequest)
	if err != nil || resp.StatusCode != http.StatusOK {
		log.Printf("回调主服务失败！resp=%v,err=%v", resp, err)
	}
	log.Printf("回调主服务成功!%v", resp)
	return true
}

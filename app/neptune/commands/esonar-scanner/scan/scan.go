package scan

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
)

type IScanCodeScanner struct{}

func (u *IScanCodeScanner) DiffCode(appPath string, bizParam params.QualityScanBizParam, codeParam params.QualityScanCodeParam) string {
	if params.ModeTotal == bizParam.Mode {
		return ""
	}
	log.Printf("增量模式,计算文件差异")
	var gitCmd *exec.Cmd
	if params.AppTypeFront == strings.ToLower(bizParam.AppType) {
		log.Println("前端增量逻辑!")
		gitCmd = exec.Command("/bin/bash", "-c", "cd "+appPath+" && git diff --name-only "+codeParam.BaseCommitId+" "+
			codeParam.CompareCommitId+" | grep -E '*.js|*.jsx|*.ts|*.tsx|*.vue' > ./diff.diff")

	} else {
		log.Println("后端语言逻辑逻辑!")
		gitCmd = exec.Command("/bin/bash", "-c", "cd "+appPath+" && git diff --name-only "+codeParam.BaseCommitId+" "+
			codeParam.CompareCommitId+" > ./diff.diff")
	}
	err := utils.SyncOutLog(gitCmd)
	if err != nil {
		log.Println("获取增量差异失败!执行增量非diff逻辑")
		return ""
	}
	file, err := os.ReadFile(appPath + "/diff.diff")
	if err != nil {
		return ""
	}
	content := string(file)
	if content == "" {
		return ""
	}
	scanFile := ""
	split := strings.Split(content, "\n")
	if len(split) > 100 {
		log.Printf("增量文件个数%d > 100个,不再进行筛选! \n ", len(split))
		return scanFile
	}
	for _, s := range split {
		if s != "" && strings.Contains(s, bizParam.ModulePath) {
			replaceS := strings.Replace(s, bizParam.ModulePath, "**", 1)
			log.Println("增量文件===> ", replaceS)
			scanFile += replaceS + ","
		}
	}
	return scanFile
}

func (u *IScanCodeScanner) CodeCusDeal(appPath string, codeParam params.QualityScanCodeParam) error {
	checkCmd := exec.Command("/bin/bash", "-c", "cd "+appPath+" && git checkout -f "+codeParam.CompareCommitId)
	return utils.SyncOutLog(checkCmd)
}

func (u *IScanCodeScanner) ScannerCli(appPath, language, modulePath, diffInclude string, sonarP params.QualityScanSonarParam) error {
	if sonarP.InclusionPath != "" {
		diffInclude += "," + sonarP.InclusionPath
	}
	pdfSkip := !sonarP.SonarPdf
	sonarCmdTemp := "sonar-scanner -Dsonar.host.url=%s -Dsonar.projectKey=%s -Dsonar.projectName=%s -Dsonar.projectDate=%s " +
		"-Dsonar.projectVersion=%s -Dsonar.language=%s -Dsonar.sources=. -Dsonar.scm.disabled=false " +
		"-Dsonar.scm.provider=git -Dsonar.analysis.isHookUseful=%s -Dsonar.exclusions=%s " +
		"-Dsonar.inclusions=%s -Dsonar.pdf.skip=%v -Dsonar.login=%s -Dsonar.analysis.isHookUseful=%v "
	sonarCmdP := fmt.Sprintf(sonarCmdTemp, sonarP.SonarUrl, sonarP.SonarKey, sonarP.SonarKey, sonarP.ExecDate,
		sonarP.ExecCommitId, language,
		sonarP.SonarCallback, sonarP.ExclusionPath,
		diffInclude, pdfSkip, sonarP.SonarToken, sonarP.HookUseful)
	execPath := path.Join(appPath, modulePath)
	if params.LanguageJava == strings.ToLower(language) {
		err := os.MkdirAll(path.Join(execPath, "target", "classes"), 0o755)
		if err != nil {
			log.Println("创建 target/classes 失败!")
		}
		sonarCmdP += "-Dsonar.java.source=1.8 -Dsonar.java.binaries=target/classes "
	}
	log.Printf("sonar 命令:%s", sonarCmdP)
	command := exec.Command("/bin/sh", "-c", "cd "+execPath+
		" && export JAVA_HOME="+sonarP.SonarJavaHome+
		" && "+sonarCmdP)
	return utils.SyncOutLog(command)
}

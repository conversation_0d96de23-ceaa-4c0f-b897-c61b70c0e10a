package unit

import (
	"fmt"
	"log"
	"os"
)

const mvnSettingTemplate = "<settings>\n" +
	"<mirrors>\n" +
	"<mirror>\n" +
	"<id>magic-mvn</id>\n" +
	"<name>magic-mvn</name>\n" +
	"<url>%s</url>\n" +
	"<mirrorOf>*</mirrorOf>\n" +
	"</mirror>\n" +
	"</mirrors>\n" +
	"<servers>\n" +
	"<server>\n" +
	"<id>magic-mvn</id>\n" +
	"<username>%s</username>\n" +
	"<password>%s</password>\n" +
	"</server>\n" +
	"</servers>\n" +
	"</settings>"

func genMvnSetting(url, user, pw, outputPath string) error {
	if url == "" {
		log.Println("mvn repo url is empty, use aliyun mvn repo !")
		url = "http://maven.aliyun.com/nexus/content/groups/public"
	}
	sprintf := fmt.Sprintf(mvnSettingTemplate, url, user, pw)
	err := os.WriteFile(outputPath, []byte(sprintf), 0o644)
	return err
}

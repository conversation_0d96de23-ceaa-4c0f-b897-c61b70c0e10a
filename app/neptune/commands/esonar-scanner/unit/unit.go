package unit

import (
	"fmt"
	"log"
	"os/exec"
	"path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
)

type IUnitCodeScanner struct{}

func (u *IUnitCodeScanner) CodeCusDeal(appPath string, codeParam params.QualityScanCodeParam) error {
	checkCmd := exec.Command("/bin/bash", "-c", "cd "+appPath+" && git checkout -f "+codeParam.CompareCommitId)
	log.Printf("git check : %s", checkCmd.String())
	err := utils.SyncOutLog(checkCmd)
	if err != nil {
		return fmt.Errorf("check commit error! %v", err)
	}
	mp := codeParam.MvnParam
	log.Printf("初始化mvn配置！%+v", mp)
	err = genMvnSetting(mp.MvnRepo, mp.MvnUser, mp.MvnPw, params.MavenConfigPath)
	if err != nil {
		return fmt.Errorf("生成mvn配置文件报错!%v", err)
	}
	execPath := path.Join(appPath, codeParam.ModulePath)
	testCmd := exec.Command("/bin/bash", "-c", "cd "+execPath+" && "+
		"export JAVA_HOME="+codeParam.JavaHome+" && "+
		"mvn -B -U  org.jacoco:jacoco-maven-plugin:0.8.5:prepare-agent test -Dmaven.test.failure.ignore=true '-Dtest=*Test' "+
		"-DfailIfNoTests=false -Dautoconfig.skip")
	log.Printf("执行 mvn test 命令: %s", testCmd.String())
	err = utils.SyncOutLog(testCmd)
	if err != nil {
		return fmt.Errorf("单元测试执行异常！")
	}
	reportCmd := exec.Command("/bin/bash", "-c", "cd "+execPath+
		" && mvn org.jacoco:jacoco-maven-plugin:0.8.5:report")
	log.Printf("执行报告生成命令: %s", reportCmd.String())
	return utils.SyncOutLog(reportCmd)
}

func (u *IUnitCodeScanner) ScannerCli(appPath, language, modulePath, diffInclude string, sonarP params.QualityScanSonarParam) error {
	pdfSkip := !sonarP.SonarPdf
	sonarCmdTemp := "mvn -Duser.timezone=Asia/Shanghai -Dsonar.host.url=%s -Dsonar.projectKey=%s -Dsonar.projectName=%s " +
		"org.sonarsource.scanner.maven:sonar-maven-plugin:sonar " +
		"-Dsonar.projectDate=%s  -Dsonar.projectVersion=%s -Dsonar.analysis.isHookUseful=%s " +
		"-Dsonar.coverage.exclusions=%s  -Dsonar.pdf.skip=%v -Dsonar.login=%s -Dsonar.analysis.isHookUseful=%v" +
		"-Dsonar.scm.provider=git -Dsonar.java.source=1.8 -Dsonar.java.binaries=target/classes -Dsonar.language=java " +
		"-Dsonar.sources=src/ -Dsonar.scm.disabled=false -Dsonar.coverage.jacoco.xmlReportPaths=target/site/jacoco/jacoco.xml"
	sonarCmdP := fmt.Sprintf(sonarCmdTemp, sonarP.SonarUrl, sonarP.SonarKey, sonarP.SonarKey,
		sonarP.ExecDate, sonarP.ExecCommitId, sonarP.SonarCallback,
		sonarP.ExclusionPath, pdfSkip, sonarP.SonarToken, sonarP.HookUseful)
	execPath := path.Join(appPath, modulePath)
	command := exec.Command("/bin/sh", "-c", "cd "+execPath+
		" && export JAVA_HOME="+sonarP.SonarJavaHome+
		" && "+sonarCmdP)
	log.Printf("sonar 命令:%s", sonarCmdP)
	return utils.SyncOutLog(command)
}

func (u *IUnitCodeScanner) DiffCode(appPath string, bizParam params.QualityScanBizParam, codeParam params.QualityScanCodeParam) string {
	return ""
}

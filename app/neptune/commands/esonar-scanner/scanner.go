package esonar_scanner

import (
	"encoding/base64"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os/exec"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/scan"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/esonar-scanner/unit"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
	"github.com/duke-git/lancet/v2/netutil"
)

const (
	gitCloneTem   = "git clone -b %s %s://%s:%s@%s %s"
	gitDateFormat = "cd %s && git log --pretty=format:%%ct %s -1"
)

type CodeScanner interface {
	CodeCusDeal(appPath string, codeParam params.QualityScanCodeParam) error
	ScannerCli(appPath, language, modulePath, diffInclude string, params params.QualityScanSonarParam) error
	DiffCode(appPath string, bizParam params.QualityScanBizParam, codeParam params.QualityScanCodeParam) string
}

func Execute(pp *params.Params) error {
	rootPath := "/root/workspace/scanning"
	err := gitClone(rootPath, pp.CodeParam)
	if err != nil {
		return err
	}
	log.Println("app path -> ", rootPath)
	err = createSonarProject(pp.SonarParam, pp.BizParam.Language)
	if err != nil {
		return fmt.Errorf("创建sonar project 失败！,%v", err)
	}
	err, baseDate, compareDate := gitCommitDate(rootPath, pp.CodeParam)
	if err != nil {
		return fmt.Errorf("获取commit 提交时间失败 ")
	}
	var privateScan CodeScanner
	if pp.BizParam.Type == "scan" {
		privateScan = &scan.IScanCodeScanner{}
	} else {
		privateScan = &unit.IUnitCodeScanner{}
	}
	log.Println("扫描 base commit id ... ", pp.CodeParam.BaseCommitId)
	err = scanCore(privateScan, pp, rootPath, pp.CodeParam.BaseCommitId, baseDate, true)
	if err != nil {
		return err
	}
	if params.ModeIncrement == pp.BizParam.Mode {
		log.Println("扫描 compare commit id ... ", pp.CodeParam.CompareCommitId)
		return scanCore(privateScan, pp, rootPath, pp.CodeParam.CompareCommitId, compareDate, false)
	}
	return nil
}

// scanCore 扫描核心流程！
func scanCore(privateScan CodeScanner, pp *params.Params, appPath, execCid, execDate string, first bool) error {
	if first {
		err := privateScan.CodeCusDeal(appPath, pp.CodeParam)
		if err != nil {
			return err
		}

	}
	diff := privateScan.DiffCode(appPath, pp.BizParam, pp.CodeParam)
	if params.ModeIncrement == pp.BizParam.Mode && first {
		pp.SonarParam.HookUseful = false
	} else {
		pp.SonarParam.HookUseful = true
	}
	pp.SonarParam.ExecCommitId = execCid
	pp.SonarParam.ExecDate = execDate
	return privateScan.ScannerCli(appPath, pp.BizParam.Language, pp.BizParam.ModulePath, diff, pp.SonarParam)
}

// 获取两次commit id 对应的时间
func gitCommitDate(appPath string, code params.QualityScanCodeParam) (error, string, string) {
	gitDataCmd := exec.Command("/bin/sh", "-c", fmt.Sprintf(gitDateFormat, appPath, code.BaseCommitId))
	baseDate, err := gitDataCmd.Output()
	if err != nil {
		return err, "", ""
	}
	gitDataCmd2 := exec.Command("/bin/sh", "-c", fmt.Sprintf(gitDateFormat, appPath, code.CompareCommitId))
	compareDate, err := gitDataCmd2.Output()
	if err != nil {
		return err, "", ""
	}
	base, err := strconv.ParseInt(string(baseDate), 10, 64)
	if err != nil {
		return err, "", ""
	}
	compare, err := strconv.ParseInt(string(compareDate), 10, 64)
	if err != nil {
		return err, "", ""
	}
	if base > compare {
		temp := compare
		compare = base
		base = temp
	}
	return nil, time.Unix(base, 0).Format("2006-01-02T15:04:05+0000"), time.Unix(compare, 0).Format("2006-01-02T15:04:05+0000")
}

// gitClone 克隆项目
func gitClone(rootPath string, codeParam params.QualityScanCodeParam) error {
	log.Println("clone code ...")
	codeSplit := strings.SplitN(codeParam.CodeUrl, "://", 2)
	gitCmd := exec.Command("/bin/sh", "-c", fmt.Sprintf(gitCloneTem, codeParam.Branch, codeSplit[0], codeParam.GitUser, codeParam.GitToken, codeSplit[1], rootPath))
	return utils.SyncOutLog(gitCmd)
}

// createSonarProject 创建sonar项目
func createSonarProject(sonarParam params.QualityScanSonarParam, language string) error {
	sonarKey := sonarParam.SonarKey
	httpClient := netutil.NewHttpClient()
	auth := base64.StdEncoding.EncodeToString([]byte(sonarParam.SonarToken + ":"))
	header := make(http.Header)
	header.Add("Authorization", "Basic "+auth)
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	log.Printf("创建项目 -> %s", sonarKey)
	createData := make(url.Values)
	createData.Add("project", sonarKey)
	createData.Add("name", sonarKey)
	createRequest := &netutil.HttpRequest{
		RawURL:   sonarParam.SonarUrl + "/api/projects/create",
		Method:   http.MethodPost,
		FormData: createData,
		Headers:  header,
	}
	resp, err := httpClient.SendRequest(createRequest)
	if err != nil || resp.StatusCode != 200 {
		return fmt.Errorf("创建sonar项目失败!resp=%v,resp=%+v", err, resp)
	}
	log.Printf("创建项目响应！%+v", resp)
	log.Printf("绑定规则 -> %s", sonarKey)
	bindData := make(url.Values)
	bindData.Add("project", sonarKey)
	bindData.Add("qualityProfile", sonarParam.SonarProfile)
	bindData.Add("language", language)
	bindRequest := &netutil.HttpRequest{
		RawURL:   sonarParam.SonarUrl + "/api/qualityprofiles/add_project",
		Method:   http.MethodPost,
		FormData: bindData,
		Headers:  header,
	}
	resp, _ = httpClient.SendRequest(bindRequest)
	log.Printf("绑定规则响应！%+v", resp)
	log.Printf("创建 webhock -> %s", sonarKey)
	wData := make(url.Values)
	wData.Add("project", sonarKey)
	wData.Add("name", "sonar.webhooks.project")
	wData.Add("url", sonarParam.SonarCallback)
	wRequest := &netutil.HttpRequest{
		RawURL:   sonarParam.SonarUrl + "/api/webhooks/create",
		Method:   http.MethodPost,
		FormData: wData,
		Headers:  header,
	}
	resp, err = httpClient.SendRequest(wRequest)
	if err != nil || resp.StatusCode != 200 {
		return fmt.Errorf("创建webhock失败!err=%v,resp=%+v", err, resp)
	}
	log.Printf("创建webhock响应！%+v", resp)
	return err
}

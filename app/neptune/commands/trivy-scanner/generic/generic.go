package generic

import (
	"bytes"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path"
	"strings"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"

	"github.com/spf13/pflag"
)

type GenericScanner struct {
	*trivyscanner.BaseScanner
	filename                      string
	artifactDownloadUrl           string
	artifactDownloadToken         string
	artifactDownloadAuthorization string
}

func NewGenericScanner(f *pflag.FlagSet) (*GenericScanner, error) {
	baseScanner, err := trivyscanner.NewBaseScanner(f)
	if err != nil {
		return nil, err
	}
	err = baseScanner.IgnoreFile()
	if err != nil {
		return nil, err
	}

	filename, err := f.GetString("filename")
	if err != nil {
		return nil, err
	}
	artifactDownloadUrl, err := f.GetString("artifact-download-url")
	if err != nil {
		return nil, err
	}
	artifactDownloadToken, err := f.GetString("artifact-download-token")
	if err != nil {
		return nil, err
	}
	artifactDownloadAuthorization, err := f.GetString("artifact-download-authorization")
	if err != nil {
		return nil, err
	}
	return &GenericScanner{
		BaseScanner:                   baseScanner,
		filename:                      filename,
		artifactDownloadUrl:           artifactDownloadUrl,
		artifactDownloadToken:         artifactDownloadToken,
		artifactDownloadAuthorization: artifactDownloadAuthorization,
	}, nil
}

func (s *GenericScanner) Exec() error {
	log.Println("制品名称: ", s.filename)
	log.Printf("制品下载地址: %s", s.artifactDownloadUrl)
	filePath := path.Join("/root/", s.filename)
	/*filePath = fmt.Sprintf(`"%s"`, filePath)
	// 下载制品文件
	artifactDownloadUrl := fmt.Sprintf(`"%s"`, s.artifactDownloadUrl)
	var loginCmd *exec.Cmd
	if s.artifactDownloadToken != "" {
		loginCmd = exec.Command("curl", "-sS", "-X", "GET", artifactDownloadUrl,
			"-H", fmt.Sprintf(`"Token:%s"`, s.artifactDownloadToken), "--output", filePath)
	} else {
		loginCmd = exec.Command("curl", "-sS", "-X", "GET", artifactDownloadUrl,
			"-H", fmt.Sprintf(`"Authorization:Basic %s"`, s.artifactDownloadAuthorization), "--output", filePath)
	}
	log.Println(loginCmd.String())
	err := utils.SyncOutLog(loginCmd)*/
	err := downloadFile(s.artifactDownloadUrl, s.artifactDownloadToken, s.artifactDownloadAuthorization, filePath)
	if err != nil {
		log.Println("下载制品失败", err)
		return err
	}

	// 解压
	var shell string
	if strings.HasSuffix(s.filename, ".zip") {
		shell = fmt.Sprintf("unzip %s -d /root/scanning", filePath)
	} else if strings.HasSuffix(s.filename, ".tar.gz") || strings.HasSuffix(s.filename, ".tar") {
		shell = fmt.Sprintf("tar -xf %s -C /root/scanning", filePath)
	}

	var scanPath string
	if shell != "" {
		scanPath = "/root/scanning"
		err = os.MkdirAll(scanPath, os.ModePerm)
		if err != nil {
			return err
		}
		buf := bytes.NewBufferString(shell)
		err = ioutil.WriteFile("/root/extract.sh", buf.Bytes(), os.ModePerm)
		if err != nil {
			return err
		}
		err = utils.SyncOutLog(exec.Command("sh", "-x", "/root/extract.sh"))
		if err != nil {
			return err
		}
	} else {
		scanPath = filePath
	}
	filePath = fmt.Sprintf(`"%s"`, filePath)
	// trivy扫描
	arg := []string{
		"rootfs", scanPath, "--ignorefile", trivyscanner.IgnoreFile, "--skip-db-update", "--skip-java-db-update", "--scanners", "vuln",
		"--timeout", "15m", "--format", "template", "--template", "@/root/gitlab.tpl", "--output", trivyscanner.ResultFilePath,
	}
	trivyCmd := exec.Command("trivy", arg...)
	log.Println(trivyCmd.String())
	err = utils.SyncOutLog(trivyCmd)
	if err != nil {
		return err
	}

	return nil
}

func downloadFile(url string, token, auth, filePath string) error {
	client := &http.Client{}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Println("Error creating request:", err)
		return err
	}
	if token != "" {
		req.Header.Set("Token", token)
	} else {
		req.Header.Set("Authorization", "Basic "+auth)
	}
	req.Header.Set("Accept", "application/octet-stream")
	log.Printf("req header %+v", req.Header)
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("resp.StatusCode is not 200 ,is %d", resp.StatusCode)
	}
	// 创建文件用于保存下载的数据
	file, err := os.Create(filePath)
	if err != nil {
		log.Println("Error creating the file:", err)
		return err
	}
	defer file.Close()
	_, err = io.Copy(file, resp.Body)
	return err
}

package trivy_scanner

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"

	"github.com/spf13/pflag"
)

const (
	ResultFilePath = "/root/output.json"
	IgnoreFile     = "/root/trivyignore"
)

type Scanner interface {
	Exec() error
	UploadResult() error
}

type BaseScanner struct {
	cubeMantistUrl  string
	uploadResultUrl string
	whiteCVE        string
}

func NewBaseScanner(f *pflag.FlagSet) (*BaseScanner, error) {
	cubeMantistUrl, err := f.GetString("cube-mantis-url")
	if err != nil {
		return nil, err
	}
	uploadResultUrl, err := f.GetString("upload-result-url")
	if err != nil {
		return nil, err
	}
	whiteCVE, err := f.GetString("white-cve")
	if err != nil {
		return nil, err
	}
	return &BaseScanner{
		cubeMantistUrl:  cubeMantistUrl,
		uploadResultUrl: uploadResultUrl,
		whiteCVE:        whiteCVE,
	}, nil
}

func (s *BaseScanner) Exec() error {
	return nil
}

func (s *BaseScanner) IgnoreFile() error {
	// 创建文件
	file, err := os.Create(IgnoreFile)
	if err != nil {
		return err
	}
	defer func() {
		err = file.Close()
		if err != nil {
			logger.Logger.Errorf("Error closing file: %v", err)
		}
	}()

	// 使用 bufio.NewWriter 提供的缓冲写入
	writer := bufio.NewWriter(file)
	for _, line := range strings.Split(s.whiteCVE, ",") {
		// 按行写入文件
		_, err := writer.WriteString(line + "\n")
		if err != nil {
			return err
		}
	}

	// 确保所有缓冲的数据写入底层的文件
	if err := writer.Flush(); err != nil {
		return err
	}

	return nil
}

func (s *BaseScanner) UploadResult() error {
	// 读取文件
	file, err := os.Open(ResultFilePath)
	if err != nil {
		return fmt.Errorf("could not open file: %v", err)
	}
	defer func() {
		err = file.Close()
		if err != nil {
			logger.Logger.Errorf("Error closing file: %v", err)
		}
	}()

	// 创建一个缓冲区来存储表单数据
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// 创建一个表单文件字段
	part, err := writer.CreateFormFile("file", filepath.Base(file.Name()))
	if err != nil {
		return fmt.Errorf("could not create form file: %v", err)
	}

	// 将文件内容复制到表单字段
	_, err = io.Copy(part, file)
	if err != nil {
		return fmt.Errorf("could not copy file content: %v", err)
	}

	// 关闭multipart写入器，确保数据写入完成
	err = writer.Close()
	if err != nil {
		return fmt.Errorf("could not close writer: %v", err)
	}

	// 创建HTTP请求
	apiUrl, err := url.JoinPath(s.cubeMantistUrl, s.uploadResultUrl)
	if err != nil {
		return fmt.Errorf("cubeMantistUrl: %s, uploadResultUrl: %s, invaild url: %v", s.cubeMantistUrl, s.uploadResultUrl, err)
	}

	req, err := http.NewRequest("POST", apiUrl, body)
	if err != nil {
		return fmt.Errorf("could not create request: %v", err)
	}

	// 设置Content-Type为multipart/form-data并包含boundary
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("could not send request: %v", err)
	}
	defer func() {
		err = resp.Body.Close()
		if err != nil {
			logger.Logger.Errorf("Error closing response body: %v", err)
		}
	}()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("上传失败: %s", resp.Status)
	}

	// 读取响应内容
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("could not read controller body: %v", err)
	}

	var rsp controller.Response
	if err := json.Unmarshal(responseBody, &rsp); err != nil {
		return nil
	} else {
		if rsp.Code == constants.ResponseErrorCode {
			return fmt.Errorf("upload failed")
		}
	}

	log.Println("上传成功")

	return nil
}

package docker

import (
	"fmt"
	"log"
	"os"
	"os/exec"

	trivyscanner "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/trivy-scanner"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
	"github.com/spf13/pflag"
)

type DockerScanner struct {
	*trivyscanner.BaseScanner
	image    string
	addr     string
	user     string
	password string
	nonSsl   string
}

func NewDockerScanner(f *pflag.FlagSet) (*DockerScanner, error) {
	baseScanner, err := trivyscanner.NewBaseScanner(f)
	if err != nil {
		return nil, err
	}
	err = baseScanner.IgnoreFile()
	if err != nil {
		return nil, err
	}

	image, err := f.GetString("image")
	if err != nil {
		return nil, err
	}
	addr, err := f.GetString("docker-addr")
	if err != nil {
		return nil, err
	}
	user, err := f.GetString("docker-user")
	if err != nil {
		return nil, err
	}
	password, err := f.GetString("docker-password")
	if err != nil {
		return nil, err
	}
	nonSsl, err := f.GetString("non-ssl")
	if err != nil {
		return nil, err
	}
	return &DockerScanner{
		BaseScanner: baseScanner,
		image:       image,
		addr:        addr,
		user:        user,
		password:    password,
		nonSsl:      nonSsl,
	}, nil
}

func (s *DockerScanner) Exec() error {
	log.Println("制品名称: ", s.image)
	if s.user != "" && s.password != "" {
		loginCmd := exec.Command("docker", "login", s.addr, "-u", s.user, "-p", s.password)
		err := utils.SyncOutLog(loginCmd)
		if err != nil {
			return err
		}
	}

	arg := []string{
		"image", s.image, "--ignorefile", trivyscanner.IgnoreFile, "--skip-db-update", "--skip-java-db-update", "--scanners", "vuln",
		"--timeout", "15m", "--format", "template", "--template", "@/root/gitlab.tpl", "--output", trivyscanner.ResultFilePath,
	}
	trivyCmd := exec.Command("trivy", arg...)
	trivyCmd.Env = append(os.Environ(), fmt.Sprintf("TRIVY_NON_SSL=%s", s.nonSsl))
	log.Println(trivyCmd.String())
	err := utils.SyncOutLog(trivyCmd)
	if err != nil {
		return err
	}
	return nil
}

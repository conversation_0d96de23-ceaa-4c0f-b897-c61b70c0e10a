package jacococoverage

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"os"
	"os/exec"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/jacoco-coverage/params"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/commands/utils"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
)

func Exec(p params.Param) error {
	// 创建taskNo目录并将其设置为工作目录，之后的exec命令都将在此目录下执行
	if err := os.Mkdir("/jacoco/"+p.TaskNo, 0o755); err != nil {
		return err
	}
	if err := os.Chdir("/jacoco/" + p.TaskNo); err != nil {
		return err
	}
	if wd, err := os.Getwd(); err != nil {
		return err
	} else {
		log.Printf("工作目录切换为:%s", wd)
	}
	// clone 代码
	if err := gitClone(p); err != nil {
		return err
	}
	// 将oss的配置写入环境变量
	if err := os.Setenv("store_s3_endpoint", p.Oss.Endpoint); err != nil {
		return err
	}
	if err := os.Setenv("store_s3_bucketName", p.Oss.Bucket); err != nil {
		return err
	}
	if err := os.Setenv("store_s3_accessId", p.Oss.AccessId); err != nil {
		return err
	}
	if err := os.Setenv("store_s3_accessKey", p.Oss.AccessKey); err != nil {
		return err
	}
	if err := os.Setenv("path_style_access", strconv.FormatBool(p.Oss.PathStyle)); err != nil {
		return err
	}
	// 执行flyer
	var mode string
	if p.Mode == "total" {
		mode = "getShipResource"
	} else {
		mode = "getShipDeleteResource"
	}
	args := []string{
		"-jar", "/flyer/flyer.jar",
		"--mode", mode, "load", "merge", "report",
		"-a", p.AppName,
		"-i", p.Ip,
		"-p", p.Port,
		"--packages", p.Packages,
		"--svnUrl", p.Git.GitUrl,
		"--exclude", p.Exclude,
		"--moduleName", p.ModuleName,
		"--branch", p.Branch,
		"--osPath", p.Oss.Path,
		"--taskNo", p.TaskNo,
		"--baseVersion", p.BaseCommitId,
		"--currentVersion", p.CompareCommitId,
		"--execPath", p.OldExecPath,
	}
	args = append(args, "load", "merge", "report")
	flyerCmd := exec.Command("java", args...)
	log.Println(flyerCmd.String())
	err := utils.SyncOutLog(flyerCmd)
	if err != nil {
		return err
	}
	// 解析结果
	res, execPath, err := calculateResult(p)
	if err != nil {
		log.Printf("解析结果报错, err=%s", err.Error())
		return err
	}
	result := make(map[string]any)
	for k, v := range res {
		result[k] = v
	}
	result["oldExecPath"] = execPath
	hisId, err := strconv.ParseInt(p.TaskNo, 10, 64)
	if err != nil {
		return err
	}
	result["id"] = hisId
	// 上传结果
	s3store.InitWithoutConfig(p.Oss.Endpoint, p.Oss.AccessId, p.Oss.AccessKey, p.Oss.Bucket)
	log.Println("开始上传报告")
	if err := uploadReport("coverageReport", "/mantis/jacoco-coverage/"+p.TaskNo); err != nil {
		return err
	}
	log.Println("上传报告结束")
	result["reportUrl"] = p.Oss.Endpoint + `/` + p.Oss.Bucket + `/mantis/jacoco-coverage/` + p.TaskNo + `/coverageReport/index.html`
	// 回调
	callBackUrl := p.MantisCallBack
	data, err := json.Marshal(&result)
	if err != nil {
		return err
	}
	log.Printf("回调mantis主服务数据: %s", data)
	req, err := http.NewRequest(http.MethodPost, callBackUrl, bytes.NewBuffer(data))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	log.Printf("回调返回:%s", body)
	if resp.StatusCode != http.StatusOK {
		return errors.New("回调返回不为200")
	}
	return nil
}

func gitClone(p params.Param) error {
	authPart := fmt.Sprintf(`%s:%s@`, p.Git.GitUser, p.Git.GitToken)
	log.Println(authPart)
	var gitAuthUrl string
	if strings.HasPrefix(p.Git.GitUrl, "http://") {
		gitAuthUrl = strings.Replace(p.Git.GitUrl, "http://", "http://"+authPart, 1)
	} else if strings.HasPrefix(p.Git.GitUrl, "https://") {
		gitAuthUrl = strings.Replace(p.Git.GitUrl, "https://", "https://"+authPart, 1)
	} else {
		gitAuthUrl = strings.Replace(p.Git.GitUrl, "git", authPart+"git", 0)
	}
	log.Println(gitAuthUrl)
	gitCmd := exec.Command("git", "clone", "-b", p.Branch, gitAuthUrl)
	log.Println(gitCmd.String())
	if err := utils.SyncOutLog(gitCmd); err != nil {
		return err
	}
	return nil
}

func calculateResult(p params.Param) (res map[string]float64, execPath string, err error) {
	log.Println("开始计算结果")
	file, err := os.Open(p.TaskNo)
	if err != nil {
		log.Printf("Error opening result file: %v\n", err)
		return nil, "", err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	lines := make([]string, 0)
	for scanner.Scan() {
		line := scanner.Text()
		lines = append(lines, line)
	}
	// 检查是否有扫描错误
	if err := scanner.Err(); err != nil {
		log.Printf("Error scanning result file: %v\n", err)
		return nil, "", err
	}
	// 处理
	if len(lines) < 2 {
		log.Println("结果文件错误")
		return nil, "", errors.New("结果文件格式错误")
	}
	infos := strings.Split(lines[0], "\u0001")
	if len(infos) == 0 {
		return nil, "", fmt.Errorf("结果文件格式错误, 未给出exec path, 结果文件第一行内容:%s", lines[0])
	}
	execPath = infos[0]
	title := lines[1]
	titleSplit := strings.Split(title, "\u0001")
	fieldCatalog := make(map[string]int)
	for i := 0; i < len(titleSplit); i++ {
		fieldCatalog[titleSplit[i]] = i
	}
	res = make(map[string]float64)
	calPercentageFunc := func(covered, total float64) float64 {
		if total == 0 {
			return 0
		}
		return math.Round(covered/total*10000) / 10000
	}
	if p.Mode == "total" {
		useFieldCatalog := map[string]int{
			"instructionCovered": fieldCatalog["instruction_covered"],
			"instructionTotal":   fieldCatalog["instruction_total"],
			"branchCovered":      fieldCatalog["branch_covered"],
			"branchTotal":        fieldCatalog["branch_total"],
			"lineCovered":        fieldCatalog["line_covered"],
			"lineTotal":          fieldCatalog["line_total"],
			"classCovered":       fieldCatalog["class_covered"],
			"classTotal":         fieldCatalog["class_total"],
		}
		sumField := make(map[string]float64)
		for i := 2; i < len(lines); i++ {
			split := strings.Split(lines[i], "\u0001")
			for k, v := range useFieldCatalog {
				var value float64 = 0
				if len(split[v]) != 0 {
					value1, err := strconv.ParseFloat(split[v], 64)
					if err != nil {
						continue
					}
					value = value1
				}
				sumField[k] = sumField[k] + value
			}
		}
		log.Printf("全量 sumField=%+v\n", sumField)
		for k, v := range sumField {
			res[k] = v
		}
		res["instructionPercentage"] = calPercentageFunc(res["instructionCovered"], res["instructionTotal"])
		res["branchPercentage"] = calPercentageFunc(res["branchCovered"], res["branchTotal"])
		res["linePercentage"] = calPercentageFunc(res["lineCovered"], res["lineTotal"])
		res["classPercentage"] = calPercentageFunc(res["classCovered"], res["classTotal"])
	} else {
		res["lineTotal"] = float64(len(lines) - 1)
		coveredIndex := fieldCatalog["covered"]
		lineMiss := float64(0)
		for i := 1; i < len(lines); i++ {
			split := strings.Split(lines[i], "\u0001")
			if split[coveredIndex] == "0" {
				lineMiss++
			}
		}
		res["lineCovered"] = res["lineTotal"] - lineMiss
		res["linePercentage"] = calPercentageFunc(res["lineCovered"], res["lineTotal"])
	}
	log.Printf("计算结果成功, res=%+v\n", res)
	return res, execPath, nil
}

func uploadReport(srcPath string, ossBasePath string) error {
	// 上传html报告到oss
	reportDir, err := os.Open(srcPath)
	if err != nil {
		return err
	}
	reports, err := reportDir.ReadDir(-1)
	if err != nil {
		return err
	}
	for _, f := range reports {
		if f.IsDir() {
			uploadReport(srcPath+`/`+f.Name(), ossBasePath)
		} else {
			if _, err := s3store.UploadFileWithContentType(srcPath+`/`+f.Name(), ossBasePath+`/`+srcPath+`/`+f.Name()); err != nil {
				return err
			}
		}
	}
	return nil
}

package params

import (
	"github.com/spf13/pflag"
)

type Param struct {
	Mode     string
	AppName  string
	Ip       string
	Port     string
	Packages string
	Git      struct {
		GitUrl   string
		GitUser  string
		GitToken string
	}
	Exclude    string
	ModuleName string
	Branch     string
	Oss        struct {
		Path      string
		Endpoint  string
		Bucket    string
		AccessId  string
		AccessKey string
		PathStyle bool
	}
	TaskNo          string
	BaseCommitId    string
	CompareCommitId string
	MantisCallBack  string
	OldExecPath     string
}

func InitParams(cf *pflag.FlagSet) (Param, error) {
	p := Param{}
	if mode, err := cf.GetString("mode"); err != nil {
		return p, err
	} else {
		p.Mode = mode
	}
	if appName, err := cf.GetString("app-name"); err != nil {
		return p, err
	} else {
		p.AppName = appName
	}
	if ip, err := cf.GetString("ip"); err != nil {
		return p, err
	} else {
		p.Ip = ip
	}
	if port, err := cf.GetString("port"); err != nil {
		return p, err
	} else {
		p.Port = port
	}
	if packages, err := cf.GetString("packages"); err != nil {
		return p, err
	} else {
		p.Packages = packages
	}
	if gitUrl, err := cf.GetString("git-url"); err != nil {
		return p, err
	} else {
		p.Git.GitUrl = gitUrl
	}
	if gitUser, err := cf.GetString("git-user"); err != nil {
		return p, err
	} else {
		p.Git.GitUser = gitUser
	}
	if gitToken, err := cf.GetString("git-token"); err != nil {
		return p, err
	} else {
		p.Git.GitToken = gitToken
	}
	if exclude, err := cf.GetString("exclude"); err != nil {
		return p, err
	} else {
		p.Exclude = exclude
	}
	if moduleName, err := cf.GetString("module-name"); err != nil {
		return p, err
	} else {
		p.ModuleName = moduleName
	}
	if branch, err := cf.GetString("branch"); err != nil {
		return p, err
	} else {
		p.Branch = branch
	}
	if ossPath, err := cf.GetString("oss-path"); err != nil {
		return p, err
	} else {
		p.Oss.Path = ossPath
	}
	if ossEndpoint, err := cf.GetString("oss-endpoint"); err != nil {
		return p, err
	} else {
		p.Oss.Endpoint = ossEndpoint
	}
	if bucketName, err := cf.GetString("oss-bucket-name"); err != nil {
		return p, err
	} else {
		p.Oss.Bucket = bucketName
	}
	if accessId, err := cf.GetString("oss-access-id"); err != nil {
		return p, err
	} else {
		p.Oss.AccessId = accessId
	}
	if accessKey, err := cf.GetString("oss-access-key"); err != nil {
		return p, err
	} else {
		p.Oss.AccessKey = accessKey
	}
	if pathStyle, err := cf.GetBool("oss-path-style"); err != nil {
		return p, err
	} else {
		p.Oss.PathStyle = pathStyle
	}
	if taskNo, err := cf.GetString("task-no"); err != nil {
		return p, err
	} else {
		p.TaskNo = taskNo
	}
	if baseCommitId, err := cf.GetString("base-commit-id"); err != nil {
		return p, err
	} else {
		p.BaseCommitId = baseCommitId
	}
	if compareCommitId, err := cf.GetString("compare-commit-id"); err != nil {
		return p, err
	} else {
		p.CompareCommitId = compareCommitId
	}
	if callBack, err := cf.GetString("mantis-call-back"); err != nil {
		return p, err
	} else {
		p.MantisCallBack = callBack
	}
	if oldExecPath, err := cf.GetString("old-exec-path"); err != nil {
		return p, err
	} else {
		p.OldExecPath = oldExecPath
	}
	return p, nil
}

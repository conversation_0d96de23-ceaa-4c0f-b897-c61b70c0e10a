// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"
)

var (
	_accessLog alice.Constructor

	_auditLog alice.Constructor

	_authentication alice.Constructor

	_authorization alice.Constructor

	_recovery alice.Constructor

	_trace alice.Constructor
)

func SetAccessLogMiddleware(accessLog alice.Constructor) {
	_accessLog = accessLog
}

func SetAuditLogMiddleware(auditLog alice.Constructor) {
	_auditLog = auditLog
}

func SetAuthenticationMiddleware(authentication alice.Constructor) {
	_authentication = authentication
}

func SetAuthorizationMiddleware(authorization alice.Constructor) {
	_authorization = authorization
}

func SetRecoveryMiddleware(recovery alice.Constructor) {
	_recovery = recovery
}

func SetTraceMiddleware(trace alice.Constructor) {
	_trace = trace
}

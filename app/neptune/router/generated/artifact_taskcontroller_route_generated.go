// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/artifact"

	artifact1 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/artifact"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/neptune/v1/task") -> artifact.TaskController.AddTask
func artifactTaskControllerAddTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddTask",
		Patten:            "/neptune/v1/task",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "制品扫描",
			"auth.code":         "MANTIS-NEPTUNE-PRODUCT-TASK-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		task := artifact1.ScanTask{}
		if i, ok := decode.Implements(&task); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &task); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(task); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddTask(task)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/neptune/v1/task") -> artifact.TaskController.UpdateTask
func artifactTaskControllerUpdateTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateTask",
		Patten:            "/neptune/v1/task",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "制品扫描",
			"auth.code":         "MANTIS-NEPTUNE-PRODUCT-TASK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		task := artifact1.ScanTask{}
		if i, ok := decode.Implements(&task); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &task); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(task); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateTask(task)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/neptune/v1/task/{id}") -> artifact.TaskController.DeleteTask
func artifactTaskControllerDeleteTaskHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteTask",
		Patten:            "/neptune/v1/task/{id}",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "制品扫描",
			"auth.code":         "MANTIS-NEPTUNE-PRODUCT-TASK-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.TaskIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteTask(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/neptune/v1/task/{id}/exec") -> artifact.TaskController.Exec
func artifactTaskControllerExecHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Exec",
		Patten:            "/neptune/v1/task/{id}/exec",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "制品扫描",
			"auth.code":         "MANTIS-NEPTUNE-PRODUCT-TASK-EXEC",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.TaskIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Exec(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/neptune/v1/task/{id}/abort") -> artifact.TaskController.Abort
func artifactTaskControllerAbortHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Abort",
		Patten:            "/neptune/v1/task/{id}/abort",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "制品扫描",
			"auth.code":         "MANTIS-NEPTUNE-PRODUCT-TASK-STOP",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.TaskIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Abort(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/neptune/v1/task/list") -> artifact.TaskController.List
func artifactTaskControllerListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "List",
		Patten:            "/neptune/v1/task/list",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.QueryWithOrderDTOReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.List(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/neptune/v1/task/{id}/{eid}") -> artifact.TaskController.Info
func artifactTaskControllerInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Info",
		Patten:            "/neptune/v1/task/{id}/{eid}",
		Desc:              "",
		ControllerPkgName: "artifact",
		ControllerName:    "TaskController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "制品扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := artifact.InitializeTaskController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := artifact.TaskInfoInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Info(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

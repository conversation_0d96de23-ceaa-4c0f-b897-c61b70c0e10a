// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	quality1 "git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/models/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/rick/api/profileTemplate/query") -> quality.ScanProfileTemplateController.QueryProfileTemplateByLanguage
func qualityScanProfileTemplateControllerQueryProfileTemplateByLanguageHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryProfileTemplateByLanguage",
		Patten:            "/rick/api/profileTemplate/query",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.ScanProfileTemplateLanguageInSchemaReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryProfileTemplateByLanguage(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/api/profileTemplate/save") -> quality.ScanProfileTemplateController.SaveScanProfileTemplate
func qualityScanProfileTemplateControllerSaveScanProfileTemplateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveScanProfileTemplate",
		Patten:            "/rick/api/profileTemplate/save",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		template := quality1.ScanProfileTemplate{}
		if i, ok := decode.Implements(&template); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &template); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(template); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveScanProfileTemplate(template)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/api/profileTemplate/copy") -> quality.ScanProfileTemplateController.CopyScanProfileTemplate
func qualityScanProfileTemplateControllerCopyScanProfileTemplateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CopyScanProfileTemplate",
		Patten:            "/rick/api/profileTemplate/copy",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		reqDTO := dto.CopyProfileTemplateReqDTO{}
		if i, ok := decode.Implements(&reqDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &reqDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(reqDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CopyScanProfileTemplate(reqDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/api/profileTemplate/import") -> quality.ScanProfileTemplateController.ImportProfiles
func qualityScanProfileTemplateControllerImportProfilesHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ImportProfiles",
		Patten:            "/rick/api/profileTemplate/import",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ImportProfiles()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/profileTemplate/export/{id}") -> quality.ScanProfileTemplateController.ExportProfile
func qualityScanProfileTemplateControllerExportProfileHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExportProfile",
		Patten:            "/rick/api/profileTemplate/export/{id}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.ScanProfileTemplateIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ExportProfile(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/rick/api/profileTemplate/{id}") -> quality.ScanProfileTemplateController.DeleteScanProfileTemplate
func qualityScanProfileTemplateControllerDeleteScanProfileTemplateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteScanProfileTemplate",
		Patten:            "/rick/api/profileTemplate/{id}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanProfileTemplateController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanProfileTemplateController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.ScanProfileTemplateIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteScanProfileTemplate(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

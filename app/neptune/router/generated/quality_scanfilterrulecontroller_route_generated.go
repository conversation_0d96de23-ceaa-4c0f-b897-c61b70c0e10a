// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/rick/api/filter") -> quality.ScanFilterRuleController.CreateFilterRule
func qualityScanFilterRuleControllerCreateFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CreateFilterRule",
		Patten:            "/rick/api/filter",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		filterDTO := dto.ScanTaskFilterDTO{}
		if i, ok := decode.Implements(&filterDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &filterDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(filterDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CreateFilterRule(filterDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/rick/api/filter") -> quality.ScanFilterRuleController.ModifyFilterRule
func qualityScanFilterRuleControllerModifyFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ModifyFilterRule",
		Patten:            "/rick/api/filter",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		filterDTO := dto.ScanTaskFilterDTO{}
		if i, ok := decode.Implements(&filterDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &filterDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(filterDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ModifyFilterRule(filterDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/api/filter/app") -> quality.ScanFilterRuleController.SaveAppFilterRule
func qualityScanFilterRuleControllerSaveAppFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveAppFilterRule",
		Patten:            "/rick/api/filter/app",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		filterDto := dto.AppScanTaskFilterDTO{}
		if i, ok := decode.Implements(&filterDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &filterDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(filterDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveAppFilterRule(filterDto)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/rick/api/filter/app") -> quality.ScanFilterRuleController.UpdateAppFilterRule
func qualityScanFilterRuleControllerUpdateAppFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateAppFilterRule",
		Patten:            "/rick/api/filter/app",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		filterDto := dto.AppScanTaskFilterDTO{}
		if i, ok := decode.Implements(&filterDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &filterDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(filterDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateAppFilterRule(filterDto)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/rick/api/filter/app/delete/{id}") -> quality.ScanFilterRuleController.DeleteAppFilterRule
func qualityScanFilterRuleControllerDeleteAppFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DeleteAppFilterRule",
		Patten:            "/rick/api/filter/app/delete/{id}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.QueryAppFilterInfoIdReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.DeleteAppFilterRule(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/rick/api/filter/app/updateDescription") -> quality.ScanFilterRuleController.UpdateDescriptioon
func qualityScanFilterRuleControllerUpdateDescriptioonHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateDescriptioon",
		Patten:            "/rick/api/filter/app/updateDescription",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "代码扫描",
			"auth.code":         "MAGIC-NEPTUNE-CODE-CONFIG-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		description := quality.UpdateAppFilterDescriptionReq{}
		if i, ok := decode.Implements(&description); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &description); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(description); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateDescriptioon(description)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/filter/list/{relationId}/{type}") -> quality.ScanFilterRuleController.QueryFilterRule
func qualityScanFilterRuleControllerQueryFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryFilterRule",
		Patten:            "/rick/api/filter/list/{relationId}/{type}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.QueryFilterRuleReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryFilterRule(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/filter/query/type") -> quality.ScanFilterRuleController.QueryFilterExclusionEnum
func qualityScanFilterRuleControllerQueryFilterExclusionEnumHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryFilterExclusionEnum",
		Patten:            "/rick/api/filter/query/type",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryFilterExclusionEnum()

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/filter/app/list") -> quality.ScanFilterRuleController.QueryAppFilterRule
func qualityScanFilterRuleControllerQueryAppFilterRuleHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryAppFilterRule",
		Patten:            "/rick/api/filter/app/list",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.QueryAppFilterRuleReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		request := gormx.PageRequest{}
		if i, ok := decode.Implements(&request); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &request); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(request); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryAppFilterRule(req1, request)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/filter/app/info/{id}") -> quality.ScanFilterRuleController.QueryAppFilterInfo
func qualityScanFilterRuleControllerQueryAppFilterInfoHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryAppFilterInfo",
		Patten:            "/rick/api/filter/app/info/{id}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.QueryAppFilterInfoIdReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryAppFilterInfo(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/rick/api/filter/app/infoByAppId/{id}") -> quality.ScanFilterRuleController.QueryAppFilterInfoByAppId
func qualityScanFilterRuleControllerQueryAppFilterInfoByAppIdHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "QueryAppFilterInfoByAppId",
		Patten:            "/rick/api/filter/app/infoByAppId/{id}",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "ScanFilterRuleController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeScanFilterRuleController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := quality.QueryAppFilterInfoAppIdReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.QueryAppFilterInfoByAppId(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/neptune/internal/controller/quality"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/rick/openapi/coverage/register") -> quality.JacocoController.PipelineRegister
func qualityJacocoControllerPipelineRegisterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PipelineRegister",
		Patten:            "/rick/openapi/coverage/register",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "JacocoController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeJacocoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.PipelineCollectCoverageReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PipelineRegister(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/openapi/coverage/collect") -> quality.JacocoController.PipelineExec
func qualityJacocoControllerPipelineExecHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "PipelineExec",
		Patten:            "/rick/openapi/coverage/collect",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "JacocoController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeJacocoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.PipelineCollectCoverageReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.PipelineExec(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/openapi/coverage/reset/auto") -> quality.JacocoController.AptRegister
func qualityJacocoControllerAptRegisterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AptRegister",
		Patten:            "/rick/openapi/coverage/reset/auto",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "JacocoController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeJacocoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.AptRegister()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call AptRegister failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/openapi/coverage/collect/auto") -> quality.JacocoController.AptExec
func qualityJacocoControllerAptExecHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AptExec",
		Patten:            "/rick/openapi/coverage/collect/auto",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "JacocoController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeJacocoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.AptExec()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call AptExec failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/rick/openapi/coverage/callBack") -> quality.JacocoController.CallBack
func qualityJacocoControllerCallBackHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CallBack",
		Patten:            "/rick/openapi/coverage/callBack",
		Desc:              "",
		ControllerPkgName: "quality",
		ControllerName:    "JacocoController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "代码扫描",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := quality.InitializeJacocoController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := dto.CallBackReqDTO{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CallBack(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

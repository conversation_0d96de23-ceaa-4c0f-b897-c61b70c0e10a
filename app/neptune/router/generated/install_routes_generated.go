// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: artifact

	//
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/detail").Handler(artifactExecHisControllerGetProductExecResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/list").Handler(artifactExecHisControllerGetExecHistListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/execHis/{id}/detail/log").Handler(artifactExecHisControllerGetProductExecLogHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/openapi/v1/pipeline/trigger/product").Handler(artifactPipelineControllerScanProductHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/v1/plan").Handler(artifactPlanControllerAddPlanHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/neptune/v1/plan").Handler(artifactPlanControllerUpdatePlanHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/list").Handler(artifactPlanControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/neptune/v1/plan/{id}").Handler(artifactPlanControllerDeletePlanHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/neptune/v1/plan/{id}/default/{v}").Handler(artifactPlanControllerDefaultHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/{id}").Handler(artifactPlanControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/plan/{id}/bindTask").Handler(artifactPlanControllerBindTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/list").Handler(artifactRepoControllerInstanceListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/repoInfos").Handler(artifactRepoControllerRepoInfosHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/products/{repoId}/{repoName}").Handler(artifactRepoControllerProductsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/versions/{repoName}/{prodId}/{prodName}").Handler(artifactRepoControllerVersionsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/latest/{repoId}/{repoName}").Handler(artifactRepoControllerLatestProductsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/repo/instance/{id}/versionPath/{versionId}").Handler(artifactRepoControllerVersionPathHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/neptune/v1/report/{id}/detail").Handler(artifactReportControllerDetailHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/neptune/v1/report/{id}/execHis/export").Handler(artifactReportControllerExecHisExportHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/report/{id}/execHis/list").Handler(artifactReportControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/openapi/v1/result/{type}/{id}/form").Handler(artifactResultControllerUploadFormResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/v1/task").Handler(artifactTaskControllerAddTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/neptune/v1/task").Handler(artifactTaskControllerUpdateTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/neptune/v1/task/{id}").Handler(artifactTaskControllerDeleteTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/neptune/v1/task/{id}/exec").Handler(artifactTaskControllerExecHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/neptune/v1/task/{id}/abort").Handler(artifactTaskControllerAbortHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/task/list").Handler(artifactTaskControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/task/{id}/{eid}").Handler(artifactTaskControllerInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/neptune/v1/template/{type}/format").Handler(artifactTemplateControllerFormatHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/neptune/v1/template/{type}/download").Handler(artifactTemplateControllerDownloadHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//generate from Pkg: quality

	//
	mux.Methods("POST").Path("/rick/openapi/back/sonarUpdateBackCall/{id}").Handler(qualityBackControllerSonarUpdateBackCallHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("GET").Path("/rick/api/common/taskStatus").Handler(qualityCommonControllerGetTaskStatusEnumListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/languages").Handler(qualityCommonControllerGetLanguagesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/severities").Handler(qualityCommonControllerGetSeveritiesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/branchList").Handler(qualityCommonControllerGetBranchListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/branchListByVcs").Handler(qualityCommonControllerGetBranchListByVcsPathHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/branchInfo").Handler(qualityCommonControllerGetBranchHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/scan/execTypes").Handler(qualityCommonControllerGetExecTypesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/scan/modes").Handler(qualityCommonControllerGetScanModesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/task/from/enums").Handler(qualityCommonControllerGetTaskFromEnumsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/common/task/type/enums").Handler(qualityCommonControllerGetTaskTypeEnumsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/openapi/coverage/register").Handler(qualityJacocoControllerPipelineRegisterHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/collect").Handler(qualityJacocoControllerPipelineExecHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/reset/auto").Handler(qualityJacocoControllerAptRegisterHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/collect/auto").Handler(qualityJacocoControllerAptExecHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/openapi/coverage/callBack").Handler(qualityJacocoControllerCallBackHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/api/filter").Handler(qualityScanFilterRuleControllerCreateFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/rick/api/filter").Handler(qualityScanFilterRuleControllerModifyFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/filter/app").Handler(qualityScanFilterRuleControllerSaveAppFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/rick/api/filter/app").Handler(qualityScanFilterRuleControllerUpdateAppFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/rick/api/filter/app/delete/{id}").Handler(qualityScanFilterRuleControllerDeleteAppFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/rick/api/filter/app/updateDescription").Handler(qualityScanFilterRuleControllerUpdateDescriptioonHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/rick/api/filter/list/{relationId}/{type}").Handler(qualityScanFilterRuleControllerQueryFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/filter/query/type").Handler(qualityScanFilterRuleControllerQueryFilterExclusionEnumHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/filter/app/list").Handler(qualityScanFilterRuleControllerQueryAppFilterRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/filter/app/info/{id}").Handler(qualityScanFilterRuleControllerQueryAppFilterInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/filter/app/infoByAppId/{id}").Handler(qualityScanFilterRuleControllerQueryAppFilterInfoByAppIdHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/rick/api/issues/list").Handler(qualityScanIssuesControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/issues/components").Handler(qualityScanIssuesControllerComponentsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/rick/api/profileTemplate/query").Handler(qualityScanProfileTemplateControllerQueryProfileTemplateByLanguageHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/api/profileTemplate/save").Handler(qualityScanProfileTemplateControllerSaveScanProfileTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/api/profileTemplate/copy").Handler(qualityScanProfileTemplateControllerCopyScanProfileTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/api/profileTemplate/import").Handler(qualityScanProfileTemplateControllerImportProfilesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/profileTemplate/export/{id}").Handler(qualityScanProfileTemplateControllerExportProfileHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("DELETE").Path("/rick/api/profileTemplate/{id}").Handler(qualityScanProfileTemplateControllerDeleteScanProfileTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/api/programme/add").Handler(qualityScanProgrammeControllerAddHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/rick/api/programme/delete/{id}").Handler(qualityScanProgrammeControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/programme/modify").Handler(qualityScanProgrammeControllerModifyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/rick/api/programme/list").Handler(qualityScanProgrammeControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/programme/queryProfileCards/{id}").Handler(qualityScanProgrammeControllerQueryProfileCardsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/programme/query/taskList").Handler(qualityScanProgrammeControllerQueryTaskNameListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/programme/query/profiles/{id}").Handler(qualityScanProgrammeControllerQueryProgrammeProfileEnumHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("PUT").Path("/rick/api/programme/profile/active").Handler(qualityScanProgrammeControllerSetProfileActiveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/programme/query/{id}").Handler(qualityScanProgrammeControllerQueryHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/openapi/{type}").Handler(qualityScanTestCiControllerScanTestHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("GET").Path("/rick/api/sonarRule/search").Handler(qualitySonarRuleControllerSearchTemplateRulesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/sonarRule/detail/{key}").Handler(qualitySonarRuleControllerShowRuleDetailHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/sonarRule/status/enums").Handler(qualitySonarRuleControllerRuleStatusListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/sonarRule/type/enums").Handler(qualitySonarRuleControllerRuleTypesListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/rick/api/sonarRule/updateStatus").Handler(qualitySonarRuleControllerActiveOrDeActiveRuleHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/scan").Handler(qualityTaskControllerInsertScanTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/unit").Handler(qualityTaskControllerInsertTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/rick/api/scanTask/task/scan").Handler(qualityTaskControllerUpdateScanTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/rick/api/scanTask/task/unit").Handler(qualityTaskControllerUpdateTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/exec/scan").Handler(qualityTaskControllerExecuteScanTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/exec/unit").Handler(qualityTaskControllerExecuteTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/rick/api/scanTask/task/delete/{id}/scan").Handler(qualityTaskControllerDeleteScanTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/rick/api/scanTask/task/delete/{id}/unit").Handler(qualityTaskControllerDeleteTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/stop/{id}/scan").Handler(qualityTaskControllerStopScanTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/rick/api/scanTask/task/stop/{id}/unit").Handler(qualityTaskControllerStopTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/task/commitInfo").Handler(qualityTaskControllerGetCommitInfoHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/task/info/{taskId}").Handler(qualityTaskControllerGetScanTestTaskHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/taskConfig/info/{taskId}").Handler(qualityTaskControllerQueryQualityGateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/overview/{taskId}").Handler(qualityTaskControllerQueryTaskOverviewHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/query").Handler(qualityTaskControllerQueryScanTestTaskPageHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/history/query").Handler(qualityTaskControllerQueryScanTestExecHisPageByTaskIdHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/rick/api/scanTask/history/logs/{hisId}").Handler(qualityTaskControllerGetLogsByHisIdHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

}

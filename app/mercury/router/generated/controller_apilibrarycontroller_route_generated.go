// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mercury/v1/library") -> controller.ApiLibraryController.AddLibrary
func controllerApiLibraryControllerAddLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddLibrary",
		Patten:            "/mercury/v1/library",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiLibraryController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiLibrary := models.ApiLibrary{}
		if i, ok := decode.Implements(&apiLibrary); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiLibrary); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiLibrary); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddLibrary(apiLibrary)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/library/{id}") -> controller.ApiLibraryController.ModifyLibrary
func controllerApiLibraryControllerModifyLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ModifyLibrary",
		Patten:            "/mercury/v1/library/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiLibraryController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiLibrary := models.ApiLibrary{}
		if i, ok := decode.Implements(&apiLibrary); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiLibrary); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiLibrary); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiLibraryIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ModifyLibrary(apiLibrary, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/library/{id}") -> controller.ApiLibraryController.RemoveLibrary
func controllerApiLibraryControllerRemoveLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveLibrary",
		Patten:            "/mercury/v1/library/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiLibraryController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiLibraryIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveLibrary(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/library/list") -> controller.ApiLibraryController.SearchLibrary
func controllerApiLibraryControllerSearchLibraryHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SearchLibrary",
		Patten:            "/mercury/v1/library/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiLibraryController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiLibrarySearchReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SearchLibrary(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/library/{id}/doc/status") -> controller.ApiLibraryController.GetDocStatus
func controllerApiLibraryControllerGetDocStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetDocStatus",
		Patten:            "/mercury/v1/library/{id}/doc/status",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiLibraryController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiLibraryController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiLibraryIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetDocStatus(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

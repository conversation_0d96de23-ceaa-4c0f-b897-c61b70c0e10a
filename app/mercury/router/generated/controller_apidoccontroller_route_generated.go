// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/mercury/v1/doc/save") -> controller.ApiDocController.SaveDocHandler
func controllerApiDocControllerSaveDocHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveDocHandler",
		Patten:            "/mercury/v1/doc/save",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		saveDTO := dto.ApiDocSaveDTO{}
		if i, ok := decode.Implements(&saveDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &saveDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(saveDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SaveDocHandler(saveDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/doc/save") -> controller.ApiDocController.UpdateDocHandler
func controllerApiDocControllerUpdateDocHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateDocHandler",
		Patten:            "/mercury/v1/doc/save",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		saveDTO := dto.ApiDocSaveDTO{}
		if i, ok := decode.Implements(&saveDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &saveDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(saveDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateDocHandler(saveDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/doc/{id}") -> controller.ApiDocController.UpdateStatus
func controllerApiDocControllerUpdateStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateStatus",
		Patten:            "/mercury/v1/doc/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocUpdateStatusReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateStatus(idReq, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/updateResponsibleId") -> controller.ApiDocController.UpdateResponseId
func controllerApiDocControllerUpdateResponseIdHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateResponseId",
		Patten:            "/mercury/v1/doc/updateResponsibleId",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocUpdateResponseIdReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateResponseId(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/updateBatchStatus") -> controller.ApiDocController.UpdateBatchStatus
func controllerApiDocControllerUpdateBatchStatusHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateBatchStatus",
		Patten:            "/mercury/v1/doc/updateBatchStatus",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocUpdateBatchStatusReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateBatchStatus(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/doc/{id}") -> controller.ApiDocController.RemoveDocByIdHandler
func controllerApiDocControllerRemoveDocByIdHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveDocByIdHandler",
		Patten:            "/mercury/v1/doc/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveDocByIdHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/doc/ids") -> controller.ApiDocController.RemoveDocByIds
func controllerApiDocControllerRemoveDocByIdsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveDocByIds",
		Patten:            "/mercury/v1/doc/ids",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.RemoveDocByIds()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call RemoveDocByIds failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/save2Jupiter/{id}") -> controller.ApiDocController.Save2Jupiter
func controllerApiDocControllerSave2JupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Save2Jupiter",
		Patten:            "/mercury/v1/doc/save2Jupiter/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-CREATE-TEMPLATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Save2Jupiter(req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Save2Jupiter failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/saveBatch2Jupiter/{appId}") -> controller.ApiDocController.SaveBatch2Jupiter
func controllerApiDocControllerSaveBatch2JupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveBatch2Jupiter",
		Patten:            "/mercury/v1/doc/saveBatch2Jupiter/{appId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-CREATE-TEMPLATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocAppIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.SaveBatch2Jupiter(req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call SaveBatch2Jupiter failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/sync2Jupiter/{id}") -> controller.ApiDocController.Sync2Jupiter
func controllerApiDocControllerSync2JupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Sync2Jupiter",
		Patten:            "/mercury/v1/doc/sync2Jupiter/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-SYNC",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Sync2Jupiter(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/syncBatch2Jupiter") -> controller.ApiDocController.SyncBatch2Jupiter
func controllerApiDocControllerSyncBatch2JupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SyncBatch2Jupiter",
		Patten:            "/mercury/v1/doc/syncBatch2Jupiter",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-SYNC",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.SyncBatch2Jupiter()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call SyncBatch2Jupiter failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/doc/unbindJupiter/{id}") -> controller.ApiDocController.UnbindJupiter
func controllerApiDocControllerUnbindJupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UnbindJupiter",
		Patten:            "/mercury/v1/doc/unbindJupiter/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-DELETE-RELATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UnbindJupiter(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/doc/list") -> controller.ApiDocController.SearchDocPage
func controllerApiDocControllerSearchDocPageHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SearchDocPage",
		Patten:            "/mercury/v1/doc/list",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocPageSearchReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.SearchDocPage(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/doc/convert2Jupiter/{id}") -> controller.ApiDocController.Convert2Jupiter
func controllerApiDocControllerConvert2JupiterHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Convert2Jupiter",
		Patten:            "/mercury/v1/doc/convert2Jupiter/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.Convert2Jupiter(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/doc/{id}") -> controller.ApiDocController.GetDocDetailByIdHandler
func controllerApiDocControllerGetDocDetailByIdHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetDocDetailByIdHandler",
		Patten:            "/mercury/v1/doc/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiDocController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiDocController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiDocIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.GetDocDetailByIdHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// PUT Path("/mercury/v1/tree/updateFolder/api") -> controller.ApiTreeController.UpdateApiFolderHandler
func controllerApiTreeControllerUpdateApiFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateApiFolderHandler",
		Patten:            "/mercury/v1/tree/updateFolder/api",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateApiFolderHandler(apiFolderDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/tree/updateFolder/model") -> controller.ApiTreeController.UpdateModelFolderHandler
func controllerApiTreeControllerUpdateModelFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateModelFolderHandler",
		Patten:            "/mercury/v1/tree/updateFolder/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateModelFolderHandler(apiFolderDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// PUT Path("/mercury/v1/tree/updateFolder/case") -> controller.ApiTreeController.UpdateCaseFolderHandler
func controllerApiTreeControllerUpdateCaseFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UpdateCaseFolderHandler",
		Patten:            "/mercury/v1/tree/updateFolder/case",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "PUT",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-EDIT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.UpdateCaseFolderHandler(apiFolderDTO)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/{libId}/addFolder/api") -> controller.ApiTreeController.AddApiFolderHandler
func controllerApiTreeControllerAddApiFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddApiFolderHandler",
		Patten:            "/mercury/v1/tree/{libId}/addFolder/api",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeLibIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddApiFolderHandler(apiFolderDTO, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/{libId}/addFolder/model") -> controller.ApiTreeController.AddModelFolderHandler
func controllerApiTreeControllerAddModelFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddModelFolderHandler",
		Patten:            "/mercury/v1/tree/{libId}/addFolder/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeLibIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddModelFolderHandler(apiFolderDTO, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/{libId}/addFolder/case") -> controller.ApiTreeController.AddCaseFolderHandler
func controllerApiTreeControllerAddCaseFolderHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddCaseFolderHandler",
		Patten:            "/mercury/v1/tree/{libId}/addFolder/case",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		apiFolderDTO := dto.ApiFolderDTO{}
		if i, ok := decode.Implements(&apiFolderDTO); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &apiFolderDTO); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(apiFolderDTO); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeLibIdInPathReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.AddCaseFolderHandler(apiFolderDTO, req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/ordering/api") -> controller.ApiTreeController.ApiTreeOrderingHandler
func controllerApiTreeControllerApiTreeOrderingHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ApiTreeOrderingHandler",
		Patten:            "/mercury/v1/tree/ordering/api",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-SORT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		ordering := dto.TreeOrdering{}
		if i, ok := decode.Implements(&ordering); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &ordering); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(ordering); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ApiTreeOrderingHandler(ordering)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/ordering/model") -> controller.ApiTreeController.ModelTreeOrderingHandler
func controllerApiTreeControllerModelTreeOrderingHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ModelTreeOrderingHandler",
		Patten:            "/mercury/v1/tree/ordering/model",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-SORT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		ordering := dto.TreeOrdering{}
		if i, ok := decode.Implements(&ordering); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &ordering); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(ordering); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ModelTreeOrderingHandler(ordering)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/ordering/case") -> controller.ApiTreeController.CaseTreeOrderingHandler
func controllerApiTreeControllerCaseTreeOrderingHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseTreeOrderingHandler",
		Patten:            "/mercury/v1/tree/ordering/case",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-SORT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		ordering := dto.TreeOrdering{}
		if i, ok := decode.Implements(&ordering); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &ordering); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(ordering); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseTreeOrderingHandler(ordering)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/apiNode/{id}/{libId}") -> controller.ApiTreeController.ApiNodeCopyHandler
func controllerApiTreeControllerApiNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ApiNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/apiNode/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ApiNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/folderNode/api/{id}/{libId}") -> controller.ApiTreeController.ApiFolderNodeCopyHandler
func controllerApiTreeControllerApiFolderNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ApiFolderNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/folderNode/api/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ApiFolderNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/modelNode/{id}/{libId}") -> controller.ApiTreeController.ModelNodeCopyHandler
func controllerApiTreeControllerModelNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ModelNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/modelNode/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ModelNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/folderNode/model/{id}/{libId}") -> controller.ApiTreeController.ModelFolderNodeCopyHandler
func controllerApiTreeControllerModelFolderNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ModelFolderNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/folderNode/model/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ModelFolderNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/caseNode/{id}/{libId}") -> controller.ApiTreeController.CaseNodeCopyHandler
func controllerApiTreeControllerCaseNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/caseNode/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/mercury/v1/tree/copy/folderNode/case/{id}/{libId}") -> controller.ApiTreeController.CaseFolderNodeCopyHandler
func controllerApiTreeControllerCaseFolderNodeCopyHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "CaseFolderNodeCopyHandler",
		Patten:            "/mercury/v1/tree/copy/folderNode/case/{id}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.CaseFolderNodeCopyHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/apiNode/{id}") -> controller.ApiTreeController.RemoveApiNodeHandler
func controllerApiTreeControllerRemoveApiNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveApiNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/apiNode/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveApiNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/folderNode/api/{id}") -> controller.ApiTreeController.RemoveApiFolderNodeHandler
func controllerApiTreeControllerRemoveApiFolderNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveApiFolderNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/folderNode/api/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DOC-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveApiFolderNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/modelNode/{id}") -> controller.ApiTreeController.RemoveModelNodeHandler
func controllerApiTreeControllerRemoveModelNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveModelNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/modelNode/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveModelNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/folderNode/model/{id}") -> controller.ApiTreeController.RemoveModelFolderNodeHandler
func controllerApiTreeControllerRemoveModelFolderNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveModelFolderNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/folderNode/model/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-DATAMODEL-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveModelFolderNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/caseNode/{id}") -> controller.ApiTreeController.RemoveCaseNodeHandler
func controllerApiTreeControllerRemoveCaseNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveCaseNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/caseNode/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveCaseNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// DELETE Path("/mercury/v1/tree/{libId}/folderNode/case/{id}") -> controller.ApiTreeController.RemoveCaseFolderNodeHandler
func controllerApiTreeControllerRemoveCaseFolderNodeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RemoveCaseFolderNodeHandler",
		Patten:            "/mercury/v1/tree/{libId}/folderNode/case/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "DELETE",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "接口管理",
			"auth.code":         "MAGIC-MANTIS-APILIBRARY-FASTREQUEST-DELETE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeRemoveNodeReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.RemoveCaseFolderNodeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/mercury/v1/tree/{type}/{libId}") -> controller.ApiTreeController.ViewTreeHandler
func controllerApiTreeControllerViewTreeHandlerHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ViewTreeHandler",
		Patten:            "/mercury/v1/tree/{type}/{libId}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ApiTreeController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "接口管理",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeApiTreeController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ApiTreeViewRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			ctrl.ViewTreeHandler(req1)

		}
	}
	return chain.ThenFunc(HandleFunc)
}

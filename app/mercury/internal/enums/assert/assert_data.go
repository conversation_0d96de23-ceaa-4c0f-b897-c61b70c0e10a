package assert

import (
	"encoding/json"
	"errors"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"github.com/PaesslerAG/jsonpath"
	"github.com/antchfx/xmlquery"
)

type AssertDataTypeEnum interface {
	Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string)
}

type TextAssertDataTypeEnum struct{}

func (TextAssertDataTypeEnum) Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string) {
	assert := GetAsserTypeEnumByName(assertType)
	if raw.Body == "" {
		return assert.Assert(raw.Body, assertion, path, errors.New("no content"))
	} else {
		return assert.Assert(raw.Body, assertion, path, nil)
	}
}

type JsonAssertDataTypeEnum struct{}

func (j JsonAssertDataTypeEnum) Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string) {
	rawStr := raw.Body
	data, err := j.getDataWithError(rawStr, path)
	assert := GetAsserTypeEnumByName(assertType)
	return assert.Assert(data, assertion, path, err)
}

func (JsonAssertDataTypeEnum) getDataWithError(jsonStr string, jsonPathStr string) (any, error) {
	var jsonObject any
	err := json.Unmarshal([]byte(jsonStr), &jsonObject)
	if err != nil {
		return nil, err
	}
	if jsonPathStr == "" {
		return nil, errors.New("error path, please check")
	}
	res, err := jsonpath.Get(jsonPathStr, jsonObject)
	return res, err
}

type XmlAssertDataTypeEnum struct{}

func (x XmlAssertDataTypeEnum) Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string) {
	rawStr := raw.Body
	data, err := x.getDataWithError(rawStr, path)
	assert := GetAsserTypeEnumByName(assertType)
	return assert.Assert(data, assertion, path, err)
}

func (XmlAssertDataTypeEnum) getDataWithError(xmlRaw string, xpath string) (any, error) {
	if !(strings.HasPrefix(xmlRaw, "<") && strings.HasSuffix(xmlRaw, ">")) {
		return nil, errors.New("invalid xmlx data")
	}
	parse, err := xmlquery.Parse(strings.NewReader(xmlRaw))
	if err != nil {
		return nil, err
	}
	nodes, err := xmlquery.QueryAll(parse, xpath)
	if err != nil || nodes == nil {
		return nil, errors.New("error path")
	}
	res := ""
	for _, node := range nodes {
		split := strings.Split(node.InnerText(), "\n")
		for _, s := range split {
			res += strings.TrimSpace(s)
		}
	}
	return res, nil
}

type HeaderAssertDataTypeEnum struct{}

func (HeaderAssertDataTypeEnum) Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string) {
	m := make(map[string][]string)
	for _, header := range raw.Header {
		m[header.Name] = header.Value
	}
	assert := GetAsserTypeEnumByName(assertType)
	s, ok := m[path]
	if ok {
		return assert.Assert(s, assertion, path, nil)
	} else {
		return assert.Assert(s, assertion, path, errors.New("error key"))
	}
}

type CookieAssertDataTypeEnum struct{}

func (CookieAssertDataTypeEnum) Assert(assertType string, raw models.ApiRunRecord, path string, assertion string) (success bool, message string) {
	m := make(map[string]string)
	for _, cookie := range raw.Cookie {
		m[cookie.Name] = cookie.Value
	}
	assert := GetAsserTypeEnumByName(assertType)
	s, ok := m[path]
	if ok {
		return assert.Assert(s, assertion, path, nil)
	} else {
		return assert.Assert(s, assertion, path, errors.New("error key"))
	}
}

func GetAssertDataFormatEnumByName(name string) AssertDataTypeEnum {
	switch name {
	case "Response Text":
		return TextAssertDataTypeEnum{}
	case "Response JSON":
		return JsonAssertDataTypeEnum{}
	case "Response XML":
		return XmlAssertDataTypeEnum{}
	case "Response Header":
		return HeaderAssertDataTypeEnum{}
	case "Response Cookie":
		return CookieAssertDataTypeEnum{}
	default:
		return nil
	}
}

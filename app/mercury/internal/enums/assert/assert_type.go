package assert

import (
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
)

type AssertTypeEnum interface {
	Assert(before any, assertion string, path string, err error) (success bool, message string)
}

type EqualTypeEum struct{}

func (EqualTypeEum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			if resArray.Index(0).String() == assertion {
				return true, "success"
			} else {
				return false, fmt.Sprintf("expected equal %s, actually %s", assertion, resArray.Index(0).String())
			}
		} else {
			return false, "类型错误"
		}
	} else if strings.HasPrefix(assertion, "{") && strings.HasSuffix(assertion, "}") {
		var assertionInterface any
		err1 := json.Unmarshal([]byte(assertion), &assertionInterface)
		if err1 != nil {
			return false, fmt.Sprintf("expected equal %s, actually %v", assertion, fmt.Sprintf("%v", before))
		}
		if reflect.DeepEqual(before, assertionInterface) {
			return true, "success"
		} else {
			return false, fmt.Sprintf("expected equal %s, actually %v", assertion, string(jsonx.Marshal(&before)))
		}
	} else {
		resStr := fmt.Sprintf("%v", before)
		if resStr == assertion {
			return true, "success"
		} else {
			return false, fmt.Sprintf("expected equal %s, actually %s", assertion, resStr)
		}
	}
}

type NotEqualTypeEnum struct{}

func (NotEqualTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			if resArray.Index(0).String() != assertion {
				return true, "success"
			} else {
				return false, fmt.Sprintf("expected equal %s, actually %s", assertion, resArray.Index(0).String())
			}
		} else {
			return false, "类型错误"
		}
	} else if strings.HasPrefix(assertion, "{") && strings.HasSuffix(assertion, "}") {
		var assertionInterface any
		jsonx.UnMarshal([]byte(assertion), &assertionInterface)
		if !reflect.DeepEqual(before, assertionInterface) {
			return true, "success"
		} else {
			return false, fmt.Sprintf("expected not equal %s, actually %v", assertion, string(jsonx.Marshal(&before)))
		}
	} else {
		resStr := fmt.Sprintf("%v", before)
		if resStr != assertion {
			return true, "success"
		} else {
			return false, fmt.Sprintf("expected not equal %s, actually %s", assertion, resStr)
		}
	}
}

type ExistTypeEnum struct{}

func (ExistTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err == nil {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %s exist", path)
	}
}

type NotExistTypeEnum struct{}

func (NotExistTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %s not exist", path)
	}
}

type LTTypeEnum struct{}

func (LTTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			resNum, err := strconv.ParseFloat(resArray.Index(0).String(), 64)
			if err != nil {
				return false, "类型错误"
			}
			assertionNum, err := strconv.ParseFloat(assertion, 64)
			if err != nil {
				return false, "类型错误"
			}
			if resNum < assertionNum {
				return true, "success"
			} else {
				return false, fmt.Sprintf("excepted %f less than %f", resNum, assertionNum)
			}
		} else {
			return false, "类型错误"
		}
	}
	resNum, err := strconv.ParseFloat(fmt.Sprintf("%v", before), 64)
	if err != nil {
		return false, "类型错误"
	}
	assertionNum, err := strconv.ParseFloat(assertion, 64)
	if err != nil {
		return false, "类型错误"
	}
	if resNum < assertionNum {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %f less than %f", resNum, assertionNum)
	}
}

type LTETypeEnum struct{}

func (LTETypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			resNum, err := strconv.ParseFloat(resArray.Index(0).String(), 64)
			if err != nil {
				return false, "类型错误"
			}
			assertionNum, err := strconv.ParseFloat(assertion, 64)
			if err != nil {
				return false, "类型错误"
			}
			if resNum <= assertionNum {
				return true, "success"
			} else {
				return false, fmt.Sprintf("excepted %f less equal than %f", resNum, assertionNum)
			}
		} else {
			return false, "类型错误"
		}
	}
	resNum, err := strconv.ParseFloat(fmt.Sprintf("%v", before), 64)
	if err != nil {
		return false, "类型错误"
	}
	assertionNum, err := strconv.ParseFloat(assertion, 64)
	if err != nil {
		return false, "类型错误"
	}
	if resNum <= assertionNum {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %f less than equal %f", resNum, assertionNum)
	}
}

type GTTypeEnum struct{}

func (GTTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			resNum, err := strconv.ParseFloat(resArray.Index(0).String(), 64)
			if err != nil {
				return false, "类型错误"
			}
			assertionNum, err := strconv.ParseFloat(assertion, 64)
			if err != nil {
				return false, "类型错误"
			}
			if resNum > assertionNum {
				return true, "success"
			} else {
				return false, fmt.Sprintf("excepted %f greater than %f", resNum, assertionNum)
			}
		} else {
			return false, "类型错误"
		}
	}
	resNum, err := strconv.ParseFloat(fmt.Sprintf("%v", before), 64)
	if err != nil {
		return false, "类型错误"
	}
	assertionNum, err := strconv.ParseFloat(assertion, 64)
	if err != nil {
		return false, "类型错误"
	}
	if resNum > assertionNum {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %f greater then %f", resNum, assertionNum)
	}
}

type GTETypeEnum struct{}

func (GTETypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if reflect.TypeOf(before).Kind() == reflect.Slice || reflect.TypeOf(before).Kind() == reflect.Array {
		resArray := reflect.ValueOf(before)
		if resArray.Len() == 1 {
			resNum, err := strconv.ParseFloat(resArray.Index(0).String(), 64)
			if err != nil {
				return false, "类型错误"
			}
			assertionNum, err := strconv.ParseFloat(assertion, 64)
			if err != nil {
				return false, "类型错误"
			}
			if resNum >= assertionNum {
				return true, "success"
			} else {
				return false, fmt.Sprintf("excepted %f greater equal than %f", resNum, assertionNum)
			}
		} else {
			return false, "类型错误"
		}
	}
	resNum, err := strconv.ParseFloat(fmt.Sprintf("%v", before), 64)
	if err != nil {
		return false, "类型错误"
	}
	assertionNum, err := strconv.ParseFloat(assertion, 64)
	if err != nil {
		return false, "类型错误"
	}
	if resNum >= assertionNum {
		return true, "success"
	} else {
		return false, fmt.Sprintf("excepted %f greater than equal %f", resNum, assertionNum)
	}
}

type ContainTypeEnum struct{}

func (ContainTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	switch before.(type) {
	case []any:
		resArray := before.([]any)
		for i := 0; i < len(resArray); i++ {
			s := fmt.Sprintf("%v", resArray[i])
			if strings.Contains(s, assertion) {
				return true, "success"
			}
		}
		return false, fmt.Sprintf("expected %+v contain %+v", resArray, assertion)
	case []string:
		resArray := before.([]string)
		for i := 0; i < len(resArray); i++ {
			s := resArray[i]
			if strings.Contains(s, assertion) {
				return true, "success"
			}
		}
		return false, fmt.Sprintf("expected %+v contain %+v", resArray, assertion)
	case string:
		if resStr, ok := before.(string); ok {
			if strings.Contains(resStr, assertion) {
				return true, "success"
			} else {
				return false, fmt.Sprintf("expected %s contain %s", resStr, assertion)
			}
		} else {
			return false, "类型错误"
		}
	case map[string]any:
		if resMap, ok := before.(map[string]any); ok {
			if _, exist := resMap[assertion]; exist {
				return true, "success"
			} else {
				return false, fmt.Sprintf("expected %+v contain %+v", resMap, assertion)
			}
		} else {
			return false, "类型错误"
		}
	default:
		return false, "类型错误"
	}
}

type NotContainTypeEnum struct{}

func (NotContainTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	switch before.(type) {
	case []any:
		resArray := before.([]any)
		for i := 0; i < len(resArray); i++ {
			s := fmt.Sprintf("%v", resArray[i])
			if strings.Contains(s, assertion) {
				return false, fmt.Sprintf("expected %+v not contain %+v", resArray, assertion)
			}
		}
		return true, "success"
	case []string:
		resArray := before.([]string)
		for i := 0; i < len(resArray); i++ {
			s := resArray[i]
			if strings.Contains(s, assertion) {
				return false, fmt.Sprintf("expected %+v not contain %+v", resArray, assertion)
			}
		}
		return true, "success"
	case string:
		if resStr, ok := before.(string); ok {
			if strings.Contains(resStr, assertion) {
				return false, fmt.Sprintf("expected %s not contain %s", resStr, assertion)
			} else {
				return true, "success"
			}
		} else {
			return false, "类型错误"
		}
	case map[string]any:
		if resMap, ok := before.(map[string]any); ok {
			if _, exist := resMap[assertion]; exist {
				return false, fmt.Sprintf("expected %+v not contain %+v", resMap, assertion)
			} else {
				return true, "success"
			}
		} else {
			return false, "类型错误"
		}
	default:
		return false, "类型错误"
	}
}

type IsNullTypeEnum struct{}

func (IsNullTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if before == nil {
		return true, "success"
	} else {
		return false, fmt.Sprintf("expected %s is null", path)
	}
}

type NotNullTypeEnum struct{}

func (NotNullTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if before != nil {
		return true, "success"
	} else {
		return false, fmt.Sprintf("expected %s is not null", before)
	}
}

type RegularExpressionTypeEnum struct{}

func (RegularExpressionTypeEnum) Assert(before any, assertion string, path string, err error) (success bool, message string) {
	if err != nil {
		return false, fmt.Sprintf("error path, please check")
	}
	if len(assertion) == 0 {
		return false, fmt.Sprintf("assertion is null")
	}
	switch before.(type) {
	case []any:
		resArray, ok := before.([]any)
		if len(resArray) == 1 && ok {
			if len(assertion) > 1 {
				resStr := fmt.Sprintf("%v", resArray[0])
				matched, err := regexp.MatchString(assertion, resStr)
				if matched && err == nil {
					return true, "success"
				} else {
					return false, fmt.Sprintf("expected %s match %s", resStr, assertion)
				}
			} else {
				return false, "invalid regular expression"
			}
		} else {
			return false, "类型错误"
		}
	case []string:
		resArray, ok := before.([]string)
		if len(resArray) == 1 && ok {
			if len(assertion) > 1 {
				resStr := resArray[0]
				matched, err := regexp.MatchString(assertion, resStr)
				if matched && err == nil {
					return true, "success"
				} else {
					return false, fmt.Sprintf("expected %s match %s", resStr, assertion)
				}
			} else {
				return false, "invalid regular expression"
			}
		} else {
			return false, "类型错误"
		}
	}
	if len(assertion) > 1 {
		resStr := fmt.Sprintf("%v", before)
		matched, err := regexp.MatchString(assertion, resStr)
		if matched && err == nil {
			return true, "success"
		} else {
			return false, fmt.Sprintf("expected %s match %s", resStr, assertion)
		}
	} else {
		return false, "invalid regular expression"
	}
}

func GetAsserTypeEnumByName(name string) AssertTypeEnum {
	switch name {
	case "equal":
		return EqualTypeEum{}
	case "notEqual":
		return NotEqualTypeEnum{}
	case "exist":
		return ExistTypeEnum{}
	case "notExist":
		return NotExistTypeEnum{}
	case "lessThan":
		return LTTypeEnum{}
	case "lessThanEqual":
		return LTETypeEnum{}
	case "greaterThan":
		return GTTypeEnum{}
	case "greaterThanEqual":
		return GTETypeEnum{}
	case "contain":
		return ContainTypeEnum{}
	case "notContain":
		return NotContainTypeEnum{}
	case "isNull":
		return IsNullTypeEnum{}
	case "notNull":
		return NotNullTypeEnum{}
	case "regularExpression":
		return RegularExpressionTypeEnum{}
	}
	return AssertTypeEnum(nil)
}

type AssertType struct {
	Name         string `json:"name"`
	Code         string `json:"code"`
	HasAssertion bool   `json:"hasAssertion"`
}

var assertTypeSlice []AssertType

func GetAssertTypeSlice() []AssertType {
	return assertTypeSlice
}

func init() {
	assertTypeSlice = []AssertType{
		{Name: "等于", Code: "equal", HasAssertion: true},
		{Name: "不等于", Code: "notEqual", HasAssertion: true},
		{Name: "存在", Code: "exist", HasAssertion: false},
		{Name: "不存在", Code: "notExist", HasAssertion: false},
		{Name: "小于", Code: "lessThan", HasAssertion: true},
		{Name: "小于等于", Code: "lessThanEqual", HasAssertion: true},
		{Name: "大于", Code: "greaterThan", HasAssertion: true},
		{Name: "大于等于", Code: "greaterThanEqual", HasAssertion: true},
		{Name: "包含", Code: "contain", HasAssertion: true},
		{Name: "不包含", Code: "notContain", HasAssertion: true},
		{Name: "为空", Code: "isNull", HasAssertion: false},
		{Name: "不为空", Code: "notNull", HasAssertion: false},
		{Name: "正则表达式", Code: "regularExpression", HasAssertion: true},
	}
}

package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ApiFolderDTO struct {
	Id        int64  `json:"id" validate:"-"`
	Name      string `json:"name" validate:"required=true"`
	Type      string `json:"type" validate:"required=true"`
	ParentId  int64  `json:"parentId"`
	LibId     int64  `json:"libId" validate:"-"`
	OrderNo   int64  `json:"orderNo" validate:"-"`
	NodeType  string `json:"nodeType" validate:"-"`
	Key       string `json:"key" validate:"-"`
	Children  any    `json:"children" validate:"-"`
	LeafCount int32  `json:"leafCount" validate:"-"`
}

type Auth struct {
	Type    string         `json:"type"`
	AuthObj map[string]any `json:"authObj"`
}
type BasicAuth struct {
	Username string `json:"username"`
	Password string `json:"password"`
}
type BearerToken struct {
	Token string `json:"token"`
}
type BaseParameter struct {
	Name        string `json:"name"`               // 字段名
	Required    bool   `json:"required"`           // 是否必填
	Description string `json:"description"`        // 描述
	Type        string `json:"type"`               // 字段类型
	Example     any    `json:"example"`            // 字段值
	Value       any    `json:"value"`              // 实际值
	Enable      bool   `json:"enable"`             // 是否使用
	FileName    string `json:"fileName,omitempty"` // 如果是文件的话，传输文件名
}

type BaseParameterFileValue struct {
	Name string `json:"name"`
	Url  string `json:"url"`
}

type Parameters struct {
	Query  []BaseParameter `json:"query"`
	Path   []BaseParameter `json:"path"`
	Header []BaseParameter `json:"header"`
	Cookie []BaseParameter `json:"cookie"`
}

type RequestBody struct {
	Type             string          `json:"type"`
	Parameters       []BaseParameter `json:"parameters"`
	JsonSchema       *ApiBaseSchema  `json:"jsonSchema"`
	Content          string          `json:"jsonContent"`
	Example          string          `json:"example"`
	GeneratedExample string          `json:"generatedExample"`
	Description      string          `json:"description"`
}

type ApiBaseSchema struct {
	Name        string                   `json:"name"`
	AliasName   string                   `json:"aliasName"`
	Description string                   `json:"description"`
	Type        string                   `json:"type"`
	Required    bool                     `json:"required"`
	Properties  map[string]ApiBaseSchema `json:"properties,omitempty"` // 如果是object，则存在此字段
	Ref         string                   `json:"$ref,omitempty"`       // 如果是ref，则存在此字段
	RefPath     []int64                  `json:"refPath"`              // ref路径，前端使用
	Items       *ApiBaseSchema           `json:"items,omitempty"`      // 如果是数组，则存在此字段
	Position    string                   `json:"position,omitempty"`   // 当前对象是否在外层
	IsItems     bool                     `json:"isItems"`              // 判断当前schema是手动输入的key为items还是是数组的子元素
	Example     any                      `json:"-"`
}

type ApiDocRequestDTO struct {
	Auth        *Auth        `json:"auth"`
	Parameters  *Parameters  `json:"parameters"`
	RequestBody *RequestBody `json:"requestBody"`
}

type ApiDocSaveDTO struct {
	ApiDocBase          models.ApiDocBase        `json:"apiDocBase"`
	DocRequestDTO       ApiDocRequestDTO         `json:"docRequestDTO"`
	ApiResponseList     []ApiResponseDTO         `json:"apiResponseList"`
	ApiResponseCaseList []models.ApiResponseCase `json:"apiResponseCaseList"`
}

type ApiDocJupiterDTO struct {
	ApiDocBase    models.ApiDocBase `json:"apiDocBase"`
	DocRequestDTO ApiDocRequestDTO  `json:"docRequestDTO"`
	AppId         int64             `json:"appId"`
	SpaceId       int64             `json:"spaceId"`
}

type ApiResponseDTO struct {
	Id            int64          `json:"id"`
	Name          string         `json:"name"`
	RespCode      int32          `json:"respCode"`
	ContentFormat string         `json:"contentFormat"`
	Content       *ApiBaseSchema `json:"content"`
	ApiId         int64          `json:"apiId"`
	LibId         int64          `json:"libId"`
	TempId        string         `json:"tempId"`
	OrderNo       int64          `json:"orderNo"`
}

type ApiDataModelDTO struct {
	models.ApiDataModel
	JsonSchema *ApiBaseSchema `json:"jsonSchema"` // 前端请求参数，数据库操作忽略
}

type BatchDTO struct {
	ApiIds        []int64 `json:"apiIds" validate:"required=true"`
	StatusId      int64   `json:"statusId"`
	LabelIds      []int64 `json:"labelIds"`
	ResponsibleId string  `json:"responsibleId"`
}

type ApiEnvReq struct {
	Id              int64                `json:"id"`
	Name            string               `json:"name" validate:"required=true"`
	Code            string               `json:"-"`
	PreUrl          string               `json:"preUrl"`
	LibId           int64                `json:"libId" validate:"required=true"`
	AddVariables    []models.ApiVariable `json:"addVariableDTOS"`
	UpdateVariables []models.ApiVariable `json:"updateVariableDTOS"`
	DeleteIds       []int64              `json:"deletedIds"`
}

type ApiGlobalVarReq struct {
	LibId            int64                `json:"libId" validate:"required=true"`
	AddGlobalVars    []models.ApiVariable `json:"addGlobalVars" validate:"required=true"`
	UpdateGlobalVars []models.ApiVariable `json:"updateGlobalVars"`
	DeletedIds       []int64              `json:"deletedIds"`
}

type ApiLibraryRequest struct {
	commondto.AddonTimeDTO
	Name    string `json:"name" form:"name"`
	AppId   string `json:"appId" form:"appId"`
	Creator string `json:"creator" form:"creator"`
}

type TreeOrdering struct {
	DragKey      string `json:"dragKey"`
	DropKey      string `json:"dropKey"`
	DropPosition int32  `json:"dropPosition"` // 移入位置 前/里/后， -1/0/1, example = -1
	TreeType     string `json:"treeType"`     // 移动的树 api/models/case
	NodeType     string `json:"nodeType"`     // 节点类型
}

type ApiRuntimeDTO struct {
	ApiDocRunTime *models.ApiDocRuntime `json:"apiDocRunTime"`
	Setting       *ApiSetting           `json:"setting"`
	PreOperation  *ApiPreOperation      `json:"preOperation"`
	PostOperation *ApiPostOperation     `json:"postOperation"`
	DocRequestDTO *ApiDocRequestDTO     `json:"docRequestDTO"`
	PreUrl        string                `json:"preUrl,omitempty"`
	EnvId         int64                 `json:"envId,omitempty"`
	Type          string                `json:"type"`
}

type ApiQuickDocDTO struct {
	ApiDocQuick   *models.ApiDocQuickRequest `json:"apiDocRunTime"`
	Setting       *ApiSetting                `json:"setting"`
	PreOperation  *ApiPreOperation           `json:"preOperation"`
	PostOperation *ApiPostOperation          `json:"postOperation"`
	DocRequestDTO *ApiDocRequestDTO          `json:"docRequestDTO"`
}

type ApiSetting struct {
	AutoRedirect       bool  `json:"autoRedirect"`
	TimeoutMilliSecond int64 `json:"timeoutMilliSecond"` // 初始值为-1,不进行超时判定
}

type ApiPreOperation struct{}

type ApiPostOperation struct {
	AssertSlice []Assert `json:"assertSlice"`
}

type Assert struct {
	Name       string `json:"name"`
	DataFormat string `json:"dataFormat"`
	Path       string `json:"path"`
	AssertType string `json:"assertType"`
	Assertion  string `json:"assertion"`
}

type ApiRuntimeRecordDTO struct {
	ApiRunDTO         *ApiRuntimeDTO         `json:"apiRunDTO"`
	RunRecord         *models.ApiRunRecord   `json:"runRecord"`
	PostOperationResp *PostOperationResponse `json:"postOperationResp"`
	RealRequest       *RealRequestDTO        `json:"realRequest"`
}

type RealRequestDTO struct {
	Url    string    `json:"url"`
	Method string    `json:"method"`
	Header []*Header `json:"header"`
	Body   struct {
		Type       string          `json:"type"`
		Parameters []BaseParameter `json:"parameters"`
		Content    string          `json:"content"`
	} `json:"body"`
}

type Header struct {
	Name  string   `json:"name"`
	Value []string `json:"value"`
}

type ApiRuntimeRecordListDTO struct {
	Method    string `gorm:"column:request_method;->" json:"method"`
	Name      string `gorm:"column:name;->" json:"name"`
	Path      string `gorm:"column:path;->" json:"-"`
	Success   bool   `gorm:"column:success;->" json:"success"`
	Component string `gorm:"column:component;->" json:"component"`
	Key       string `gorm:"-:all" json:"key"`
	models.ApiTreeInfo
}

func (a *ApiRuntimeRecordListDTO) GetKey() string {
	return a.Key
}

func (a *ApiRuntimeRecordListDTO) SetKey(key string) {
	a.Key = key
}

type PostOperationResponse struct {
	AssertResponses []AssertResponse `json:"assertResponses"`
}

type AssertResponse struct {
	Name    string `json:"name"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type ApiShareDTO struct {
	models.ApiShare
	ChooseIdStrSlice [][]string              `json:"chooseIds"`
	ChooseLabelIds   []commondto.CodeEnumDTO `json:"chooseLabelIds"`
	ExcludeLabelIds  []commondto.CodeEnumDTO `json:"excludeLabelIds"`
}

type ShareChooseId struct {
	NodeType string `json:"nodeType"`
	Id       int64  `json:"id"`
}

type ApiExportDTO struct {
	LibId           int64                   `json:"libId"`
	ChooseType      string                  `json:"chooseType"`
	ChooseIds       [][]string              `json:"chooseIds"`
	ChooseLabelIds  []commondto.CodeEnumDTO `json:"chooseLabelIds"`
	ExcludeLabelIds []commondto.CodeEnumDTO `json:"excludeLabelIds"`
	ApiEnv          models.ApiEnv           `json:"apiEnv"`
	DocType         string                  `json:"docType"`
}

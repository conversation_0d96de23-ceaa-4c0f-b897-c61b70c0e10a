package dto

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
)

type MockDTO struct {
	models.ApiMock
	ExpectationHeader []MockHeader    `json:"expectationHeader"`
	MockConditions    []MockCondition `json:"mockConditions"`
	CreatorName       string          `json:"creatorName"`
}

type MockCondition struct {
	Type      string `json:"type"`
	Name      string `json:"name"`
	Operation string `json:"operation"`
	Value     string `json:"value"`
}

type MockHeader struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

// MockDataHtmlDTO 专为解析前端mock富文本的DTO
type MockDataHtmlDTO struct {
	RichText string `json:"richText"`
	NodeList []struct {
		Type     string                `json:"type"`
		Children []MockDataHtmlNodeDTO `json:"children"`
	} `json:"nodeList"`
}

// MockDataHtmlNodeDTO 专为解析前端mock富文本的节点DTO
type MockDataHtmlNodeDTO struct {
	Text     string `json:"text"`
	Type     string `json:"type"`
	NodeType string `json:"nodeType"`
	Value    string `json:"value"`
}

package doc_import_export

type OpenApi3DTO struct {
	LoadAndExportDocDTOContent
	Openapi    string                                `json:"openapi"`
	Info       *OpenApi3Info                         `json:"info"`
	Servers    []OpenApi3Server                      `json:"servers"`
	Tags       []OpenApi3TagDTO                      `json:"tags"`
	Paths      map[string]map[string]OpenApi3PathDTO `json:"paths"` // key:path ; value:路径下的对象集合
	Components *OpenApi3Components                   `json:"components"`
}

type OpenApi3Info struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Version     string `json:"version"`
}

type OpenApi3Server struct {
	Url         string `json:"url"`
	Description string `json:"description"`
}

type OpenApi3TagDTO struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type OpenApi3PathDTO struct {
	Tags        []string                    `json:"tags"`
	Summary     string                      `json:"summary"`
	Description string                      `json:"description"`
	Parameters  []OpenApi3Parameters        `json:"parameters"`
	RequestBody *OpenApi3RequestBody        `json:"requestBody"`
	Responses   map[string]OpenApi3Response `json:"responses"`
}

type OpenApi3Parameters struct {
	In          string          `json:"in"`               // 位置
	Name        string          `json:"name"`             // 字段名
	Required    bool            `json:"required"`         // 是否必填
	Description string          `json:"description"`      // 描述
	Schema      *OpenApi3Schema `json:"schema,omitempty"` // 引用
}

type OpenApi3RequestBody struct {
	Content map[string]OpenApi3DataStruct `json:"content"`
}

type OpenApi3DataStruct struct {
	Schema *OpenApi3Schema `json:"schema"`
}

type OpenApi3Response struct {
	Description string                        `json:"description"`
	Content     map[string]OpenApi3DataStruct `json:"content"`
}

type OpenApi3Components struct {
	Schemas         map[string]OpenApi3Schema `json:"schemas"`
	SecuritySchemas map[string]OpenApi3Schema `json:"securitySchemas"`
}

type OpenApi3Schema struct {
	Type        string                    `json:"type"`
	Title       string                    `json:"title"`
	Description string                    `json:"description"`
	Example     any                       `json:"example"`
	Required    []string                  `json:"required"`
	Properties  map[string]OpenApi3Schema `json:"properties,omitempty"`
	Items       *OpenApi3Schema           `json:"items,omitempty"`
	Ref         string                    `json:"$ref,omitempty" mapstructure:"$ref"`
}

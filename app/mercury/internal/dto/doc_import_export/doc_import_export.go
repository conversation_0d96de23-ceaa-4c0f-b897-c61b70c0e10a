package doc_import_export

import (
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type LoadDocDTO interface {
	load()
	SetDataModelFolderId(parentId int64)
	GetDataModelFolderId() int64
	SetApiDocFolderId(parentId int64)
	GetApiDocFolderId() int64
	SetLibId(libId int64)
	GetLibId() int64
	SetUserAccount(userAccount string)
	SetSpaceId(spaceId string)
	SetXServiceName(name string)
	SetXUserCenterSession(session string)
	SetStrategy(strategy int32)
}

func GetLoadDocDTO(docType string) LoadDocDTO {
	switch docType {
	case constants.SWAGGER:
		return &SwaggerDTO{}
	case constants.Postman:
		return &PostmanDTO{}
	case constants.OPENAPI_3:
		return &OpenApi3DTO{}
	default:
		return LoadDocDTO(nil)
	}
}

type TaskRequestDTO struct {
	commondto.AddonTimeDTO
	Name       string `json:"name" form:"name"`
	DataFormat string `json:"dataFormat" form:"dataFormat"`
	Creator    string `json:"creator" form:"creator"`
}

type LoadDocDTOResponse struct {
	Id                int64       `json:"id"`
	Name              string      `json:"name"`
	LibId             int64       `json:"libId"`
	DocType           string      `json:"dataFormat"`
	Url               string      `json:"url"`
	UserName          string      `json:"userName"`
	Password          string      `json:"password"`
	Auth              bool        `json:"auth"`
	ScheduleTime      string      `json:"scheduleTime"`
	DataModelFolderId []int64     `json:"dataModelFolderId"`
	ApiDocFolderId    []int64     `json:"apiDocFolderId"`
	Strategy          int32       `json:"importMode"`
	NoticeType        int         `json:"noticeType"`
	NoticeUsers       string      `json:"noticeUsers"`
	NoticeTemplates   string      `json:"noticeTemplates"`
	Type              int32       `json:"type"`
	CronContent       string      `json:"cronContent"`
	TimeLimit         []int64     `json:"timeLimit"`
	TriggerTime       *times.Time `json:"triggerTime"`
	GmtCreated        *times.Time `json:"gmtCreated"`
	GmtModified       *times.Time `json:"gmtModified"`
	Creator           string      `json:"creator"`
	Modifier          string      `json:"modifier"`
	Running           bool        `json:"running"`
	TriggerType       int64       `json:"triggerType"`
}

type LoadDocDTORequest struct {
	Name              string  `json:"name"`
	Id                int64   `json:"id"`
	LibId             int64   `json:"libId"`
	DataModelFolderId []int64 `json:"dataModelFolderId"`
	ApiDocFolderId    []int64 `json:"apiDocFolderId"`
	Strategy          int32   `json:"importMode" mapstructure:"importMode"`
	NoticeType        int     `json:"noticeType"`
	NoticeUsers       string  `json:"noticeUsers"`
	NoticeTemplates   string  `json:"noticeTemplates"`
	Type              int32   `json:"type"`
	CronContent       string  `json:"cronContent"`
	DataFormat        string  `json:"dataFormat"`
	Url               string  `json:"url"`
	UserName          string  `json:"userName"`
	Password          string  `json:"password"`
	Auth              bool    `json:"auth"`
	TimeLimit         []int64 `json:"timeLimit"`
	ScheduleTime      string  `json:"scheduleTime"`
	TriggerType       int64   `json:"triggerType"`
}

type LoadAndExportDocDTOContent struct {
	DataModelFolderIdStr []int64  `json:"DataModelFolderIdStr"`
	DataModelFolderId    int64    `json:"dataModelFolderId"`
	ApiDocFolderIdStr    []int64  `json:"apiDocFolderIdStr"`
	ApiDocFolderId       int64    `json:"apiDocFolderId"`
	LibId                int64    `json:"libId"`
	UserAccount          string   `json:"userAccount"`
	SpaceId              string   `json:"spaceId"`
	XServiceName         string   `json:"XServiceName"`
	XUserCenterSession   string   `json:"XUserCenterSession"`
	Strategy             int32    `json:"strategy"`
	NoticeType           int      `json:"noticeType"`
	NoticeUsers          []string `json:"noticeUsers"`
	NoticeTemplates      []string `json:"noticeTemplates"`
	Type                 int32    `json:"type"`
	CronContent          string   `json:"cronContent"`
	CronContentStruct    struct {
		CurrentDate *time.Time `json:"currentDate"`
	} `json:"cronContentStruct"`
	TriggerType int64 `json:"triggerType"`
}

func (l *LoadAndExportDocDTOContent) load() {}
func (l *LoadAndExportDocDTOContent) SetDataModelFolderId(parentId int64) {
	l.DataModelFolderId = parentId
}

func (l *LoadAndExportDocDTOContent) GetDataModelFolderId() int64 {
	return l.DataModelFolderId
}

func (l *LoadAndExportDocDTOContent) SetApiDocFolderId(parentId int64) {
	l.ApiDocFolderId = parentId
}

func (l *LoadAndExportDocDTOContent) GetApiDocFolderId() int64 {
	return l.ApiDocFolderId
}

func (l *LoadAndExportDocDTOContent) SetLibId(libId int64) {
	l.LibId = libId
}

func (l *LoadAndExportDocDTOContent) GetLibId() int64 {
	return l.LibId
}

func (l *LoadAndExportDocDTOContent) SetUserAccount(userAccount string) {
	l.UserAccount = userAccount
}

func (l *LoadAndExportDocDTOContent) SetSpaceId(spaceId string) {
	l.SpaceId = spaceId
}

func (l *LoadAndExportDocDTOContent) SetXServiceName(name string) {
	l.XServiceName = name
}

func (l *LoadAndExportDocDTOContent) SetXUserCenterSession(session string) {
	l.XUserCenterSession = session
}

func (l *LoadAndExportDocDTOContent) SetStrategy(strategy int32) {
	l.Strategy = strategy
}

type ExportDTO struct {
	LibDetail models.ApiLibrary          `json:"-"`
	ApiDocs   []dto.ApiDocSaveDTO        `json:"-"`
	FolderMap map[int64]models.ApiFolder `json:"-"`
	Env       models.ApiEnv              `json:"-"`
}

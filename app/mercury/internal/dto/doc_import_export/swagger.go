package doc_import_export

type SwaggerDTO struct {
	LoadAndExportDocDTOContent
	Swagger     string                               `json:"swagger"`
	Host        string                               `json:"host"`
	Tags        []SwaggerTagDTO                      `json:"tags"`
	Paths       map[string]map[string]SwaggerPathDTO `json:"paths"` // key:path ; value:路径下的对象集合
	Definitions map[string]SwaggerSchema             `json:"definitions"`
	Consumes    []string                             `json:"consumes"`
	BasePath    string                               `json:"basePath"`
}

type SwaggerTagDTO struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type SwaggerPathDTO struct {
	Tags        []string                   `json:"tags"`
	Summary     string                     `json:"summary"`
	Description string                     `json:"description"`
	Consumes    []string                   `json:"consumes"`
	Parameters  []SwaggerParameters        `json:"parameters"`
	Responses   map[string]SwaggerResponse `json:"responses"`
	Produces    []string                   `json:"produces"`
}

type SwaggerParameters struct {
	In          string         `json:"in"`          // 位置
	Name        string         `json:"name"`        // 字段名
	Required    bool           `json:"required"`    // 是否必填
	Description string         `json:"description"` // 描述
	Type        string         `json:"type"`        // 字段类型
	Schema      *SwaggerSchema `json:"schema"`      // 引用
}

type SwaggerResponse struct {
	Description string         `json:"description"`
	Schema      *SwaggerSchema `json:"schema"`
}

type SwaggerSchema struct {
	Type        string                   `json:"type"`
	Title       string                   `json:"title"`
	Description string                   `json:"description"`
	Properties  map[string]SwaggerSchema `json:"parameters"`
	Items       *SwaggerSchema           `json:"items"`
	OriginalRef string                   `json:"originalRef"`
	Ref         string                   `json:"$ref" mapstructure:"$ref"`
	Example     any                      `json:"example"`
}

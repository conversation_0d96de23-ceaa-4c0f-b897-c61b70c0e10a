package doc_import_export

type PostmanDTO struct {
	LoadAndExportDocDTOContent
	Item []PostmanItemDTO `json:"item"`
}

type PostmanItemDTO struct {
	Name     string            `json:"name"`
	Item     []PostmanItemDTO  `json:"item"`
	Request  PostmanRequest    `json:"request"`
	Response []PostmanResponse `json:"controller"`
}

type PostmanRequest struct {
	Method string                 `json:"method"`
	Header []PostmanRequestHeader `json:"header"`
	Body   PostmanRequestBody     `json:"body"`
	Url    PostmanRequestUrl      `json:"url"`
	Auth   map[string]any         `json:"auth"`
}

type PostmanRequestHeader struct {
	Key         string `json:"key"`
	Value       string `json:"value"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Disabled    bool   `json:"disabled"`
}

type PostmanRequestBody struct {
	Mode       string                   `json:"mode"`
	FormData   []PostmanRequestFormData `json:"formData"`
	Urlencoded []PostmanRequestFormData `json:"urlencoded"`
	Raw        string                   `json:"raw"`
	Options    PostmanRequestOptions    `json:"options"`
}

type PostmanRequestUrl struct {
	Raw      string   `json:"raw"`
	Protocol string   `json:"protocol"`
	Path     []string `json:"path"`
	Query    []struct {
		Key   string `json:"key"`
		Value string `json:"value"`
	} `json:"query"`
}

type PostmanRequestFormData struct {
	Key         string `json:"key"`
	Value       string `json:"value"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Disabled    bool   `json:"disabled"`
}

type PostmanRequestOptions struct {
	Raw struct {
		Language string `json:"language"`
	} `json:"raw"`
}

type PostmanRequestUrlQuery struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type PostmanResponse struct {
	Status string `json:"status"`
	Code   int32  `json:"code"`
	Type   string `json:"type" mapstructure:"_postman_previewlanguage"`
	Body   string `json:"body"`
}

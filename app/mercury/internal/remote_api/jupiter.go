package remote_api

import (
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/pkg/errors"
)

type JupiterRemoteApi struct{}

func (j JupiterRemoteApi) ConvertMercuryDTO(ctx *commoncontext.MantisContext, doc dto.ApiDocJupiterDTO) any {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api/convertMercury",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&doc),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	if responseDTO.Code != "1000" {
		logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+responseDTO.Message))
	}
	return responseDTO.Data
}

func (j JupiterRemoteApi) SyncMercuryDTO(ctx *commoncontext.MantisContext, doc dto.ApiDocJupiterDTO) {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api/syncMercury",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&doc),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	if responseDTO.Code != "1000" {
		logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+responseDTO.Message))
	}
	return
}

func (j JupiterRemoteApi) Save2Jupiter(ctx *commoncontext.MantisContext, doc any) (float64, float64) {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&doc),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	if responseDTO.Code != "1000" {
		message := responseDTO.Message
		if strings.Contains(message, "接口名称重复") {
			logger.Logger.Panicf("%+v", errors.New("接口测试中接口名称重复"))
		} else {
			logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+message))
		}
	}
	data, ok := responseDTO.Data.(map[string]any)
	if !ok {
		logger.Logger.Panicf("error in send request to jupiter, data is not map[string]interface{}")
	}
	return data["baseId"].(float64), data["versionId"].(float64)
}

type JupiterIdVersionIdResponse struct {
	Id        int64  `json:"id"`
	VersionId int64  `json:"versionId"`
	Name      string `json:"name"`
}

func (j JupiterRemoteApi) SaveBatch2Jupiter(ctx *commoncontext.MantisContext, dtoList []dto.ApiDocJupiterDTO) map[int64]JupiterIdVersionIdResponse {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api/addBatchFromMercury",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&dtoList),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	code := responseDTO.Code
	if code != "1000" {
		message := responseDTO.Message
		if strings.Contains(message, "接口名称重复") {
			logger.Logger.Panicf("%+v", errors.New("接口测试中接口名称重复"))
		} else {
			logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+responseDTO.Message))
		}
	}
	m, ok := responseDTO.Data.(map[string]any)
	if !ok {
		logger.Logger.Panicf("error in send request to jupiter, data is not map[string]interface{}")
	}
	res := make(map[int64]JupiterIdVersionIdResponse)
	for k, v := range m {
		val := JupiterIdVersionIdResponse{}
		id, _ := strconv.ParseInt(k, 10, 64)
		jsonx.UnMarshal(jsonx.Marshal(&v), &val)
		res[id] = val
	}
	return res
}

func (j JupiterRemoteApi) SyncBatch2Jupiter(ctx *commoncontext.MantisContext, dtoList []dto.ApiDocJupiterDTO) {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api/syncBatchFromMercury",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&dtoList),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	code := responseDTO.Code
	if code != "1000" {
		logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+responseDTO.Message))
	}
}

func (j JupiterRemoteApi) GetIdNameMap(ctx *commoncontext.MantisContext, ids []int64) map[int64]string {
	headers := ctx.Header
	headers.Add("Content-Type", constants.JsonContentType)
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Modules.Jupiter.Endpoint + "/v2/api/getIdNameMap",
		Method:  "POST",
		Headers: headers,
		Body:    jsonx.Marshal(&ids),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	responseDTO := commondto.MagicResponseDTO{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		logger.Logger.Panicf("error in send request to jupiter, err=%+v", err)
	}
	code := responseDTO.Code
	if code != "1000" {
		logger.Logger.Panicf("%+v", errors.New("请求jupiter错误，信息: "+responseDTO.Message))
	}
	m, ok := responseDTO.Data.(map[string]any)
	if !ok {
		logger.Logger.Panicf("error in send request to jupiter, data is not map[string]interface{}")
	}
	res := make(map[int64]string)
	for k, v := range m {
		id, _ := strconv.ParseInt(k, 10, 64)
		val := v.(string)
		res[id] = val
	}
	return res
}

package models

import (
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiTreeInfoInterface interface {
	SetOrderNo(orderNo int64)
	SetParentKey(parentKey string)
	SetNodeType(nodeType string)
	SetKey(key string)
	SetChildren(children []ApiTreeInfoInterface)
	SetVFolderId(vFolderId int64)
	SetPreNode(preNode string)
	SetPostNode(postNode string)
	GetOrderNo() int64
	GetParentKey() string
	GetNodeType() string
	GetKey() string
	GetChildren() []ApiTreeInfoInterface
	GetVFolderId() int64
	GetId() int64
	GetPreNode() string
	GetPostNode() string
	SetIsDeleted(isDeleted string)
	GetLibId() int64
	GetTreeType() string
	SetTreeType(treeType string)
}

type ApiTreeInfo struct {
	Addons
	OrderNo   int64                  `gorm:"column:order_no;type:int8" json:"orderNo"`
	ParentKey string                 `gorm:"-" json:"parentKey"`
	NodeType  string                 `gorm:"->;column:nodetype;-:migration" json:"nodeType"`
	Children  []ApiTreeInfoInterface `gorm:"-:all" json:"children"`
	VFolderId int64                  `gorm:"-:all" json:"VFolderId"`
	PreNode   string                 `gorm:"column:pre_node;type:varchar;size:100" json:"-"`
	PostNode  string                 `gorm:"column:post_node;type:varchar;size:100" json:"-"`
	TreeType  string                 `gorm:"->;column:tree_type;-:migration" json:"treeType"`
	LibId     int64                  `gorm:"column:lib_id;type:int8" json:"libId"`
}

func (tree *ApiTreeInfo) SetOrderNo(orderNo int64) {
	tree.OrderNo = orderNo
}

func (tree *ApiTreeInfo) SetParentKey(parentKey string) {
	tree.ParentKey = parentKey
}

func (tree *ApiTreeInfo) SetNodeType(nodeType string) {
	tree.NodeType = nodeType
}

func (tree *ApiTreeInfo) SetChildren(children []ApiTreeInfoInterface) {
	tree.Children = children
}

func (tree *ApiTreeInfo) SetVFolderId(vFolderId int64) {
	tree.VFolderId = vFolderId
}

func (tree *ApiTreeInfo) GetOrderNo() int64 {
	return tree.OrderNo
}

func (tree *ApiTreeInfo) GetParentKey() string {
	return tree.ParentKey
}

func (tree *ApiTreeInfo) GetNodeType() string {
	return tree.NodeType
}

func (tree *ApiTreeInfo) GetChildren() []ApiTreeInfoInterface {
	return tree.Children
}

func (tree *ApiTreeInfo) GetVFolderId() int64 {
	return tree.VFolderId
}

func (tree *ApiTreeInfo) GetId() int64 {
	return tree.Id
}

func (tree *ApiTreeInfo) GetPreNode() string {
	return tree.PreNode
}

func (tree *ApiTreeInfo) GetPostNode() string {
	return tree.PostNode
}

func (tree *ApiTreeInfo) SetPreNode(preNode string) {
	tree.PreNode = preNode
}

func (tree *ApiTreeInfo) SetPostNode(postNode string) {
	tree.PostNode = postNode
}

func (tree *ApiTreeInfo) SetIsDeleted(isDeleted string) {
	tree.IsDeleted = isDeleted
}

func (tree *ApiTreeInfo) GetLibId() int64 {
	return tree.LibId
}

func (tree *ApiTreeInfo) GetTreeType() string {
	return tree.TreeType
}

func (tree *ApiTreeInfo) SetTreeType(treeType string) {
	tree.TreeType = treeType
}

type Cookie struct {
	Name     string     `json:"name"`
	Value    string     `json:"value"`
	Domain   string     `json:"domain"`
	Path     string     `json:"path"`
	Expires  times.Time `json:"expires"`
	MaxAge   int        `json:"maxAge"`
	Secure   bool       `json:"secure"`
	HttpOnly bool       `json:"httpOnly"`
}

type Header struct {
	Name  string   `json:"name"`
	Value []string `json:"value"`
}

type MockDataTypeExample struct {
	Example string `json:"example"`
	Result  []any  `json:"result"`
}

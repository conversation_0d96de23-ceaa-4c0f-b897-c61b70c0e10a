package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiResponse struct {
	Addons
	Name          string `gorm:"column:name;type:varchar;size:255" json:"name"`
	RespCode      int32  `gorm:"column:resp_code;type:int" json:"respCode"`
	ContentFormat string `gorm:"column:content_format;type:varchar;size:32" json:"contentFormat"`
	Content       string `gorm:"column:content;type:text" json:"content"`
	ContentRaw    any    `gorm:"-:all" json:"-"`
	ApiId         int64  `gorm:"column:api_id;type:int8;index:api_response_api_id" json:"apiId"`
	LibId         int64  `gorm:"column:lib_id;type:int8;index:api_response_lib_id" json:"libId"`
	TempId        string `gorm:"-:all" json:"tempId"`
	OrderNo       int64  `gorm:"order_no;type:int8" json:"orderNo"`
}

func (ApiResponse) TableName() string {
	return "api_response"
}

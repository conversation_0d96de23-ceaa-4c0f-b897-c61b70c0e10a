package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiVariable struct {
	Addons
	LibId       int64  `gorm:"column:lib_id;type:int8" json:"libId"`
	Type        int64  `gorm:"column:type;type:int" json:"type"` // 1 全局 2 环境
	EnvId       int64  `gorm:"column:env_id;type:int8" json:"envId"`
	VarKey      string `gorm:"column:var_key;type:varchar;size:255" json:"varKey"`
	VarValue    string `gorm:"column:var_value;type:text" json:"varValue"`
	Description string `gorm:"column:description;type:varchar;size:5000" json:"description"`
	OrderNo     int32  `gorm:"column:order_no;type:int8" json:"orderNo"`
}

func (ApiVariable) TableName() string {
	return "api_variable"
}

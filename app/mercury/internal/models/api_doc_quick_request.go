package models

import "gorm.io/gorm"

type ApiDocQuickRequest struct {
	ApiTreeInfo
	Type             string `gorm:"column:type;default:'http';type:varchar;size:32" json:"type"`
	Name             string `gorm:"column:name;type:varchar;size:255" json:"name"`
	Method           string `gorm:"column:method;type:varchar;size:32" json:"method"`
	Path             string `gorm:"column:path;type:varchar;size:512" json:"path"`
	Status           int64  `gorm:"column:status;type:int8" json:"status"`
	Description      string `gorm:"column:description;type:varchar;size:5000" json:"description"`
	Request          string `gorm:"column:request;type:text" json:"request"`
	FolderId         int64  `gorm:"column:folder_id;type:int8" json:"folderId"`
	SettingStr       string `gorm:"column:setting;type:text" json:"-"`
	PreOperationStr  string `gorm:"column:pre_operation;type:text" json:"-"`
	PostOperationStr string `gorm:"column:post_operation;type:text" json:"-"`
	Key              string `gorm:"column:key;type:varchar;size:100;index:case_key_idx" json:"key"`
}

func (a *ApiDocQuickRequest) TableName() string {
	return "api_doc_quick_request"
}

func (a *ApiDocQuickRequest) AfterFind(tx *gorm.DB) error {
	a.VFolderId = a.FolderId
	return nil
}

func (a *ApiDocQuickRequest) GetKey() string {
	return a.Key
}

func (a *ApiDocQuickRequest) SetKey(key string) {
	a.Key = key
}

package models

import (
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"gorm.io/gorm"
)

type ApiDocBase struct {
	Type             string                 `gorm:"column:type;default:'http';type:varchar;size:32" json:"type"`
	Name             string                 `gorm:"column:name;type:varchar;size:255" json:"name"`
	Method           string                 `gorm:"column:method;type:varchar;size:32" json:"method"`
	Path             string                 `gorm:"column:path;type:varchar;size:512" json:"path"`
	Status           int64                  `gorm:"column:status;type:int8" json:"status"`
	Description      string                 `gorm:"column:description;type:varchar;size:512" json:"description"`
	Request          string                 `gorm:"column:request;type:text" json:"request"`
	ResponsibleId    string                 `gorm:"column:responsible_id;type:varchar;size:100" json:"responsibleId"`
	FolderId         int64                  `gorm:"column:folder_id;type:int8" json:"folderId"`
	BindLabelId      string                 `gorm:"column:bind_label_id;type:varchar;size:255" json:"bindLabelId"`
	JupiterApiId     int64                  `gorm:"column:jupiter_api_id;type:int8" json:"jupiterApiId"`
	JupiterVersionId int64                  `gorm:"column:jupiter_version_id;type:int8" json:"jupiterVersionId"`
	BindLabelNames   []string               `gorm:"-:all" json:"bindLabelNames,omitempty"`
	BindLabelIds     []int64                `gorm:"-:all" json:"-"`
	CopyId           int64                  `gorm:"-:all" json:"-"`
	LeafCount        int32                  `gorm:"-:all" json:"leafCount"`
	StatusName       string                 `gorm:"->;column:statusName;-:migration" json:"statusName"` // 数据库查询时只读
	FolderName       string                 `gorm:"-:all" json:"folderName"`
	ResponsibleName  string                 `gorm:"-:all" json:"responsibleName"`
	CreatorName      string                 `gorm:"-:all" json:"creatorName"`
	ModifierName     string                 `gorm:"-:all" json:"modifierName"`
	DirPath          string                 `gorm:"-:all" json:"dirPath"`
	Labels           []commondto.CubeTagDTO `gorm:"-:all" json:"labels"`
	JupiterApiName   string                 `gorm:"-:all" json:"jupiterApiName,omitempty"`
	Key              string                 `gorm:"column:key;type:varchar;size:100;index:api_key_idx" json:"key"`
	ApiTreeInfo
}

func (a *ApiDocBase) TableName() string {
	return "api_doc_base"
}

func (a *ApiDocBase) AfterFind(tx *gorm.DB) error {
	a.VFolderId = a.FolderId
	return nil
}

func (a *ApiDocBase) GetKey() string {
	return a.Key
}

func (a *ApiDocBase) SetKey(key string) {
	a.Key = key
}

package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiShare struct {
	Addons
	Name              string `gorm:"column:name;type:varchar;size:255" json:"name"`
	LibId             int64  `gorm:"column:lib_id;type:int8" json:"libId"`
	EnvId             int64  `gorm:"column:env_id;type:int8" json:"envId"`
	ChooseType        string `gorm:"column:choose_type;type:varchar;size:20" json:"chooseType"`
	ChooseIdStr       string `gorm:"column:choose_ids;type:text" json:"-"`
	ChooseLabelIdStr  string `gorm:"column:choose_label_ids;type:text" json:"-"`
	ExcludeLabelIdStr string `gorm:"column:exclude_label_ids;type:text" json:"-"`
}

func (a ApiShare) TableName() string {
	return "api_share"
}

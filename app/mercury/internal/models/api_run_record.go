package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiRunRecord struct {
	Addons
	Path                  string    `gorm:"column:path;type:varchar;size:512" json:"path"`
	LibId                 int64     `gorm:"column:lib_id;type:int8" json:"libId"`
	ApiDocId              int64     `gorm:"column:api_doc_id;type:int8" json:"apiDocBaseId"`
	Name                  string    `gorm:"column:name;type:varchar;size:512" json:"name"`
	Request               string    `gorm:"column:request;type:text" json:"request"`
	RequestMethod         string    `gorm:"column:request_method;type:varchar;size:100" json:"requestMethod"`
	Success               bool      `gorm:"column:success;type:bool" json:"success"`
	RespCode              int32     `gorm:"column:resp_code;type:int" json:"respCode"`
	ContentType           string    `gorm:"column:content_type;type:varchar;size:100" json:"contentType"`
	Body                  string    `gorm:"column:body;type:text" json:"body"`
	ReturnFile            bool      `gorm:"column:return_file;type:bool" json:"returnFile"`
	FileBody              []byte    `gorm:"column:file_body;type:bytea" json:"fileBody"`
	Header                []*Header `gorm:"-:all" json:"header"`
	HeaderStr             string    `gorm:"column:header;type:text" json:"-"`
	Cookie                []*Cookie `gorm:"-:all" json:"cookie"`
	CookieStr             string    `gorm:"column:cookie;type:text" json:"-"`
	UseTime               int64     `gorm:"column:use_time;type:int8" json:"useTime"`
	PostOperationResponse string    `gorm:"column:post_operation_response;type:text" json:"postOperationResponse"`
	ContentLength         int64     `gorm:"column:content_length;type:int8" json:"contentLength"`
	IsFailed              bool      `gorm:"column:is_failed;type:bool" json:"isFailed"`
	ErrorMessage          string    `gorm:"column:error_message;type:varchar;size:5000" json:"errorMessage"`
	RealRequest           string    `gorm:"column:real_request;type:text" json:"-"`
	CreatorName           string    `gorm:"-:all" json:"creatorName"`
}

func (ApiRunRecord) TableName() string {
	return "api_run_record"
}

package models

type ApiDocBaseHistory struct {
	ApiDocBaseId int64 `gorm:"column:api_doc_base_id;type:int8;index:api_doc_base_his_api_doc_base_id_idx" json:"apiDocBaseId"`
	ApiDocBase
	ResponseList     string `gorm:"column:response_list;type:text" json:"responseList"`
	ResponseCaseList string `gorm:"column:response_case_list;type:text" json:"responseCaseList"`
}

func (a ApiDocBaseHistory) TableName() string {
	return "api_doc_base_his"
}

package models

import "gorm.io/gorm"

type ApiDataModel struct {
	Name        string `gorm:"column:name;type:varchar;size:1000" json:"name"`
	<PERSON>as<PERSON>ame   string `gorm:"column:alias_name;type:varchar;size:255" json:"aliasName"`
	Description string `gorm:"column:description;type:varchar;size:512" json:"description"`
	Content     string `gorm:"column:content;type:text" json:"content"`
	FolderId    int64  `gorm:"column:folder_id;type:int8" json:"folderId"`
	Key         string `gorm:"column:key;type:varchar;size:100;index:model_key_idx" json:"key"`
	ApiTreeInfo
}

func (a *ApiDataModel) TableName() string {
	return "api_data_model"
}

func (a *ApiDataModel) AfterFind(tx *gorm.DB) error {
	a.VFolderId = a.FolderId
	return nil
}

func (a *ApiDataModel) GetKey() string {
	return a.Key
}

func (a *ApiDataModel) SetKey(key string) {
	a.Key = key
}

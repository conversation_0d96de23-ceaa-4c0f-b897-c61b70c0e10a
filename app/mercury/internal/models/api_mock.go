package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiMock struct {
	Addons
	Name                   string `gorm:"column:name;type:varchar;size:255" json:"name"`
	Condition              string `gorm:"column:condition;type:text" json:"condition"`
	ApiDocBaseId           int64  `gorm:"column:api_doc_base_id;type:int8" json:"apiDocBaseId"`
	Enabled                *bool  `gorm:"column:enabled;type:bool" json:"enabled"`
	Sort                   int64  `gorm:"column:sort;type:int8" json:"sort"`
	ExpectationBody        string `gorm:"column:expectation_body;type:text" json:"expectationBody"`
	ExpectationContentType string `gorm:"column:expectation_content_type;type:varchar;size:200" json:"expectationContentType"`
	ExpectationHeaderStr   string `gorm:"column:expectation_header;type:text" json:"-"`
	ExpectationCode        int32  `gorm:"column:expectation_code;type:int" json:"expectationCode"`
}

func (a ApiMock) TableName() string {
	return "api_mock"
}

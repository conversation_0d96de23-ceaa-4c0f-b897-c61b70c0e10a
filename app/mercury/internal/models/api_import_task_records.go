package models

import (
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiImportTaskRecords struct {
	Addons
	TaskName     string      `gorm:"column:task_name;type:varchar;size:100" json:"taskName"`
	TaskId       int64       `gorm:"column:task_id;type:int8" json:"taskId"`
	RunTime      *times.Time `gorm:"column:run_time;type:timestamp" json:"runTime"`
	Success      bool        `gorm:"column:success;type:bool" json:"success"`
	Message      string      `gorm:"column:message;type:text" json:"message"`
	Operator     string      `gorm:"column:operator;type:varchar;size:100" json:"operator"`
	OperatorName string      `gorm:"-:all" json:"operatorName"`
}

func (ApiImportTaskRecords) TableName() string {
	return "api_import_task_records"
}

package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiResponseCase struct {
	Addons
	Name    string `gorm:"column:name;type:varchar;size:255" json:"name"`
	Content string `gorm:"column:content;type:text" json:"content"`
	RespId  string `gorm:"column:resp_id;type:int8;index:api_response_case_resp_id" json:"respId"`
	OrderNo int64  `gorm:"order_no;type:int8" json:"orderNo"`
}

func (ApiResponseCase) TableName() string {
	return "api_response_case"
}

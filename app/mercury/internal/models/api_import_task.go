package models

import (
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

type ApiImportTask struct {
	Addons
	Name         string      `gorm:"column:name;type:varchar;size:100" json:"name"`
	LibId        int64       `gorm:"column:lib_id;type:int8" json:"libId"`
	DocType      string      `gorm:"column:doc_type;type:varchar;size:100" json:"dataFormat"`
	Url          string      `gorm:"column:url;type:varchar;size:100" json:"url"`
	UserName     string      `gorm:"column:username;type:varchar;size:100" json:"userName"`
	Password     string      `gorm:"column:password;type:varchar;size:100" json:"password"`
	Auth         bool        `gorm:"column:auth;type:bool" json:"auth"`
	StartTime    *time.Time  `gorm:"column:start_time;type:timestamp" json:"-"`
	EndTime      *time.Time  `gorm:"column:end_time;type:timestamp" json:"-"`
	ScheduleTime string      `gorm:"column:cron;type:varchar;size:100" json:"scheduleTime"`
	Content      string      `gorm:"column:content;type:text" json:"-"`
	TimeLimit    []int64     `gorm:"-:all" json:"timeLimit"`
	TriggerTime  *times.Time `gorm:"column:trigger_time;type:timestamp" json:"triggerTime"`
	Running      bool        `gorm:"column:running;type:bool" json:"running"`
}

func (ApiImportTask) TableName() string {
	return "api_import_task"
}

package models

import "gorm.io/gorm"

type ApiFolder struct {
	Name           string      `gorm:"column:name;type:varchar;size:255" json:"name"`
	Type           string      `gorm:"column:type;type:varchar;size:64" json:"type"`
	ParentId       int64       `gorm:"column:parent_id;type:int8" json:"parentId"`
	Path           string      `gorm:"column:path;type:text;index:idx_path" json:"path"`
	FolderChildren []ApiFolder `gorm:"-:all" json:"folderChildren"`
	CopyId         int64       `gorm:"-:all" json:"-"`
	LeafCount      int32       `gorm:"-:all" json:"leafCount"`
	Key            string      `gorm:"column:key;type:varchar;size:100;index:folder_key_idx" json:"key"`
	ApiTreeInfo
}

func (*ApiFolder) TableName() string {
	return "api_folder"
}

func (a *ApiFolder) AfterFind(tx *gorm.DB) (err error) {
	a.VFolderId = a.ParentId
	return
}

func (a *ApiFolder) GetKey() string {
	return a.Key
}

func (a *ApiFolder) SetKey(key string) {
	a.Key = key
}

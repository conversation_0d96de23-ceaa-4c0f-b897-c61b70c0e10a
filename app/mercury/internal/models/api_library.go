package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiLibrary struct {
	Addons
	Name          string `gorm:"column:name;type:varchar;size:255" json:"name"`
	AppId         string `gorm:"column:app_id;type:text" json:"appId"`
	Description   string `gorm:"column:description;type:varchar;size:512" json:"description"`
	BindApiStatus string `gorm:"column:bind_api_status;type:varchar;size:255" json:"bindApiStatus"`
	SpaceId       string `gorm:"column:space_id;type:varchar;size:50" json:"-"`
	ApiCount      int64  `gorm:"column:api_count;type:int8" json:"apiCount"`
	AppName       string `gorm:"-:all" json:"appName"`
	CreatorName   string `gorm:"-:all" json:"creatorName"`
	ModifierName  string `gorm:"-:all" json:"modifierName"`
}

func (ApiLibrary) TableName() string {
	return "api_library"
}

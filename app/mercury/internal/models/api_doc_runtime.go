package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiDocRuntime struct {
	Addons
	Type             string `gorm:"column:type;default:'http';type:varchar;size:32" json:"type"`
	Name             string `gorm:"column:name;type:varchar;size:255" json:"name"`
	Method           string `gorm:"column:method;type:varchar;size:32" json:"method"`
	Path             string `gorm:"column:path;type:varchar;size:512" json:"path"`
	Status           int64  `gorm:"column:status;type:int8" json:"status"`
	Description      string `gorm:"column:description;type:varchar;size:5000" json:"description"`
	Request          string `gorm:"column:request;type:text" json:"request"`
	LibId            int64  `gorm:"column:lib_id;type:int8" json:"libId"`
	ApiDocBaseId     int64  `gorm:"column:api_doc_base_id;type:int8" json:"apiDocBaseId"`
	SettingStr       string `gorm:"column:setting;type:text" json:"-"`
	PreOperationStr  string `gorm:"column:pre_operation;type:text" json:"-"`
	PostOperationStr string `gorm:"column:post_operation;type:text" json:"-"`
}

func (a *ApiDocRuntime) TableName() string {
	return "api_doc_runtime"
}

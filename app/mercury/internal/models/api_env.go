package models

import . "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

type ApiEnv struct {
	Addons
	Name    string `gorm:"column:name;type:varchar;size:255" json:"name"`
	Code    string `gorm:"column:code;type:varchar;size:255" json:"code"`
	PreUrl  string `gorm:"column:pre_url;type:varchar;size:5000" json:"preUrl"`
	LibId   int64  `gorm:"column:lib_id;type:int8;index:api_env_lib_id" json:"libId"`
	OrderNo int32  `gorm:"column:order_no;type:int8" json:"orderNo"`
}

func (ApiEnv) TableName() string {
	return "api_env"
}

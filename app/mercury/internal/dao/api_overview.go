package dao

import (
	"math"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiOverviewDao struct{}

func (a ApiOverviewDao) GetApiCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	sql := `select count(1) from api_doc_base where lib_id = ? and is_deleted = 'N'`
	var res int64
	gormx.RawX(ctx, sql, &res, libId)
	return res
}

func (a ApiOverviewDao) GetPassRecordCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	sql := `select count(1) from api_run_record where lib_id = ? and success = true and is_deleted = 'N'`
	var res int64
	gormx.RawX(ctx, sql, &res, libId)
	return res
}

func (a ApiOverviewDao) GetPassRecordPercent(ctx *commoncontext.MantisContext, libId int64) float64 {
	sql1 := `select count(1) from api_run_record where lib_id = ? and success = true and is_deleted = 'N'`
	var countPass int64
	gormx.RawX(ctx, sql1, &countPass, libId)
	sql2 := `select count(1) from api_run_record where lib_id = ? and is_deleted = 'N'`
	var countRecord int64
	gormx.RawX(ctx, sql2, &countRecord, libId)
	res := float64(countPass) / float64(countRecord)
	if math.IsNaN(res) {
		return 0
	}
	return res
}

func (a ApiOverviewDao) GetCompletedApiPercent(ctx *commoncontext.MantisContext, libId int64) float64 {
	sql1 := `select count(1) from api_doc_base where lib_id = ? and status = 3 and is_deleted = 'N'`
	var countCompleted int64
	gormx.RawX(ctx, sql1, &countCompleted, libId)
	sql2 := `select count(1) from api_doc_base where lib_id = ? and is_deleted = 'N'`
	var countApi int64
	gormx.RawX(ctx, sql2, &countApi, libId)
	res := float64(countCompleted) / float64(countApi)
	if math.IsNaN(res) {
		return 0
	}
	return res
}

func (a ApiOverviewDao) GetShareCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	sql := `select count(1) from api_share where lib_id = ? and is_deleted = 'N'`
	var res int64
	gormx.RawX(ctx, sql, &res, libId)
	return res
}

func (a ApiOverviewDao) GetApiCoverage(ctx *commoncontext.MantisContext, libId int64) float64 {
	sql1 := `select count(1) from api_doc_base where lib_id = ? and is_deleted = 'N' and jupiter_api_id is not null and jupiter_api_id != 0`
	var countCovered int64
	gormx.RawX(ctx, sql1, &countCovered, libId)
	sql2 := `select count(1) from api_doc_base where lib_id = ? and is_deleted = 'N'`
	var countApi int64
	gormx.RawX(ctx, sql2, &countApi, libId)
	res := float64(countCovered) / float64(countApi)
	if math.IsNaN(res) {
		return 0
	}
	return res
}

package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiMockDao struct{}

// SelectLastSortByDocId 查询一个文档下mock的最大排序
func (ApiMockDao) SelectLastSortByDocId(ctx *commoncontext.MantisContext, docId int64) int64 {
	sql := `select coalesce(max(sort), -1) from api_mock where api_doc_base_id = ? and is_deleted = 'N'`
	var res int64
	gormx.RawX(ctx, sql, &res, docId)
	return res
}

// UpdateSortAfter 将docId文档下afterSort之后的mock的sort+1
func (ApiMockDao) UpdateSortAfter(ctx *commoncontext.MantisContext, docId int64, afterSort int64) {
	sql := `update api_mock set sort = sort + 1 where api_doc_base_id = ? and sort > ?`
	gormx.ExecX(ctx, sql, docId, afterSort)
}

// UpdateSortPre 将docId文档下preSort之前的mock的sort-1
func (ApiMockDao) UpdateSortPre(ctx *commoncontext.MantisContext, docId int64, preSort int64) {
	sql := `update api_mock set sort = sort - 1 where api_doc_base_id = ? and sort < ?`
	gormx.ExecX(ctx, sql, docId, preSort)
}

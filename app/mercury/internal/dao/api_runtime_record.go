package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiRuntimeRecordDao struct{}

func (a ApiRuntimeRecordDao) SelectNodesByLibId(ctx *commoncontext.MantisContext, libId int64) []dto.ApiRuntimeRecordListDTO {
	sql := `select id, name, path, success, request_method, gmt_created, ? as nodeType,concat('recordNode.',id) as "key", 'historyData' as component from api_run_record where lib_id = ? and is_deleted = 'N' order by gmt_created desc`
	res := make([]dto.ApiRuntimeRecordListDTO, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeMenu, libId)
	return res
}

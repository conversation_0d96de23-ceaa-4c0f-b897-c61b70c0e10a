package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiStatusDao struct{}

func (a ApiStatusDao) GetApiStatusList(ctx *commoncontext.MantisContext) []models.ApiStatus {
	builder := gormx.NewParamBuilder()
	builder.Eq("is_deleted", constants.DeleteNo)
	statuses := make([]models.ApiStatus, 0)
	gormx.SelectByParamBuilderX(ctx, builder, &statuses)
	return statuses
}

func (a ApiStatusDao) GetApiStatusById(ctx *commoncontext.MantisContext, id int64) *models.ApiStatus {
	apiStatus := models.ApiStatus{}
	apiStatus.Id = id
	apiStatus.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &apiStatus)
	return &apiStatus
}

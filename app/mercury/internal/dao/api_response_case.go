package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiResponseCaseDao struct{}

func (a ApiResponseCaseDao) GetApiResponseCaseListByApiResponseIds(ctx *commoncontext.MantisContext, ids []int64) []models.ApiResponseCase {
	apiResponseCases := make([]models.ApiResponseCase, 0)
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("resp_id", ids).Eq("is_deleted", constants.DeleteNo)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiResponseCases)
	return apiResponseCases
}

func (a ApiResponseCaseDao) UpdateApiResponseCaseByApiResponseIds(ctx *commoncontext.MantisContext, ids []int64, respCase models.ApiResponseCase) {
	respCase.GmtModified = times.Now()
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("id", ids)
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &respCase)
}

func (a ApiResponseCaseDao) UpdateApiResponseCaseById(ctx *commoncontext.MantisContext, respCase *models.ApiResponseCase) {
	gormx.UpdateOneByConditionX(ctx, respCase)
}

func (a ApiResponseCaseDao) SaveApiResponseCaseList(ctx *commoncontext.MantisContext, respCaseList []models.ApiResponseCase) {
	gormx.InsertBatchX(ctx, respCaseList)
}

package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiDocQuickDao struct{}

func (a ApiDocQuickDao) SelectNodeByLibId(ctx *commoncontext.MantisContext, libId int64) []models.ApiDocQuickRequest {
	sql := `select *, 
       ? as nodeType, 'case' as "tree_type"  from api_doc_quick_request where lib_id = ? and is_deleted = 'N'`
	res := make([]models.ApiDocQuickRequest, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeCase, libId)
	return res
}

package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiEnvDao struct{}

func (a ApiEnvDao) GetMaxOrderNo(ctx *commoncontext.MantisContext, libId int64) int32 {
	sql := "select coalesce(max(order_no), 0) from api_env where lib_id=? and is_deleted='N' "
	var res int32
	gormx.RawX(ctx, sql, &res, libId)
	return res
}

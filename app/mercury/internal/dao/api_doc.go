package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiDocDao struct{}

func (a ApiDocDao) GetApiDocById(ctx *commoncontext.MantisContext, id int64) *models.ApiDocBase {
	apiDocById := models.ApiDocBase{}
	apiDocById.Id = id
	apiDocById.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &apiDocById)
	return &apiDocById
}

func (a ApiDocDao) UpdateApiDocById(ctx *commoncontext.MantisContext, docBase *models.ApiDocBase) {
	gormx.InsertUpdateOneX(ctx, docBase)
}

func (a ApiDocDao) SaveApiDoc(ctx *commoncontext.MantisContext, docBase *models.ApiDocBase) {
	gormx.InsertUpdateOneX(ctx, docBase)
}

func (a ApiDocDao) SelectApiNum(ctx *commoncontext.MantisContext, m map[int64]int64) map[int64]int64 {
	libIds := make([]int64, 0, len(m))
	type tmp struct {
		LibId int64
		Num   int64
	}
	res := make([]tmp, 0)
	for k := range m {
		libIds = append(libIds, k)
	}
	sql := "select lib_id, count(1) as num from api_doc_base adb where lib_id in ? and is_deleted = 'N' group by lib_id"
	gormx.RawX(ctx, sql, &res, libIds)
	resMap := make(map[int64]int64)
	for _, v := range res {
		resMap[v.LibId] = v.Num
	}
	return resMap
}

func (a ApiDocDao) SelectNodeByLibId(ctx *commoncontext.MantisContext, libId int64) []models.ApiDocBase {
	sql := `select t1.*, 
       ? as nodeType, 'api' as "tree_type", t2.name as statusName 
		from api_doc_base t1 left join api_status t2 on t1.status =t2.id where t1.lib_id=? and t1.is_deleted='N' `
	res := make([]models.ApiDocBase, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeApi, libId)
	return res
}

func (a ApiDocDao) SelectNodeByIds(ctx *commoncontext.MantisContext, ids []int64) []models.ApiDocBase {
	sql := `select t1.*,
		? as nodeType, 'api' as "tree_type", t2.name as statusName
		from api_doc_base t1 left join api_status t2 
		on t1.status =t2.id where t1.id in ? and t1.is_deleted='N' `
	res := make([]models.ApiDocBase, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeApi, ids)
	return res
}

func (a ApiDocDao) SelectNodeByFolderIds(ctx *commoncontext.MantisContext, folderIds []int64) []models.ApiDocBase {
	sql := `select t1.id,t1.name,t1.type,t1.method,t1.path,t1.status,t1.responsible_id,t1.lib_id,t1.folder_id,t1.order_no,
       ? as nodeType, 'api' as "tree_type", t1.creator,t1.modifier,t1.bind_label_id,t2.name as statusName
		from api_doc_base t1 left join api_status t2
		on t1.status =t2.id where t1.folder_id in ? and t1.is_deleted='N' `
	res := make([]models.ApiDocBase, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeApi, folderIds)
	return res
}

func (a ApiDocDao) GetMaxOrderNo(ctx *commoncontext.MantisContext, parentId int64, libId int64) int64 {
	sql := "select coalesce(max(order_no), -1) as res from api_doc_base where lib_id = ? and folder_id = ?"
	var res int64
	gormx.RawX(ctx, sql, &res, libId, parentId)
	return res
}

// RecursiveDeleteRef 循环删除引用
func (a ApiDocDao) RecursiveDeleteRef(ctx *commoncontext.MantisContext, ref string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_doc_base set "request" = replace("request", ?, '""'), "modifier" = ?, "gmt_modified" = ? where id in (
				select distinct id from (
					select id, jsonb_path_query("request"::jsonb, '$.**."$ref"')::text as "json_schema" from api_doc_base where lib_id = ? and request != '' and is_deleted = 'N'
				) t where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, userAccount, time, libId, ref)
}

// RecursiveUpdateRef 循环删除引用
func (a ApiDocDao) RecursiveUpdateRef(ctx *commoncontext.MantisContext, ref string, newRef string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_doc_base set "request" = replace("request", ?, ?), "modifier" = ?, "gmt_modified" = ? where id in (
				select distinct id from (
					select id, jsonb_path_query("request"::jsonb, '$.**."$ref"')::text as "json_schema" from api_doc_base where lib_id = ? and request != '' and is_deleted = 'N'
				) t where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, newRef, userAccount, time, libId, ref)
}

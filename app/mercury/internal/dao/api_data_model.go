package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiDataModelDao struct{}

func (a ApiDataModelDao) InsertAndUpdateApiDataModel(ctx *commoncontext.MantisContext, model *models.ApiDataModel) {
	gormx.InsertUpdateOneX(ctx, model)
}

func (a ApiDataModelDao) GetApiModelById(ctx *commoncontext.MantisContext, id int64) *models.ApiDataModel {
	model := models.ApiDataModel{}
	model.Id = id
	model.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &model)
	return &model
}

func (a ApiDataModelDao) SelectNodeByFolderIds(ctx *commoncontext.MantisContext, ids []int64) []models.ApiDataModel {
	sql := `select *,? as nodeType,creator,modifier, 'model' as "tree_type" from api_data_model where is_deleted='N' and folder_id in ?`
	res := make([]models.ApiDataModel, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeModel, ids)
	return res
}

func (a ApiDataModelDao) SelectNodeByLibId(ctx *commoncontext.MantisContext, libId int64) []models.ApiDataModel {
	res := make([]models.ApiDataModel, 0)
	sql := `select *,? as nodeType,creator,modifier, 'model' as "tree_type" from api_data_model where lib_id=? and is_deleted='N' `
	gormx.RawX(ctx, sql, &res, constants.NodeTypeModel, libId)
	return res
}

func (a ApiDataModelDao) GetMaxOrderNo(ctx *commoncontext.MantisContext, parentId int64, libId int64) int64 {
	sql := "select coalesce(max(order_no), -1) as res from api_data_model where lib_id = ? and folder_id = ?"
	var res int64
	gormx.RawX(ctx, sql, &res, libId, parentId)
	return res
}

// RecursiveDeleteRef 循环删除引用
func (a ApiDataModelDao) RecursiveDeleteRef(ctx *commoncontext.MantisContext, ref string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_data_model set "content" = replace("content", ?, '""'), "modifier" = ?, "gmt_modified" = ? where id in (
				select id from (
					select distinct id, jsonb_path_query("content"::jsonb, '$.**."$ref"')::text as "json_schema" from api_data_model where lib_id = ? and is_deleted = 'N'
				) t where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, userAccount, time, libId, ref)
}

// RecursiveUpdateRef 循环更新引用
func (a ApiDataModelDao) RecursiveUpdateRef(ctx *commoncontext.MantisContext, ref string, newRef string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_data_model set "content" = replace("content", ?, ?), "modifier" = ?, "gmt_modified" = ? where id in (
				select id from (
					select distinct id, jsonb_path_query("content"::jsonb, '$.**."$ref"')::text as "json_schema" from api_data_model where lib_id = ? and is_deleted = 'N'
				) t where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, newRef, userAccount, time, libId, ref)
}

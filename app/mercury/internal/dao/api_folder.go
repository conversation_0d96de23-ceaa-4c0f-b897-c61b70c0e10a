package dao

import (
	"fmt"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

type ApiFolderDao struct{}

func (a ApiFolderDao) SelectNodeFolderByLibIdAndType(ctx *commoncontext.MantisContext, typeStr string, libId int64) []models.ApiFolder {
	sql := `select *,? as nodeType, 
       type as "tree_type" from api_folder where lib_id=? and type=? and is_deleted='N' `
	res := make([]models.ApiFolder, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeFolder, libId, typeStr)
	return res
}

func (a ApiFolderDao) SelectNodeFolderByIds(ctx *commoncontext.MantisContext, typeStr string, ids []int64) []models.ApiFolder {
	sql := `select *,? as nodeType, 
       	type as "tree_type" from api_folder where id in ? and type=? and is_deleted='N' `
	res := make([]models.ApiFolder, 0)
	gormx.RawX(ctx, sql, &res, constants.NodeTypeFolder, ids, typeStr)
	return res
}

func (a ApiFolderDao) SelectPathById(ctx *commoncontext.MantisContext, id int64) string {
	sql := "select path from api_folder where id=?"
	var res string
	gormx.RawX(ctx, sql, &res, id)
	return res
}

func (a ApiFolderDao) SelectFolderTreeById(ctx *commoncontext.MantisContext, id int64) models.ApiFolder {
	folder := models.ApiFolder{}
	folder.Id = id
	folder.IsDeleted = common_constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &folder)
	a.selectChildren(ctx, &folder)
	return folder
}

func (a ApiFolderDao) selectChildren(ctx *commoncontext.MantisContext, folder *models.ApiFolder) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("parent_id", folder.Id).Eq("is_deleted", common_constants.DeleteNo)
	children := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &children)
	if children != nil && len(children) != 0 {
		for i, apiFolder := range children {
			a.selectChildren(ctx, &apiFolder)
			children[i] = apiFolder
		}
	}
	folder.FolderChildren = children
}

func (a ApiFolderDao) SelectNodeFolderById(ctx *commoncontext.MantisContext, id int64, path string) []models.ApiFolder {
	sql := `select id,type,name,parent_id,lib_id,path,order_no,gmt_created,? as nodeType, "key" from api_folder where path like ? and is_deleted='N' `
	res := make([]models.ApiFolder, 0)
	likeStr := path + strconv.FormatInt(id, 10) + ",%"
	gormx.RawX(ctx, sql, &res, constants.NodeTypeFolder, likeStr)
	return res
}

func (a ApiFolderDao) MoveToInner(ctx *commoncontext.MantisContext, id int64, pathOld string, pathPrefix string) {
	sql := "update api_folder set path = replace(path, ?, ?) where id = ? or path like ?"
	idStr := pathOld + strconv.FormatInt(id, 10) + ",%"
	gormx.ExecX(ctx, sql, pathOld, pathPrefix, id, idStr)
}

func (a ApiFolderDao) GetMaxOrderNo(ctx *commoncontext.MantisContext, parentId int64, libId int64) int64 {
	sql := "select coalesce(max(order_no), -1) as res from api_folder where lib_id = ? and parent_id = ?"
	var res int64
	gormx.RawX(ctx, sql, &res, libId, parentId)
	return res
}

// GetChildFolderIdByParentId 获取所有的自目录id，接口包含传入的parentId
func (a ApiFolderDao) GetChildFolderIdByParentId(ctx *commoncontext.MantisContext, parentId int64, libId int64) []int64 {
	if parentId != 0 {
		sql := `WITH RECURSIVE subordinates AS (
				SELECT id, parent_id FROM api_folder WHERE id = ? and is_deleted = 'N'
				UNION
				SELECT e.id, e.parent_id FROM api_folder e
      			INNER JOIN subordinates s ON s.id = e.parent_id 
			) SELECT id FROM subordinates;`
		res := make([]int64, 0)
		gormx.RawX(ctx, sql, &res, parentId)
		return res
	} else {
		sql := `select id from api_folder where lib_id = ? and is_deleted = 'N'`
		res := make([]int64, 0)
		gormx.RawX(ctx, sql, &res, libId)
		res = append(res, 0)
		return res
	}
}

// FixOrderingPath 移动目录后进行path修正
func (a ApiFolderDao) FixOrderingPath(ctx *commoncontext.MantisContext, dragKey string, dropKey string, pos int32) {
	drag := models.ApiFolder{}
	drag.Key = dragKey
	gormx.SelectOneByConditionX(ctx, &drag)
	drop := models.ApiFolder{}
	drop.Key = dropKey
	gormx.SelectOneByConditionX(ctx, &drop)
	dragPath := drag.Path
	if pos == 0 {
		drag.ParentId = drop.Id
		drag.Path = drop.Path + fmt.Sprintf("%d,", drop.Id)
	} else {
		drag.ParentId = drop.ParentId
		drag.Path = drop.Path
	}
	gormx.InsertUpdateOneX(ctx, &drag)
	// 修正drag的子目录们
	sql := `update api_folder set path = REPLACE(path, ?, ? ) where path like ? and type = ? and is_deleted = 'N'`
	gormx.ExecX(ctx, sql, dragPath, drag.Path, fmt.Sprintf("%s%d,", dragPath, drag.Id)+"%", drag.Type)
}

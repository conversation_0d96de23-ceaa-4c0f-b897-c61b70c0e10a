package dao

import (
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

type ApiImportTaskDao struct{}

func (a ApiImportTaskDao) GetRunningTask(ctx *commoncontext.MantisContext) []models.ApiImportTask {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiImportTask{}).Eq("is_deleted", constants.DeleteNo).Gt("end_time", time.Now()).Eq("running", true)
	res := make([]models.ApiImportTask, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
	return res
}

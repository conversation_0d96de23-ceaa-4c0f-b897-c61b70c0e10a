package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiLibraryDao struct{}

func (a ApiLibraryDao) CheckName(ctx *commoncontext.MantisContext, spaceId string, name string) int64 {
	sql := "select id from api_library al where space_id = ? and name = ? and is_deleted='N' limit 1"
	var res int64
	gormx.RawX(ctx, sql, &res, spaceId, name)
	return res
}

package dao

import (
	sql2 "database/sql"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

type ApiVariableDao struct{}

func (a ApiVariableDao) GetMaxOrderNo(ctx *commoncontext.MantisContext, envId int64, libId int64, typeId int64) int32 {
	sql := `select max(order_no) from api_variable where is_deleted='N'`
	if envId != 0 {
		sql += " and env_id = " + strconv.FormatInt(envId, 10)
	}
	if typeId != 0 {
		sql += " and type = " + strconv.FormatInt(typeId, 10)
	}
	if libId != 0 {
		sql += " and lib_id = " + strconv.FormatInt(libId, 10)
	}
	var res sql2.NullInt32
	gormx.RawX(ctx, sql, &res)
	return res.Int32
}

func (a ApiVariableDao) GetVarMap(ctx *commoncontext.MantisContext, libId int64, envId int64) map[string]string {
	sql := `select * from api_variable where ((type = 1 and lib_id = ?) or (type = 2 and env_id = ?)) and is_deleted = 'N'`
	vars := make([]models.ApiVariable, 0)
	gormx.RawX(ctx, sql, &vars, libId, envId)
	res := make(map[string]string)
	for _, variable := range vars {
		if _, ok := res[variable.VarKey]; ok {
			if variable.Type == 2 {
				res[variable.VarKey] = variable.VarValue
			}
		} else {
			res[variable.VarKey] = variable.VarValue
		}
	}
	return res
}

func (a ApiVariableDao) GetCurrentVars(ctx *commoncontext.MantisContext, libId int64, envId int64) []models.ApiVariable {
	sql := `select * from api_variable where ((type = 1 and lib_id = ?) or (type = 2 and env_id = ?)) and is_deleted = 'N'`
	vars := make([]models.ApiVariable, 0)
	gormx.RawX(ctx, sql, &vars, libId, envId)
	return vars
}

func (a ApiVariableDao) GetVarKeySet(ctx *commoncontext.MantisContext, libId int64, envId int64) set.Set[string] {
	sql := `select * from api_variable where ((type = 1 and lib_id = ?) or (type = 2 and env_id = ?)) and is_deleted = 'N'`
	vars := make([]models.ApiVariable, 0)
	gormx.RawX(ctx, sql, &vars, libId, envId)
	res := set.New[string]()
	for _, variable := range vars {
		res.Add(variable.VarKey)
	}
	return res
}

func (a ApiVariableDao) GetAllVarKeySet(ctx *commoncontext.MantisContext, libId int64) set.Set[string] {
	sql := `select * from api_variable where lib_id = ? and is_deleted = 'N'`
	vars := make([]models.ApiVariable, 0)
	gormx.RawX(ctx, sql, &vars, libId)
	res := set.New[string]()
	for _, variable := range vars {
		res.Add(variable.VarKey)
	}
	return res
}

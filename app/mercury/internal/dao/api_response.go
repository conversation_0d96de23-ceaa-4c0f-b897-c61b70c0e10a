package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
)

type ApiResponseDao struct{}

func (a ApiResponseDao) GetResponseListByApiDocId(ctx *commoncontext.MantisContext, apiId int64) []models.ApiResponse {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", apiId).Eq("is_deleted", constants.DeleteNo)
	responses := make([]models.ApiResponse, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &responses)
	return responses
}

func (a ApiResponseDao) UpdateResponseByApiDocId(ctx *commoncontext.MantisContext, apiDocId int64, resp models.ApiResponse) {
	resp.GmtModified = times.Now()
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", apiDocId)
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &resp)
}

func (a ApiResponseDao) UpdateResponseByIds(ctx *commoncontext.MantisContext, ids []int64, resp models.ApiResponse) {
	resp.GmtModified = times.Now()
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponse{}).In("id", ids)
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &resp)
}

func (a ApiResponseDao) UpdateResponseById(ctx *commoncontext.MantisContext, resp *models.ApiResponse) {
	gormx.UpdateOneByConditionX(ctx, resp)
}

func (a ApiResponseDao) InsertResponses(ctx *commoncontext.MantisContext, responses []models.ApiResponse) {
	gormx.InsertBatchX(ctx, responses)
}

// RecursiveDeleteRef 循环删除引用
func (a ApiResponseDao) RecursiveDeleteRef(ctx *commoncontext.MantisContext, ref string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_response set "content" = replace("content", ?, '""'), "modifier" = ?, "gmt_modified" = ? where id in (
				select distinct id from (
					select id, jsonb_path_query("content"::jsonb, '$.**."$ref"')::text as "json_schema" from api_response where lib_id = ? and content_format = 'json' and content != '' and is_deleted = 'N'
				) t
				where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, userAccount, time, libId, ref)
}

// RecursiveUpdateRef 循环删除引用
func (a ApiResponseDao) RecursiveUpdateRef(ctx *commoncontext.MantisContext, ref string, newRef string, libId int64, userAccount string, time *times.Time) {
	sql := `update api_response set "content" = replace("content", ?, ?), "modifier" = ?, "gmt_modified" = ? where id in (
				select distinct id from (
					select id, jsonb_path_query("content"::jsonb, '$.**."$ref"')::text as "json_schema" from api_response where lib_id = ? and content_format = 'json' and content != '' and is_deleted = 'N'
				) t
				where "json_schema" = ?
			)`
	gormx.ExecX(ctx, sql, ref, newRef, userAccount, time, libId, ref)
}

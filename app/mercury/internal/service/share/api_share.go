package share

import (
	"container/list"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/mocker"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/tree_node/tree_operate"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/remote_api"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"golang.org/x/exp/slices"
)

var (
	apiDocDao              dao.ApiDocDao
	apiStatusDao           dao.ApiStatusDao
	apiFolderDao           dao.ApiFolderDao
	jupiterRemoteApi       remote_api.JupiterRemoteApi
	apiDataModelService    service.ApiDataModelService
	apiResponseService     service.ApiResponseService
	apiResponseCaseService service.ApiResponseCaseService
	treeOperation          tree_operate.TreeOperation
	apiRunService          service.ApiRunService
	apiMockService         mocker.ApiMockService
)

type ApiShareService struct{}

const rootId int64 = 0

var rootKey = constants.NodeTypeFolder + "." + strconv.FormatInt(rootId, 10)

func (a ApiShareService) SaveShare(ctx *commoncontext.MantisContext, shareDTO dto.ApiShareDTO, user commondto.UserInfo) {
	share := shareDTO.ApiShare
	share.ExcludeLabelIdStr = string(jsonx.Marshal(&(shareDTO.ExcludeLabelIds)))
	share.ChooseIdStr = string(jsonx.Marshal(&(shareDTO.ChooseIdStrSlice)))
	share.ChooseLabelIdStr = string(jsonx.Marshal(&(shareDTO.ChooseLabelIds)))
	share.IsDeleted = commonconstants.DeleteNo
	share.GmtModified = times.Now()
	share.Modifier = user.AdAccount
	if share.Id == 0 {
		share.GmtCreated = times.Now()
		share.Creator = user.AdAccount
	}
	gormx.InsertUpdateOneX(ctx, &share)
}

func (a ApiShareService) GetShareById(ctx *commoncontext.MantisContext, id int64) dto.ApiShareDTO {
	share := models.ApiShare{}
	share.Id = id
	gormx.SelectOneByConditionX(ctx, &share)
	return a.convertModel2DTO(share)
}

func (a ApiShareService) GetShares(ctx *commoncontext.MantisContext, libId int64) []dto.ApiShareDTO {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiShare{}).Eq("lib_id", libId).Eq("is_deleted", commonconstants.DeleteNo)
	shares := make([]models.ApiShare, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &shares)
	res := make([]dto.ApiShareDTO, 0)
	for _, share := range shares {
		shareDTO := dto.ApiShareDTO{}
		shareDTO.ApiShare = share
		res = append(res, shareDTO)
	}
	return res
}

func (a ApiShareService) convertModel2DTO(share models.ApiShare) dto.ApiShareDTO {
	res := dto.ApiShareDTO{}
	res.ApiShare = share
	chooseIds := make([][]string, 0)
	jsonx.UnMarshal([]byte(share.ChooseIdStr), &chooseIds)
	chooseLabelIds := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ChooseLabelIdStr), &chooseLabelIds)
	excludeLabelIds := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ExcludeLabelIdStr), &excludeLabelIds)
	res.ChooseIdStrSlice = chooseIds
	res.ChooseLabelIds = chooseLabelIds
	res.ExcludeLabelIds = excludeLabelIds
	return res
}

func (a ApiShareService) GetShareData(ctx *commoncontext.MantisContext, id int64) []dto.ApiFolderDTO {
	apiShare := models.ApiShare{}
	apiShare.Id = id
	apiShare.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &apiShare)
	switch apiShare.ChooseType {
	case constants.AllShareType:
		return a.getShareDataAll(ctx, apiShare)
	case constants.LabelShareType:
		return a.getShareDataByLabel(ctx, apiShare)
	case constants.ChooseIdType:
		return a.getShareDataByChooseIds(ctx, apiShare)
	}
	return nil
}

func (a ApiShareService) GetDataInHubByAppId(ctx *commoncontext.MantisContext, appId string) []dto.ApiFolderDTO {
	// 先查询lib
	libs := make([]models.ApiLibrary, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiLibrary{}).Eq("app_id", appId).
			Eq("is_deleted", commonconstants.DeleteNo).OrderByDesc("gmt_created").Limit(1),
		&libs)
	if len(libs) == 0 {
		return nil
	}
	libId := libs[0].Id
	apiFolderDTO := dto.ApiFolderDTO{}
	apiFolderDTO.Id = rootId
	apiFolderDTO.Key = rootKey
	apiFolderDTO.LibId = libId
	apiFolderDTO.NodeType = constants.NodeTypeFolder
	apiFolderMap := make(map[int64]*models.ApiFolder)
	apiFolders := apiFolderDao.SelectNodeFolderByLibIdAndType(ctx, "api", libId)
	for i, apiFolder := range apiFolders {
		apiFolderMap[apiFolder.Id] = &apiFolders[i]
	}
	var apiTreeNodes []models.ApiTreeInfoInterface
	apiFolderDTO.Name = "接口"
	apiFolderDTO.Type = constants.FolderTypeApi
	docBases := apiDocDao.SelectNodeByLibId(ctx, libId)
	for i := range docBases {
		apiTreeNodes = append(apiTreeNodes, &(docBases[i]))
	}
	a.leafCount(apiFolderMap, apiTreeNodes)
	tree := a.convertTree(ctx, apiFolderMap, apiTreeNodes, libId, constants.FolderTypeApi)
	apiFolderDTO.Children = tree.Children
	apiFolderDTO.LeafCount = int32(len(docBases))
	// 查询model树
	modelFolderDTO := a.getModels(ctx, models.ApiShare{LibId: libId})
	return []dto.ApiFolderDTO{apiFolderDTO, modelFolderDTO}
}

func (a ApiShareService) getShareDataAll(ctx *commoncontext.MantisContext, share models.ApiShare) []dto.ApiFolderDTO {
	excludeLabels := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ExcludeLabelIdStr), &excludeLabels)
	// 查询接口树
	apiFolderDTO := dto.ApiFolderDTO{}
	apiFolderDTO.Id = rootId
	apiFolderDTO.Key = rootKey
	apiFolderDTO.LibId = share.LibId
	apiFolderDTO.NodeType = constants.NodeTypeFolder
	apiFolderMap := make(map[int64]*models.ApiFolder)
	apiFolders := apiFolderDao.SelectNodeFolderByLibIdAndType(ctx, "api", share.LibId)
	for i, apiFolder := range apiFolders {
		apiFolderMap[apiFolder.Id] = &apiFolders[i]
	}
	var apiTreeNodes []models.ApiTreeInfoInterface
	apiFolderDTO.Name = "接口"
	apiFolderDTO.Type = constants.FolderTypeApi
	docBases := a.excludeTags(apiDocDao.SelectNodeByLibId(ctx, share.LibId), excludeLabels)
	for i := range docBases {
		apiTreeNodes = append(apiTreeNodes, docBases[i])
	}
	a.leafCount(apiFolderMap, apiTreeNodes)
	tree := a.convertTree(ctx, apiFolderMap, apiTreeNodes, share.LibId, constants.FolderTypeApi)
	apiFolderDTO.Children = tree.Children
	apiFolderDTO.LeafCount = int32(len(docBases))
	// 查询model树
	modelFolderDTO := a.getModels(ctx, share)
	return []dto.ApiFolderDTO{apiFolderDTO, modelFolderDTO}
}

func (a ApiShareService) getShareDataByLabel(ctx *commoncontext.MantisContext, share models.ApiShare) []dto.ApiFolderDTO {
	excludeLabels := make([]commondto.CodeEnumDTO, 0)
	includeLabels := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ExcludeLabelIdStr), &excludeLabels)
	jsonx.UnMarshal([]byte(share.ChooseLabelIdStr), &includeLabels)
	// 查询接口
	includeDocs := a.includeTags(apiDocDao.SelectNodeByLibId(ctx, share.LibId), includeLabels)
	docs := a.excludeTags(includeDocs, excludeLabels)
	root := a.convert2TreeByLeafNodes(ctx, docs, share.LibId, constants.FolderTypeApi)
	apiTree := dto.ApiFolderDTO{
		Name:      "接口",
		Type:      "api",
		ParentId:  0,
		LibId:     share.LibId,
		NodeType:  "folderNode",
		Key:       "folderNode.0",
		Children:  root.Children,
		LeafCount: int32(len(docs)),
	}
	// 查询数据模型
	modelFolderDTO := a.getModels(ctx, share)
	return []dto.ApiFolderDTO{apiTree, modelFolderDTO}
}

func (a ApiShareService) includeTags(docs []models.ApiDocBase, includeTags []commondto.CodeEnumDTO) []models.ApiDocBase {
	res := make([]models.ApiDocBase, 0)
out:
	for _, doc := range docs {
		if doc.BindLabelId != "" {
			labelIds := common_util.StringToInt64List(doc.BindLabelId, ",")
			for _, tag := range includeTags {
				tagInt, ok := tag.Value.(float64)
				if !ok {
					continue
				}
				if slices.Contains(labelIds, int64(tagInt)) {
					res = append(res, doc)
					continue out
				}
			}
		}
	}
	return res
}

func (a ApiShareService) getShareDataByChooseIds(ctx *commoncontext.MantisContext, share models.ApiShare) []dto.ApiFolderDTO {
	excludeLabels := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ExcludeLabelIdStr), &excludeLabels)
	chooseIdStrSlice := make([][]string, 0)
	jsonx.UnMarshal([]byte(share.ChooseIdStr), &chooseIdStrSlice)
	chooseIds := make([][]dto.ShareChooseId, 0, len(chooseIdStrSlice))
	for i := 0; i < len(chooseIdStrSlice); i++ {
		curSlice := make([]dto.ShareChooseId, 0, len(chooseIdStrSlice[i]))
		for j := 0; j < len(chooseIdStrSlice[i]); j++ {
			curDTO := dto.ShareChooseId{}
			jsonx.UnMarshal([]byte(chooseIdStrSlice[i][j]), &curDTO)
			curSlice = append(curSlice, curDTO)
		}
		chooseIds = append(chooseIds, curSlice)
	}
	// 统计需要的目录以及接口id
	folderSet := make(map[int64]int8)
	docSet := make(map[int64]int8)
	for _, sli := range chooseIds {
		for _, shareChooseId := range sli {
			if shareChooseId.NodeType == "apiNode" {
				docSet[shareChooseId.Id] = 0
			} else if shareChooseId.NodeType == "folderNode" {
				folderSet[shareChooseId.Id] = 0
			}
		}
	}
	folderIds := make([]int64, 0)
	for k := range folderSet {
		folderIds = append(folderIds, k)
	}
	docIds := make([]int64, 0)
	for k := range docSet {
		docIds = append(docIds, k)
	}
	// 构建接口树
	apiFolderDTO := dto.ApiFolderDTO{}
	apiFolderDTO.Id = rootId
	apiFolderDTO.Key = rootKey
	apiFolderDTO.LibId = share.LibId
	apiFolderDTO.NodeType = constants.NodeTypeFolder
	apiFolderMap := make(map[int64]*models.ApiFolder)
	apiFolders := apiFolderDao.SelectNodeFolderByIds(ctx, "api", folderIds)
	for i, apiFolder := range apiFolders {
		apiFolderMap[apiFolder.Id] = &apiFolders[i]
	}
	var apiTreeNodes []models.ApiTreeInfoInterface
	apiFolderDTO.Name = "接口"
	apiFolderDTO.Type = constants.FolderTypeApi
	docBases := a.excludeTags(apiDocDao.SelectNodeByIds(ctx, docIds), excludeLabels)
	for i := range docBases {
		apiTreeNodes = append(apiTreeNodes, docBases[i])
	}
	a.leafCount(apiFolderMap, apiTreeNodes)
	tree := a.convertTree(ctx, apiFolderMap, apiTreeNodes, share.LibId, constants.FolderTypeApi)
	apiFolderDTO.Children = tree.Children
	apiFolderDTO.LeafCount = int32(len(docBases))
	// 查询model树
	modelFolderDTO := a.getModels(ctx, share)
	return []dto.ApiFolderDTO{apiFolderDTO, modelFolderDTO}
}

func (a ApiShareService) leafCount(folderMap map[int64]*models.ApiFolder, list []models.ApiTreeInfoInterface) {
	if list == nil || len(list) == 0 {
		return
	}
	paddingMap := make(map[int64]int32)
	for _, doc := range list {
		paddingMap[doc.GetVFolderId()] += 1
	}
	for k, v := range folderMap {
		size := paddingMap[k]
		v.LeafCount = v.LeafCount + size
		for _, str := range strings.Split(v.Path, constants.RequestSplitChar) {
			if str == "" {
				continue
			}
			parseInt, _ := strconv.ParseInt(str, 10, 64)
			folder := folderMap[parseInt]
			if folder != nil {
				folder.LeafCount = folder.LeafCount + size
			}
		}
	}
}

func (a ApiShareService) convertTree(ctx *commoncontext.MantisContext, tree map[int64]*models.ApiFolder, list []models.ApiTreeInfoInterface, libId int64, treeType string) models.ApiFolder {
	// 构建父节点id和叶子结点的key内容表
	paddingMap := make(map[int64]map[string]models.ApiTreeInfoInterface)
	// 叶子节点写入paddingMap，顶层节点写入rootList
	for _, node := range list {
		nodeMap, ok := paddingMap[node.GetVFolderId()]
		if !ok || nodeMap == nil {
			paddingMap[node.GetVFolderId()] = make(map[string]models.ApiTreeInfoInterface)
			nodeMap = paddingMap[node.GetVFolderId()]
		}
		nodeMap[node.GetKey()] = node
	}
	// 目录写入paddingMap, 顶层目录写入rootList
	for _, v := range tree {
		folderMap, ok := paddingMap[v.GetVFolderId()]
		if !ok || folderMap == nil {
			paddingMap[v.GetVFolderId()] = make(map[string]models.ApiTreeInfoInterface)
			folderMap = paddingMap[v.GetVFolderId()]
		}
		folderMap[v.GetKey()] = v
	}
	// 开始构建树
	// 遍历目录
	for k, v := range tree {
		// 从paddingMap中取出当前目录的子节点
		children := paddingMap[k]
		if children != nil {
			// 对children进行排序
			sortedChildren := a.sortChildren(ctx, children, k, libId, treeType)
			v.Children = sortedChildren
		}
	}
	// 对rootList下的节点进行排序
	rootList := a.sortChildren(ctx, paddingMap[0], 0, libId, treeType)
	// 组合返回结果
	root := models.ApiFolder{}
	root.Children = rootList
	rootKeys := make([]string, 0, len(rootList))
	for _, rootNode := range rootList {
		rootKeys = append(rootKeys, rootNode.GetKey())
	}
	return root
}

func (a ApiShareService) sortChildren(ctx *commoncontext.MantisContext, childrenMap map[string]models.ApiTreeInfoInterface, parentId int64, libId int64, treeType string) []models.ApiTreeInfoInterface {
	keys := treeOperation.FindNodeListByFolderIdFromDB(ctx, parentId, treeType, libId)
	res := make([]models.ApiTreeInfoInterface, 0, len(childrenMap))
	for _, key := range keys {
		if child, ok := childrenMap[key]; ok {
			res = append(res, child)
		}
	}
	return res
}

func (a ApiShareService) convert2TreeByLeafNodes(ctx *commoncontext.MantisContext, leafNodes []models.ApiTreeInfoInterface, libId int64, dataType string) *models.ApiFolder {
	folders := apiFolderDao.SelectNodeFolderByLibIdAndType(ctx, dataType, libId)
	// 将folders构建为map
	folderMap := make(map[int64]*models.ApiFolder, len(folders))
	for i := range folders {
		folder := folders[i]
		folderMap[folder.Id] = &folder
	}
	// 开始构建树
	// 生成根节点
	root := &models.ApiFolder{
		Name:     "root",
		ParentId: -1,
	}
	root.LibId = libId
	root.NodeType = "folderNode"
	// 根节点加入folderMap
	folderMap[0] = root
	// 初始化queue
	queue := list.New()
	// 加入初始子节点
	for _, leaf := range leafNodes {
		folder, ok := folderMap[leaf.GetVFolderId()]
		if ok {
			folder.Children = append(folder.Children, leaf)
			folder.LeafCount += 1
			if !common_util.ListContain(queue, folder) {
				queue.PushBack(folder)
			}
		}
	}
	// 开始循环拼接树
	for {
		if queue.Len() == 0 {
			break
		}
		listLen := queue.Len()
		for i := 0; i < listLen; i++ {
			// 从queue中取出元素
			folder := queue.Front().Value.(*models.ApiFolder)
			// 将它的子节点进行排序
			childrenMap := make(map[string]models.ApiTreeInfoInterface)
			for _, child := range folder.Children {
				childrenMap[child.GetKey()] = child
			}
			folder.Children = a.sortChildren(ctx, childrenMap, folder.Id, libId, dataType)
			// 删除folder
			queue.Remove(queue.Front())
			// 将folder的父节点加入队列
			parent, ok := folderMap[folder.ParentId]
			if ok {
				parent.Children = append(parent.Children, folder)
				parent.LeafCount += folder.LeafCount
				if !common_util.ListContain(queue, parent) {
					queue.PushBack(parent)
				}
			}
		}
	}
	return root
}

func (a ApiShareService) excludeTags(docs []models.ApiDocBase, excludeTags []commondto.CodeEnumDTO) []models.ApiTreeInfoInterface {
	res := make([]models.ApiTreeInfoInterface, 0)
	// 切分任务
	length := len(docs)
	// 这样循环，不使用range避免指针问题
out:
	for i := 0; i < length; i++ {
		doc := docs[i]
		if doc.BindLabelId != "" {
			labelIds := common_util.StringToInt64List(doc.BindLabelId, ",")
			for _, tag := range excludeTags {
				tagInt, ok := tag.Value.(float64)
				if !ok {
					continue
				}
				if slices.Contains(labelIds, int64(tagInt)) {
					continue out
				}
			}
		}
		res = append(res, &doc)
	}
	return res
}

func (a ApiShareService) getModels(ctx *commoncontext.MantisContext, share models.ApiShare) dto.ApiFolderDTO {
	modelFolderDTO := dto.ApiFolderDTO{}
	modelFolderDTO.Id = rootId
	modelFolderDTO.Key = rootKey
	modelFolderDTO.LibId = share.LibId
	modelFolderDTO.NodeType = constants.NodeTypeFolder
	modelFolderMap := make(map[int64]*models.ApiFolder)
	modelFolders := apiFolderDao.SelectNodeFolderByLibIdAndType(ctx, "datamodels", share.LibId)
	for i, modelFolder := range modelFolders {
		modelFolderMap[modelFolder.Id] = &modelFolders[i]
	}
	var modelTreeNodes []models.ApiTreeInfoInterface
	modelFolderDTO.Name = "数据模型"
	modelFolderDTO.Type = constants.FolderTypeModel
	datamodels := apiDataModelService.GetDataModelByLibId(ctx, share.LibId)
	for i := range datamodels {
		modelTreeNodes = append(modelTreeNodes, &datamodels[i])
	}
	a.leafCount(modelFolderMap, modelTreeNodes)
	modelTree := a.convertTree(ctx, modelFolderMap, modelTreeNodes, share.LibId, constants.FolderTypeModel)
	modelFolderDTO.Children = modelTree.Children
	modelFolderDTO.LeafCount = modelTree.LeafCount
	return modelFolderDTO
}

func (a ApiShareService) RemoveShare(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	share := models.ApiShare{}
	share.Id = id
	share.GmtModified = times.Now()
	share.Modifier = user.AdAccount
	share.IsDeleted = commonconstants.DeleteYes
	gormx.UpdateOneByConditionX(ctx, &share)
}

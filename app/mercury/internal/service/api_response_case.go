package service

import (
	"fmt"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

var apiResponseCaseDao dao.ApiResponseCaseDao

type ApiResponseCaseService struct{}

func (a ApiResponseCaseService) GetApiResponseCaseListByApiResponseIds(ctx *commoncontext.MantisContext, ids []int64) []models.ApiResponseCase {
	return apiResponseCaseDao.GetApiResponseCaseListByApiResponseIds(ctx, ids)
}

func (a ApiResponseCaseService) RemoveApiResponseCaseByApiResponseIds(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	resp := models.ApiResponseCase{}
	resp.IsDeleted = constants.DeleteYes
	resp.Modifier = user.AdAccount
	apiResponseCaseDao.UpdateApiResponseCaseByApiResponseIds(ctx, ids, resp)
}

func (a ApiResponseCaseService) RemoveApiResponseCasesByIds(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	resp := models.ApiResponseCase{}
	resp.IsDeleted = constants.DeleteYes
	resp.Modifier = user.AdAccount
	resp.GmtModified = times.Now()
	apiResponseCaseDao.UpdateApiResponseCaseByApiResponseIds(ctx, ids, resp)
}

func (a ApiResponseCaseService) InsertUpdateResponseCase(ctx *commoncontext.MantisContext, tempIdMap map[string]int64, apiResponseCaseList []models.ApiResponseCase,
	user commondto.UserInfo,
) {
	if apiResponseCaseList == nil || len(apiResponseCaseList) == 0 {
		return
	}
	insertRespCaseList := make([]models.ApiResponseCase, 0, len(apiResponseCaseList))
	updateRespCaseList := make([]models.ApiResponseCase, 0, len(apiResponseCaseList))
	for i, respCase := range apiResponseCaseList {
		respCase.IsDeleted = constants.DeleteNo
		respCase.Modifier = user.AdAccount
		respCase.GmtModified = times.Now()
		respCase.OrderNo = int64(i)
		if respCase.Id == 0 {
			respCase.Creator = user.AdAccount
			respCase.GmtCreated = times.Now()
			if strings.HasPrefix(respCase.RespId, "tempId-") {
				respCase.RespId = fmt.Sprintf("%d", tempIdMap[respCase.RespId[len("tempId-"):]])
			}
			insertRespCaseList = append(insertRespCaseList, respCase)
		} else {
			updateRespCaseList = append(updateRespCaseList, respCase)
		}
	}
	if len(insertRespCaseList) != 0 {
		logger.Logger.Infof("新增响应示例，共计%d条", len(insertRespCaseList))
		apiResponseCaseDao.SaveApiResponseCaseList(ctx, insertRespCaseList)
	}
	if len(updateRespCaseList) != 0 {
		logger.Logger.Infof("更新响应示例，共计%d条", len(updateRespCaseList))
		for _, respCase := range updateRespCaseList {
			apiResponseCaseDao.UpdateApiResponseCaseById(ctx, &respCase)
		}
	}
}

func (a ApiResponseCaseService) CopyCaseRespIds(ctx *commoncontext.MantisContext, respMap map[string]int64, user commondto.UserInfo) {
	if respMap == nil || len(respMap) == 0 {
		return
	}
	respMapKeys := make([]string, 0, len(respMap))
	for k := range respMap {
		respMapKeys = append(respMapKeys, k)
	}
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("resp_id", respMapKeys).Eq("is_deleted", constants.DeleteNo)
	apiResponseCases := make([]models.ApiResponseCase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiResponseCases)
	if apiResponseCases != nil && len(apiResponseCases) != 0 {
		for i, arc := range apiResponseCases {
			arc.RespId = strconv.FormatInt(respMap[arc.RespId], 10)
			arc.Modifier = user.AdAccount
			arc.Creator = user.AdAccount
			arc.GmtCreated = times.Now()
			arc.GmtModified = times.Now()
			arc.Id = 0
			apiResponseCases[i] = arc
		}
		gormx.InsertBatchX(ctx, apiResponseCases)
	}
}

package tree_node

import (
	"errors"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type NodeHandler interface {
	GetNodeTypeEnum() string
	Remove(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo)
	Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO
	Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering)
}

type ApiNodeHandler struct{}

func (handler ApiNodeHandler) GetNodeTypeEnum() string {
	return constants.NodeTypeApi
}

func (handler ApiNodeHandler) Remove(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	apiDocService.RemoveDocById(ctx, id, user)
}

func (handler ApiNodeHandler) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO {
	apiDocBase := apiDocService.Copy(ctx, id, user, projectNo)
	return dto.ApiFolderDTO{
		Id:       apiDocBase.Id,
		Name:     apiDocBase.Name,
		Type:     constants.FolderTypeApi,
		ParentId: apiDocBase.FolderId,
		Key:      apiDocBase.Key,
		LibId:    apiDocBase.LibId,
		NodeType: constants.NodeTypeApi,
	}
}

func (handler ApiNodeHandler) Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering) {
	if ordering.DropPosition == constants.TreePositionIn {
		api := models.ApiDocBase{}
		api.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &api)
		drop := models.ApiFolder{}
		drop.Key = ordering.DropKey
		gormx.SelectOneByConditionX(ctx, &drop)
		api.FolderId = drop.Id
		gormx.UpdateOneByConditionX(ctx, &api)
	} else {
		api := models.ApiDocBase{}
		api.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &api)
		drop := treeOperation.GetNodeByKey(ctx, ordering.DropKey)
		api.FolderId = drop.GetVFolderId()
		gormx.InsertUpdateOneX(ctx, &api)
	}
}

type CaseNodeHandler struct{}

func (handler CaseNodeHandler) GetNodeTypeEnum() string {
	return constants.NodeTypeCase
}

func (handler CaseNodeHandler) Remove(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	apiDocQuickRequestService.RemoveById(ctx, id, user)
}

func (handler CaseNodeHandler) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO {
	doc := apiDocQuickRequestService.Copy(ctx, id, user)
	return dto.ApiFolderDTO{
		Id:       doc.Id,
		Name:     doc.Name,
		Type:     constants.FolderTypeApi,
		ParentId: doc.FolderId,
		Key:      doc.Key,
		LibId:    doc.LibId,
		NodeType: constants.NodeTypeApi,
	}
}

func (handler CaseNodeHandler) Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering) {
	if ordering.DropPosition == constants.TreePositionIn {
		api := models.ApiDocQuickRequest{}
		api.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &api)
		drop := models.ApiFolder{}
		drop.Key = ordering.DropKey
		gormx.SelectOneByConditionX(ctx, &drop)
		api.FolderId = drop.Id
		gormx.UpdateOneByConditionX(ctx, &api)
	} else {
		api := models.ApiDocQuickRequest{}
		api.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &api)
		drop := treeOperation.GetNodeByKey(ctx, ordering.DropKey)
		api.FolderId = drop.GetVFolderId()
		gormx.InsertUpdateOneX(ctx, &api)
	}
}

type FolderNodeHandler struct{}

func (handler FolderNodeHandler) GetNodeTypeEnum() string {
	return constants.NodeTypeFolder
}

func (handler FolderNodeHandler) Remove(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	apiTreeService.DeleteFolder(ctx, id, user)
}

func (handler FolderNodeHandler) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO {
	apiFolder := models.ApiFolder{}
	apiFolder.Id = id
	gormx.SelectOneByConditionX(ctx, &apiFolder)
	// 先复制自己
	apiFolder.Id = 0
	apiFolder.Name = apiFolder.Name + constants.CopyNameSuffix
	apiFolder.Creator = user.AdAccount
	apiFolder.GmtCreated = times.Now()
	apiFolder.Modifier = user.AdAccount
	apiFolder.GmtModified = times.Now()
	apiFolder.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeFolder)
	oriFolder := models.ApiFolder{}
	oriFolder.Id = id
	gormx.SelectOneByConditionX(ctx, &oriFolder)
	// 添加进树
	treeOperation.AddOneNodeToTree(ctx, &apiFolder, &oriFolder, constants.TreePositionPost, apiFolder.Type)
	// 递归处理子节点
	handler.copyFolderTree(ctx, apiFolder.LibId, oriFolder.Id, apiFolder.Id, user, apiFolder.Type, projectNo)
	return apiTreeService.ViewTreeByPid(ctx, apiFolder.Id)
}

func (handler FolderNodeHandler) copyFolderTree(ctx *commoncontext.MantisContext, libId int64, srcFolderId int64, tarFolderId int64, user commondto.UserInfo,
	folderType string, projectNo string,
) {
	// 子目录
	apiFolderSource := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("parent_id", srcFolderId).
			Eq("is_deleted", commonconstants.DeleteNo),
		&apiFolderSource)
	apiFolders := make([]models.ApiFolder, 0)
	oldNewKeyMap := make(map[string]string)                             // 旧数据与新数据的key对应表
	oldNewKeyMap[constants.NodeTypeDefault] = constants.NodeTypeDefault // 加入默认数据
	// 从缓存中获取源目录的keys数组
	keys := treeOperation.GetKeysFromFolder(ctx, libId, srcFolderId, folderType)
	newKeys := make([]string, 0, len(keys))
	for _, key := range keys {
		if key == "" {
			continue
		}
		newKey := fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), strings.Split(key, "+")[1])
		oldNewKeyMap[key] = newKey
		newKeys = append(newKeys, newKey)
	}
	// 写入缓存
	treeOperation.RefreshRedisByFolder(libId, tarFolderId, newKeys, folderType)
	// 处理子目录
	for _, v := range apiFolderSource {
		apiFolder := models.ApiFolder{}
		apiFolder.CopyId = v.Id
		apiFolder.Name = v.Name
		apiFolder.Type = v.Type
		apiFolder.ParentId = tarFolderId
		apiFolder.LibId = v.LibId
		apiFolder.Path = apiTreeService.FixPath(ctx, apiFolder.ParentId)
		apiFolder.IsDeleted = commonconstants.DeleteNo
		apiFolder.Creator = user.AdAccount
		apiFolder.GmtCreated = times.Now()
		apiFolder.Modifier = user.AdAccount
		apiFolder.GmtModified = times.Now()
		apiFolder.Key = oldNewKeyMap[v.Key]
		// 修正顺序
		apiFolder.SetPreNode(oldNewKeyMap[v.GetPreNode()])
		apiFolder.SetPostNode(oldNewKeyMap[v.GetPostNode()])
		apiFolders = append(apiFolders, apiFolder)
	}
	if len(apiFolders) != 0 {
		gormx.InsertBatchX(ctx, apiFolders)
	}
	// 处理子叶子节点
	switch folderType {
	case constants.FolderTypeModel:
		apiDataModelService.CopyMultiByFolder(ctx, srcFolderId, tarFolderId, oldNewKeyMap, user)
	case constants.FolderTypeApi:
		apiDocService.CopyByFolderId(ctx, srcFolderId, tarFolderId, oldNewKeyMap, user, projectNo)
	case constants.FolderTypeCase:
		apiDocQuickRequestService.CopyByFolderId(ctx, srcFolderId, tarFolderId, oldNewKeyMap, user)
	}
	// 循环子目录并且递归处理
	for _, folder := range apiFolders {
		handler.copyFolderTree(ctx, libId, folder.CopyId, folder.Id, user, folderType, projectNo)
	}
}

func (handler FolderNodeHandler) Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering) {
	apiFolderDao.FixOrderingPath(ctx, ordering.DragKey, ordering.DropKey, ordering.DropPosition)
}

type ModelNodeHandler struct{}

func (handler ModelNodeHandler) GetNodeTypeEnum() string {
	return constants.NodeTypeModel
}

func (handler ModelNodeHandler) Remove(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	apiDataModelService.RemoveDataModel(ctx, id, user.AdAccount)
}

func (handler ModelNodeHandler) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO {
	apiDataModel := apiDataModelService.Copy(ctx, id, constants.CopyNameSuffix, user)
	return apiDataModelService.ToTreeNodeById(ctx, apiDataModel.Id)
}

func (handler ModelNodeHandler) Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering) {
	if ordering.DropPosition == constants.TreePositionIn {
		apiDataModel := models.ApiDataModel{}
		apiDataModel.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &apiDataModel)
		drop := models.ApiFolder{}
		drop.Key = ordering.DropKey
		gormx.SelectOneByConditionX(ctx, &drop)
		apiDataModel.FolderId = drop.Id
		gormx.UpdateOneByConditionX(ctx, &apiDataModel)
	} else {
		dataModel := models.ApiDataModel{}
		dataModel.Key = ordering.DragKey
		gormx.SelectOneByConditionX(ctx, &dataModel)
		drop := treeOperation.GetNodeByKey(ctx, ordering.DropKey)
		dataModel.FolderId = drop.GetVFolderId()
		gormx.InsertUpdateOneX(ctx, &dataModel)
	}
}

func CreateNodeHandler(nodeName string) NodeHandler {
	switch nodeName {
	case constants.NodeTypeApi:
		return ApiNodeHandler{}
	case constants.NodeTypeCase:
		return CaseNodeHandler{}
	case constants.NodeTypeFolder:
		return FolderNodeHandler{}
	case constants.NodeTypeModel:
		return ModelNodeHandler{}
	}
	logger.Logger.Panicf("%+v", xerror.Wrap(errors.New("没有对应的nodeHandler"), "没有对应的nodeHandler"))
	return ApiNodeHandler{}
}

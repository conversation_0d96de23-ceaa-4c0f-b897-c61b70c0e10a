package tree_node

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/tree_node/tree_operate"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

const rootId int64 = 0

var (
	apiDocDao                 dao.ApiDocDao
	apiDocQuickRequestService service.ApiDocQuickRequestService
	apiFolderDao              dao.ApiFolderDao
	apiDataModelService       service.ApiDataModelService
	apiDocService             service.ApiDocService
	apiTreeService            ApiTreeService
	treeOperation             tree_operate.TreeOperation
)

var rootKey = constants.NodeTypeFolder + "." + strconv.FormatInt(rootId, 10)

type ApiTreeService struct{}

func (a ApiTreeService) View(ctx *commoncontext.MantisContext, typeStr string, libId int64) dto.ApiFolderDTO {
	folderDTO := dto.ApiFolderDTO{}
	folderDTO.Id = rootId
	folderDTO.Key = rootKey
	folderDTO.LibId = libId
	folderDTO.NodeType = constants.NodeTypeFolder
	apiFolderMap := make(map[int64]*models.ApiFolder)
	apiFolders := apiFolderDao.SelectNodeFolderByLibIdAndType(ctx, typeStr, libId)
	for i, apiFolder := range apiFolders {
		apiFolderMap[apiFolder.Id] = &apiFolders[i]
	}
	var apiTreeNodes []models.ApiTreeInfoInterface
	var treeType string
	switch typeStr {
	case constants.FolderTypeApi:
		folderDTO.Name = "接口"
		folderDTO.Type = constants.FolderTypeApi
		treeType = constants.FolderTypeApi
		docBases := apiDocService.GetNodeByLibId(ctx, libId)
		for i := range docBases {
			apiTreeNodes = append(apiTreeNodes, &docBases[i])
		}
	case constants.FolderTypeModel:
		folderDTO.Name = "数据模型"
		folderDTO.Type = constants.FolderTypeModel
		treeType = constants.FolderTypeModel
		dataModels := apiDataModelService.GetDataModelByLibId(ctx, libId)
		for i := range dataModels {
			apiTreeNodes = append(apiTreeNodes, &dataModels[i])
		}
	case constants.FolderTypeCase:
		folderDTO.Name = "快捷请求"
		folderDTO.Type = constants.FolderTypeCase
		treeType = constants.FolderTypeCase
		cases := apiDocQuickRequestService.GetByLibId(ctx, libId)
		for i := range cases {
			apiTreeNodes = append(apiTreeNodes, &cases[i])
		}
	}
	a.LeafCount(apiFolderMap, apiTreeNodes)
	tree := a.ConvertTree(apiFolderMap, apiTreeNodes, libId, treeType)
	folderDTO.Children = tree.Children
	folderDTO.LeafCount = tree.LeafCount
	return folderDTO
}

func (a ApiTreeService) AddFolder(ctx *commoncontext.MantisContext, folderDTO dto.ApiFolderDTO, user commondto.UserInfo) dto.ApiFolderDTO {
	// 因为要写索引，所以上锁
	apiFolder := models.ApiFolder{
		Name:     folderDTO.Name,
		Type:     folderDTO.Type,
		ParentId: folderDTO.ParentId,
	}
	// 判重
	repeat := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", folderDTO.LibId).
			Eq("type", folderDTO.Type).Eq("parent_id", folderDTO.ParentId).Eq("name", folderDTO.Name).
			Eq("is_deleted", commonconstants.DeleteNo),
		&repeat)
	if len(repeat) != 0 {
		logger.Logger.Panicf("%+v", errors.New("同一层级下目录名称重复，请修改"))
	}
	apiFolder.LibId = folderDTO.LibId
	apiFolder.IsDeleted = commonconstants.DeleteNo
	apiFolder.Creator = user.AdAccount
	apiFolder.Modifier = user.AdAccount
	apiFolder.GmtCreated = times.Now()
	apiFolder.GmtModified = times.Now()
	apiFolder.Path = a.FixPath(ctx, apiFolder.ParentId)
	// 生成自己的key
	apiFolder.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeFolder)
	treeOperation.AddOneNodeToTail(ctx, apiFolder.ParentId, &apiFolder, apiFolder.Type)
	jsonx.UnMarshal(jsonx.Marshal(&apiFolder), &folderDTO)
	folderDTO.NodeType = constants.NodeTypeFolder
	return folderDTO
}

func (a ApiTreeService) UpdateFolder(ctx *commoncontext.MantisContext, folderDTO dto.ApiFolderDTO, user commondto.UserInfo) {
	apiFolder := models.ApiFolder{
		Name: folderDTO.Name,
	}
	// 判重
	repeat := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", folderDTO.LibId).
			Eq("type", folderDTO.Type).Eq("parent_id", folderDTO.ParentId).NotEq("id", folderDTO.Id).
			Eq("name", folderDTO.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&repeat)
	if len(repeat) != 0 {
		logger.Logger.Panicf("%+v", errors.New("同一层级下接口目录名称重复，请修改"))
	}
	apiFolder.Id = folderDTO.Id
	apiFolder.Modifier = user.AdAccount
	apiFolder.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &apiFolder)
}

func (a ApiTreeService) RemoveNode(ctx *commoncontext.MantisContext, libId int64, nodeType string, id int64, user commondto.UserInfo) {
	nodeHandler := CreateNodeHandler(nodeType)
	nodeHandler.Remove(ctx, id, user)
}

func (a ApiTreeService) Copy(ctx *commoncontext.MantisContext, nodeType string, id int64, libId int64, user commondto.UserInfo, projectNo string) dto.ApiFolderDTO {
	nodeHandler := CreateNodeHandler(nodeType)
	return nodeHandler.Copy(ctx, id, user, projectNo)
}

func (a ApiTreeService) Ordering(ctx *commoncontext.MantisContext, ordering dto.TreeOrdering, user commondto.UserInfo) {
	dragNode := treeOperation.GetNodeByKey(ctx, ordering.DragKey)
	// 删除dragNode原来的位置
	treeOperation.RemoveOneNodeOnTree(ctx, dragNode, ordering.TreeType)
	dropNode := treeOperation.GetNodeByKey(ctx, ordering.DropKey)
	// 将源节点添加到指定位置
	treeOperation.AddOneNodeToTree(ctx, dragNode, dropNode, ordering.DropPosition, ordering.TreeType)
	nodeHandler := CreateNodeHandler(ordering.NodeType)
	nodeHandler.Ordering(ctx, ordering)
}

func (a ApiTreeService) FixPath(ctx *commoncontext.MantisContext, pid int64) string {
	path := ""
	if rootId != pid {
		path = apiFolderDao.SelectPathById(ctx, pid)
	}
	return path + strconv.FormatInt(pid, 10) + constants.RequestSplitChar
}

func (a ApiTreeService) ViewTreeByPid(ctx *commoncontext.MantisContext, id int64) dto.ApiFolderDTO {
	apiFolder := models.ApiFolder{}
	apiFolder.Id = id
	gormx.SelectOneByConditionX(ctx, &apiFolder)
	apiFolderDTO := dto.ApiFolderDTO{
		Id:       apiFolder.Id,
		Name:     apiFolder.Name,
		Type:     apiFolder.Type,
		ParentId: apiFolder.ParentId,
		OrderNo:  apiFolder.OrderNo,
		Key:      constants.NodeTypeFolder + "." + strconv.FormatInt(apiFolder.Id, 10),
		LibId:    apiFolder.LibId,
		NodeType: constants.NodeTypeFolder,
	}
	apiFolders := apiFolderDao.SelectNodeFolderById(ctx, id, apiFolder.Path+strconv.FormatInt(id, 10)+",")
	vrMap := make(map[int64]*models.ApiFolder)
	if apiFolders != nil && len(apiFolders) != 0 {
		for i, folder := range apiFolders {
			vrMap[folder.Id] = &apiFolders[i]
		}
	}
	var apiTreeNodes []models.ApiTreeInfoInterface
	folderIds := make([]int64, 0, len(apiFolders)+1)
	for _, folder := range apiFolders {
		folderIds = append(folderIds, folder.Id)
	}
	folderIds = append(folderIds, apiFolder.Id)
	switch apiFolder.Type {
	case constants.FolderTypeApi:
		docBases := apiDocDao.SelectNodeByFolderIds(ctx, folderIds)
		for i := range docBases {
			apiTreeNodes = append(apiTreeNodes, &docBases[i])
		}
		break
	case constants.FolderTypeModel:
		dataModels := apiDataModelService.GetNodeByFolderIds(ctx, folderIds)
		for i := range dataModels {
			apiTreeNodes = append(apiTreeNodes, &dataModels[i])
		}
		break
	}
	a.LeafCount(vrMap, apiTreeNodes)
	apiFolderN := a.ConvertTree(vrMap, apiTreeNodes, apiFolder.LibId, apiFolder.Type)
	apiFolderChildren := &models.ApiFolder{}
	if apiFolderN.Children != nil && len(apiFolderN.Children) != 0 {
		apiFolderChildren = apiFolderN.Children[0].(*models.ApiFolder)
	}
	apiFolderDTO.Children = apiFolderChildren.Children
	apiFolderDTO.LeafCount = apiFolderChildren.LeafCount
	return apiFolderDTO
}

func (a ApiTreeService) LeafCount(folderMap map[int64]*models.ApiFolder, list []models.ApiTreeInfoInterface) {
	if list == nil || len(list) == 0 {
		return
	}
	paddingMap := make(map[int64]int32)
	for _, doc := range list {
		paddingMap[doc.GetVFolderId()] += 1
	}
	for k, v := range folderMap {
		size := paddingMap[k]
		v.LeafCount = v.LeafCount + size
		for _, str := range strings.Split(v.Path, constants.RequestSplitChar) {
			if str == "" {
				continue
			}
			parseInt, _ := strconv.ParseInt(str, 10, 64)
			folder := folderMap[parseInt]
			if folder != nil {
				folder.LeafCount = folder.LeafCount + size
			}
		}
	}
}

func (a ApiTreeService) ConvertTree(tree map[int64]*models.ApiFolder, list []models.ApiTreeInfoInterface, libId int64, treeType string) models.ApiFolder {
	// 构建父节点id和叶子结点的key内容表
	paddingMap := make(map[int64]map[string]models.ApiTreeInfoInterface)
	// 叶子节点写入paddingMap，顶层节点写入rootList
	for _, node := range list {
		nodeMap, ok := paddingMap[node.GetVFolderId()]
		if !ok || nodeMap == nil {
			paddingMap[node.GetVFolderId()] = make(map[string]models.ApiTreeInfoInterface)
			nodeMap = paddingMap[node.GetVFolderId()]
		}
		nodeMap[node.GetKey()] = node
	}
	// 目录写入paddingMap, 顶层目录写入rootList
	for _, v := range tree {
		folderMap, ok := paddingMap[v.GetVFolderId()]
		if !ok || folderMap == nil {
			paddingMap[v.GetVFolderId()] = make(map[string]models.ApiTreeInfoInterface)
			folderMap = paddingMap[v.GetVFolderId()]
		}
		folderMap[v.GetKey()] = v
	}
	// 开始构建树
	// 遍历目录
	for k, v := range tree {
		// 从paddingMap中取出当前目录的子节点
		children := paddingMap[k]
		if children != nil {
			// 对children进行排序
			sortedChildren, keys := a.sortChildren(children)
			v.Children = sortedChildren
			// 上锁将key数组写入redis缓存
			key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, libId, v.Id)
			lock, err := cache.TryLock(30*time.Second, key, 3000)
			if err != nil {
				logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
			}
			treeOperation.RefreshRedisByFolder(libId, k, keys, treeType)
			cache.Unlock(lock)
		}
	}
	// 对rootList下的节点进行排序
	rootList, keys := a.sortChildren(paddingMap[0])
	// 将root下的key数组写入redis缓存
	if keys != nil && len(keys) != 0 {
		// 上锁将key数组写入redis缓存
		key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, libId, 0)
		lock, err := cache.TryLock(30*time.Second, key, 3000)
		if err != nil {
			logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
		}
		treeOperation.RefreshRedisByFolder(libId, 0, keys, treeType)
		cache.Unlock(lock)
	}
	// 组合返回结果
	root := models.ApiFolder{}
	root.Children = rootList
	// 将rootList写入缓存
	rootKeys := make([]string, 0, len(rootList))
	for _, rootNode := range rootList {
		rootKeys = append(rootKeys, rootNode.GetKey())
	}
	if len(rootKeys) != 0 {
		treeOperation.RefreshRedisByFolder(libId, 0, rootKeys, treeType)
	}
	return root
}

func (a ApiTreeService) sortChildren(childrenMap map[string]models.ApiTreeInfoInterface) ([]models.ApiTreeInfoInterface, []string) {
	res := make([]models.ApiTreeInfoInterface, 0, len(childrenMap))
	keys := make([]string, 0, len(childrenMap))
	// 先遍历找到头节点
	var head models.ApiTreeInfoInterface
	for _, v := range childrenMap {
		if v.GetPreNode() == constants.NodeTypeDefault {
			head = v
			break
		}
	}
	if head == nil {
		return res, keys
	}
	// 从头节点开始构建子目录
	for {
		res = append(res, head)
		keys = append(keys, head.GetKey())
		if head.GetPostNode() == constants.NodeTypeDefault {
			break
		}
		head = childrenMap[head.GetPostNode()]
	}
	return res, keys
}

func (a ApiTreeService) DeleteFolder(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	apiFolderInfo := models.ApiFolder{}
	apiFolderInfo.Id = id
	gormx.SelectOneByConditionX(ctx, &apiFolderInfo)
	apiFolderInfo.IsDeleted = commonconstants.DeleteYes
	apiFolderInfo.GmtModified = times.Now()
	apiFolderInfo.Modifier = user.AdAccount
	gormx.InsertUpdateOneX(ctx, &apiFolderInfo)
	// 将该目录从树上删除，其他级联删除的都是该目录的子节点，无须从树上删除
	treeOperation.RemoveOneNodeOnTree(ctx, &apiFolderInfo, apiFolderInfo.Type)
	delPath := fmt.Sprintf(`%s%d,`, apiFolderInfo.Path, apiFolderInfo.Id) + `%`
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiFolder{}).Like("path", delPath).Eq("is_deleted", commonconstants.DeleteNo)
	apiFoldersNeedDelete := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiFoldersNeedDelete)
	folders := make([]int64, 0, len(apiFoldersNeedDelete))
	for _, folder := range apiFoldersNeedDelete {
		folders = append(folders, folder.Id)
	}
	folders = append(folders, id)
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, paramBuilder, map[string]any{"is_deleted": commonconstants.DeleteYes})
	// 删除目录下的data models
	apiDataModelService.RemoveDataModelMultiByFolder(ctx, folders, user)
	// 删除目录下的api doc
	apiDocService.RemoveBatchByFolder(ctx, folders, user)
	// 删除目录下的快捷请求
	apiDocQuickRequestService.RemoveBatchByFolder(ctx, folders, user)
}

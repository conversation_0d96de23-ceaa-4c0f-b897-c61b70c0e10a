// Package tree_node Package tree_operation 对树操作的封装，便于其他service使用
package tree_operate

import (
	"fmt"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/go-redis/redis/v8"
)

const expiration = 60 * time.Minute

type TreeOperation struct{}

// GetKeysFromFolder 获取目录下的key数组，如果发现redis中不存在会重新从数据库刷新进入缓存
func (t TreeOperation) GetKeysFromFolder(ctx *commoncontext.MantisContext, libId int64, folderId int64, treeType string) []string {
	listKey := fmt.Sprintf("%d+%d+%s", libId, folderId, treeType)
	list, err := cache.GetList(listKey)
	switch {
	case err == redis.Nil || len(list) == 0:
		// 重新刷新redis
		keys := t.FindNodeListByFolderIdFromDB(ctx, folderId, treeType, libId)
		t.RefreshRedisByFolder(folderId, libId, keys, treeType)
		return keys
	case err != nil:
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in operating redis"))
		return nil
	default:
		// 给这个目录的缓存续期
		err = cache.ExpireKey(listKey, expiration)
		if err != nil {
			return nil
		}
		return list
	}
}

// GetLastKeyFromFolder 获取目录下的最后一位的key, 如果发现redis中不存在会重新从数据库刷新进入缓存
func (t TreeOperation) GetLastKeyFromFolder(ctx *commoncontext.MantisContext, libId int64, folderId int64, treeType string) string {
	keys := t.GetKeysFromFolder(ctx, libId, folderId, treeType)
	if keys == nil || len(keys) == 0 {
		return ""
	}
	return keys[len(keys)-1]
}

// RefreshRedisByFolder 刷新redis中的缓存数据
func (t TreeOperation) RefreshRedisByFolder(libId int64, folderId int64, keys []string, treeType string) {
	listKey := fmt.Sprintf("%d+%d+%s", libId, folderId, treeType)
	var err error
	if len(keys) == 0 {
		err = cache.ListRefresh(listKey, expiration)
	} else {
		err = cache.ListChange(listKey, keys, expiration)
	}
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in operating redis"))
	}
}

// addNodeKeyAtLast 向redis中加入新节点的key
func (t TreeOperation) addNodeKeyAtLast(libId int64, folderId int64, treeType string, ctx *commoncontext.MantisContext, keys ...string) {
	listKey := fmt.Sprintf("%d+%d+%s", libId, folderId, treeType)
	// 去看一下redis中是否有这个key，没有的话进行一次刷新
	t.GetKeysFromFolder(ctx, folderId, libId, treeType)
	// 加入redis
	err := cache.ListRPush(listKey, keys...)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in operating redis"))
	}
	// 续期
	err = cache.ExpireKey(listKey, expiration)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in operating redis"))
	}
}

// AddOneNodeToTail 在一个目录下新增一个叶子节点
func (t TreeOperation) AddOneNodeToTail(ctx *commoncontext.MantisContext, folderId int64, node models.ApiTreeInfoInterface, treeType string) {
	key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, node.GetLibId(), folderId)
	lock, err := cache.TryLock(30*time.Second, key, 30000)
	if err != nil {
		logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
	}
	defer cache.Unlock(lock)
	// 获取缓存中最后一个key
	lastKey := t.GetLastKeyFromFolder(ctx, node.GetLibId(), folderId, treeType)
	// 向缓存里增加自己的key
	t.addNodeKeyAtLast(node.GetLibId(), folderId, treeType, ctx, node.GetKey())
	// 写库
	// 取lastKey对应的节点
	if lastKey != "" {
		lastNode := t.GetNodeByKey(ctx, lastKey)
		lastNode.SetPostNode(node.GetKey())
		if lastNode.GetId() != 0 {
			gormx.UpdateOneByConditionX(ctx, lastNode)
		}
		node.SetPreNode(lastKey)
	} else {
		node.SetPreNode(constants.NodeTypeDefault)
	}
	node.SetPostNode(constants.NodeTypeDefault)
	gormx.InsertUpdateOneX(ctx, node)
}

func (t TreeOperation) AddBatchDataModel2Tail(ctx *commoncontext.MantisContext, libId int64, folderId int64, nodeList []models.ApiDataModel,
	treeType string,
) {
	key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, libId, folderId)
	lock, err := cache.TryLock(30*time.Second, key, 30000)
	if err != nil {
		logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
	}
	defer cache.Unlock(lock)
	keys := make([]string, 0, len(nodeList))
	for _, node := range nodeList {
		keys = append(keys, node.GetKey())
	}
	// 获取缓存中最后一个key
	lastKey := t.GetLastKeyFromFolder(ctx, libId, folderId, treeType)
	// 向缓存里增加自己的key
	t.addNodeKeyAtLast(libId, folderId, treeType, ctx, keys...)
	// 写库
	lastNode := models.ApiTreeInfoInterface(nil)
	if lastKey != "" {
		lastNode = t.GetNodeByKey(ctx, lastKey)
		lastNode.SetPostNode(nodeList[0].GetKey())
		gormx.UpdateOneByConditionX(ctx, lastNode)
	}
	for i := 0; i < len(nodeList); i++ {
		node := nodeList[i]
		if lastNode == nil {
			node.SetPreNode(constants.NodeTypeDefault)
		} else {
			node.SetPreNode(lastNode.GetKey())
		}
		if i == len(nodeList)-1 {
			node.SetPostNode(constants.NodeTypeDefault)
		} else {
			node.SetPostNode(nodeList[i+1].GetKey())
		}
		nodeList[i] = node
		lastNode = &node
	}
	gormx.InsertBatchX(ctx, nodeList)
}

func (t TreeOperation) AddBatchApiDoc2Tail(ctx *commoncontext.MantisContext, libId int64, folderId int64, nodeList []models.ApiDocBase,
	keyIdMap map[string]int64,
) {
	keys := make([]string, 0, len(nodeList))
	for _, node := range nodeList {
		keys = append(keys, node.GetKey())
	}
	// 获取缓存中最后一个key
	lastKey := t.GetLastKeyFromFolder(ctx, libId, folderId, constants.FolderTypeApi)
	// 向缓存里增加自己的key
	t.addNodeKeyAtLast(libId, folderId, constants.FolderTypeApi, ctx, keys...)
	// 写库
	lastNode := models.ApiTreeInfoInterface(nil)
	if lastKey != "" {
		lastNode = t.GetNodeByKey(ctx, lastKey)
		lastNode.SetPostNode(nodeList[0].GetKey())
		gormx.UpdateOneByConditionX(ctx, lastNode)
	}
	for i := 0; i < len(nodeList); i++ {
		node := nodeList[i]
		if lastNode == nil {
			node.SetPreNode(constants.NodeTypeDefault)
		} else {
			node.SetPreNode(lastNode.GetKey())
		}
		if i == len(nodeList)-1 {
			node.SetPostNode(constants.NodeTypeDefault)
		} else {
			node.SetPostNode(nodeList[i+1].GetKey())
		}
		nodeList[i] = node
		lastNode = &node
	}
	gormx.InsertBatchX(ctx, nodeList)
	for _, doc := range nodeList {
		keyIdMap[doc.Key] = doc.Id
	}
}

// GetNodeByKey 根据key获取节点详情
func (t TreeOperation) GetNodeByKey(ctx *commoncontext.MantisContext, key string) models.ApiTreeInfoInterface {
	res := models.ApiTreeInfoInterface(nil)
	split := strings.Split(key, "+")
	switch split[1] {
	case constants.NodeTypeApi:
		doc := models.ApiDocBase{}
		doc.Key = key
		doc.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &doc)
		res = &doc
	case constants.NodeTypeModel:
		model := models.ApiDataModel{}
		model.Key = key
		model.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &model)
		res = &model
	case constants.NodeTypeCase:
		quick := models.ApiDocQuickRequest{}
		quick.Key = key
		quick.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &quick)
		res = &quick
	case constants.NodeTypeFolder:
		folder := models.ApiFolder{}
		folder.Key = key
		folder.IsDeleted = commonconstants.DeleteNo
		gormx.SelectOneByConditionX(ctx, &folder)
		res = &folder
	}
	return res
}

// RemoveOneNodeOnTree 从树上删除一个node
func (t TreeOperation) RemoveOneNodeOnTree(ctx *commoncontext.MantisContext, node models.ApiTreeInfoInterface, treeType string) {
	key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, node.GetLibId(), node.GetVFolderId())
	lock, err := cache.TryLock(30*time.Second, key, 30000)
	if err != nil {
		logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
	}
	defer cache.Unlock(lock)
	// 获取缓存中的数据
	parentId := node.GetVFolderId()
	libId := node.GetLibId()
	keys := t.GetKeysFromFolder(ctx, libId, parentId, treeType)
	// 修改keys，写入缓存
	newKeys := make([]string, 0, len(keys)-1)
	for _, key := range keys {
		if key == node.GetKey() {
			continue
		}
		newKeys = append(newKeys, key)
	}
	t.RefreshRedisByFolder(node.GetLibId(), parentId, newKeys, treeType)
	// 修改数据库中的链表结构
	if node.GetPreNode() != constants.NodeTypeDefault {
		preNode := t.GetNodeByKey(ctx, node.GetPreNode())
		preNode.SetPostNode(node.GetPostNode())
		gormx.UpdateOneByConditionX(ctx, preNode)
	}
	if node.GetPostNode() != constants.NodeTypeDefault {
		postNode := t.GetNodeByKey(ctx, node.GetPostNode())
		postNode.SetPreNode(node.GetPreNode())
		gormx.UpdateOneByConditionX(ctx, postNode)
	}
}

// AddOneNodeToTree 在树上的指定位置添加一个节点
func (t TreeOperation) AddOneNodeToTree(ctx *commoncontext.MantisContext, srcNode models.ApiTreeInfoInterface, tarNode models.ApiTreeInfoInterface, pos int32,
	treeType string,
) {
	// 操作目标目录的缓存
	parentId := tarNode.GetVFolderId()
	libId := tarNode.GetLibId()
	keys := t.GetKeysFromFolder(ctx, libId, parentId, treeType)
	switch pos {
	case constants.TreePositionPre: // 前
		// 刷新缓存
		key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, tarNode.GetLibId(), tarNode.GetVFolderId())
		lock, err := cache.TryLock(30*time.Second, key, 30000)
		if err != nil {
			logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
		}
		defer cache.Unlock(lock)
		newKeys := make([]string, 0, len(keys)+1)
		for _, key := range keys {
			if key == tarNode.GetKey() {
				newKeys = append(newKeys, srcNode.GetKey())
			}
			newKeys = append(newKeys, key)
		}
		t.RefreshRedisByFolder(libId, parentId, newKeys, treeType)
		// 写库
		if tarNode.GetPreNode() != constants.NodeTypeDefault {
			preNode := t.GetNodeByKey(ctx, tarNode.GetPreNode())
			preNode.SetPostNode(srcNode.GetKey())
			gormx.UpdateOneByConditionX(ctx, preNode)
		}
		srcNode.SetPreNode(tarNode.GetPreNode())
		srcNode.SetPostNode(tarNode.GetKey())
		tarNode.SetPreNode(srcNode.GetKey())
		gormx.InsertUpdateOneX(ctx, srcNode)
		gormx.UpdateOneByConditionX(ctx, tarNode)
	case constants.TreePositionPost: // 后
		// 刷新缓存
		key := fmt.Sprintf("%s-%d%d", constants.MantisTreeRedisLock, tarNode.GetLibId(), tarNode.GetVFolderId())
		lock, err := cache.TryLock(30*time.Second, key, 30000)
		if err != nil {
			logger.Logger.Panicf("error obtain lock, err=%s", err.Error())
		}
		defer cache.Unlock(lock)
		newKeys := make([]string, 0, len(keys)+1)
		for _, key := range keys {
			newKeys = append(newKeys, key)
			if key == tarNode.GetKey() {
				newKeys = append(newKeys, srcNode.GetKey())
			}
		}
		t.RefreshRedisByFolder(libId, parentId, newKeys, treeType)
		// 写库
		if tarNode.GetPostNode() != constants.NodeTypeDefault {
			postNode := t.GetNodeByKey(ctx, tarNode.GetPostNode())
			postNode.SetPreNode(srcNode.GetKey())
			gormx.UpdateOneByConditionX(ctx, postNode)
		}
		srcNode.SetPreNode(tarNode.GetKey())
		srcNode.SetPostNode(tarNode.GetPostNode())
		tarNode.SetPostNode(srcNode.GetKey())
		gormx.InsertUpdateOneX(ctx, srcNode)
		gormx.UpdateOneByConditionX(ctx, tarNode)
	case constants.TreePositionIn:
		t.AddOneNodeToTail(ctx, tarNode.GetId(), srcNode, treeType)
	}
}

// FindNodeListByFolderIdFromDB 从数据库找出一个目录下的一级子节点的数组
func (t TreeOperation) FindNodeListByFolderIdFromDB(ctx *commoncontext.MantisContext, folderId int64, treeType string, libId int64) []string {
	// 子节点key, value map
	childrenMap := make(map[string]models.ApiTreeInfoInterface)
	// 头节点head
	head := models.ApiTreeInfoInterface(nil)
	// 查询子目录
	folderChildren := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", libId).
			Eq("parent_id", folderId).Eq("is_deleted", commonconstants.DeleteNo).Eq("type", treeType),
		&folderChildren)
	for i := 0; i < len(folderChildren); i++ {
		folder := folderChildren[i]
		childrenMap[folder.Key] = &folder
		if folder.PreNode == constants.NodeTypeDefault {
			head = &folder
		}
	}
	// 查询子数据节点
	switch treeType {
	case constants.FolderTypeApi:
		res := make([]models.ApiDocBase, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", libId).Eq("folder_id", folderId).
				Eq("is_deleted", commonconstants.DeleteNo),
			&res)
		for i := 0; i < len(res); i++ {
			node := res[i]
			childrenMap[node.GetKey()] = &node
			if node.GetPreNode() == constants.NodeTypeDefault {
				head = &node
			}
		}
	case constants.FolderTypeModel:
		res := make([]models.ApiDataModel, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", libId).Eq("folder_id", folderId).
				Eq("is_deleted", commonconstants.DeleteNo),
			&res)
		for i := 0; i < len(res); i++ {
			node := res[i]
			childrenMap[node.GetKey()] = &node
			if node.GetPreNode() == constants.NodeTypeDefault {
				head = &node
			}
		}
	case constants.FolderTypeCase:
		res := make([]models.ApiDocQuickRequest, 0)
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiDocQuickRequest{}).Eq("lib_id", libId).Eq("folder_id", folderId).
				Eq("is_deleted", commonconstants.DeleteNo),
			&res)
		for i := 0; i < len(res); i++ {
			node := res[i]
			childrenMap[node.GetKey()] = &node
			if node.GetPreNode() == constants.NodeTypeDefault {
				head = &node
			}
		}
	}
	// 获取了头节点，进行排序
	res := make([]string, 0, len(childrenMap))
	for {
		if head == nil {
			break
		}
		res = append(res, head.GetKey())
		head = childrenMap[head.GetPostNode()]
	}
	return res
}

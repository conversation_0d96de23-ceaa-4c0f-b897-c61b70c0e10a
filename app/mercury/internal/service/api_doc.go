package service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/tree_node/tree_operate"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/remote_api"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/mocker"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/response_body_mode"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

var (
	apiDocDao            dao.ApiDocDao
	apiFolderDao         dao.ApiFolderDao
	apiStatusDao         dao.ApiStatusDao
	cubeRemoteApi        remote.CubeBaseRemoteApi
	jupiterRemoteApi     remote_api.JupiterRemoteApi
	apiResponseService   ApiResponseService
	treeOperation        tree_operate.TreeOperation
	apiRunService        ApiRunService
	apiMockService       mocker.ApiMockService
	apiDocHistoryService ApiDocHistoryService
	usercenterRemoteApi  remote.UserCenterRemoteApi
)

type ApiDocService struct{}

func (a ApiDocService) GetDocDetailById(ctx *commoncontext.MantisContext, id int64, projectNo string) *dto.ApiDocSaveDTO {
	apiDocById := apiDocDao.GetApiDocById(ctx, id)
	apiDocById.NodeType = "apiNode"
	if apiDocById == nil {
		return nil
	}
	apiDocSaveDTO := a.ConvertModel2DTO(ctx, *apiDocById)
	return &apiDocSaveDTO
}

func (a ApiDocService) ConvertModel2DTO(ctx *commoncontext.MantisContext, apiDocById models.ApiDocBase) dto.ApiDocSaveDTO {
	var apiDocSaveDTO dto.ApiDocSaveDTO
	userMap := make(map[string]string, 3)
	if apiDocById.Status != 0 {
		apiDocById.StatusName = apiStatusDao.GetApiStatusById(ctx, apiDocById.Status).Name
	}
	// 填入用户信息
	if apiDocById.ResponsibleId != "" {
		userMap[apiDocById.ResponsibleId] = ""
	}
	userMap[apiDocById.Creator] = ""
	userMap[apiDocById.Modifier] = ""
	err := usercenterRemoteApi.GetUserAdAccountNameMap(userMap, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	apiDocById.ResponsibleName = userMap[apiDocById.ResponsibleId]
	apiDocById.CreatorName = userMap[apiDocById.Creator]
	apiDocById.ModifierName = userMap[apiDocById.Modifier]
	if apiDocById.BindLabelId != "" {
		tags := cubeRemoteApi.GetTagsByIds(ctx, apiDocById.BindLabelId)
		apiDocById.Labels = tags
	}
	if apiDocById.Request != "" {
		requestDTO := dto.ApiDocRequestDTO{}
		jsonx.UnMarshal([]byte(apiDocById.Request), &requestDTO)
		schema := requestDTO.RequestBody.JsonSchema
		AnalyseSchemaRef(ctx, schema, -1)
		requestDTO.RequestBody.JsonSchema = schema
		if schema != nil {
			requestDTO.RequestBody.GeneratedExample = convertSchema2Json(ctx, *schema, set.New[int64]())
		}
		apiDocSaveDTO.DocRequestDTO = requestDTO
	}
	apiDocSaveDTO.ApiDocBase = apiDocById
	apiResponses := apiResponseService.GetResponseListByApiDocId(ctx, apiDocById.Id)
	apiDocSaveDTO.ApiResponseList = apiResponses
	if apiResponses != nil && len(apiResponses) != 0 {
		apiDocSaveDTO.ApiResponseList = apiResponses
		respIds := make([]int64, len(apiResponses))
		for i, resp := range apiResponses {
			respIds[i] = resp.Id
		}
		apiDocSaveDTO.ApiResponseCaseList = apiResponseCaseService.GetApiResponseCaseListByApiResponseIds(ctx, respIds)
	}
	return apiDocSaveDTO
}

func convertSchema2Json(ctx *commoncontext.MantisContext, schema dto.ApiBaseSchema, set set.Set[int64]) string {
	jsonStr := ""
	if schema.Type == "引用" {
		if schema.Ref != "" {
			dataModel := apiDataModelService.GetApiDataModelByRef(ctx, schema.Ref)
			if dataModel.Id == 0 || set.Contain(dataModel.Id) {
				jsonStr += "{}"
			} else {
				set.Add(dataModel.Id)
				v := dto.ApiBaseSchema{}
				jsonx.UnMarshal([]byte(dataModel.Content), &v)
				jsonStr += convertSchema2Json(ctx, v, set)
				set.Delete(dataModel.Id)
			}
		} else {
			jsonStr += "null"
		}
	} else if schema.Type == "object" {
		jsonStr += "{"
		if schema.Properties != nil && len(schema.Properties) != 0 {
			for k, v := range schema.Properties {
				jsonStr += `"` + k + `"` + ": " + convertSchema2Json(ctx, v, set) + ","
			}
			jsonStr = jsonStr[:len(jsonStr)-1]
		}
		jsonStr += "}"
	} else if schema.Type == "array" {
		jsonStr += "["
		if schema.Items != nil {
			jsonStr += convertSchema2Json(ctx, *(schema.Items), set)
		}
		jsonStr += "]"
	} else {
		jsonStr += getDefaultValueByType(schema.Type)
	}
	return jsonStr
}

func getDefaultValueByType(typeStr string) string {
	switch typeStr {
	case "boolean":
		return "false"
	case "string":
		return `"string"`
	case "integer", "number":
		return "0"
	default:
		return "null"
	}
}

func (a ApiDocService) GetNodeByLibId(ctx *commoncontext.MantisContext, libId int64) []models.ApiDocBase {
	res := apiDocDao.SelectNodeByLibId(ctx, libId)
	return res
}

func (a ApiDocService) RemoveBatchByFolder(ctx *commoncontext.MantisContext, folder []int64, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("folder_id", folder)
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, paramBuilder, map[string]any{
		"is_deleted":   commonconstants.DeleteYes,
		"modifier":     user.AdAccount,
		"gmt_modified": times.Now(),
	})
}

func (a ApiDocService) RemoveBatchByIds(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	gormx.Transaction(ctx, func() error {
		for _, id := range ids {
			api := models.ApiDocBase{}
			api.Id = id
			gormx.SelectOneByConditionX(ctx, &api)
			treeOperation.RemoveOneNodeOnTree(ctx, &api, constants.FolderTypeApi)
			api.IsDeleted = commonconstants.DeleteYes
			api.GmtModified = times.Now()
			api.Modifier = user.AdAccount
			gormx.UpdateOneByConditionX(ctx, &api)
		}
		return nil
	})
}

func (a ApiDocService) RemoveDocById(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	docBase := models.ApiDocBase{}
	docBase.Id = id
	gormx.SelectOneByConditionX(ctx, &docBase)
	// 从树上移除
	treeOperation.RemoveOneNodeOnTree(ctx, &docBase, constants.FolderTypeApi)
	docBase.Modifier = user.AdAccount
	docBase.IsDeleted = commonconstants.DeleteYes
	apiDocDao.UpdateApiDocById(ctx, &docBase)
	responseIds := apiResponseService.GetResponseIdsByApiDocId(ctx, id)
	if responseIds != nil && len(responseIds) != 0 {
		apiResponseCaseService.RemoveApiResponseCaseByApiResponseIds(ctx, responseIds, user)
		apiResponseService.RemoveResponseByApiDocId(ctx, id, user)
	}
	apiRunService.DeleteByDocId(ctx, id, user)
	apiMockService.DeleteMockByDocId(ctx, id, user)
}

func (a ApiDocService) SaveDoc(ctx *commoncontext.MantisContext, saveDTO dto.ApiDocSaveDTO, user commondto.UserInfo, projectNo string) int64 {
	var docId int64
	gormx.Transaction(ctx, func() error {
		libId := saveDTO.ApiDocBase.LibId
		doc := a.dealDocBase(ctx, saveDTO.ApiDocBase, saveDTO.DocRequestDTO, user)
		docId = doc.Id
		apiRespList := make([]models.ApiResponse, 0, len(saveDTO.ApiResponseList))
		for _, v := range saveDTO.ApiResponseList {
			AnalyseSchemaRefPath(ctx, v.Content)
			resp := models.ApiResponse{
				Name:          v.Name,
				RespCode:      v.RespCode,
				ContentFormat: v.ContentFormat,
				Content:       string(jsonx.Marshal(v.Content)),
				ApiId:         v.ApiId,
				LibId:         v.LibId,
				TempId:        v.TempId,
			}
			resp.Id = v.Id
			apiRespList = append(apiRespList, resp)
		}
		respOldList, respCaseOldList := a.getRespAndRespCaseByApiId(ctx, doc.Id)
		respSet := set.New[int64]()
		for _, resp := range saveDTO.ApiResponseList {
			respSet.Add(resp.Id)
		}
		respCaseSet := set.New[int64]()
		for _, c := range saveDTO.ApiResponseCaseList {
			respCaseSet.Add(c.Id)
		}
		respDelList := make([]int64, 0)
		for _, old := range respOldList {
			if !respSet.Contain(old) {
				respDelList = append(respDelList, old)
			}
		}
		respCaseDelList := make([]int64, 0)
		for _, old := range respCaseOldList {
			if !respCaseSet.Contain(old) {
				respCaseDelList = append(respCaseDelList, old)
			}
		}
		a.deleteRespAndRespCase(ctx, respDelList, respCaseDelList, user)
		tempIdMap := apiResponseService.InsertUpdateApiResponse(ctx, libId, doc.Id, apiRespList, user)
		apiResponseCaseService.InsertUpdateResponseCase(ctx, tempIdMap, saveDTO.ApiResponseCaseList, user)
		// 新增历史记录
		apiDocHistoryService.SaveToHistory(ctx, a.ConvertModel2DTO(ctx, doc), user)
		return nil
	})
	return docId
}

func (a ApiDocService) UpdateStatus(ctx *commoncontext.MantisContext, id int64, status int64, user commondto.UserInfo) {
	docBase := models.ApiDocBase{}
	docBase.Id = id
	docBase.Status = status
	docBase.Modifier = user.AdAccount
	docBase.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &docBase)
}

func (a ApiDocService) UpdateBatchStatus(ctx *commoncontext.MantisContext, docIds []int64, status int64, user commondto.UserInfo) {
	gormx.UpdateBatchByParamBuilderAndMapX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", docIds).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"status":       status,
			"modifier":     user.AdAccount,
			"gmt_modified": times.Now(),
		})
}

func (a ApiDocService) getRespAndRespCaseByApiId(ctx *commoncontext.MantisContext, docId int64) (respList []int64, respCaseList []int64) {
	responses := make([]models.ApiResponse, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", docId).
			Eq("is_deleted", commonconstants.DeleteNo),
		&responses)
	respList = make([]int64, 0, len(responses))
	for _, resp := range responses {
		respList = append(respList, resp.Id)
	}
	cases := make([]models.ApiResponseCase, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("resp_id", respList).
			Eq("is_deleted", commonconstants.DeleteNo),
		&cases)
	respCaseList = make([]int64, 0, len(cases))
	for _, c := range cases {
		respCaseList = append(respCaseList, c.Id)
	}
	return respList, respCaseList
}

func (a ApiDocService) deleteRespAndRespCase(ctx *commoncontext.MantisContext, delRespList []int64, delRespCaseList []int64, user commondto.UserInfo) {
	if delRespList != nil && len(delRespList) != 0 {
		apiResponseService.RemoveResponsesByIds(ctx, delRespList, user)
	}
	if delRespCaseList != nil && len(delRespCaseList) != 0 {
		apiResponseCaseService.RemoveApiResponseCasesByIds(ctx, delRespCaseList, user)
	}
}

func (a ApiDocService) dealDocBase(ctx *commoncontext.MantisContext, apiDocBase models.ApiDocBase, requestDTO dto.ApiDocRequestDTO, user commondto.UserInfo) models.ApiDocBase {
	schema := requestDTO.RequestBody.JsonSchema
	AnalyseSchemaRefPath(ctx, schema)
	apiDocBase.Request = string(jsonx.Marshal(requestDTO))
	apiDocBase.Modifier = user.AdAccount
	apiDocBase.GmtModified = times.Now()
	if apiDocBase.Id == 0 {
		apiDocBase.Creator = user.AdAccount
		apiDocBase.OrderNo = apiDocDao.GetMaxOrderNo(ctx, apiDocBase.FolderId, apiDocBase.LibId) + 1
		apiDocBase.GmtCreated = times.Now()
		apiDocBase.IsDeleted = commonconstants.DeleteNo
		// 在树上新增
		apiDocBase.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi)
		treeOperation.AddOneNodeToTail(ctx, apiDocBase.FolderId, &apiDocBase, constants.FolderTypeApi)
	} else {
		oldDoc := models.ApiDocBase{}
		oldDoc.Id = apiDocBase.Id
		gormx.SelectOneByConditionX(ctx, &oldDoc)
		apiDocBase.Key = oldDoc.Key
		apiDocBase.PreNode = oldDoc.PreNode
		apiDocBase.PostNode = oldDoc.PostNode
		apiDocDao.UpdateApiDocById(ctx, &apiDocBase)
	}
	return apiDocBase
}

func (a ApiDocService) Copy(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo, projectNo string) models.ApiDocBase {
	apiDocBase := models.ApiDocBase{}
	apiDocBase.Id = id
	gormx.SelectOneByConditionX(ctx, &apiDocBase)
	apiDocBase.Id = 0
	if len(apiDocBase.Name) <= 250 {
		apiDocBase.Name = apiDocBase.Name + constants.CopyNameSuffix
	}
	apiDocBase.Creator = user.AdAccount
	apiDocBase.Modifier = user.AdAccount
	apiDocBase.GmtCreated = times.Now()
	apiDocBase.GmtModified = times.Now()
	apiDocBase.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi)
	apiDocBase.JupiterApiId = 0
	apiDocBase.JupiterVersionId = 0
	apiDocBase.JupiterApiName = ""
	// 加进树里，加到原节点的后面
	oriApi := models.ApiDocBase{}
	oriApi.Id = id
	gormx.SelectOneByConditionX(ctx, &oriApi)
	treeOperation.AddOneNodeToTree(ctx, &apiDocBase, &oriApi, constants.TreePositionPost, constants.FolderTypeApi)
	// 复制response
	apiResponseService.CopyApiRespByApiId(ctx, id, apiDocBase.Id, user)
	// 新增历史记录
	apiDocHistoryService.SaveToHistory(ctx, a.ConvertModel2DTO(ctx, apiDocBase), user)
	return apiDocBase
}

func (a ApiDocService) CopyByFolderId(ctx *commoncontext.MantisContext, srcFolderId int64, tarFolderId int64, oldNewKeyMap map[string]string, user commondto.UserInfo,
	projectNo string,
) {
	// 复制api doc
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("folder_id", srcFolderId).Eq("is_deleted", commonconstants.DeleteNo)
	apiDocList := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocList)
	copyDocList := make([]models.ApiDocBase, 0, len(apiDocList))
	// 历史记录
	apiDocHistoryList := make([]models.ApiDocBaseHistory, 0, len(apiDocList))
	if apiDocList != nil && len(apiDocList) != 0 {
		for i := range apiDocList {
			doc := apiDocList[i]
			doc.Name = doc.Name + constants.CopyNameSuffix
			doc.FolderId = tarFolderId
			doc.CopyId = doc.Id
			doc.Creator = user.AdAccount
			doc.Modifier = user.AdAccount
			doc.Id = 0
			doc.Key = oldNewKeyMap[doc.Key]
			doc.JupiterApiId = 0
			doc.JupiterVersionId = 0
			doc.JupiterApiName = ""
			// 修正顺序
			doc.SetPreNode(oldNewKeyMap[doc.GetPreNode()])
			doc.SetPostNode(oldNewKeyMap[doc.GetPostNode()])
			copyDocList = append(copyDocList, doc)
		}
		gormx.Transaction(ctx, func() error {
			gormx.InsertBatchX(ctx, copyDocList)
			// 复制api controller
			for _, doc := range copyDocList {
				apiResponseService.CopyApiRespByApiId(ctx, doc.CopyId, doc.Id, user)
				apiDocHistoryList = append(apiDocHistoryList, apiDocHistoryService.ConvertDoc2DocHistory(a.ConvertModel2DTO(ctx, doc), user))
			}
			// 写入历史记录
			apiDocHistoryService.SaveBatchHistory(ctx, apiDocHistoryList)
			return nil
		})
	}
}

func (a ApiDocService) SearchByFolderId(ctx *commoncontext.MantisContext, folderId int64, libId int64, search string, method string,
	request gormx.PageRequest,
) gormx.PageResult {
	folderIds := apiFolderDao.GetChildFolderIdByParentId(ctx, folderId, libId)
	paramBuilder := gormx.NewParamBuilder().
		Model(&models.ApiDocBase{}).
		Select("id, name, path, method, status, bind_label_id, responsible_id, jupiter_api_id , creator, modifier, gmt_created, gmt_modified").
		Eq("lib_id", libId).In("folder_id", folderIds).Eq("is_deleted", commonconstants.DeleteNo).
		Or(gormx.NewParamBuilder().Like("name", "%"+search+"%"), gormx.NewParamBuilder().Like("path", "%"+search+"%")).
		OrderByAsc("name")
	if method != "" {
		paramBuilder.Eq("method", method)
	}
	res := make([]models.ApiDocBase, 0)
	pageResult := gormx.PageSelectByParamBuilderX(ctx, paramBuilder, &res, request)
	userMap := make(map[string]string)
	statusIdMap := make(map[int64]string)
	labelStrs := make([]string, 0)
	jupiterIds := make([]int64, 0, len(res))
	for i, doc := range res {
		userMap[doc.ResponsibleId] = ""
		userMap[doc.Creator] = ""
		userMap[doc.Modifier] = ""
		statusIdMap[doc.Status] = ""
		labelStrs = append(labelStrs, doc.BindLabelId)
		labelIdStr := strings.Split(doc.BindLabelId, ",")
		labels := make([]int64, 0, len(labelIdStr))
		for _, str := range labelIdStr {
			labelId, err := strconv.ParseInt(str, 10, 64)
			if str == "" || err != nil {
				continue
			}
			labels = append(labels, labelId)
		}
		doc.BindLabelIds = labels
		if doc.JupiterApiId != 0 {
			jupiterIds = append(jupiterIds, doc.JupiterApiId)
		}
		res[i] = doc
	}
	err := usercenterRemoteApi.GetUserAdAccountNameMap(userMap, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	// 处理status
	statusIds := make([]int64, 0)
	for k := range statusIdMap {
		statusIds = append(statusIds, k)
	}
	statusList := make([]models.ApiStatus, len(statusIds))
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&models.ApiStatus{}).In("id", statusIds), &statusList)
	for _, status := range statusList {
		statusIdMap[status.Id] = status.Name
	}
	// 处理label
	tagsByIds := cubeRemoteApi.GetTagsByIds(ctx, strings.Join(labelStrs, ","))
	labelMap := make(map[int64]commondto.CubeTagDTO)
	for _, tag := range tagsByIds {
		labelMap[tag.Value] = tag
	}
	// 处理关联jupiter信息
	jupiterIdNameMap := jupiterRemoteApi.GetIdNameMap(ctx, jupiterIds)
	// 需要解除关联的数据
	deleteJupiterIds := make([]int64, 0)
	// 填入额外信息
	for i, doc := range res {
		doc.ResponsibleName = userMap[doc.ResponsibleId]
		doc.CreatorName = userMap[doc.Creator]
		doc.Modifier = userMap[doc.Modifier]
		doc.StatusName = statusIdMap[doc.Status]
		doc.NodeType = constants.NodeTypeApi
		doc.TreeType = "api"
		doc.Type = "api"
		tags := make([]commondto.CubeTagDTO, 0, len(doc.BindLabelIds))
		for _, tagId := range doc.BindLabelIds {
			tags = append(tags, labelMap[tagId])
		}
		doc.Labels = tags
		if doc.JupiterApiId != 0 {
			name, ok := jupiterIdNameMap[doc.JupiterApiId]
			if ok {
				doc.JupiterApiName = name
			} else {
				deleteJupiterIds = append(deleteJupiterIds, doc.Id)
			}
		}
		res[i] = doc
	}
	// 解除关联
	if len(deleteJupiterIds) != 0 {
		gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", deleteJupiterIds), map[string]any{
			"jupiter_api_id":     0,
			"jupiter_version_id": 0,
		})
	}
	pageResult.List = res
	return *pageResult
}

func (a ApiDocService) UpdateResponseId(ctx *commoncontext.MantisContext, ids []int64, responsibleId string, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", ids)
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, paramBuilder, map[string]any{
		"responsible_id": responsibleId,
		"gmt_modified":   times.Now(),
		"modifier":       user.AdAccount,
	})
}

func (a ApiDocService) dealWithDocRequestHtml(requestDTO *dto.ApiDocRequestDTO) {
	parameters := requestDTO.Parameters
	requestBody := requestDTO.RequestBody
	if parameters != nil {
		if parameters.Path != nil && len(parameters.Path) != 0 {
			for i, path := range parameters.Path {
				parameters.Path[i].Example = a.dealWithHtmlAndVariable(path.Example)
			}
		}
		if parameters.Query != nil && len(parameters.Query) != 0 {
			for i, query := range parameters.Query {
				parameters.Query[i].Example = a.dealWithHtmlAndVariable(query.Example)
			}
		}
		if parameters.Cookie != nil && len(parameters.Cookie) != 0 {
			for i, cookie := range parameters.Cookie {
				parameters.Cookie[i].Example = a.dealWithHtmlAndVariable(cookie.Example)
			}
		}
		if parameters.Header != nil && len(parameters.Header) != 0 {
			for i, h := range parameters.Header {
				if h.Type == data_type.ARRAY {
					arr := make([]string, 0)
					vals := h.Example.([]any)
					if vals != nil && len(vals) != 0 {
						for _, val := range vals {
							arr = append(arr, a.dealWithHtmlAndVariable(val))
						}
					}
					parameters.Header[i].Example = arr
				} else {
					parameters.Header[i].Example = a.dealWithHtmlAndVariable(h.Example)
				}
			}
		}
	}
	if requestBody != nil && requestBody.Parameters != nil {
		if len(requestBody.Parameters) != 0 {
			for i, p := range requestBody.Parameters {
				if p.Type != response_body_mode.FILE {
					value := ""
					if p.Type == data_type.ARRAY {
						pList, ok := p.Example.([]any)
						if !ok || pList == nil || len(pList) == 0 {
							requestBody.Parameters[i].Example = ""
						} else {
							newList := make([]string, 0, len(pList))
							for j := range pList {
								newList = append(newList, a.dealWithHtmlAndVariable(pList[j]))
							}
							requestBody.Parameters[i].Example = newList
						}
					} else {
						value = a.dealWithHtmlAndVariable(p.Example)
						requestBody.Parameters[i].Example = value
					}
				} else {
					requestBody.Parameters[i].Example = ""
				}
			}
		}
	}
}

// dealWithHtmlAndVariable 剥离前端传入的html部分
func (a ApiDocService) dealWithHtmlAndVariable(value any) string {
	if value == nil {
		return ""
	}
	str := fmt.Sprintf("%v", value)
	if str != "" {
		resStr := ""
		mockDataHtmlDTO := dto.MockDataHtmlDTO{}
		err := json.Unmarshal([]byte(str), &mockDataHtmlDTO)
		if err != nil {
			resStr = str
		} else {
			children := mockDataHtmlDTO.NodeList[0].Children
			if children != nil && len(children) != 0 {
				for _, node := range children {
					if node.NodeType == "var" {
						resStr += node.Value
					} else if node.NodeType == "func" {
						resStr += node.Value
					} else {
						resStr += node.Text
					}
				}
			}
		}
		return resStr
	}
	return ""
}

func (a ApiDocService) Convert2JupiterDTO(ctx *commoncontext.MantisContext, id int64) any {
	base := models.ApiDocBase{}
	base.Id = id
	base.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &base)
	requestDTO := dto.ApiDocRequestDTO{}
	if base.Request != "" {
		jsonx.UnMarshal([]byte(base.Request), &requestDTO)
	}
	jupiterDTO := dto.ApiDocJupiterDTO{
		ApiDocBase:    base,
		DocRequestDTO: requestDTO,
	}
	a.dealWithDocRequestHtml(&(jupiterDTO.DocRequestDTO))
	return jupiterRemoteApi.ConvertMercuryDTO(ctx, jupiterDTO)
}

func (a ApiDocService) Sync2Jupiter(ctx *commoncontext.MantisContext, id int64) {
	base := models.ApiDocBase{}
	base.Id = id
	base.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &base)
	requestDTO := dto.ApiDocRequestDTO{}
	jsonx.UnMarshal([]byte(base.Request), &requestDTO)
	jupiterDTO := dto.ApiDocJupiterDTO{
		ApiDocBase:    base,
		DocRequestDTO: requestDTO,
	}
	a.dealWithDocRequestHtml(&(jupiterDTO.DocRequestDTO))
	jupiterRemoteApi.SyncMercuryDTO(ctx, jupiterDTO)
}

func (a ApiDocService) Save2Jupiter(ctx *commoncontext.MantisContext, id int64, doc map[string]any) {
	jupiterId, versionId := jupiterRemoteApi.Save2Jupiter(ctx, doc)
	base := models.ApiDocBase{}
	base.Id = id
	base.JupiterApiId = int64(jupiterId)
	base.JupiterVersionId = int64(versionId)
	gormx.UpdateOneByConditionX(ctx, &base)
}

func (a ApiDocService) SaveBatch2JupiterByIds(ctx *commoncontext.MantisContext, ids []int64, appId int64, spaceId int64) {
	docBaseList := make([]models.ApiDocBase, 0, len(ids))
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", ids).Eq("is_deleted", commonconstants.DeleteNo)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &docBaseList)
	docDtoList := make([]dto.ApiDocJupiterDTO, 0, len(ids))
	for _, doc := range docBaseList {
		if doc.JupiterApiId != 0 && doc.JupiterVersionId != 0 {
			continue
		}
		requestDTO := dto.ApiDocRequestDTO{}
		jsonx.UnMarshal([]byte(doc.Request), &requestDTO)
		jupiterDTO := dto.ApiDocJupiterDTO{
			ApiDocBase:    doc,
			DocRequestDTO: requestDTO,
			AppId:         appId,
			SpaceId:       spaceId,
		}
		a.dealWithDocRequestHtml(&(jupiterDTO.DocRequestDTO))
		docDtoList = append(docDtoList, jupiterDTO)
	}
	response := jupiterRemoteApi.SaveBatch2Jupiter(ctx, docDtoList)
	for _, doc := range docBaseList {
		jupiterIdVersionIdResponse := response[doc.Id]
		doc.JupiterApiId = jupiterIdVersionIdResponse.Id
		doc.JupiterVersionId = jupiterIdVersionIdResponse.VersionId
		gormx.UpdateOneByConditionX(ctx, &doc)
	}
}

func (a ApiDocService) SyncBatch2JupiterByIds(ctx *commoncontext.MantisContext, ids []int64) {
	docBaseList := make([]models.ApiDocBase, 0, len(ids))
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", ids).Eq("is_deleted", commonconstants.DeleteNo)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &docBaseList)
	docDtoList := make([]dto.ApiDocJupiterDTO, 0, len(ids))
	for _, doc := range docBaseList {
		if doc.JupiterApiId == 0 && doc.JupiterVersionId == 0 {
			continue
		}
		requestDTO := dto.ApiDocRequestDTO{}
		jsonx.UnMarshal([]byte(doc.Request), &requestDTO)
		jupiterDTO := dto.ApiDocJupiterDTO{
			ApiDocBase:    doc,
			DocRequestDTO: requestDTO,
		}
		a.dealWithDocRequestHtml(&(jupiterDTO.DocRequestDTO))
		docDtoList = append(docDtoList, jupiterDTO)
	}
	jupiterRemoteApi.SyncBatch2Jupiter(ctx, docDtoList)
}

func (a ApiDocService) UnbindJupiter(ctx *commoncontext.MantisContext, jupiterId int64, user commondto.UserInfo) {
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Eq("jupiter_api_id", jupiterId).Eq("is_deleted", commonconstants.DeleteNo),
		map[string]any{
			"jupiter_api_id":     nil,
			"jupiter_version_id": nil,
			"modifier":           user.AdAccount,
			"gmt_modified":       times.Now(),
		})
}

func (a ApiDocService) OneDocUnbindJupiter(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	api := models.ApiDocBase{}
	api.Id = id
	api.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &api)
	api.JupiterApiId = 0
	api.JupiterVersionId = 0
	api.GmtModified = times.Now()
	api.Modifier = user.AdAccount
	gormx.InsertUpdateOneX(ctx, &api)
}

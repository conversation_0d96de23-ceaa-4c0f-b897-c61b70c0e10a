package doc_export

import (
	"fmt"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

type MDExportDocService struct{}

func (handler MDExportDocService) GetExportTypeEnum() string {
	return constants.Markdown
}

func (handler MDExportDocService) AnalyseAndExport(ctx *commoncontext.MantisContext, exportDTO doc_import_export.ExportDTO) (string, string) {
	res := ""
	res += handler.dealWithTitleAndEnv(exportDTO)
	res += handler.dealWithApi(exportDTO)
	res += handler.dealWithDataModels(ctx, exportDTO.LibDetail.Id)
	return res, exportDTO.LibDetail.Name + ".md"
}

func (handler MDExportDocService) dealWithTitleAndEnv(exportDTO doc_import_export.ExportDTO) string {
	return fmt.Sprintf("# %s\n\nBase Url: %s\n\n", exportDTO.LibDetail.Name, exportDTO.Env.PreUrl)
}

func (handler MDExportDocService) dealWithApi(exportDTO doc_import_export.ExportDTO) string {
	res := ""
	folderIdNamePathMap := handler.dealWithFolders(exportDTO.FolderMap)
	pathApiDocsMap := handler.transferDocSlice2Map(exportDTO.ApiDocs)
	for k, v := range pathApiDocsMap {
		res += fmt.Sprintf("# %s\n\n", folderIdNamePathMap[k])
		if v != nil && len(v) != 0 {
			for _, docDTO := range v {
				res += handler.dealWithOneApi(docDTO)
			}
		}
	}
	return res
}

func (handler MDExportDocService) dealWithFolders(folderMap map[int64]models.ApiFolder) map[int64]string {
	folderIdNamePathMap := make(map[int64]string)
	if folderMap != nil && len(folderMap) != 0 {
	out:
		for k, v := range folderMap {
			namePath := ""
			pathIds := strings.Split(v.Path, ",")
			if pathIds != nil && len(pathIds) != 0 {
				for _, idStr := range pathIds {
					if idStr == "" || idStr == " " || idStr == "0" {
						continue
					}
					id, err := strconv.ParseInt(idStr, 0, 64)
					if err != nil {
						continue out
					}
					namePath += folderMap[id].Name + "/"
				}
			}
			namePath += v.Name
			folderIdNamePathMap[k] = namePath
		}
	}
	folderIdNamePathMap[0] = "Default"
	return folderIdNamePathMap
}

func (handler MDExportDocService) transferDocSlice2Map(docs []dto.ApiDocSaveDTO) map[int64][]dto.ApiDocSaveDTO {
	pathApiDocsMap := make(map[int64][]dto.ApiDocSaveDTO)
	if docs != nil && len(docs) != 0 {
		for i := range docs {
			folderId := docs[i].ApiDocBase.FolderId
			_, ok := pathApiDocsMap[folderId]
			if !ok {
				pathApiDocsMap[folderId] = make([]dto.ApiDocSaveDTO, 0)
			}
			pathApiDocsMap[folderId] = append(pathApiDocsMap[folderId], docs[i])
		}
	}
	return pathApiDocsMap
}

func (handler MDExportDocService) dealWithOneApi(docDTO dto.ApiDocSaveDTO) string {
	apiDocBase := docDTO.ApiDocBase
	res := fmt.Sprintf("## %s %s\n\n%s %s\n\n", apiDocBase.Method, apiDocBase.Name, apiDocBase.Method, apiDocBase.Path)
	// 处理参数
	res += handler.dealWithApiRequest(docDTO.DocRequestDTO)
	// 处理response
	res += handler.dealWithResponseAndCase(docDTO.ApiResponseList, docDTO.ApiResponseCaseList)
	res += "\n\n"
	return res
}

func (handler MDExportDocService) dealWithApiRequest(requestDTO dto.ApiDocRequestDTO) string {
	res := "### Params\n\n|Name|Location|Type|Required|Description|\n|---|---|---|---|---|\n"
	// 处理parameter
	if requestDTO.Parameters != nil {
		parameters := requestDTO.Parameters
		// 处理query参数
		if parameters.Query != nil && len(parameters.Query) != 0 {
			for _, q := range parameters.Query {
				res += fmt.Sprintf("|%s|query|%s|%s|%s|\n", q.Name, q.Type, strconv.FormatBool(q.Required), q.Description)
			}
		}
		// 处理path参数
		if parameters.Path != nil && len(parameters.Path) != 0 {
			for _, p := range parameters.Path {
				res += fmt.Sprintf("|%s|path|%s|%s|%s|\n", p.Name, p.Type, strconv.FormatBool(p.Required), p.Description)
			}
		}
		// 处理header参数
		if parameters.Header != nil && len(parameters.Header) != 0 {
			for _, p := range parameters.Header {
				res += fmt.Sprintf("|%s|header|%s|%s|%s|\n", p.Name, p.Type, strconv.FormatBool(p.Required), p.Description)
			}
		}
		// 处理cookie参数
		if parameters.Cookie != nil && len(parameters.Cookie) != 0 {
			for _, p := range parameters.Cookie {
				res += fmt.Sprintf("|%s|cookie|%s|%s|%s|\n", p.Name, p.Type, strconv.FormatBool(p.Required), p.Description)
			}
		}
	}
	// 处理request body
	if requestDTO.RequestBody != nil {
		body := requestDTO.RequestBody
		// 处理 form
		if body.Parameters != nil && len(body.Parameters) != 0 {
			for _, p := range body.Parameters {
				res += fmt.Sprintf("|%s|body|%s|%s|%s|\n", p.Name, p.Type, strconv.FormatBool(p.Required), p.Description)
			}
		}
		// 处理 body
		if body.JsonSchema != nil {
			res += handler.dealWithJsonSchema(*(body.JsonSchema), "") + "\n"
		}
		// 处理examples
		example := ""
		if body.Example != "" {
			example = body.Example
		} else if body.GeneratedExample != "" {
			example = body.GeneratedExample
		}
		if example != "" {
			res += "> Body Example\n\n"
			if strings.Contains(body.Content, common_constants.JsonContentType) {
				res += fmt.Sprintf("```json\n%s\n```\n\n", example)
			} else if strings.Contains(body.Content, common_constants.XmlContentType) {
				res += fmt.Sprintf("```xmlx\n%s\n```\n\n", example)
			} else {
				res += fmt.Sprintf("```\n%s\n```\n\n", example)
			}
		}
	}
	return res
}

func (handler MDExportDocService) dealWithJsonSchema(schema dto.ApiBaseSchema, prefix string) string {
	res := ""
	name := prefix + " " + schema.Name
	t := schema.Type
	if schema.Ref != "" {
		typeName := strings.SplitN(schema.Ref, "+", 2)[1]
		t = fmt.Sprintf("[%s](#%s)", typeName, strings.ToLower(typeName))
		if schema.Position == constants.BaseSchemaOuterPos {
			name = prefix + " " + "外层引用"
		}
	}
	if schema.Name == "" {
		return res
	}
	required := strconv.FormatBool(schema.Required)
	res += fmt.Sprintf("|%s|body|%s|%s|%s|\n", name, t, required, schema.Description)
	if schema.Properties != nil && len(schema.Properties) != 0 {
		for _, v := range schema.Properties {
			res += handler.dealWithJsonSchema(v, prefix+"»")
		}
	} else if schema.Items != nil {
		res += handler.dealWithJsonSchema(*(schema.Items), prefix+"»")
	}
	return res
}

func (handler MDExportDocService) dealWithResponseAndCase(responses []dto.ApiResponseDTO, cases []models.ApiResponseCase) string {
	res := "### Responses\n\n"
	respIdCasesMap := make(map[int64][]models.ApiResponseCase)
	for i, c := range cases {
		respId, err := strconv.ParseInt(c.RespId, 0, 64)
		if err != nil {
			continue
		}
		_, ok := respIdCasesMap[respId]
		if !ok {
			respIdCasesMap[respId] = make([]models.ApiResponseCase, 0)
		}
		respIdCasesMap[respId] = append(respIdCasesMap[respId], cases[i])
	}
	if responses != nil && len(responses) != 0 {
		for _, resp := range responses {
			res += fmt.Sprintf("#### %s\n\nHTTP Status Code: %s\n\n", resp.Name, strconv.FormatInt(int64(resp.RespCode), 10))
			if resp.Content != nil {
				res += fmt.Sprintf("Data Fotmat:\n\n|Name|Location|Type|Required|Description|\n|---|---|---|---|---|\n")
				res += handler.dealWithJsonSchema(*(resp.Content), "")
			}
			res += "\n"
			responseCases, ok := respIdCasesMap[resp.Id]
			if ok && len(responseCases) != 0 {
				res += "> Response Cases: \n\n"
				for _, c := range responseCases {
					res += fmt.Sprintf("%s:\n```json\n%s\n```\n\n", c.Name, c.Content)
				}
			}
		}
	}
	return res
}

func (handler MDExportDocService) dealWithDataModels(ctx *commoncontext.MantisContext, libId int64) string {
	res := "# Data Models\n\n"
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", libId).Eq("is_deleted", common_constants.DeleteNo)
	dataModels := make([]models.ApiDataModel, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &dataModels)
	for _, model := range dataModels {
		res += fmt.Sprintf("## %s\n\n### Attribute\n\n|Name|Type|Description|\n|---|---|---|\n", model.Name)
		apiBaseSchema := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(model.Content), &apiBaseSchema)
		res += handler.dealWithModelSchema(apiBaseSchema, "")
		res += "\n"
	}
	return res
}

func (handler MDExportDocService) dealWithModelSchema(schema dto.ApiBaseSchema, prefix string) string {
	res := ""
	name := prefix + " " + schema.Name
	t := schema.Type
	if schema.Ref != "" {
		typeName := strings.SplitN(schema.Ref, "+", 2)[1]
		t = fmt.Sprintf("[%s](#%s)", typeName, strings.ToLower(typeName))
		if schema.Position == constants.BaseSchemaOuterPos {
			name = prefix + " " + "外层引用"
		}
	}
	if schema.Name == "" {
		return res
	}
	res += fmt.Sprintf("|%s|%s|%s|\n", name, t, schema.Description)
	if schema.Properties != nil && len(schema.Properties) != 0 {
		for _, v := range schema.Properties {
			res += handler.dealWithModelSchema(v, prefix+"»")
		}
	} else if schema.Items != nil {
		res += handler.dealWithModelSchema(*(schema.Items), prefix+"»")
	}
	return res
}

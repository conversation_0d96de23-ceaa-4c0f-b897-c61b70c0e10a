package doc_export

import (
	"sort"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	load_and_export_doc_dto "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	common_constants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

type OpenApi3ExportDocService struct{}

func (handler OpenApi3ExportDocService) GetExportTypeEnum() string {
	return constants.OPENAPI_3
}

func (handler OpenApi3ExportDocService) AnalyseAndExport(ctx *commoncontext.MantisContext, exportDTO load_and_export_doc_dto.ExportDTO) (string, string) {
	openApiDTO := handler.analyseAndExportToOpenApiDTO(ctx, exportDTO)
	return string(jsonx.Marshal(&openApiDTO)), openApiDTO.Info.Title + ".json"
}

func (handler OpenApi3ExportDocService) analyseAndExportToOpenApiDTO(ctx *commoncontext.MantisContext, exportDTO load_and_export_doc_dto.ExportDTO) load_and_export_doc_dto.OpenApi3DTO {
	res := load_and_export_doc_dto.OpenApi3DTO{}
	res.Openapi = "3.0.0"
	res.Servers = []load_and_export_doc_dto.OpenApi3Server{
		{
			Url:         exportDTO.Env.PreUrl,
			Description: exportDTO.Env.Name,
		},
	}
	// info处理
	handler.dealWithInfo(exportDTO.LibDetail, &res)
	// 处理folder为tagMap，为了后续处理接口使用，不写入openapi3对象
	tagMap := handler.dealWithFolder(exportDTO.FolderMap)
	// 处理api
	folderUsages, _ := handler.dealWithApi(exportDTO.ApiDocs, exportDTO.LibDetail.Id, &res, tagMap)
	// 写入tags
	handler.dealWithTags(folderUsages, tagMap, &res)
	// 写入dataModel
	handler.dealWithDataModels(ctx, exportDTO.LibDetail.Id, &res)
	return res
}

func (handler OpenApi3ExportDocService) dealWithInfo(library models.ApiLibrary, openApi3DTO *load_and_export_doc_dto.OpenApi3DTO) {
	info := load_and_export_doc_dto.OpenApi3Info{
		Title:       library.Name,
		Description: library.Description,
		Version:     "1.0",
	}
	openApi3DTO.Info = &info
}

func (handler OpenApi3ExportDocService) dealWithFolder(folderMap map[int64]models.ApiFolder) map[int64]string {
	// 对folderMap的id进行排序
	folderIds := make([]int64, len(folderMap))
	for k := range folderMap {
		folderIds = append(folderIds, k)
	}
	sort.Slice(folderIds, func(i, j int) bool {
		return folderIds[i] < folderIds[j]
	})
	tagsMap := make(map[int64]string)
	// 循环完成tags map
	for _, folderId := range folderIds {
		if folderId == 0 {
			continue
		}
		folder := folderMap[folderId]
		prefix := tagsMap[folder.ParentId]
		tagsMap[folderId] = prefix + folder.Name
	}
	return tagsMap
}

func (handler OpenApi3ExportDocService) dealWithApi(apis []dto.ApiDocSaveDTO, libId int64, openApi3DTO *load_and_export_doc_dto.OpenApi3DTO, tagsMap map[int64]string) ([]int64, []string) {
	paths := make(map[string]map[string]load_and_export_doc_dto.OpenApi3PathDTO)
	tagIdSet := set.New[int64]()
	dataModelNameSet := set.New[string]()
	for _, api := range apis {
		tagIdSet.Add(api.ApiDocBase.FolderId)
		methodMap, ok := paths[api.ApiDocBase.Path]
		if !ok {
			methodMap = make(map[string]load_and_export_doc_dto.OpenApi3PathDTO)
			paths[api.ApiDocBase.Path] = methodMap
		}
		pathDTO := load_and_export_doc_dto.OpenApi3PathDTO{
			Tags:        []string{tagsMap[api.ApiDocBase.FolderId]},
			Summary:     api.ApiDocBase.Name,
			Description: api.ApiDocBase.Description,
			Parameters:  handler.dealWithParameters(api.DocRequestDTO.Parameters),
			RequestBody: handler.dealWithRequestBody(api.DocRequestDTO.RequestBody, libId, dataModelNameSet),
			Responses:   handler.dealWithResponse(api.ApiResponseList, libId, dataModelNameSet),
		}
		methodMap[strings.ToLower(api.ApiDocBase.Method)] = pathDTO
	}
	openApi3DTO.Paths = paths
	return tagIdSet.ToSlice(), dataModelNameSet.ToSlice()
}

func (handler OpenApi3ExportDocService) dealWithParameters(parameters *dto.Parameters) []load_and_export_doc_dto.OpenApi3Parameters {
	res := make([]load_and_export_doc_dto.OpenApi3Parameters, 0)
	if parameters == nil {
		return res
	}
	// 处理header
	if parameters.Header != nil && len(parameters.Header) != 0 {
		for _, v := range parameters.Header {
			parameter := load_and_export_doc_dto.OpenApi3Parameters{
				In:          "header",
				Name:        v.Name,
				Required:    v.Required,
				Description: v.Description,
				Schema:      &load_and_export_doc_dto.OpenApi3Schema{Type: v.Type},
			}
			res = append(res, parameter)
		}
	}
	// 处理cookie
	if parameters.Cookie != nil && len(parameters.Cookie) != 0 {
		for _, v := range parameters.Cookie {
			parameter := load_and_export_doc_dto.OpenApi3Parameters{
				In:          "cookie",
				Name:        v.Name,
				Required:    v.Required,
				Description: v.Description,
				Schema:      &load_and_export_doc_dto.OpenApi3Schema{Type: v.Type},
			}
			res = append(res, parameter)
		}
	}
	// 处理path
	if parameters.Path != nil && len(parameters.Path) != 0 {
		for _, v := range parameters.Path {
			parameter := load_and_export_doc_dto.OpenApi3Parameters{
				In:          "path",
				Name:        v.Name,
				Required:    v.Required,
				Description: v.Description,
				Schema:      &load_and_export_doc_dto.OpenApi3Schema{Type: v.Type},
			}
			res = append(res, parameter)
		}
	}
	// 处理query
	if parameters.Query != nil && len(parameters.Query) != 0 {
		for _, v := range parameters.Query {
			parameter := load_and_export_doc_dto.OpenApi3Parameters{
				In:          "query",
				Name:        v.Name,
				Required:    v.Required,
				Description: v.Description,
				Schema:      &load_and_export_doc_dto.OpenApi3Schema{Type: v.Type},
			}
			res = append(res, parameter)
		}
	}
	return res
}

func (handler OpenApi3ExportDocService) dealWithRequestBody(body *dto.RequestBody, libId int64, dataModelSet set.Set[string]) *load_and_export_doc_dto.OpenApi3RequestBody {
	contentMap := make(map[string]load_and_export_doc_dto.OpenApi3DataStruct)
	if body == nil {
		return &load_and_export_doc_dto.OpenApi3RequestBody{Content: contentMap}
	}
	if body.Content == common_constants.FormContentType {
		openApi3Schema := load_and_export_doc_dto.OpenApi3Schema{
			Type:       data_type.OBJECT,
			Properties: make(map[string]load_and_export_doc_dto.OpenApi3Schema),
		}
		for _, parameter := range body.Parameters {
			openApi3Schema.Properties[parameter.Name] = load_and_export_doc_dto.OpenApi3Schema{
				Type:        parameter.Type,
				Description: parameter.Description,
				Example:     parameter.Example,
			}
		}
		contentMap[common_constants.FormContentType] = load_and_export_doc_dto.OpenApi3DataStruct{Schema: &openApi3Schema}
	} else if body.Type == common_constants.JsonContentType {
		openApi3Schema := handler.convertApiBaseSchema2OpenApi3Schema(*(body.JsonSchema), libId, dataModelSet)
		contentMap[common_constants.FormContentType] = load_and_export_doc_dto.OpenApi3DataStruct{Schema: &openApi3Schema}
	}
	return &load_and_export_doc_dto.OpenApi3RequestBody{Content: contentMap}
}

func (handler OpenApi3ExportDocService) dealWithResponse(responseList []dto.ApiResponseDTO, libId int64, dataModelSet set.Set[string]) map[string]load_and_export_doc_dto.OpenApi3Response {
	res := make(map[string]load_and_export_doc_dto.OpenApi3Response)
	for _, response := range responseList {
		code := strconv.FormatInt(int64(response.RespCode), 10)
		apiBaseSchema := response.Content
		schema := handler.convertApiBaseSchema2OpenApi3Schema(*apiBaseSchema, libId, dataModelSet)
		res[code] = load_and_export_doc_dto.OpenApi3Response{
			Description: response.Name,
			Content: map[string]load_and_export_doc_dto.OpenApi3DataStruct{
				response.ContentFormat: {Schema: &schema},
			},
		}
	}
	return res
}

func (handler OpenApi3ExportDocService) convertApiBaseSchema2OpenApi3Schema(schema dto.ApiBaseSchema, libId int64, dataModelSet set.Set[string]) load_and_export_doc_dto.OpenApi3Schema {
	res := load_and_export_doc_dto.OpenApi3Schema{
		Type:        schema.Type,
		Title:       schema.Name,
		Description: schema.Description,
		Properties:  make(map[string]load_and_export_doc_dto.OpenApi3Schema),
	}
	if schema.Ref != "" {
		dataModelName := strings.ReplaceAll(schema.Ref, strconv.FormatInt(libId, 10)+"+", "")
		res.Ref = "#/components/schemas/" + dataModelName
		if dataModelSet != nil {
			dataModelSet.Add(dataModelName)
		}
	}
	required := make([]string, 0)
	// convert properties
	for k, property := range schema.Properties {
		if property.Required {
			required = append(required, k)
		}
		res.Properties[k] = handler.convertApiBaseSchema2OpenApi3Schema(property, libId, dataModelSet)
	}
	// convert items
	if schema.Items != nil {
		baseSchema := *(schema.Items)
		if baseSchema.Required {
			required = append(required, baseSchema.Name)
		}
		api3Schema := handler.convertApiBaseSchema2OpenApi3Schema(baseSchema, libId, dataModelSet)
		res.Items = &api3Schema
	}
	res.Required = required
	return res
}

func (handler OpenApi3ExportDocService) dealWithTags(folderUsages []int64, tagMap map[int64]string, openApi3DTO *load_and_export_doc_dto.OpenApi3DTO) {
	tags := make([]load_and_export_doc_dto.OpenApi3TagDTO, 0)
	for _, folderId := range folderUsages {
		if folderId == 0 {
			continue
		}
		tag := load_and_export_doc_dto.OpenApi3TagDTO{
			Name: tagMap[folderId],
		}
		tags = append(tags, tag)
	}
	openApi3DTO.Tags = tags
}

func (handler OpenApi3ExportDocService) dealWithDataModels(ctx *commoncontext.MantisContext, libId int64, openApi3dto *load_and_export_doc_dto.OpenApi3DTO) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", libId).Eq("is_deleted", common_constants.DeleteNo)
	dataModels := make([]models.ApiDataModel, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &dataModels)
	schemas := make(map[string]load_and_export_doc_dto.OpenApi3Schema)
	for _, dataModel := range dataModels {
		apiBaseSchema := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(dataModel.Content), &apiBaseSchema)
		schemas[dataModel.Name] = handler.convertApiBaseSchema2OpenApi3Schema(apiBaseSchema, libId, nil)
	}
	openApi3dto.Components = &load_and_export_doc_dto.OpenApi3Components{
		Schemas: schemas,
	}
}

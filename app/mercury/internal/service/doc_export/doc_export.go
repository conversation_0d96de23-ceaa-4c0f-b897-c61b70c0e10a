package doc_export

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"golang.org/x/exp/slices"
)

type ExportDocService interface {
	GetExportTypeEnum() string
	AnalyseAndExport(ctx *commoncontext.MantisContext, dto doc_import_export.ExportDTO) (string, string) // 返回服务器tmp文件路径
}

func GetDocExportHandler(docType string) ExportDocService {
	switch docType {
	case constants.SWAGGER:
		return ExportDocService(nil)
	case constants.Postman:
		return ExportDocService(nil)
	case constants.OPENAPI_3:
		return OpenApi3ExportDocService{}
	case constants.Markdown:
		return MDExportDocService{}
	default:
		return ExportDocService(nil)
	}
}

var apiDocService service.ApiDocService

type ApiDocExportService struct{}

func (a ApiDocExportService) ExportInHub(ctx *commoncontext.MantisContext, appId int64, docType string, projectNo string) (string, string) {
	libIds := make([]int64, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&models.ApiLibrary{}).Select("id").Eq("app_id", appId).Eq("is_deleted", commonconstants.DeleteNo), &libIds)
	if len(libIds) == 0 {
		return "", ""
	}
	libId := libIds[0]
	exportDTO := dto.ApiExportDTO{
		LibId:      libId,
		ChooseType: constants.AllShareType,
		DocType:    docType,
	}
	return a.Export(ctx, exportDTO, projectNo)
}

func (a ApiDocExportService) ExportByShare(ctx *commoncontext.MantisContext, shareId int64, docType string, projectNo string) (string, string) {
	share := models.ApiShare{}
	share.Id = shareId
	share.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &share)
	chooseIds := make([][]string, 0)
	jsonx.UnMarshal([]byte(share.ChooseIdStr), &chooseIds)
	chooseLabelIds := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ChooseLabelIdStr), &chooseLabelIds)
	excludeLabelIds := make([]commondto.CodeEnumDTO, 0)
	jsonx.UnMarshal([]byte(share.ExcludeLabelIdStr), &excludeLabelIds)
	exportDTO := dto.ApiExportDTO{
		LibId:           share.LibId,
		ChooseType:      share.ChooseType,
		ChooseIds:       chooseIds,
		ChooseLabelIds:  chooseLabelIds,
		ExcludeLabelIds: excludeLabelIds,
		DocType:         docType,
	}
	return a.Export(ctx, exportDTO, projectNo)
}

func (a ApiDocExportService) Export(ctx *commoncontext.MantisContext, exportDTO dto.ApiExportDTO, projectNo string) (string, string) {
	realExportDTO := a.getExportDTO(ctx, exportDTO, projectNo)
	exportHandler := GetDocExportHandler(exportDTO.DocType)
	return exportHandler.AnalyseAndExport(ctx, realExportDTO)
}

func (a ApiDocExportService) getExportDTO(ctx *commoncontext.MantisContext, exportDTO dto.ApiExportDTO, projectNo string) doc_import_export.ExportDTO {
	// 查出所有的folder，封装为folderMap
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", exportDTO.LibId).Eq("is_deleted", commonconstants.DeleteNo)
	folders := make([]models.ApiFolder, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &folders)
	folderMap := make(map[int64]models.ApiFolder)
	for _, folder := range folders {
		folderMap[folder.Id] = folder
	}
	// 查出library详情
	library := models.ApiLibrary{}
	library.Id = exportDTO.LibId
	gormx.SelectOneByConditionX(ctx, &library)
	var apiDocs []dto.ApiDocSaveDTO
	// 根据条件查询apis
	switch exportDTO.ChooseType {
	case constants.AllShareType:
		apiDocs = a.getAllApis(ctx, exportDTO, projectNo)
	case constants.LabelShareType:
		apiDocs = a.getApisByIncludeTags(ctx, exportDTO, projectNo)
	case constants.ChooseIdType:
		apiDocs = a.getApisByChooseIds(ctx, exportDTO, projectNo)
	}
	// 拼装返回
	return doc_import_export.ExportDTO{
		LibDetail: library,
		ApiDocs:   apiDocs,
		FolderMap: folderMap,
		Env:       exportDTO.ApiEnv,
	}
}

func (a ApiDocExportService) getAllApis(ctx *commoncontext.MantisContext, exportDTO dto.ApiExportDTO, projectNo string) []dto.ApiDocSaveDTO {
	// 查出所有的api
	excludeLabels := exportDTO.ExcludeLabelIds
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", exportDTO.LibId).Eq("is_deleted", commonconstants.DeleteNo)
	apiDocs := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocs)
	apiDocDTOs := a.excludeTags(ctx, apiDocs, excludeLabels, projectNo)
	return apiDocDTOs
}

func (a ApiDocExportService) getApisByIncludeTags(ctx *commoncontext.MantisContext, exportDTO dto.ApiExportDTO, projectNo string) []dto.ApiDocSaveDTO {
	// 查出所有的api
	excludeLabels := exportDTO.ExcludeLabelIds
	includeTags := exportDTO.ChooseLabelIds
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", exportDTO.LibId).Eq("is_deleted", commonconstants.DeleteNo)
	apiDocs := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocs)
	res := make([]models.ApiDocBase, 0)
out:
	for _, doc := range apiDocs {
		if doc.BindLabelId != "" {
			labelIds := common_util.StringToInt64List(doc.BindLabelId, ",")
			for _, tag := range includeTags {
				tagInt, ok := tag.Value.(float64)
				if !ok {
					continue
				}
				if slices.Contains(labelIds, int64(tagInt)) {
					res = append(res, doc)
					continue out
				}
			}
		}
	}
	apiDocDTOs := a.excludeTags(ctx, res, excludeLabels, projectNo)
	return apiDocDTOs
}

func (a ApiDocExportService) getApisByChooseIds(ctx *commoncontext.MantisContext, exportDTO dto.ApiExportDTO, projectNo string) []dto.ApiDocSaveDTO {
	// 拿到所有的id
	apiIds := make([]int64, 0)
	for _, arr := range exportDTO.ChooseIds {
		for _, choose := range arr {
			curDTO := dto.ShareChooseId{}
			jsonx.UnMarshal([]byte(choose), &curDTO)
			if curDTO.NodeType == "apiNode" {
				apiIds = append(apiIds, curDTO.Id)
			}
		}
	}
	// 查询
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBase{}).In("id", apiIds)
	apiDocs := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocs)
	return a.excludeTags(ctx, apiDocs, exportDTO.ExcludeLabelIds, projectNo)
}

func (a ApiDocExportService) excludeTags(ctx *commoncontext.MantisContext, apiDocs []models.ApiDocBase,
	excludeTags []commondto.CodeEnumDTO, projectNo string,
) []dto.ApiDocSaveDTO {
	apiDocDTOs := make([]dto.ApiDocSaveDTO, 0)
	// 存放结果的管道
	channel := make(chan dto.ApiDocSaveDTO, len(apiDocs))
	// 限制器
	tasks := make([]func(), 0, len(apiDocs))
	for i := 0; i < len(apiDocs); i++ {
		i := i
		tasks = append(tasks, func() {
			doc := apiDocs[i]
			if doc.BindLabelId != "" {
				labelIds := common_util.StringToInt64List(doc.BindLabelId, ",")
				for _, tag := range excludeTags {
					tagInt, ok := tag.Value.(float64)
					if !ok {
						continue
					}
					if slices.Contains(labelIds, int64(tagInt)) {
						return
					}
				}
			}
			docSaveDTO := apiDocService.ConvertModel2DTO(ctx, doc)
			channel <- docSaveDTO
		})
	}
	// 启动多协程运行
	err := goroutine.RunTasks(tasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导出错误"))
	}
	for {
		if len(channel) == 0 {
			break
		}
		apiDocDTOs = append(apiDocDTOs, <-channel)
	}
	return apiDocDTOs
}

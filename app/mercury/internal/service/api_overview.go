package service

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

var apiOverviewDao dao.ApiOverviewDao

type ApiOverviewService struct{}

func (a ApiOverviewService) GetApiCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	return apiOverviewDao.GetApiCount(ctx, libId)
}

func (a ApiOverviewService) GetPassRecordCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	return apiOverviewDao.GetPassRecordCount(ctx, libId)
}

func (ApiOverviewService) GetPassRecordPercent(ctx *commoncontext.MantisContext, libId int64) float64 {
	return apiOverviewDao.GetPassRecordPercent(ctx, libId)
}

func (a ApiOverviewService) GetCompletedApiPercent(ctx *commoncontext.MantisContext, libId int64) float64 {
	return apiOverviewDao.GetCompletedApiPercent(ctx, libId)
}

func (a ApiOverviewService) GetShareCount(ctx *commoncontext.MantisContext, libId int64) int64 {
	return apiOverviewDao.GetShareCount(ctx, libId)
}

func (a ApiOverviewService) GetApiCoverPercent(ctx *commoncontext.MantisContext, libId int64) float64 {
	return apiOverviewDao.GetApiCoverage(ctx, libId)
}

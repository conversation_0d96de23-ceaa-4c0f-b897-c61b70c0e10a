package service

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

var apiDataModelDao dao.ApiDataModelDao

type ApiDataModelService struct{}

func (a ApiDataModelService) InsertAndUpdateApiDataModel(ctx *commoncontext.MantisContext, apiDataModelDTO *dto.ApiDataModelDTO, user string) {
	AnalyseSchemaRefPath(ctx, apiDataModelDTO.JsonSchema)
	apiDataModelDTO.Content = string(jsonx.Marshal(apiDataModelDTO.JsonSchema))
	apiDataModelDTO.Modifier = user
	apiDataModelDTO.GmtModified = times.Now()
	// 判断是否重名，新增在树上新增
	if apiDataModelDTO.Id == 0 {
		paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", apiDataModelDTO.LibId).Eq("name", apiDataModelDTO.Name).Eq("is_deleted", commonconstants.DeleteNo)
		res := make([]models.ApiDataModel, 0)
		gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
		if res != nil && len(res) != 0 {
			logger.Logger.Panicf("%+v", xerror.New("存在同名数据模型"))
		}
		// 初始化orderNo
		apiDataModelDTO.OrderNo = apiDataModelDao.GetMaxOrderNo(ctx, apiDataModelDTO.FolderId, apiDataModelDTO.LibId) + 1
		apiDataModelDTO.Creator = user
		apiDataModelDTO.GmtCreated = times.Now()
		apiDataModelDTO.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeModel)
		treeOperation.AddOneNodeToTail(ctx, apiDataModelDTO.FolderId, &(apiDataModelDTO.ApiDataModel), constants.FolderTypeModel)
	} else {
		paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDataModel{}).NotEq("id", apiDataModelDTO.Id).Eq("lib_id", apiDataModelDTO.LibId).Eq("name", apiDataModelDTO.Name).Eq("is_deleted", commonconstants.DeleteNo)
		res := make([]models.ApiDataModel, 0)
		gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
		if res != nil && len(res) != 0 {
			logger.Logger.Panicf("%+v", xerror.New("存在同名数据模型"))
		}
		oldModel := models.ApiDataModel{}
		oldModel.Id = apiDataModelDTO.Id
		gormx.SelectOneByConditionX(ctx, &oldModel)
		apiDataModelDTO.ApiDataModel.PreNode = oldModel.PreNode
		apiDataModelDTO.ApiDataModel.PostNode = oldModel.PostNode
		apiDataModelDao.InsertAndUpdateApiDataModel(ctx, &apiDataModelDTO.ApiDataModel)
		if oldModel.Name != apiDataModelDTO.Name {
			gormx.Transaction(ctx, func() error {
				refStr := `"` + strconv.FormatInt(oldModel.LibId, 10) + "+" + oldModel.Name + `"`
				newRefStr := `"` + strconv.FormatInt(apiDataModelDTO.LibId, 10) + "+" + apiDataModelDTO.Name + `"`
				tasks := make([]func(), 3)
				// 搜索并修改所有关联此data model的data apiDataModelDTO
				tasks = append(tasks, func() {
					apiDataModelDao.RecursiveUpdateRef(ctx, refStr, newRefStr, apiDataModelDTO.LibId, user, times.Now())
				})
				// 搜索并修改所有关联此data model的doc
				tasks = append(tasks, func() {
					apiDocDao.RecursiveUpdateRef(ctx, refStr, newRefStr, apiDataModelDTO.LibId, user, times.Now())
				})
				// 搜索并修改所有关联此data model的response
				tasks = append(tasks, func() {
					apiResponseDao.RecursiveUpdateRef(ctx, refStr, newRefStr, apiDataModelDTO.LibId, user, times.Now())
				})
				err := goroutine.RunTasks(tasks)
				if err != nil {
					logger.Logger.Panicf("%v", xerror.Wrap(err, "error in recursive updating data apiDataModelDTO ref"))
				}
				return nil
			})
		}
	}
}

func (a ApiDataModelService) GetApiModelById(ctx *commoncontext.MantisContext, id int64, outId int64) *dto.ApiDataModelDTO {
	if id == outId {
		logger.Logger.Panicf("%+v", xerror.New("存在外层循环引用，添加失败"))
	}
	apiModelById := apiDataModelDao.GetApiModelById(ctx, id)
	apiModelById.NodeType = "modelNode"
	schema := dto.ApiBaseSchema{}
	jsonx.UnMarshal([]byte(apiModelById.Content), &schema)
	AnalyseSchemaRef(ctx, &schema, outId)
	return &dto.ApiDataModelDTO{
		ApiDataModel: *apiModelById,
		JsonSchema:   &schema,
	}
}

func (a ApiDataModelService) GetApiDataModelByRef(ctx *commoncontext.MantisContext, ref string) *models.ApiDataModel {
	split := strings.Split(ref, "+")
	if len(split) < 2 {
		logger.Logger.Panicf("%+v", xerror.New("error in split ref"))
	}
	libId, err := strconv.ParseInt(split[0], 10, 64)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in parse string to int64"))
	}
	name := split[1]
	for i := 2; i < len(split); i++ {
		name += "+" + split[i]
	}
	model := models.ApiDataModel{
		Name: name,
	}
	model.LibId = libId
	model.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &model)
	return &model
}

func (a ApiDataModelService) GetDataModelByLibId(ctx *commoncontext.MantisContext, libId int64) []dto.ApiDataModelDTO {
	models := apiDataModelDao.SelectNodeByLibId(ctx, libId)
	res := make([]dto.ApiDataModelDTO, 0, len(models))
	for _, model := range models {
		schema := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(model.Content), &schema)
		AnalyseSchemaRef(ctx, &schema, -1)
		model.Content = string(jsonx.Marshal(&schema))
		m := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(model.Content), &m)
		res = append(res, dto.ApiDataModelDTO{
			ApiDataModel: model,
			JsonSchema:   &m,
		})
	}
	return res
}

func (a ApiDataModelService) GetNodeByFolderIds(ctx *commoncontext.MantisContext, folderIds []int64) []dto.ApiDataModelDTO {
	models := apiDataModelDao.SelectNodeByFolderIds(ctx, folderIds)
	res := make([]dto.ApiDataModelDTO, 0, len(models))
	for _, model := range models {
		schema := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(model.Content), &schema)
		AnalyseSchemaRef(ctx, &schema, -1)
		model.Content = string(jsonx.Marshal(&schema))
		m := dto.ApiBaseSchema{}
		jsonx.UnMarshal([]byte(model.Content), &m)
		res = append(res, dto.ApiDataModelDTO{
			ApiDataModel: model,
			JsonSchema:   &m,
		})
	}
	return res
}

func (a ApiDataModelService) RemoveDataModel(ctx *commoncontext.MantisContext, id int64, user string) {
	apiDataModel := models.ApiDataModel{}
	apiDataModel.Id = id
	gormx.SelectOneByConditionX(ctx, &apiDataModel)
	// 将该节点从树上删除
	treeOperation.RemoveOneNodeOnTree(ctx, &apiDataModel, constants.FolderTypeModel)
	apiDataModel.GmtModified = times.Now()
	apiDataModel.Modifier = user
	apiDataModel.IsDeleted = commonconstants.DeleteYes
	gormx.Transaction(ctx, func() error {
		gormx.UpdateOneByConditionX(ctx, &apiDataModel)
		refStr := `"` + strconv.FormatInt(apiDataModel.LibId, 10) + "+" + apiDataModel.Name + `"`
		tasks := make([]func(), 0, 3)
		// 搜索并修改所有关联此data model的data models
		tasks = append(tasks, func() {
			apiDataModelDao.RecursiveDeleteRef(ctx, refStr, apiDataModel.LibId, user, times.Now())
		})
		// 搜索并修改所有关联此data model的doc
		tasks = append(tasks, func() {
			apiDocDao.RecursiveDeleteRef(ctx, refStr, apiDataModel.LibId, user, times.Now())
		})
		// 搜索并修改所有关联此data model的response
		tasks = append(tasks, func() {
			apiResponseDao.RecursiveDeleteRef(ctx, refStr, apiDataModel.LibId, user, times.Now())
		})
		err := goroutine.RunTasks(tasks)
		if err != nil {
			logger.Logger.Errorf("%v", xerror.Wrap(err, "error in recursive deleting data models ref"))
			return err
		}
		return nil
	})
}

func (a ApiDataModelService) ToTreeNodeById(ctx *commoncontext.MantisContext, id int64) dto.ApiFolderDTO {
	apiDataModel := a.GetApiModelById(ctx, id, -1)
	return dto.ApiFolderDTO{
		Id:       apiDataModel.Id,
		Name:     apiDataModel.Name,
		Type:     constants.FolderTypeModel,
		ParentId: apiDataModel.FolderId,
		OrderNo:  apiDataModel.OrderNo,
		Key:      fmt.Sprintf("%s.%d", constants.NodeTypeModel, apiDataModel.Id),
		LibId:    apiDataModel.LibId,
		NodeType: constants.NodeTypeModel,
	}
}

func (a ApiDataModelService) Copy(ctx *commoncontext.MantisContext, id int64, copyNameSuffix string, user commondto.UserInfo) models.ApiDataModel {
	apiDataModel := models.ApiDataModel{}
	apiDataModel.Id = id
	gormx.SelectOneByConditionX(ctx, &apiDataModel)
	apiDataModel.Name = apiDataModel.Name + copyNameSuffix
	apiDataModel.Creator = user.AdAccount
	apiDataModel.Id = 0
	apiDataModel.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeModel)
	// 加进树里，加到原节点的后面
	oriDataModel := models.ApiDataModel{}
	oriDataModel.Id = id
	gormx.SelectOneByConditionX(ctx, &oriDataModel)
	treeOperation.AddOneNodeToTree(ctx, &apiDataModel, &oriDataModel, constants.TreePositionPost, constants.FolderTypeModel)
	return apiDataModel
}

func (a ApiDataModelService) RemoveDataModelMultiByFolder(ctx *commoncontext.MantisContext, folder []int64, user commondto.UserInfo) {
	apiDataModel := models.ApiDataModel{}
	apiDataModel.GmtModified = times.Now()
	apiDataModel.Modifier = user.AdAccount
	apiDataModel.IsDeleted = commonconstants.DeleteYes
	dataModels := make([]models.ApiDataModel, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiDataModel{}).In("folder_id", folder).
			Eq("is_deleted", commonconstants.DeleteNo),
		&dataModels)
	ids := make([]int64, 0, len(dataModels))
	for _, model := range dataModels {
		ids = append(ids, model.Id)
	}
	gormx.Transaction(ctx, func() error {
		gormx.UpdateBatchByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&models.ApiDataModel{}).In("id", ids),
			&apiDataModel)
		tasks := make([]func(), 0, len(dataModels))
		for _, model := range dataModels {
			tasks = append(tasks, func() {
				refStr := `"` + strconv.FormatInt(model.LibId, 10) + "+" + model.Name + `"`
				apiDataModelDao.RecursiveDeleteRef(ctx, refStr, model.LibId, user.AdAccount, times.Now())
				apiDocDao.RecursiveDeleteRef(ctx, refStr, model.LibId, user.AdAccount, times.Now())
				apiResponseDao.RecursiveDeleteRef(ctx, refStr, model.LibId, user.AdAccount, times.Now())
			})
		}
		err := goroutine.RunTasks(tasks)
		if err != nil {
			logger.Logger.Errorf("%v", xerror.Wrap(err, "error in recursive deleting data models ref"))
			return err
		}
		return nil
	})
}

func (a ApiDataModelService) CopyMultiByFolder(ctx *commoncontext.MantisContext, folderId int64, copyFolderId int64, oldNewKeyMap map[string]string,
	user commondto.UserInfo,
) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("folder_id", folderId).Eq("is_deleted", commonconstants.DeleteNo)
	apiDataModels := make([]models.ApiDataModel, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDataModels)
	if apiDataModels != nil && len(apiDataModels) != 0 {
		copyDataModelList := make([]models.ApiDataModel, 0, len(apiDataModels))
		for _, dataModel := range apiDataModels {
			apiDataModel := models.ApiDataModel{
				Name:        dataModel.Name + constants.CopyNameSuffix + "_" + strconv.FormatInt(time.Now().UnixMilli(), 10),
				AliasName:   dataModel.AliasName,
				Description: dataModel.Description,
				Content:     dataModel.Content,
			}
			apiDataModel.LibId = dataModel.LibId
			apiDataModel.FolderId = copyFolderId
			apiDataModel.OrderNo = dataModel.OrderNo
			apiDataModel.Creator = user.AdAccount
			apiDataModel.Modifier = user.AdAccount
			apiDataModel.GmtCreated = times.Now()
			apiDataModel.GmtModified = times.Now()
			apiDataModel.Key = oldNewKeyMap[dataModel.Key]
			// 修正顺序
			apiDataModel.PreNode = oldNewKeyMap[dataModel.GetPreNode()]
			apiDataModel.PostNode = oldNewKeyMap[dataModel.GetPostNode()]
			copyDataModelList = append(copyDataModelList, apiDataModel)
		}
		if len(copyDataModelList) != 0 {
			// 批量写入
			gormx.InsertBatchX(ctx, copyDataModelList)
		}
	}
}

package doc_import

import (
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	set "github.com/duke-git/lancet/v2/datastructure/set"
)

func convertSwaggerSchema2Json(schema doc_import_export.SwaggerSchema, set set.Set[string], definitions map[string]doc_import_export.SwaggerSchema) string {
	jsonStr := ""
	if schema.Example != nil {
		if schema.Type == "string" {
			s := string(jsonx.Marshal(&schema.Example))
			if strings.HasPrefix(s, `"`) && strings.HasSuffix(s, `"`) {
				jsonStr += s
			} else {
				jsonStr += `"` + s + `"`
			}
		} else {
			jsonStr += string(jsonx.Marshal(&schema.Example))
		}
	} else {
		if schema.OriginalRef != "" {
			v, ok := definitions[schema.OriginalRef]
			if !ok || set.Contain(schema.OriginalRef) {
				jsonStr += "{}"
			} else {
				set.Add(schema.OriginalRef)
				jsonStr += convertSwaggerSchema2Json(v, set, definitions)
				set.Delete(schema.OriginalRef)
			}
		} else if schema.Type == "object" {
			jsonStr += "{"
			if schema.Properties != nil && len(schema.Properties) != 0 {
				for k, v := range schema.Properties {
					jsonStr += `"` + k + `"` + ": " + convertSwaggerSchema2Json(v, set, definitions) + ","
				}
				jsonStr = jsonStr[:len(jsonStr)-1]
			}
			jsonStr += "}"
		} else if schema.Type == "array" {
			jsonStr += "["
			if schema.Items != nil {
				jsonStr += convertSwaggerSchema2Json(*(schema.Items), set, definitions)
			}
			jsonStr += "]"
		} else {
			jsonStr += getDefaultValueByType(schema.Type)
		}
	}
	return jsonStr
}

func convertOpenApi3Schema2Json(schema doc_import_export.OpenApi3Schema, set set.Set[string], definitions map[string]doc_import_export.OpenApi3Schema) string {
	jsonStr := ""
	if schema.Example != nil {
		if schema.Type == "string" {
			s := string(jsonx.Marshal(&schema.Example))
			if strings.HasPrefix(s, `"`) && strings.HasSuffix(s, `"`) {
				jsonStr += s
			} else {
				jsonStr += `"` + s + `"`
			}
		} else {
			jsonStr += string(jsonx.Marshal(&schema.Example))
		}
	} else {
		if schema.Ref != "" {
			originRef := strings.ReplaceAll(schema.Ref, "#/components/schemas/", "")
			v, ok := definitions[originRef]
			if !ok || set.Contain(originRef) {
				jsonStr += "{}"
			} else {
				set.Add(originRef)
				jsonStr += convertOpenApi3Schema2Json(v, set, definitions)
				set.Delete(originRef)
			}
		} else if schema.Type == "object" {
			jsonStr += "{"
			if schema.Properties != nil && len(schema.Properties) != 0 {
				for k, v := range schema.Properties {
					jsonStr += `"` + k + `"` + ": " + convertOpenApi3Schema2Json(v, set, definitions) + ","
				}
				jsonStr = jsonStr[:len(jsonStr)-1]
			}
			jsonStr += "}"
		} else if schema.Type == "array" {
			jsonStr += "["
			if schema.Items != nil {
				jsonStr += convertOpenApi3Schema2Json(*(schema.Items), set, definitions)
			}
			jsonStr += "]"
		} else {
			jsonStr += getDefaultValueByType(schema.Type)
		}
	}
	return jsonStr
}

func getDefaultValueByType(typeStr string) string {
	switch typeStr {
	case "boolean":
		return "false"
	case "string":
		return `"string"`
	case "integer", "number":
		return "0"
	default:
		return "null"
	}
}

const rootId = 0

func fixPath(ctx *commoncontext.MantisContext, pid int64) string {
	path := ""
	if rootId != pid {
		path = apiFolderDao.SelectPathById(ctx, pid)
	}
	return path + strconv.FormatInt(pid, 10) + constants.RequestSplitChar
}

var limit *goroutine.Limit

func Init() {
	l, err := goroutine.NewLimit(10)
	if err != nil {
		logger.Logger.Panicf("error in creating limit for mercury doc import, %v", err)
	}
	limit = l
}

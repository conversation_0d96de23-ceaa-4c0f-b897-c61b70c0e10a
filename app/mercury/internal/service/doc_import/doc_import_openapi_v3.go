package doc_import

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	loadandexportdocdto2 "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/xerror"
	"golang.org/x/exp/slices"
)

type OpenApi3LoadDocService struct{}

func (handler OpenApi3LoadDocService) AnalyseAndLoadDoc(ctx *commoncontext.MantisContext, loadDocDto loadandexportdocdto2.LoadDocDTO) {
	doc := loadDocDto.(*loadandexportdocdto2.OpenApi3DTO)
	goroutine.Run(func() {
		handler.analyseAndLoadOpenApi3Components(ctx, doc.Components, doc.UserAccount,
			doc.DataModelFolderId, doc.LibId, doc.Strategy)
	})
	handler.analyseAndLoadOpenApi3Apis(ctx, *doc)
}

func (handler OpenApi3LoadDocService) analyseAndLoadOpenApi3Components(ctx *commoncontext.MantisContext, components *loadandexportdocdto2.OpenApi3Components,
	user string, folderId int64, libId int64, strategy int32,
) {
	handler.analyseAndLoadOpenApi3ComponentSchema(ctx, components.Schemas, user, folderId, libId, strategy)
	handler.analyseAndLoadOpenApi3ComponentSchema(ctx, components.SecuritySchemas, user, folderId, libId, strategy)
}

func (handler OpenApi3LoadDocService) analyseAndLoadOpenApi3ComponentSchema(ctx *commoncontext.MantisContext, components map[string]loadandexportdocdto2.OpenApi3Schema,
	user string, folderId int64, libId int64, strategy int32,
) {
	dataModels := make([]models.ApiDataModel, 0, len(components))
	for k, v := range components {
		schema := handler.analyseOneOpenApi3DataModelSchema(v, libId)
		dataModel := models.ApiDataModel{
			Name:        k,
			AliasName:   v.Title,
			Description: v.Description,
			Content:     string(jsonx.Marshal(&schema)),
			FolderId:    folderId,
		}
		dataModel.LibId = libId
		dataModel.Creator = user
		dataModel.Modifier = user
		dataModel.GmtCreated = times.Now()
		dataModel.GmtModified = times.Now()
		dataModel.IsDeleted = commonconstants.DeleteNo
		oldModel := models.ApiDataModel{}
		// 查询数据库中是否有同库同名的data models
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", libId).Eq("name", k).
				Eq("is_deleted", commonconstants.DeleteNo),
			&oldModel)
		if oldModel.Id != 0 {
			// 如果有
			if strategy == constants.ImportKeepStrategy {
				// 如果策略是不覆盖
				continue
			} else {
				// 如果策略是覆盖
				dataModel.Id = oldModel.Id
				dataModel.GmtModified = times.Now()
				dataModel.Modifier = user
				dataModel.FolderId = oldModel.FolderId
				gormx.UpdateOneByConditionX(ctx, &dataModel)
			}
		} else {
			dataModel.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeModel)
			dataModels = append(dataModels, dataModel)
		}
	}
	// 新增需要新增的dataModel
	if dataModels != nil && len(dataModels) > 0 {
		treeOperation.AddBatchDataModel2Tail(ctx, libId, folderId, dataModels, constants.FolderTypeModel)
	}
}

func (handler OpenApi3LoadDocService) analyseOneOpenApi3DataModelSchema(v loadandexportdocdto2.OpenApi3Schema, libId int64) dto.ApiBaseSchema {
	schema := dto.ApiBaseSchema{
		Type:        v.Type,
		Name:        "根节点",
		Description: v.Description,
	}
	// 如果是Object， 则分析property
	if v.Type == constants.SwaggerObject {
		props := make(map[string]dto.ApiBaseSchema)
		properties := v.Properties
		for pK, pV := range properties {
			baseSchema := dto.ApiBaseSchema{
				Name:        pK,
				Type:        pV.Type,
				Description: pV.Description,
				Example:     v.Example,
			}
			if pV.Ref != "" {
				baseSchema.Type = data_type.REF
				baseSchema.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(pV.Ref, "#/components/schemas/", "")
			}
			if pV.Items != nil {
				baseSchema.Items = &dto.ApiBaseSchema{
					Type: pV.Items.Type,
				}
				if pV.Items.Ref != "" {
					baseSchema.Items.Type = data_type.REF
					baseSchema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(pV.Items.Ref, "#/components/schemas/", "")
				}
			}
			props[pK] = baseSchema
		}
		schema.Properties = props
	}
	// 如果是array，则分析items
	if v.Type == constants.SwaggerArray {
		schema.Items = &dto.ApiBaseSchema{
			Type: v.Items.Type,
		}
		if v.Items.Ref != "" {
			schema.Items.Type = data_type.REF
			schema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(v.Items.Ref, "#/components/schemas/", "")
		}
	}
	return schema
}

func (handler OpenApi3LoadDocService) analyseOneOpenApi3Schema(v loadandexportdocdto2.OpenApi3Schema, libId int64) dto.ApiBaseSchema {
	res := dto.ApiBaseSchema{
		Type:       v.Type,
		Name:       "根节点",
		Properties: make(map[string]dto.ApiBaseSchema),
		Required:   false,
		Example:    v.Example,
	}
	// 如果是array，则分析items
	if v.Type == constants.SwaggerArray {
		res.Type = constants.SwaggerArray
		res.Items = &dto.ApiBaseSchema{
			Type: v.Items.Type,
		}
		if v.Items.Ref != "" {
			res.Items.Type = data_type.REF
			res.Items.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(v.Items.Ref, "#/components/schemas/", "")
		}
	} else {
		// 如果是Object， 则分析property
		if v.Type == constants.SwaggerObject {
			props := make(map[string]dto.ApiBaseSchema)
			properties := v.Properties
			for pK, pV := range properties {
				baseSchema := dto.ApiBaseSchema{
					Name:        pK,
					Type:        pV.Type,
					Description: pV.Description,
					Required:    slices.Contains(v.Required, pK),
					Example:     pV.Example,
				}
				if pV.Ref != "" {
					baseSchema.Type = data_type.REF
					baseSchema.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(pV.Ref, "#/components/schemas/", "")
				}
				if pV.Items != nil {
					baseSchema.Items = &dto.ApiBaseSchema{
						Type: pV.Items.Type,
					}
					if pV.Items.Ref != "" {
						baseSchema.Items.Type = data_type.REF
						baseSchema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(pV.Items.Ref, "#/components/schemas/", "")
					}
				}
				props[pK] = baseSchema
			}
			res.Properties = props
		}
	}
	if v.Ref != "" {
		res.Type = data_type.REF
		refSplit := strings.Split(v.Ref, "/")
		if len(refSplit) != 0 {
			res.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
		}
	}
	return res
}

func (handler OpenApi3LoadDocService) analyseAndLoadOpenApi3Apis(ctx *commoncontext.MantisContext, openApi3DTO loadandexportdocdto2.OpenApi3DTO) {
	wg := sync.WaitGroup{}
	folderMap := handler.analyseOpenApi3Tags(ctx, int(openApi3DTO.Strategy), openApi3DTO.Tags,
		openApi3DTO.LibId, openApi3DTO.ApiDocFolderId, openApi3DTO.UserAccount)
	paths := openApi3DTO.Paths
	// 记录folderId和doc的map
	folderIdDocsMap := make(map[int64][]models.ApiDocBase)
	docChan := make(chan models.ApiDocBase, 50)
	// 记录key 和 OpenApi3PathDTO的map，用于处理response
	apiKeyPathDtoMap := make(map[string]loadandexportdocdto2.OpenApi3PathDTO)
	respChan := make(chan struct {
		Key     string
		PathDto loadandexportdocdto2.OpenApi3PathDTO
	}, 50)
	// 任务队列
	apiTasks := make([]func(), 0)
	wg.Add(2)
	// 开一个协程去读取docChan的内容，读不到就阻塞，直到读到一个id是-1的停止标记
	go func() {
		defer wg.Done()
		for {
			api := <-docChan
			if api.Id == -1 {
				return
			}
			_, ok := folderIdDocsMap[api.FolderId]
			if !ok {
				folderIdDocsMap[api.FolderId] = make([]models.ApiDocBase, 0)
			}
			folderIdDocsMap[api.FolderId] = append(folderIdDocsMap[api.FolderId], api)
		}
	}()
	// 再开一个协程去读取respChan的内容，读不到就阻塞，直到读到一个key是空字符串的停止
	go func() {
		defer wg.Done()
		for {
			structResp := <-respChan
			if structResp.Key == "" {
				return
			}
			apiKeyPathDtoMap[structResp.Key] = structResp.PathDto
		}
	}()
	// 给每一个任务开一个协程
	for k, v := range paths {
		for pK, pV := range v {
			k := k
			pK := pK
			pV := pV
			apiTasks = append(apiTasks, func() {
				folderId := openApi3DTO.ApiDocFolderId
				if len(pV.Tags) != 0 {
					if folder, ok := folderMap[pV.Tags[0]]; ok {
						folderId = folder.Id
					}
				}
				api := models.ApiDocBase{
					Type:          "http",
					Name:          pV.Summary,
					Method:        strings.ToUpper(pK),
					Path:          k,
					Status:        defaultApiDocStatus,
					FolderId:      folderId, // 只会加入第一个目录
					ResponsibleId: openApi3DTO.UserAccount,
				}
				api.LibId = openApi3DTO.LibId
				// 处理parameter
				handler.dealWithParameters(pV, openApi3DTO, &api)
				// 写入api_doc
				api.Creator = openApi3DTO.UserAccount
				api.Modifier = openApi3DTO.UserAccount
				api.GmtCreated = times.Now()
				api.GmtModified = times.Now()
				api.IsDeleted = commonconstants.DeleteNo
				// 判断是否重名
				oldApis := make([]models.ApiDocBase, 0)
				gormx.SelectByParamBuilderX(ctx,
					gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", api.LibId).
						Eq("path", api.Path).Eq("method", api.Method).Eq("name", api.Name).
						Eq("is_deleted", commonconstants.DeleteNo),
					&oldApis)
				if len(oldApis) != 0 {
					// 存在重名
					if openApi3DTO.Strategy == constants.ImportUpdateStrategy {
						for _, oldApi := range oldApis {
							// 覆盖策略，更新旧的
							api.Id = oldApi.Id
							api.GmtCreated = oldApi.GmtCreated
							api.Creator = oldApi.Creator
							// 位置不改变
							api.FolderId = oldApi.FolderId
							api.Key = oldApi.Key
							api.PreNode = oldApi.PreNode
							api.PostNode = oldApi.PostNode
							gormx.UpdateOneByConditionX(ctx, &api)
							// 更新response，先删除后写入
							responses := make([]models.ApiResponse, 0)
							gormx.SelectByParamBuilderX(ctx,
								gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", oldApi.Id).
									Eq("is_deleted", commonconstants.DeleteNo),
								&responses)
							responseIds := make([]int64, 0)
							for _, resp := range responses {
								responseIds = append(responseIds, resp.Id)
							}
							// 删除response
							gormx.UpdateBatchByParamBuilderAndMapX(ctx,
								gormx.NewParamBuilder().Model(&models.ApiResponse{}).In("id", responseIds),
								map[string]any{"is_deleted": commonconstants.DeleteYes})
							// 删除responseCase
							gormx.UpdateBatchByParamBuilderAndMapX(ctx,
								gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("resp_id", responseIds),
								map[string]any{"is_deleted": commonconstants.DeleteYes})
							// 处理response
							handler.dealWithResponses(ctx, pV, oldApi.Id, openApi3DTO)
						}
					} else {
						// 不覆盖策略，跳过剩下的处理
						return
					}
				} else {
					// 不重名，正常写入
					api.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi)
					docChan <- api
					respChan <- struct {
						Key     string
						PathDto loadandexportdocdto2.OpenApi3PathDTO
					}{Key: api.Key, PathDto: pV}
				}
			})
		}
	}
	// 运行协程
	err := limit.RunTasks(apiTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
	// 去给两个channel终止符
	api := models.ApiDocBase{}
	api.Id = -1
	docChan <- api
	respChan <- struct {
		Key     string
		PathDto loadandexportdocdto2.OpenApi3PathDTO
	}{Key: "", PathDto: loadandexportdocdto2.OpenApi3PathDTO{}}
	logger.Logger.Info("终止符已经发送")
	wg.Wait()
	// 记录apikey和id的map的chan
	apiKeyIdChan := make(chan map[string]int64, 50)
	docTasks := make([]func(), 0, len(folderIdDocsMap))
	for k, v := range folderIdDocsMap {
		if v != nil && len(v) != 0 {
			k := k
			v := v
			apiKeyIdChan := apiKeyIdChan
			docTasks = append(docTasks, func() {
				currentMap := make(map[string]int64)
				treeOperation.AddBatchApiDoc2Tail(ctx, openApi3DTO.LibId, k, v, currentMap)
				apiKeyIdChan <- currentMap
			})
		}
	}
	// 记录apikey和id的map
	wg.Add(1)
	apiKeyIdMap := make(map[string]int64)
	go func() {
		defer wg.Done()
		for {
			m := <-apiKeyIdChan
			if m == nil {
				return
			}
			for k, v := range m {
				apiKeyIdMap[k] = v
			}
		}
	}()
	err = limit.RunTasks(docTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
	// 发送终止符
	apiKeyIdChan <- nil
	wg.Wait()
	// 处理response
	respTasks := make([]func(), 0, len(apiKeyPathDtoMap))
	for k, v := range apiKeyPathDtoMap {
		v := v
		apiId, ok := apiKeyIdMap[k]
		if !ok || apiId == 0 {
			continue
		}
		openapi3DTO := openApi3DTO
		respTasks = append(respTasks, func() {
			handler.dealWithResponses(ctx, v, apiId, openapi3DTO)
		})
	}
	err = limit.RunTasks(respTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
}

func (handler OpenApi3LoadDocService) dealWithParameters(pV loadandexportdocdto2.OpenApi3PathDTO, openApi3DTO loadandexportdocdto2.OpenApi3DTO, api *models.ApiDocBase) {
	requestDTO := dto.ApiDocRequestDTO{
		Auth: &dto.Auth{},
		Parameters: &dto.Parameters{
			Query:  make([]dto.BaseParameter, 0),
			Path:   make([]dto.BaseParameter, 0),
			Header: make([]dto.BaseParameter, 0),
			Cookie: make([]dto.BaseParameter, 0),
		},
		RequestBody: &dto.RequestBody{
			Parameters: make([]dto.BaseParameter, 0),
			JsonSchema: &dto.ApiBaseSchema{},
			Example:    "",
		},
	}
	for _, openApi3Parameters := range pV.Parameters {
		switch openApi3Parameters.In {
		case constants.Openapi3ParamInQuery:
			requestDTO.Parameters.Query = append(requestDTO.Parameters.Query, handler.convertOpenApi3Parameter2BaseParameter(openApi3Parameters))
			break
		case constants.Openapi3ParamInPath:
			requestDTO.Parameters.Path = append(requestDTO.Parameters.Path, handler.convertOpenApi3Parameter2BaseParameter(openApi3Parameters))
			break
		case constants.Openapi3ParamInHeader:
			requestDTO.Parameters.Header = append(requestDTO.Parameters.Header, handler.convertOpenApi3Parameter2BaseParameter(openApi3Parameters))
			break
		}
	}
	if pV.RequestBody != nil && len(pV.RequestBody.Content) != 0 {
		for k, v := range pV.RequestBody.Content {
			if v.Schema != nil {
				if k == "*/*" {
					requestDTO.RequestBody.Type = "application/json"
				} else {
					requestDTO.RequestBody.Type = k
				}
				schema := handler.convertOpenApi3Schema2ApiBaseSchema(*v.Schema, openApi3DTO.LibId)
				requestDTO.RequestBody.JsonSchema = &schema
				api.Request = string(jsonx.Marshal(&requestDTO))
			}
		}
	}
	api.Request = string(jsonx.Marshal(&requestDTO))
}

func (handler OpenApi3LoadDocService) dealWithResponses(ctx *commoncontext.MantisContext, pV loadandexportdocdto2.OpenApi3PathDTO, apiId int64,
	openApi3DTO loadandexportdocdto2.OpenApi3DTO,
) {
	responses := pV.Responses
	addResponses := make([]models.ApiResponse, 0, len(responses))
	orderNo := 0
	for k, v := range responses {
		code, err := strconv.ParseInt(k, 10, 32)
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in convert resp code"))
		}
		response := models.ApiResponse{
			Name:     v.Description,
			RespCode: int32(code),
			ApiId:    apiId,
			LibId:    openApi3DTO.LibId,
			OrderNo:  int64(orderNo),
		}
		for k, v := range v.Content {
			if k == "*/*" {
				response.ContentFormat = "json"
			} else {
				response.ContentFormat = strings.ReplaceAll(k, "application/", "")
			}
			baseSchema := dto.ApiBaseSchema{}
			if v.Schema != nil {
				baseSchema = handler.analyseOneOpenApi3Schema(*(v.Schema), openApi3DTO.LibId)
				response.ContentRaw = *(v.Schema)
			}
			response.Content = string(jsonx.Marshal(&baseSchema))
			response.Creator = openApi3DTO.UserAccount
			response.Modifier = openApi3DTO.UserAccount
			response.GmtCreated = times.Now()
			response.GmtModified = times.Now()
			response.IsDeleted = commonconstants.DeleteNo
		}
		addResponses = append(addResponses, response)
		orderNo += 1
	}
	if len(addResponses) != 0 {
		gormx.InsertBatchX(ctx, &addResponses)
		addResponseList := make([]models.ApiResponseCase, 0)
		for _, response := range addResponses {
			responseCase := models.ApiResponseCase{
				Name:    "generated",
				RespId:  strconv.FormatInt(response.Id, 10),
				OrderNo: 0,
				Addons: Addons{
					Creator:     openApi3DTO.UserAccount,
					Modifier:    openApi3DTO.UserAccount,
					GmtCreated:  times.Now(),
					GmtModified: times.Now(),
					IsDeleted:   commonconstants.DeleteNo,
				},
			}
			if response.ContentRaw != nil {
				if schema, ok := response.ContentRaw.(loadandexportdocdto2.OpenApi3Schema); ok {
					responseCase.Content = convertOpenApi3Schema2Json(schema, set.New[string](), openApi3DTO.Components.Schemas)
				}
			}
			addResponseList = append(addResponseList, responseCase)
		}
		if len(addResponseList) != 0 {
			gormx.InsertBatchX(ctx, &addResponseList)
		}
	}
}

func (handler OpenApi3LoadDocService) analyseOpenApi3Tags(ctx *commoncontext.MantisContext, strategy int, tags []loadandexportdocdto2.OpenApi3TagDTO,
	libId int64, parentId int64, userAdAccount string,
) map[string]*models.ApiFolder {
	m := make(map[string]*models.ApiFolder)
	for _, tag := range tags {
		apiFolder := models.ApiFolder{
			Name:     tag.Name,
			ParentId: parentId,
			Type:     constants.FolderTypeApi,
		}
		apiFolder.LibId = libId
		apiFolder.IsDeleted = commonconstants.DeleteNo
		apiFolder.Creator = userAdAccount
		apiFolder.Modifier = userAdAccount
		apiFolder.GmtCreated = times.Now()
		apiFolder.GmtModified = times.Now()
		apiFolder.Path = fixPath(ctx, apiFolder.ParentId)
		// 判断重名
		repeat := models.ApiFolder{}
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", libId).
				Eq("parent_id", parentId).Eq("name", apiFolder.Name).Eq("is_deleted", commonconstants.DeleteNo),
			&repeat)
		if repeat.Id != 0 {
			// 存在重名
			if strategy == constants.ImportUpdateStrategy {
				// 覆盖策略，更新旧的
				apiFolder.Id = repeat.Id
				apiFolder.GmtCreated = repeat.GmtCreated
				apiFolder.Creator = repeat.Creator
				// 位置不改变
				apiFolder.ParentId = repeat.ParentId
				gormx.UpdateOneByConditionX(ctx, &apiFolder)
			} else {
				// 不覆盖策略，跳过剩下的处理
				apiFolder = repeat
			}
		} else {
			// 加入树
			apiFolder.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeFolder)
			treeOperation.AddOneNodeToTail(ctx, apiFolder.ParentId, &apiFolder, constants.FolderTypeApi)
		}
		m[tag.Name] = &apiFolder
	}
	return m
}

func (handler OpenApi3LoadDocService) convertOpenApi3Parameter2BaseParameter(schema loadandexportdocdto2.OpenApi3Parameters) dto.BaseParameter {
	return dto.BaseParameter{
		Name:        schema.Name,
		Required:    schema.Required,
		Description: schema.Description,
		Type:        schema.Schema.Type,
		Example:     nil,
		Enable:      true,
	}
}

func (handler OpenApi3LoadDocService) convertOpenApi3Schema2ApiBaseSchema(openApi3Schema loadandexportdocdto2.OpenApi3Schema, libId int64) dto.ApiBaseSchema {
	res := dto.ApiBaseSchema{
		Name:        "根节点",
		Type:        openApi3Schema.Type,
		Properties:  make(map[string]dto.ApiBaseSchema),
		Description: openApi3Schema.Description,
		Required:    slices.Contains(openApi3Schema.Required, openApi3Schema.Title),
	}
	// 如果是引用
	if openApi3Schema.Ref != "" {
		res.Type = data_type.REF
		res.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(openApi3Schema.Ref, "#/components/schemas/", "")
	} else if openApi3Schema.Items != nil {
		// 如果是数组
		res.Type = constants.SwaggerArray
		res.Items = &dto.ApiBaseSchema{
			Name:        openApi3Schema.Items.Title,
			AliasName:   "",
			Description: openApi3Schema.Items.Description,
			Type:        openApi3Schema.Items.Type,
		}
		if openApi3Schema.Items.Ref != "" {
			res.Items.Type = data_type.REF
			res.Items.Ref = strconv.FormatInt(libId, 10) + "+" + strings.ReplaceAll(openApi3Schema.Items.Ref, "#/components/schemas/", "")
		}
	} else if openApi3Schema.Properties != nil {
		for k, v := range openApi3Schema.Properties {
			res.Properties[k] = dto.ApiBaseSchema{
				Name:        k,
				Description: v.Description,
				Type:        v.Type,
				Required:    slices.Contains(v.Required, k),
			}
		}
	}
	return res
}

func (handler OpenApi3LoadDocService) GetLoadTypeEnum() string {
	return constants.OPENAPI_3
}

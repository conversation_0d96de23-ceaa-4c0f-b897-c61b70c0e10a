package doc_import

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	loadandexportdocdto2 "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"github.com/duke-git/lancet/v2/xerror"
)

type PostmanLoadDocService struct{}

func (handler PostmanLoadDocService) AnalyseAndLoadDoc(ctx *commoncontext.MantisContext, loadDocDto loadandexportdocdto2.LoadDocDTO) {
	doc := loadDocDto.(*loadandexportdocdto2.PostmanDTO)
	handler.analyseItems(ctx, doc, doc.Item)
}

func (handler PostmanLoadDocService) analyseItems(ctx *commoncontext.MantisContext, doc *loadandexportdocdto2.PostmanDTO, items []loadandexportdocdto2.PostmanItemDTO) {
	id := doc.ApiDocFolderId
	folder := models.ApiFolder{}
	folder.Id = id
	folder.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &folder)
	for _, item := range items {
		if item.Item != nil {
			handler.dealWithFolder(ctx, doc, item, folder.Path+strconv.FormatInt(doc.ApiDocFolderId, 10))
		} else {
			handler.dealWithApiDoc(ctx, doc, item, doc.ApiDocFolderId)
		}
	}
}

func (handler PostmanLoadDocService) dealWithFolder(ctx *commoncontext.MantisContext, doc *loadandexportdocdto2.PostmanDTO, itemFolder loadandexportdocdto2.PostmanItemDTO,
	parentPath string,
) {
	path := strings.Split(parentPath, ",")
	parentId, err := strconv.ParseInt(path[len(path)-1], 10, 64)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get path"))
	}
	folder := models.ApiFolder{
		Name:     itemFolder.Name,
		Type:     constants.FolderTypeApi,
		Path:     parentPath + ",",
		ParentId: parentId,
		ApiTreeInfo: models.ApiTreeInfo{
			Addons: Addons{
				Creator:     doc.UserAccount,
				Modifier:    doc.UserAccount,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
			LibId: doc.LibId,
		},
	}
	// 判断重名
	repeat := models.ApiFolder{}
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", folder.LibId).
			Eq("parent_id", parentId).Eq("name", folder.Name).Eq("is_deleted", commonconstants.DeleteNo),
		&repeat)
	if repeat.Id != 0 {
		// 存在重名
		if doc.Strategy == constants.ImportUpdateStrategy {
			// 覆盖策略，更新旧的
			folder.Id = repeat.Id
			folder.GmtCreated = repeat.GmtCreated
			folder.Creator = repeat.Creator
			// 位置不改变
			folder.ParentId = repeat.ParentId
			gormx.UpdateOneByConditionX(ctx, &folder)
		} else {
			// 不覆盖策略，跳过剩下的处理
			folder = repeat
		}
	} else {
		// 写入树
		folder.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeFolder)
		treeOperation.AddOneNodeToTail(ctx, folder.ParentId, &folder, constants.FolderTypeApi)
	}
	if itemFolder.Item != nil && len(itemFolder.Item) != 0 {
		for _, item := range itemFolder.Item {
			if item.Item != nil {
				handler.dealWithFolder(ctx, doc, item, parentPath+","+strconv.FormatInt(folder.Id, 10))
			} else {
				handler.dealWithApiDoc(ctx, doc, item, folder.Id)
			}
		}
	}
}

func (handler PostmanLoadDocService) dealWithApiDoc(ctx *commoncontext.MantisContext, doc *loadandexportdocdto2.PostmanDTO, item loadandexportdocdto2.PostmanItemDTO,
	parentId int64,
) {
	url := ""
	for _, path := range item.Request.Url.Path {
		url += "/" + path
	}
	apiDocBase := models.ApiDocBase{
		Type:     "http",
		Name:     item.Name,
		Method:   strings.ToUpper(item.Request.Method),
		Path:     url,
		Status:   defaultApiDocStatus,
		FolderId: parentId,
		ApiTreeInfo: models.ApiTreeInfo{
			Addons: Addons{
				Creator:     doc.UserAccount,
				Modifier:    doc.UserAccount,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
			LibId: doc.LibId,
		},
	}
	handler.dealWithRequest(item.Request, &apiDocBase)
	// 判断是否重名
	oldApis := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx,
		gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", apiDocBase.LibId).
			Eq("path", apiDocBase.Path).Eq("method", apiDocBase.Method).Eq("name", apiDocBase.Name).
			Eq("is_deleted", commonconstants.DeleteNo), &oldApis)
	if len(oldApis) != 0 {
		// 存在重名
		if doc.Strategy == constants.ImportUpdateStrategy {
			for _, oldApi := range oldApis {
				// 覆盖策略，更新旧的
				apiDocBase.Id = oldApi.Id
				apiDocBase.GmtCreated = oldApi.GmtCreated
				apiDocBase.Creator = oldApi.Creator
				// 位置不改变
				apiDocBase.FolderId = oldApi.FolderId
				apiDocBase.Key = oldApi.Key
				apiDocBase.PreNode = oldApi.PreNode
				apiDocBase.PostNode = oldApi.PostNode
				gormx.UpdateOneByConditionX(ctx, &apiDocBase)
			}
		} else {
			// 不覆盖策略，跳过剩下的处理
			return
		}
	} else {
		// 不重名，正常写入
		apiDocBase.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants.NodeTypeApi)
		treeOperation.AddOneNodeToTail(ctx, apiDocBase.FolderId, &apiDocBase, constants.FolderTypeApi)
	}
	handler.dealWithResponse(ctx, item.Response, &apiDocBase)
}

func (handler PostmanLoadDocService) dealWithRequest(request loadandexportdocdto2.PostmanRequest, api *models.ApiDocBase) {
	requestDTO := dto.ApiDocRequestDTO{
		Auth: &dto.Auth{},
		Parameters: &dto.Parameters{
			Query:  make([]dto.BaseParameter, 0),
			Path:   make([]dto.BaseParameter, 0),
			Header: make([]dto.BaseParameter, 0),
			Cookie: make([]dto.BaseParameter, 0),
		},
		RequestBody: &dto.RequestBody{
			Parameters: make([]dto.BaseParameter, 0),
		},
	}
	// 处理auth
	if request.Auth != nil {
		authType := fmt.Sprintf("%v", request.Auth["type"])
		requestDTO.Auth = &dto.Auth{}
		switch authType {
		case "bearer":
			requestDTO.Auth.Type = "bearerToken"
			requestDTO.Auth.AuthObj = make(map[string]any)
			requestDTO.Auth.AuthObj["token"] = request.Auth["bearer"].([]any)[0].(map[string]any)["value"]
		case "basic":
			requestDTO.Auth.Type = "basic"
			requestDTO.Auth.AuthObj = make(map[string]any)
			for _, val := range request.Auth["basic"].([]any) {
				valMap := val.(map[string]any)
				key := valMap["key"].(string)
				if key == "username" {
					key = "userName"
				}
				requestDTO.Auth.AuthObj[key] = valMap["value"]
			}
		}
	}
	if request.Header != nil && len(request.Header) != 0 {
		for _, header := range request.Header {
			baseParameter := dto.BaseParameter{
				Name:        header.Key,
				Description: header.Description,
				Example:     header.Value,
				Type:        "string",
				Enable:      !header.Disabled,
			}
			requestDTO.Parameters.Header = append(requestDTO.Parameters.Header, baseParameter)
		}
	}
	if request.Url.Query != nil && len(request.Url.Query) != 0 {
		for _, query := range request.Url.Query {
			requestDTO.Parameters.Query = append(requestDTO.Parameters.Query, dto.BaseParameter{
				Name: query.Key,
				Type: "string",
			})
		}
	}
	if request.Body.Mode == constants.PostmanRequestBodyModeFormData {
		requestDTO.RequestBody.Type = commonconstants.FormContentType
		for _, formData := range request.Body.FormData {
			baseParameter := dto.BaseParameter{
				Name:        formData.Key,
				Description: formData.Description,
				Example:     formData.Value,
				Enable:      !formData.Disabled,
				Type:        formData.Type,
			}
			if baseParameter.Type == "text" {
				baseParameter.Type = "string"
			}
			requestDTO.RequestBody.Parameters = append(requestDTO.RequestBody.Parameters, baseParameter)
		}
	} else if request.Body.Mode == constants.PostmanRequestBodyModeUrlencoded {
		requestDTO.RequestBody.Type = commonconstants.UrlencodedContentType
		for _, formData := range request.Body.Urlencoded {
			baseParameter := dto.BaseParameter{
				Name:        formData.Key,
				Description: formData.Description,
				Example:     formData.Value,
				Enable:      !formData.Disabled,
				Type:        formData.Type,
			}
			if baseParameter.Type == "text" {
				baseParameter.Type = "string"
			}
			requestDTO.RequestBody.Parameters = append(requestDTO.RequestBody.Parameters, baseParameter)
		}
	} else if request.Body.Mode == constants.PostmanRequestBodyModeRaw {
		switch request.Body.Options.Raw.Language {
		case constants.PostmanRequestBodyTypeJson:
			requestDTO.RequestBody.Type = commonconstants.JsonContentType
			requestDTO.RequestBody.JsonSchema = handler.dealWithJson(request.Body.Raw)
		case constants.PostmanRequestBodyTypeXml:
			requestDTO.RequestBody.Type = commonconstants.XmlContentType
		}
	}
	api.Request = string(jsonx.Marshal(requestDTO))
}

func (handler PostmanLoadDocService) dealWithResponse(ctx *commoncontext.MantisContext, responses []loadandexportdocdto2.PostmanResponse, api *models.ApiDocBase) {
	addResps := make([]models.ApiResponse, 0)
	for _, response := range responses {
		apiResponse := models.ApiResponse{
			Name:          response.Status,
			RespCode:      response.Code,
			ContentFormat: response.Type,
			ApiId:         api.Id,
			LibId:         api.LibId,
			Addons: Addons{
				Creator:     api.Creator,
				Modifier:    api.Modifier,
				GmtCreated:  times.Now(),
				GmtModified: times.Now(),
				IsDeleted:   commonconstants.DeleteNo,
			},
		}
		if apiResponse.ContentFormat == "json" {
			schema := handler.dealWithJson(response.Body)
			apiResponse.Content = string(jsonx.Marshal(&schema))
		}
		addResps = append(addResps, apiResponse)
	}
	if addResps != nil && len(addResps) != 0 {
		gormx.InsertBatchX(ctx, &addResps)
	}
}

func (handler PostmanLoadDocService) dealWithJson(raw string) *dto.ApiBaseSchema {
	res := &dto.ApiBaseSchema{
		Name:       "根节点",
		Type:       constants.SwaggerObject,
		Required:   false,
		Properties: make(map[string]dto.ApiBaseSchema),
	}
	if raw == "" {
		return res
	}
	// 去除/r/n
	strings.ReplaceAll(raw, "/r/n", "")
	// 如果json本身是一个数组
	if strings.HasPrefix(raw, "[") && strings.HasSuffix(raw, "]") {
		arr := make([]any, 0)
		err := json.Unmarshal([]byte(raw), &arr)
		if err != nil {
			// raw有问题
			return res
		}
		res.Type = "array"
		handler.dealWithArray(arr, res)
	} else if strings.HasPrefix(raw, "{") && strings.HasSuffix(raw, "}") {
		// 反序列化为map
		bodyMap := make(map[string]any)
		err := json.Unmarshal([]byte(raw), &bodyMap)
		if err != nil {
			// raw有问题
			return res
		}
		// 解析map
		handler.dealWithMap(bodyMap, res)
	} else {
		var body any
		err := json.Unmarshal([]byte(raw), &body)
		if err != nil {
			// raw有问题
			return res
		}
		t := convertGoTypeToMercuryType(reflect.TypeOf(body))
		res.Type = t
	}
	return res
}

func (handler PostmanLoadDocService) dealWithMap(m map[string]any, res *dto.ApiBaseSchema) {
	for k, v := range m {
		schema := &dto.ApiBaseSchema{
			Name:       k,
			Properties: make(map[string]dto.ApiBaseSchema),
		}
		if v == nil {
			schema.Type = "null"
		} else {
			t := convertGoTypeToMercuryType(reflect.TypeOf(v))
			schema.Type = t
			if t == "array" {
				handler.dealWithArray(v.([]any), schema)
			} else if t == "object" {
				handler.dealWithMap(v.(map[string]any), schema)
			}
		}
		res.Properties[k] = *schema
	}
}

func (handler PostmanLoadDocService) dealWithArray(arr []any, res *dto.ApiBaseSchema) {
	res.Type = constants.SwaggerArray
	schema := &dto.ApiBaseSchema{
		Properties: make(map[string]dto.ApiBaseSchema),
	}
	if len(arr) == 0 {
		schema.Type = "null"
	} else {
		item := arr[0]
		t := convertGoTypeToMercuryType(reflect.TypeOf(item))
		schema.Type = t
		if t == "array" {
			handler.dealWithArray(item.([]any), schema)
		} else if t == "object" {
			handler.dealWithMap(item.(map[string]any), schema)
		}
	}
	res.Items = schema
}

func (handler PostmanLoadDocService) GetLoadTypeEnum() string {
	return constants.Postman
}

func convertGoTypeToMercuryType(goType reflect.Type) string {
	switch goType.Kind() {
	case reflect.String:
		return "string"
	case reflect.Int64, reflect.Int32, reflect.Int:
		return "integer"
	case reflect.Bool:
		return "boolean"
	case reflect.Array, reflect.Slice:
		return "array"
	case reflect.Interface, reflect.Map:
		return "object"
	case reflect.Float32, reflect.Float64:
		return "number"
	default:
		return "null"
	}
}

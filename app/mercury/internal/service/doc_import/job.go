package doc_import

import (
	"context"
	"fmt"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/reugn/go-quartz/quartz"
)

type MercuryImportJob struct {
	taskId string
}

func NewMercuryImportJob(taskId string) *MercuryImportJob {
	return &MercuryImportJob{
		taskId: taskId,
	}
}

func (job *MercuryImportJob) Execute(_ context.Context) (err error) {
	logger.Logger.Infof("mercury import task execute, id: %s", job.taskId)
	defer func() {
		e := recover()
		if e != nil {
			err = fmt.Errorf("%v", e)
		}
	}()
	ApiDocImportService{}.ImportDocByTaskId(&commoncontext.MantisContext{}, job.taskId)
	return err
}

func (job *MercuryImportJob) Description() string {
	return job.taskId
}

func DealMercuryImportJob(description string) (quartz.Job, error) {
	return NewMercuryImportJob(description), nil
}

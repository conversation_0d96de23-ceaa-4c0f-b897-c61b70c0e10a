package doc_import

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	constants2 "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	loadandexportdocdto2 "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/xerror"
)

type SwaggerLoadDocService struct{}

func (handler SwaggerLoadDocService) AnalyseAndLoadDoc(ctx *commoncontext.MantisContext, loadDocDto loadandexportdocdto2.LoadDocDTO) {
	doc := loadDocDto.(*loadandexportdocdto2.SwaggerDTO)
	goroutine.Run(func() {
		handler.analyseAndLoadSwaggerDefinitions(ctx, doc.Definitions, doc.UserAccount,
			doc.DataModelFolderId, doc.LibId, doc.Strategy)
	})
	handler.analyseAndLoadSwaggerApis(ctx, *doc)
}

func (handler SwaggerLoadDocService) analyseAndLoadSwaggerDefinitions(ctx *commoncontext.MantisContext, definitions map[string]loadandexportdocdto2.SwaggerSchema,
	user string, folderId int64, libId int64, strategy int32,
) {
	dataModels := make([]models.ApiDataModel, 0, len(definitions))
	for k, v := range definitions {
		schema := handler.analyseOneSwaggerDataModelSchema(v, libId)
		dataModel := models.ApiDataModel{
			Name:        k,
			AliasName:   v.Title,
			Description: v.Description,
			Content:     string(jsonx.Marshal(&schema)),
			FolderId:    folderId,
		}
		dataModel.LibId = libId
		dataModel.Creator = user
		dataModel.Modifier = user
		dataModel.GmtCreated = times.Now()
		dataModel.GmtModified = times.Now()
		dataModel.IsDeleted = commonconstants.DeleteNo
		oldModel := models.ApiDataModel{}
		// 查询数据库中是否有同库同名的data models
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiDataModel{}).Eq("lib_id", libId).
				Eq("name", k).Eq("is_deleted", commonconstants.DeleteNo),
			&oldModel)
		if oldModel.Id != 0 {
			// 如果有
			if strategy == constants2.ImportKeepStrategy {
				// 如果策略是不覆盖
				continue
			} else {
				// 如果策略是覆盖
				dataModel.Id = oldModel.Id
				dataModel.GmtModified = times.Now()
				dataModel.Modifier = user
				dataModel.FolderId = oldModel.FolderId
				gormx.UpdateOneByConditionX(ctx, &dataModel)
			}
		} else {
			dataModel.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants2.NodeTypeModel)
			dataModels = append(dataModels, dataModel)
		}
	}
	if dataModels != nil && len(dataModels) > 0 {
		// 新增需要新增的dataModel进入树
		treeOperation.AddBatchDataModel2Tail(ctx, libId, folderId, dataModels, constants2.FolderTypeModel)
	}
}

func (handler SwaggerLoadDocService) analyseOneSwaggerDataModelSchema(v loadandexportdocdto2.SwaggerSchema, libId int64) dto.ApiBaseSchema {
	schema := dto.ApiBaseSchema{
		Type:        v.Type,
		Name:        "根节点",
		Description: v.Description,
	}
	// 如果是Object， 则分析property
	if v.Type == constants2.SwaggerObject {
		props := make(map[string]dto.ApiBaseSchema)
		properties := v.Properties
		for pK, pV := range properties {
			baseSchema := dto.ApiBaseSchema{
				Name:        pK,
				Type:        pV.Type,
				Description: pV.Description,
				Example:     pV.Example,
			}
			if pV.Ref != "" {
				baseSchema.Type = data_type.REF
				refSplit := strings.Split(pV.Ref, "/")
				if len(refSplit) != 0 {
					baseSchema.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
				}
			}
			if pV.Items != nil {
				baseSchema.Items = &dto.ApiBaseSchema{
					Type: pV.Items.Type,
				}
				if pV.Items.Ref != "" {
					baseSchema.Items.Type = data_type.REF
					refSplit := strings.Split(pV.Items.Ref, "/")
					if len(refSplit) != 0 {
						baseSchema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
					}
				}
			}
			props[pK] = baseSchema
		}
		schema.Properties = props
	}
	// 如果是array，则分析items
	if v.Type == constants2.SwaggerArray {
		schema.Items = &dto.ApiBaseSchema{
			Type: v.Items.Type,
		}
		if v.Items.Ref != "" {
			schema.Items.Type = data_type.REF
			refSplit := strings.Split(v.Items.Ref, "/")
			if len(refSplit) != 0 {
				schema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
			}
		}
	}
	return schema
}

func (handler SwaggerLoadDocService) analyseOneSwaggerSchema(v loadandexportdocdto2.SwaggerSchema, libId int64) dto.ApiBaseSchema {
	res := dto.ApiBaseSchema{
		Type:       v.Type,
		Name:       "根节点",
		Properties: make(map[string]dto.ApiBaseSchema),
		Required:   false,
		Example:    v.Example,
	}
	// 如果是array，则分析items
	if v.Type == constants2.SwaggerArray {
		res.Type = constants2.SwaggerArray
		res.Items = &dto.ApiBaseSchema{
			Type: v.Items.Type,
		}
		if v.Items.OriginalRef != "" {
			res.Items.Type = data_type.REF
			res.Items.Ref = strconv.FormatInt(libId, 10) + "+" + v.Items.OriginalRef
		}
	} else {
		// 如果是Object， 则分析property
		if v.Type == constants2.SwaggerObject {
			props := make(map[string]dto.ApiBaseSchema)
			properties := v.Properties
			for pK, pV := range properties {
				baseSchema := dto.ApiBaseSchema{
					Name:        pK,
					Type:        pV.Type,
					Description: pV.Description,
				}
				if pV.Ref != "" {
					baseSchema.Type = data_type.REF
					refSplit := strings.Split(pV.Ref, "/")
					if len(refSplit) != 0 {
						baseSchema.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
					}
				}
				if pV.Items != nil {
					baseSchema.Items = &dto.ApiBaseSchema{
						Type: pV.Items.Type,
					}
					if pV.Items.Ref != "" {
						baseSchema.Items.Type = data_type.REF
						refSplit := strings.Split(pV.Items.Ref, "/")
						if len(refSplit) != 0 {
							baseSchema.Items.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
						}
					}
				}
				props[pK] = baseSchema
			}
			res.Properties = props
		}
		// 如果ref不为空，直接使用
		if v.Ref != "" {
			res.Type = data_type.REF
			refSplit := strings.Split(v.Ref, "/")
			if len(refSplit) != 0 {
				res.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
			}
		}
	}
	return res
}

func (handler SwaggerLoadDocService) analyseAndLoadSwaggerApis(ctx *commoncontext.MantisContext, swaggerDTO loadandexportdocdto2.SwaggerDTO) {
	wg := sync.WaitGroup{}
	folderMap := handler.analyseSwaggerTags(ctx, int(swaggerDTO.Strategy), swaggerDTO.Tags,
		swaggerDTO.LibId, swaggerDTO.ApiDocFolderId, swaggerDTO.UserAccount)
	paths := swaggerDTO.Paths
	// 记录目录新增doc的map
	folderIdDocsMap := make(map[int64][]models.ApiDocBase)
	docChan := make(chan models.ApiDocBase, 50)
	// 记录key 和 OpenApi3PathDTO的map，用于处理response
	apiKeyPathDtoMap := make(map[string]loadandexportdocdto2.SwaggerPathDTO)
	respChan := make(chan struct {
		Key     string
		PathDto loadandexportdocdto2.SwaggerPathDTO
	}, 50)
	// 任务队列
	apiTasks := make([]func(), 0)
	// 开一个协程去读取docChan的内容，读不到就阻塞，直到读到一个id是-1的停止标记
	wg.Add(2)
	go func() {
		defer wg.Done()
		for {
			api := <-docChan
			if api.Id == -1 {
				logger.Logger.Info("doc chan 终止符已读取")
				return
			}
			_, ok := folderIdDocsMap[api.FolderId]
			if !ok {
				folderIdDocsMap[api.FolderId] = make([]models.ApiDocBase, 0)
			}
			folderIdDocsMap[api.FolderId] = append(folderIdDocsMap[api.FolderId], api)
		}
	}()
	// 再开一个协程去读取respChan的内容，读不到就阻塞，直到读到一个key是空字符串的停止
	go func() {
		defer wg.Done()
		for {
			structResp := <-respChan
			if structResp.Key == "" {
				logger.Logger.Info("resp chan 终止符已读取")
				return
			}
			apiKeyPathDtoMap[structResp.Key] = structResp.PathDto
		}
	}()
	// 每一个文档都开一个协程去处理
	for k, v := range paths {
		for pK, pV := range v {
			k := k
			pK := pK
			pV := pV
			apiTasks = append(apiTasks, func() {
				folderId := swaggerDTO.ApiDocFolderId
				if len(pV.Tags) != 0 {
					if folder, ok := folderMap[pV.Tags[0]]; ok {
						folderId = folder.Id
					}
				}
				api := models.ApiDocBase{
					Type:          "http",
					Name:          pV.Summary,
					Method:        strings.ToUpper(pK),
					Path:          swaggerDTO.BasePath + k,
					Status:        defaultApiDocStatus,
					FolderId:      folderId, // 只会加入第一个目录
					ResponsibleId: swaggerDTO.UserAccount,
				}
				api.LibId = swaggerDTO.LibId
				// 处理parameter
				handler.dealWithParameters(pV, swaggerDTO, &api)
				// 写入api_doc
				api.Creator = swaggerDTO.UserAccount
				api.Modifier = swaggerDTO.UserAccount
				api.GmtCreated = times.Now()
				api.GmtModified = times.Now()
				api.IsDeleted = commonconstants.DeleteNo
				// 判断是否重名
				oldApis := make([]models.ApiDocBase, 0)
				gormx.SelectByParamBuilderX(ctx,
					gormx.NewParamBuilder().Model(&models.ApiDocBase{}).Eq("lib_id", api.LibId).
						Eq("path", api.Path).Eq("method", api.Method).Eq("name", api.Name).
						Eq("is_deleted", commonconstants.DeleteNo),
					&oldApis)
				if len(oldApis) != 0 {
					// 存在重名
					if swaggerDTO.Strategy == constants2.ImportUpdateStrategy {
						for _, oldApi := range oldApis {
							// 覆盖策略，更新旧的
							api.Id = oldApi.Id
							api.GmtCreated = oldApi.GmtCreated
							api.Creator = oldApi.Creator
							// 位置不改变
							api.FolderId = oldApi.FolderId
							api.Key = oldApi.Key
							api.PreNode = oldApi.PreNode
							api.PostNode = oldApi.PostNode
							gormx.UpdateOneByConditionX(ctx, &api)
							// 更新response，先删除后写入
							responses := make([]models.ApiResponse, 0)
							gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", oldApi.Id).Eq("is_deleted", commonconstants.DeleteNo), &responses)
							responseIds := make([]int64, 0)
							for _, resp := range responses {
								responseIds = append(responseIds, resp.Id)
							}
							// 删除response
							gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiResponse{}).In("id", responseIds), map[string]any{"is_deleted": commonconstants.DeleteYes})
							// 删除responseCase
							gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiResponseCase{}).In("resp_id", responseIds), map[string]any{"is_deleted": commonconstants.DeleteYes})
							// 处理response
							handler.dealWithResponses(ctx, pV, oldApi.Id, swaggerDTO)
						}
					} else {
						// 不覆盖策略，跳过剩下的处理
						return
					}
				} else {
					// 不重名，正常写入
					api.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants2.NodeTypeApi)
					docChan <- api
					respChan <- struct {
						Key     string
						PathDto loadandexportdocdto2.SwaggerPathDTO
					}{Key: api.Key, PathDto: pV}
				}
			})
		}
	}
	// 运行协程
	err := limit.RunTasks(apiTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
	api := models.ApiDocBase{}
	api.Id = -1
	docChan <- api
	respChan <- struct {
		Key     string
		PathDto loadandexportdocdto2.SwaggerPathDTO
	}{Key: "", PathDto: loadandexportdocdto2.SwaggerPathDTO{}}
	logger.Logger.Info("终止符已经发送")
	wg.Wait()
	// 记录apikey和id的map的chan
	apiKeyIdChan := make(chan map[string]int64, 50)
	docTasks := make([]func(), 0, len(folderIdDocsMap))
	for k, v := range folderIdDocsMap {
		if v != nil && len(v) != 0 {
			k := k
			v := v
			apiKeyIdChan := apiKeyIdChan
			docTasks = append(docTasks, func() {
				currentMap := make(map[string]int64)
				treeOperation.AddBatchApiDoc2Tail(ctx, swaggerDTO.LibId, k, v, currentMap)
				apiKeyIdChan <- currentMap
			})
		}
	}
	// 记录apikey和id的map
	wg.Add(1)
	apiKeyIdMap := make(map[string]int64)
	go func() {
		defer wg.Done()
		for {
			m := <-apiKeyIdChan
			if m == nil {
				return
			}
			for k, v := range m {
				apiKeyIdMap[k] = v
			}
		}
	}()
	err = limit.RunTasks(docTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
	// 发送终止符
	apiKeyIdChan <- nil
	wg.Wait()
	// 处理response
	respTasks := make([]func(), 0, len(apiKeyPathDtoMap))
	for k, v := range apiKeyPathDtoMap {
		v := v
		apiId, ok := apiKeyIdMap[k]
		if !ok || apiId == 0 {
			continue
		}
		swaggerDTO := swaggerDTO
		respTasks = append(respTasks, func() {
			handler.dealWithResponses(ctx, v, apiId, swaggerDTO)
		})
	}
	err = limit.RunTasks(respTasks)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.New("文档导入错误。"))
	}
}

func (handler SwaggerLoadDocService) dealWithParameters(pV loadandexportdocdto2.SwaggerPathDTO, swaggerDTO loadandexportdocdto2.SwaggerDTO, api *models.ApiDocBase) {
	requestDTO := dto.ApiDocRequestDTO{
		Auth: &dto.Auth{},
		Parameters: &dto.Parameters{
			Query:  make([]dto.BaseParameter, 0),
			Path:   make([]dto.BaseParameter, 0),
			Header: make([]dto.BaseParameter, 0),
			Cookie: make([]dto.BaseParameter, 0),
		},
		RequestBody: &dto.RequestBody{
			Parameters: make([]dto.BaseParameter, 0),
			JsonSchema: &dto.ApiBaseSchema{},
			Example:    "",
		},
	}
	if swaggerDTO.Consumes != nil && len(swaggerDTO.Consumes) != 0 {
		requestDTO.RequestBody.Type = swaggerDTO.Consumes[0]
	}
	for _, swaggerSchema := range pV.Parameters {
		switch swaggerSchema.In {
		case constants2.SwaggerParamInQuery:
			requestDTO.Parameters.Query = append(requestDTO.Parameters.Query, handler.convertSwaggerParameter2BaseParameter(swaggerSchema))
			break
		case constants2.SwaggerParamInPath:
			requestDTO.Parameters.Path = append(requestDTO.Parameters.Path, handler.convertSwaggerParameter2BaseParameter(swaggerSchema))
			break
		case constants2.SwaggerParamInHeader:
			requestDTO.Parameters.Header = append(requestDTO.Parameters.Header, handler.convertSwaggerParameter2BaseParameter(swaggerSchema))
			break
		case constants2.SwaggerParamInForm:
			requestDTO.RequestBody.Parameters = append(requestDTO.RequestBody.Parameters, handler.convertSwaggerParameter2BaseParameter(swaggerSchema))
			break
		case constants2.SwaggerParamInBody:
			format := commonconstants.JsonContentType
			if (pV.Produces != nil && len(pV.Produces) > 0 && pV.Produces[0] != "*/*") == true {
				format = pV.Produces[0]
			}
			requestDTO.RequestBody.Type = format
			schema := handler.convertSwaggerParameter2ApiBaseSchema(swaggerSchema, swaggerDTO.LibId)
			requestDTO.RequestBody.JsonSchema = &schema
			break
		}
	}
	api.Request = string(jsonx.Marshal(&requestDTO))
}

func (handler SwaggerLoadDocService) dealWithResponses(ctx *commoncontext.MantisContext, pV loadandexportdocdto2.SwaggerPathDTO, apiId int64,
	swaggerDTO loadandexportdocdto2.SwaggerDTO,
) {
	responses := pV.Responses
	addResponses := make([]models.ApiResponse, 0, len(responses))
	for k, v := range responses {
		code, err := strconv.ParseInt(k, 10, 32)
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in convert resp code"))
		}
		format := "json"
		if (pV.Produces != nil && len(pV.Produces) > 0 && pV.Produces[0] != "*/*") == true {
			format = strings.ReplaceAll(pV.Produces[0], "application/", "")
		}
		response := models.ApiResponse{
			Name:          v.Description,
			RespCode:      int32(code),
			ContentFormat: format,
			ApiId:         apiId,
			LibId:         swaggerDTO.LibId,
		}
		baseSchema := dto.ApiBaseSchema{}
		if v.Schema != nil {
			baseSchema = handler.analyseOneSwaggerSchema(*(v.Schema), swaggerDTO.LibId)
			response.ContentRaw = *(v.Schema)
		}
		response.Content = string(jsonx.Marshal(&baseSchema))
		response.Creator = swaggerDTO.UserAccount
		response.Modifier = swaggerDTO.UserAccount
		response.GmtCreated = times.Now()
		response.GmtModified = times.Now()
		response.IsDeleted = commonconstants.DeleteNo
		addResponses = append(addResponses, response)
	}
	if len(addResponses) != 0 {
		gormx.InsertBatchX(ctx, &addResponses)
		addResponseList := make([]models.ApiResponseCase, 0)
		for _, response := range addResponses {
			responseCase := models.ApiResponseCase{
				Name:    "generated",
				RespId:  strconv.FormatInt(response.Id, 10),
				OrderNo: 0,
				Addons: Addons{
					Creator:     swaggerDTO.UserAccount,
					Modifier:    swaggerDTO.UserAccount,
					GmtCreated:  times.Now(),
					GmtModified: times.Now(),
					IsDeleted:   commonconstants.DeleteNo,
				},
			}
			if response.ContentRaw != nil {
				if schema, ok := response.ContentRaw.(loadandexportdocdto2.SwaggerSchema); ok {
					responseCase.Content = convertSwaggerSchema2Json(schema, set.New[string](), swaggerDTO.Definitions)
				}
			}
			addResponseList = append(addResponseList, responseCase)
		}
		if len(addResponseList) != 0 {
			gormx.InsertBatchX(ctx, &addResponseList)
		}
	}
}

func (handler SwaggerLoadDocService) analyseSwaggerTags(ctx *commoncontext.MantisContext, strategy int, tags []loadandexportdocdto2.SwaggerTagDTO,
	libId int64, parentId int64,
	userAdAccount string,
) map[string]*models.ApiFolder {
	m := make(map[string]*models.ApiFolder)
	for _, tag := range tags {
		apiFolder := models.ApiFolder{
			Name:     tag.Name,
			ParentId: parentId,
			Type:     constants2.FolderTypeApi,
		}
		apiFolder.LibId = libId
		apiFolder.IsDeleted = commonconstants.DeleteNo
		apiFolder.Creator = userAdAccount
		apiFolder.Modifier = userAdAccount
		apiFolder.GmtCreated = times.Now()
		apiFolder.GmtModified = times.Now()
		apiFolder.Path = fixPath(ctx, apiFolder.ParentId)
		// 判断重名
		repeat := models.ApiFolder{}
		gormx.SelectByParamBuilderX(ctx,
			gormx.NewParamBuilder().Model(&models.ApiFolder{}).Eq("lib_id", libId).Eq("parent_id", parentId).Eq("name", apiFolder.Name).Eq("is_deleted", commonconstants.DeleteNo),
			&repeat)
		if repeat.Id != 0 {
			// 存在重名
			if strategy == constants2.ImportUpdateStrategy {
				// 覆盖策略，更新旧的
				apiFolder.Id = repeat.Id
				apiFolder.GmtCreated = repeat.GmtCreated
				apiFolder.Creator = repeat.Creator
				// 位置不改变
				apiFolder.ParentId = repeat.ParentId
				gormx.UpdateOneByConditionX(ctx, &apiFolder)
			} else {
				// 不覆盖策略，跳过剩下的处理
				apiFolder = repeat
			}
		} else {
			// 加入树
			apiFolder.Key = fmt.Sprintf("%d+%s", snowflake.GenSnowFlakeId(), constants2.NodeTypeFolder)
			treeOperation.AddOneNodeToTail(ctx, apiFolder.ParentId, &apiFolder, constants2.FolderTypeApi)
		}
		m[tag.Name] = &apiFolder
	}
	return m
}

func (handler SwaggerLoadDocService) convertSwaggerParameter2BaseParameter(schema loadandexportdocdto2.SwaggerParameters) dto.BaseParameter {
	return dto.BaseParameter{
		Name:        schema.Name,
		Required:    schema.Required,
		Description: schema.Description,
		Type:        schema.Type,
		Example:     nil,
		Enable:      true,
	}
}

func (handler SwaggerLoadDocService) convertSwaggerParameter2ApiBaseSchema(parameters loadandexportdocdto2.SwaggerParameters, libId int64) dto.ApiBaseSchema {
	res := dto.ApiBaseSchema{
		Name:        "根节点",
		Type:        parameters.Type,
		Properties:  make(map[string]dto.ApiBaseSchema),
		AliasName:   "",
		Description: parameters.Description,
		Required:    parameters.Required,
	}
	// 如果是对象或者数组
	if parameters.Schema != nil {
		// 如果是对象
		if parameters.Schema.Items == nil {
			res.Type = data_type.REF
			refSplit := strings.Split(parameters.Schema.Ref, "/")
			if len(refSplit) != 0 {
				res.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
			}
		} else {
			// 如果是数组
			res.Type = data_type.ARRAY
			res.Items = &dto.ApiBaseSchema{
				Name:        parameters.Schema.Items.Title,
				AliasName:   "",
				Description: parameters.Schema.Items.Description,
				Type:        parameters.Schema.Items.Type,
			}
			if parameters.Schema.Items.Ref != "" {
				res.Items.Type = data_type.REF
				refSplit := strings.Split(parameters.Schema.Items.Ref, "/")
				if len(refSplit) != 0 {
					res.Items.Ref = strconv.FormatInt(libId, 10) + "+" + refSplit[len(refSplit)-1]
				}
			}
		}
	}
	return res
}

func (handler SwaggerLoadDocService) GetLoadTypeEnum() string {
	return constants2.SWAGGER
}

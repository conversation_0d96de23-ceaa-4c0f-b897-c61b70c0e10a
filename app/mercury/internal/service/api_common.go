package service

import (
	"encoding/json"
	"reflect"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
)

type ApiCommonService struct{}

func (a ApiCommonService) TransferJson2Schema(ctx *commoncontext.MantisContext, raw string) dto.ApiBaseSchema {
	res := &dto.ApiBaseSchema{
		Name:       "根节点",
		Type:       constants.SwaggerObject,
		Required:   false,
		Properties: make(map[string]dto.ApiBaseSchema),
	}
	if raw == "" {
		return *res
	}
	// 去除/r/n
	strings.ReplaceAll(raw, "/r/n", "")
	// 如果json本身是一个数组
	if strings.HasPrefix(raw, "[") && strings.HasSuffix(raw, "]") {
		arr := make([]any, 0)
		err := json.Unmarshal([]byte(raw), &arr)
		if err != nil {
			// raw有问题
			return *res
		}
		res.Type = "array"
		a.dealWithArray(arr, res)
	} else if strings.HasPrefix(raw, "{") && strings.HasSuffix(raw, "}") {
		// 反序列化为map
		bodyMap := make(map[string]any)
		err := json.Unmarshal([]byte(raw), &bodyMap)
		if err != nil {
			// raw有问题
			return *res
		}
		// 解析map
		a.dealWithMap(bodyMap, res)
	} else {
		var body any
		err := json.Unmarshal([]byte(raw), &body)
		if err != nil {
			// raw有问题
			return *res
		}
		t := convertGoTypeToMercuryType(reflect.TypeOf(body))
		res.Type = t
	}
	return *res
}

func (a ApiCommonService) dealWithMap(m map[string]any, res *dto.ApiBaseSchema) {
	for k, v := range m {
		schema := &dto.ApiBaseSchema{
			Name:       k,
			Properties: make(map[string]dto.ApiBaseSchema),
		}
		if v == nil {
			schema.Type = "null"
		} else {
			t := convertGoTypeToMercuryType(reflect.TypeOf(v))
			schema.Type = t
			if t == "array" {
				a.dealWithArray(v.([]any), schema)
			} else if t == "object" {
				a.dealWithMap(v.(map[string]any), schema)
			}
		}
		res.Properties[k] = *schema
	}
}

func (a ApiCommonService) dealWithArray(arr []any, res *dto.ApiBaseSchema) {
	schema := &dto.ApiBaseSchema{
		Properties: make(map[string]dto.ApiBaseSchema),
	}
	if len(arr) == 0 {
		schema.Type = "null"
	} else {
		item := arr[0]
		t := convertGoTypeToMercuryType(reflect.TypeOf(item))
		schema.Type = t
		if t == "array" {
			a.dealWithArray(item.([]any), schema)
		} else if t == "object" {
			a.dealWithMap(item.(map[string]any), schema)
		}
	}
	res.Items = schema
}

func convertGoTypeToMercuryType(goType reflect.Type) string {
	switch goType.Kind() {
	case reflect.String:
		return "string"
	case reflect.Int64, reflect.Int32, reflect.Int:
		return "integer"
	case reflect.Bool:
		return "boolean"
	case reflect.Array, reflect.Slice:
		return "array"
	case reflect.Interface, reflect.Map:
		return "object"
	case reflect.Float32, reflect.Float64:
		return "number"
	default:
		return "null"
	}
}

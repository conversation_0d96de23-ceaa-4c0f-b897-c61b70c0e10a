package service

import (
	"sort"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

var (
	apiResponseDao         dao.ApiResponseDao
	apiResponseCaseService ApiResponseCaseService
)

type ApiResponseService struct{}

func (a ApiResponseService) GetResponseListByApiDocId(ctx *commoncontext.MantisContext, apiId int64) []dto.ApiResponseDTO {
	responses := apiResponseDao.GetResponseListByApiDocId(ctx, apiId)
	res := make([]dto.ApiResponseDTO, 0, len(responses))
	for _, r := range responses {
		content := dto.ApiBaseSchema{}
		if r.Content != "" {
			jsonx.UnMarshal([]byte(r.Content), &content)
		}
		AnalyseSchemaRef(ctx, &content, -1)
		dtoR := dto.ApiResponseDTO{
			Id:            r.Id,
			Name:          r.Name,
			RespCode:      r.RespCode,
			ContentFormat: r.ContentFormat,
			Content:       &content,
			ApiId:         r.ApiId,
			LibId:         r.LibId,
			TempId:        r.TempId,
			OrderNo:       r.OrderNo,
		}
		res = append(res, dtoR)
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].OrderNo < res[j].OrderNo
	})
	return res
}

func (a ApiResponseService) GetResponseIdsByApiDocId(ctx *commoncontext.MantisContext, apiDocId int64) []int64 {
	responses := a.GetResponseListByApiDocId(ctx, apiDocId)
	ids := make([]int64, len(responses))
	for i, resp := range responses {
		ids[i] = resp.Id
	}
	return ids
}

func (a ApiResponseService) RemoveResponseByApiDocId(ctx *commoncontext.MantisContext, apiDocId int64, user commondto.UserInfo) {
	resp := models.ApiResponse{}
	resp.IsDeleted = constants.DeleteYes
	resp.Modifier = user.AdAccount
	apiResponseDao.UpdateResponseByApiDocId(ctx, apiDocId, resp)
}

func (a ApiResponseService) RemoveResponsesByIds(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	resp := models.ApiResponse{}
	resp.IsDeleted = constants.DeleteYes
	resp.Modifier = user.AdAccount
	apiResponseDao.UpdateResponseByIds(ctx, ids, resp)
}

func (a ApiResponseService) InsertUpdateApiResponse(ctx *commoncontext.MantisContext, libId int64, docId int64, apiResponseList []models.ApiResponse,
	user commondto.UserInfo,
) map[string]int64 {
	m := make(map[string]int64)
	if apiResponseList == nil || len(apiResponseList) == 0 {
		return m
	}
	insertApiResponseList := make([]models.ApiResponse, 0, len(apiResponseList))
	updateApiResponseList := make([]models.ApiResponse, 0, len(apiResponseList))
	for i, resp := range apiResponseList {
		resp.IsDeleted = constants.DeleteNo
		resp.Modifier = user.AdAccount
		resp.GmtModified = times.Now()
		resp.ApiId = docId
		resp.LibId = libId
		resp.OrderNo = int64(i)
		if resp.Id == 0 {
			resp.Creator = user.AdAccount
			resp.GmtCreated = times.Now()
			insertApiResponseList = append(insertApiResponseList, resp)
		} else {
			updateApiResponseList = append(updateApiResponseList, resp)
		}
	}
	if len(insertApiResponseList) != 0 {
		logger.Logger.Infof("新增响应请求, docId=%d, 共计%d条", docId, len(insertApiResponseList))
		apiResponseDao.InsertResponses(ctx, insertApiResponseList)
		for _, insert := range insertApiResponseList {
			m[insert.TempId] = insert.Id
		}
	}
	if len(updateApiResponseList) != 0 {
		logger.Logger.Infof("修改响应请求, docId=%d, 共计%d条", docId, len(updateApiResponseList))
		for _, update := range updateApiResponseList {
			apiResponseDao.UpdateResponseById(ctx, &update)
		}
	}
	return m
}

func (a ApiResponseService) CopyApiRespByApiId(ctx *commoncontext.MantisContext, srcApiId int64, tarApiId int64, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiResponse{}).Eq("api_id", srcApiId).Eq("is_deleted", constants.DeleteNo)
	apiResponses := make([]models.ApiResponse, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiResponses)
	if apiResponses != nil && len(apiResponses) != 0 {
		for i, apiRes := range apiResponses {
			id := apiRes.Id
			apiRes.ApiId = tarApiId
			apiRes.TempId = strconv.FormatInt(id, 10)
			apiRes.Creator = user.AdAccount
			apiRes.Modifier = user.AdAccount
			apiRes.GmtCreated = times.Now()
			apiRes.GmtModified = times.Now()
			apiRes.Id = 0
			apiResponses[i] = apiRes
		}
		gormx.InsertBatchX(ctx, apiResponses)
	}
	respMap := make(map[string]int64)
	for _, apiRes := range apiResponses {
		respMap[apiRes.TempId] = apiRes.Id
	}
	apiResponseCaseService.CopyCaseRespIds(ctx, respMap, user)
}

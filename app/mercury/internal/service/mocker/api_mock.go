package mocker

import (
	"fmt"
	"io"
	"net/http"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/enum/operation_enum"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/mock"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/xmlx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	"github.com/duke-git/lancet/v2/xerror"
)

type ApiMockService struct{}

var (
	apiMockDao          dao.ApiMockDao
	usercenterRemoteApi remote.UserCenterRemoteApi
)

func (a ApiMockService) GetMocksByDocId(ctx *commoncontext.MantisContext, docId int64) []dto.MockDTO {
	mocks := make([]models.ApiMock, 0)
	gormx.SelectByParamBuilderX(ctx, gormx.NewParamBuilder().Model(&models.ApiMock{}).Eq("api_doc_base_id", docId).
		Eq("is_deleted", commonconstants.DeleteNo).OrderByAsc("sort"), &mocks)
	res := make([]dto.MockDTO, 0)
	creators := make(map[string]string)
	for _, mock := range mocks {
		creators[mock.Creator] = ""
	}
	err := usercenterRemoteApi.GetUserAdAccountNameMap(creators, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	for _, mock := range mocks {
		mockDto := dto.MockDTO{}
		mockDto.ApiMock = mock
		jsonx.UnMarshal([]byte(mock.ExpectationHeaderStr), &(mockDto.ExpectationHeader))
		mockCondition := make([]dto.MockCondition, 0)
		jsonx.UnMarshal([]byte(mock.Condition), &mockCondition)
		mockDto.MockConditions = mockCondition
		mockDto.CreatorName = creators[mockDto.Creator]
		res = append(res, mockDto)
	}
	return res
}

func (a ApiMockService) GetMockDetailById(ctx *commoncontext.MantisContext, id int64) dto.MockDTO {
	mock := models.ApiMock{}
	mock.Id = id
	gormx.SelectOneByConditionX(ctx, &mock)
	res := dto.MockDTO{}
	jsonx.UnMarshal([]byte(mock.ExpectationHeaderStr), &(res.ExpectationHeader))
	res.ApiMock = mock
	mockCondition := make([]dto.MockCondition, 0)
	jsonx.UnMarshal([]byte(mock.Condition), &mockCondition)
	res.MockConditions = mockCondition
	return res
}

func (a ApiMockService) DeleteMock(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	mock := models.ApiMock{}
	mock.Id = id
	mock.IsDeleted = commonconstants.DeleteYes
	mock.GmtModified = times.Now()
	mock.Modifier = user.AdAccount
	gormx.UpdateOneByConditionX(ctx, &mock)
}

func (a ApiMockService) DeleteMockByDocId(ctx *commoncontext.MantisContext, docId int64, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiMock{}).Eq("api_doc_base_id", docId).Eq("is_deleted", commonconstants.DeleteNo)
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, paramBuilder, map[string]any{
		"is_deleted":   commonconstants.DeleteYes,
		"modifier":     user.AdAccount,
		"gmt_modified": times.Now(),
	})
}

func (a ApiMockService) CopyMock(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	mock := models.ApiMock{}
	mock.Id = id
	mock.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &mock)
	mock.Id = 0
	mock.Name = mock.Name + constants.CopyNameSuffix
	mock.Creator = user.AdAccount
	mock.Modifier = user.AdAccount
	mock.GmtCreated = times.Now()
	mock.GmtModified = times.Now()
	mock.Sort = apiMockDao.SelectLastSortByDocId(ctx, mock.ApiDocBaseId) + 1
	gormx.InsertUpdateOneX(ctx, &mock)
}

func (a ApiMockService) SaveMock(ctx *commoncontext.MantisContext, mock dto.MockDTO, user commondto.UserInfo) {
	mockModel := mock.ApiMock
	mockModel.IsDeleted = commonconstants.DeleteNo
	mockModel.Modifier = user.AdAccount
	mockModel.Creator = user.AdAccount
	mockModel.GmtCreated = times.Now()
	mockModel.GmtModified = times.Now()
	mockModel.ExpectationHeaderStr = string(jsonx.Marshal(&(mock.ExpectationHeader)))
	mockModel.Condition = string(jsonx.Marshal(&(mock.MockConditions)))
	if mockModel.Id == 0 {
		// 新增的mock，初始化sort
		mockModel.Sort = apiMockDao.SelectLastSortByDocId(ctx, mockModel.ApiDocBaseId) + 1
	}
	gormx.InsertUpdateOneX(ctx, &mockModel)
}

// DragMock 拖拽mock，放到dropId的后面
func (ApiMockService) DragMock(ctx *commoncontext.MantisContext, dragId int64, dropId int64, pos int64) {
	drag := models.ApiMock{}
	drag.Id = dragId
	drag.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &drag)
	drop := models.ApiMock{}
	drop.Id = dropId
	drop.IsDeleted = commonconstants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &drop)
	switch pos {
	case constants.TreePositionPost: // 放在drop节点的后面
		// 修改drag之后的mock的排序
		apiMockDao.UpdateSortAfter(ctx, drag.ApiDocBaseId, drop.Sort)
		drag.Sort = drop.Sort + 1
	case constants.TreePositionPre: // 放在drop节点的前面
		// 修改drag之后的mock的排序
		apiMockDao.UpdateSortPre(ctx, drag.ApiDocBaseId, drop.Sort)
		drag.Sort = drop.Sort - 1
	}
	// 修改drag的sort
	gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiMock{}).Eq("id", drag.Id),
		map[string]any{"sort": drag.Sort})
}

func (a ApiMockService) UpdateStatus(ctx *commoncontext.MantisContext, id int64, status bool, user commondto.UserInfo) {
	mock := models.ApiMock{}
	mock.Id = id
	mock.Enabled = &status
	mock.Modifier = user.AdAccount
	mock.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &mock)
}

func (a ApiMockService) Mock(ctx *commoncontext.MantisContext, request *http.Request, writer http.ResponseWriter, docId int64) {
	// 查询docId判断方法是否对应
	doc := models.ApiDocBase{}
	doc.Id = docId
	gormx.SelectOneByConditionX(ctx, &doc)
	if strings.ToUpper(request.Method) != doc.Method {
		writeResponse(writer, 404, []byte("不存在匹配的mock设置"))
		return
	}
	// 查询mock
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiMock{}).Eq("api_doc_base_id", docId).Eq("is_deleted", commonconstants.DeleteNo).Eq("enabled", true).OrderByAsc("sort")
	mocks := make([]models.ApiMock, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &mocks)
	// 获取contentType以及body字符串
	contentType := request.Header.Get("Content-Type")
	var body string
	if contentType == commonconstants.JsonContentType || contentType == commonconstants.XmlContentType {
		d, _ := io.ReadAll(request.Body)
		body = string(d)
	}
	// 循环匹配
out:
	for _, mock := range mocks {
		mockConditions := make([]dto.MockCondition, 0)
		jsonx.UnMarshal([]byte(mock.Condition), &mockConditions)
		for _, condition := range mockConditions {
			if condition.Type == "queryCondition" {
				operation := operation_enum.GetOperationByName(condition.Operation)
				values := strings.Split(request.URL.Query().Get(condition.Name), ",")
				if len(values) > 1 {
					if !operation.Operate(string(jsonx.Marshal(&values)), condition.Value) {
						continue out
					}
				} else if len(values) == 1 {
					if !operation.Operate(values[0], condition.Value) {
						continue out
					}
				} else {
					continue out
				}
			} else if condition.Type == "headerCondition" {
				operation := operation_enum.GetOperationByName(condition.Operation)
				values := request.Header.Values(condition.Name)
				v := ""
				if values != nil && len(values) != 0 {
					for _, val := range values {
						v += val + ","
					}
					v = v[0 : len(v)-1]
				}
				if !operation.Operate(v, condition.Value) {
					continue out
				}
			} else if condition.Type == "cookieCondition" {
				operation := operation_enum.GetOperationByName(condition.Operation)
				cookie, err := request.Cookie(condition.Name)
				if err != nil || !operation.Operate(cookie.Value, condition.Value) {
					continue out
				}
			} else if condition.Type == "bodyCondition" {
				operation := operation_enum.GetOperationByName(condition.Operation)
				if strings.Contains(contentType, commonconstants.JsonContentType) {
					data, err := jsonx.GetDataByJsonPath(body, condition.Name)
					if err != nil {
						continue out
					}
					v1 := fmt.Sprintf("%v", data)
					if !operation.Operate(v1, condition.Value) {
						continue out
					}
				} else if strings.Contains(contentType, commonconstants.XmlContentType) {
					data, err := xmlx.GetDataByXpath(body, condition.Name)
					if err != nil {
						continue out
					}
					v1 := fmt.Sprintf("%v", data)
					if !operation.Operate(v1, condition.Value) {
						continue out
					}
				} else if strings.Contains(contentType, commonconstants.FormContentType) || strings.Contains(contentType, commonconstants.UrlencodedContentType) {
					str := request.FormValue(condition.Name)
					if !operation.Operate(str, condition.Value) {
						continue out
					}
				} else {
					continue out
				}
			}
		}
		// 通过全部的条件，返回用户配置的mock数据
		header := make([]dto.MockHeader, 0)
		jsonx.UnMarshal([]byte(mock.ExpectationHeaderStr), &header)
		for _, h := range header {
			writer.Header().Set(h.Name, h.Value)
		}
		writer.Header().Set("Content-Type", mock.ExpectationContentType)
		expectationCode := mock.ExpectationCode
		if expectationCode == 0 {
			expectationCode = 200
		}
		mockData := a.GetMockData(mock.ExpectationBody)
		// 支持多种content type返回
		writer.WriteHeader(int(expectationCode))
		_, err := writer.Write([]byte(mockData))
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in controller"))
		}
		return
	}
	writeResponse(writer, 404, []byte("不存在匹配的mock设置"))
}

func writeResponse(w http.ResponseWriter, code int, data []byte) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(code)
	_, err := w.Write(data)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in write data to response"))
	}
}

func (ApiMockService) GetMockParamDataTree(ctx *commoncontext.MantisContext) dto.ApiFolderDTO {
	list, err := openapi.MockTypeOpenapi{}.GetMockList(ctx)
	if err != nil {
		logger.Logger.Panicf("查询mock type错误, err=%s", err.Error())
	}
	root := dto.ApiFolderDTO{}
	types := make([]string, 0)
	typeNameMap := make(map[string][]string)
	nameMockTypesMap := make(map[string][]any)
	for _, mockType := range list {
		if _, ok := typeNameMap[mockType.Type]; !ok {
			types = append(types, mockType.Type)
			typeNameMap[mockType.Type] = make([]string, 0)
		}
		if len(typeNameMap[mockType.Type]) == 0 || typeNameMap[mockType.Type][len(typeNameMap[mockType.Type])-1] != mockType.Name {
			typeNameMap[mockType.Type] = append(typeNameMap[mockType.Type], mockType.Name)
		}
		if _, ok := nameMockTypesMap[mockType.Name]; !ok {
			nameMockTypesMap[mockType.Name] = make([]any, 0)
		}
		nameMockTypesMap[mockType.Name] = append(nameMockTypesMap[mockType.Name], mockType)
	}
	children := make([]dto.ApiFolderDTO, 0)
	for _, t := range types {
		folder := dto.ApiFolderDTO{Name: t}
		names := typeNameMap[t]
		ch := make([]dto.ApiFolderDTO, 0)
		for _, name := range names {
			f := dto.ApiFolderDTO{Name: name}
			f.Children = nameMockTypesMap[name]
			ch = append(ch, f)
		}
		folder.Children = ch
		children = append(children, folder)
	}
	root.Children = children
	return root
}

func (ApiMockService) GetMockPreview(input string) MockResponse {
	response, err := mock.Mock(input)
	if err != nil {
		return MockResponse{
			Success: false,
			Data:    "mock 错误",
		}
	}
	logger.Logger.Debugf("mock返回数据:%v", response)
	return MockResponse{
		Success: true,
		Data:    response,
	}
}

type MockResponse struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
}

func (ApiMockService) GetMockData(input string) string {
	res, err := mock.Mock(input)
	if err != nil {
		logger.Logger.Panic(err)
	}
	return res
}

package library

import (
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/doc_import"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

var (
	apiDocDao           dao.ApiDocDao
	apiLibraryDao       dao.ApiLibraryDao
	apiDocImportService doc_import.ApiDocImportService
	usercenterRemoteApi remote.UserCenterRemoteApi
)

type ApiLibraryService struct{}

func (a ApiLibraryService) SearchLibrary(ctx *commoncontext.MantisContext, request dto.ApiLibraryRequest, pageRequest gormx.PageRequest, spaceId string) gormx.PageResult {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiLibrary{}).Eq("space_id", spaceId).Eq("is_deleted", constants.DeleteNo).OrderByDesc("id")
	if request.Name != "" {
		paramBuilder.Like("name", "%"+request.Name+"%")
	}
	if request.AppId != "" {
		paramBuilder.Eq("app_id", request.AppId)
	}
	if request.Creator != "" {
		paramBuilder.Eq("creator", request.Creator)
	}
	if request.GmtCreated != "" {
		paramBuilder.Gt("gmt_created", request.GetCreatTimeStart())
		paramBuilder.Lt("gmt_created", request.GetCreatTimeEnd())
	}
	apiLibraries := make([]models.ApiLibrary, 0)
	pageResult := gormx.PageSelectByParamBuilderX(ctx, paramBuilder, &apiLibraries, pageRequest)
	if len(apiLibraries) == 0 {
		return gormx.PageResult{Pages: 0, Total: 0, CurrentPage: pageRequest.Page, PageSize: pageRequest.PageSize}
	}
	userMap := make(map[string]string)
	apiNumMap := make(map[int64]int64)
	appFilter := ""
	for _, lib := range apiLibraries {
		userMap[lib.Creator] = ""
		userMap[lib.Modifier] = ""
		appFilter += fmt.Sprintf("id='%s'||", lib.AppId)
		apiNumMap[lib.Id] = 0
	}
	if len(appFilter) > 0 {
		appFilter = appFilter[:len(appFilter)-2]
	}
	apps, err := cmdb.Client.GetAppList(ctx, url.Values{"filter": []string{appFilter}})
	if err != nil {
		logger.Logger.Panicf("查询app错误, %s", err.Error())
	}
	appMap := make(map[string]string)
	for _, app := range apps {
		appMap[utils.IDString(app.AppId)] = app.Name
	}
	err = usercenterRemoteApi.GetUserAdAccountNameMap(userMap, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	apiNumMap = apiDocDao.SelectApiNum(ctx, apiNumMap)
	for i, lib := range apiLibraries {
		lib.CreatorName = userMap[lib.Creator]
		lib.ModifierName = userMap[lib.Modifier]
		lib.AppName = appMap[lib.AppId]
		lib.ApiCount = apiNumMap[lib.Id]
		apiLibraries[i] = lib
	}
	pageResult.List = apiLibraries
	return *pageResult
}

func (a ApiLibraryService) AddLibrary(ctx *commoncontext.MantisContext, request models.ApiLibrary, user commondto.UserInfo, spaceId string) int64 {
	a.checkName(ctx, spaceId, request.Name, request.Id)
	request.Creator = user.AdAccount
	request.Modifier = user.AdAccount
	request.GmtCreated = times.Now()
	request.GmtModified = times.Now()
	request.IsDeleted = constants.DeleteNo
	request.SpaceId = spaceId
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiStatus{}).Select("id").Eq("lib_id", 0).Eq("is_deleted", constants.DeleteNo)
	statusIds := make([]int64, 0)
	gormx.SelectFieldByParamBuilderX(ctx, paramBuilder, &statusIds)
	statusIdStr := ""
	for _, id := range statusIds {
		statusIdStr += strconv.FormatInt(id, 10) + ","
	}
	request.BindApiStatus = statusIdStr[:len(statusIdStr)-1]
	gormx.InsertUpdateOneX(ctx, &request)
	a.initEnvs(ctx, user, request)
	return request.Id
}

func (a ApiLibraryService) ModifyLibrary(ctx *commoncontext.MantisContext, request models.ApiLibrary, user commondto.UserInfo, spaceId string) int64 {
	a.checkName(ctx, spaceId, request.Name, request.Id)
	request.Modifier = user.AdAccount
	request.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &request)
	if request.BindApiStatus == "" {
		gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiLibrary{}).Eq("id", request.Id),
			map[string]any{"bind_api_status": ""})
	}
	return request.Id
}

func (a ApiLibraryService) RemoveLibrary(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	library := models.ApiLibrary{}
	library.Id = id
	library.IsDeleted = constants.DeleteYes
	library.Modifier = user.AdAccount
	library.GmtModified = times.Now()
	gormx.UpdateOneByConditionX(ctx, &library)
	apiDocImportService.DeleteTaskByLibId(ctx, id, user)
	a.removeEnv(ctx, id, user)
}

func (a ApiLibraryService) GetDocStatus(ctx *commoncontext.MantisContext, id int64) []models.ApiStatus {
	apiLibrary := models.ApiLibrary{}
	apiLibrary.Id = id
	apiLibrary.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &apiLibrary)
	if apiLibrary.BindApiStatus == "" {
		logger.Logger.Info("未设置doc状态，直接返回！")
		return make([]models.ApiStatus, 0)
	}
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiStatus{}).Select("id,name").Eq("is_deleted", constants.DeleteNo).In("id", strings.Split(apiLibrary.BindApiStatus, constants.RequestSplitChar))
	res := make([]models.ApiStatus, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
	return res
}

func (a ApiLibraryService) removeEnv(ctx *commoncontext.MantisContext, id int64, user commondto.UserInfo) {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiEnv{}).Eq("lib_id", id).Eq("is_deleted", constants.DeleteNo)
	apiEnv := models.ApiEnv{}
	apiEnv.IsDeleted = constants.DeleteYes
	apiEnv.Modifier = user.AdAccount
	apiEnv.GmtModified = times.Now()
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &apiEnv)
	paramBuilder = gormx.NewParamBuilder().Model(&models.ApiVariable{}).Eq("lib_id", id).Eq("is_deleted", constants.DeleteNo)
	apiVariable := models.ApiVariable{}
	apiVariable.IsDeleted = constants.DeleteYes
	apiVariable.Modifier = user.AdAccount
	apiVariable.GmtModified = times.Now()
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &apiVariable)
}

func (a ApiLibraryService) initEnvs(ctx *commoncontext.MantisContext, user commondto.UserInfo, library models.ApiLibrary) {
	envDTOS, err := cmdb.Client.GetBaseEnvList(ctx, ctx.User.CompanyID)
	if err != nil {
		logger.Logger.Panicf("查询环境失败, err=%s", err.Error())
	}
	if len(envDTOS) != 0 {
		envList := make([]models.ApiEnv, 0)
		for i, env := range envDTOS {
			apiEnv := models.ApiEnv{
				Code:    env.EnvCode,
				Name:    env.InstanceBizName,
				LibId:   library.Id,
				OrderNo: int32(i + 1),
			}
			apiEnv.Creator = user.AdAccount
			apiEnv.Modifier = user.AdAccount
			apiEnv.GmtCreated = times.Now()
			apiEnv.GmtModified = times.Now()
			apiEnv.IsDeleted = constants.DeleteNo
			envList = append(envList, apiEnv)
		}
		gormx.InsertBatchX(ctx, envList)
	}
}

func (a ApiLibraryService) checkName(ctx *commoncontext.MantisContext, spaceId string, name string, srcId int64) {
	id := apiLibraryDao.CheckName(ctx, spaceId, name)
	if 0 == srcId && id != 0 {
		logger.Logger.Panicf("%+v", errors.New("用例库名已经存在,请更换名字"))
	}
	if srcId != 0 {
		// 修改
		if id != 0 && srcId != id {
			logger.Logger.Panicf("%+v", errors.New("用例库名已经存在,请更换名字"))
		}
	}
}

package service

import (
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dao"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/variable"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

var (
	apiEnvDao      dao.ApiEnvDao
	apiVariableDao dao.ApiVariableDao
)

type ApiEnvService struct{}

func (a ApiEnvService) GetLibraryEnvs(ctx *commoncontext.MantisContext, libId int64) []models.ApiEnv {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiEnv{}).Eq("is_deleted", constants.DeleteNo).Eq("lib_id", libId).OrderByAsc("order_no")
	apiEnvList := make([]models.ApiEnv, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiEnvList)
	return apiEnvList
}

func (a ApiEnvService) GetEnvVars(ctx *commoncontext.MantisContext, envId int64) []models.ApiVariable {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiVariable{}).Eq("env_id", envId).Eq("is_deleted", constants.DeleteNo).Eq("type", variable.ENV_VAR.GetCode())
	apiVarList := make([]models.ApiVariable, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiVarList)
	return apiVarList
}

func (a ApiEnvService) DeleteEnv(ctx *commoncontext.MantisContext, envId int64, user commondto.UserInfo) {
	env := models.ApiEnv{}
	env.Id = envId
	env.IsDeleted = constants.DeleteYes
	env.GmtModified = times.Now()
	env.Modifier = user.AdAccount
	gormx.UpdateOneByConditionX(ctx, &env)
	apiVariable := models.ApiVariable{}
	paramBuilder := gormx.NewParamBuilder().Model(&apiVariable).Eq("is_deleted", constants.DeleteNo).Eq("env_id", envId)
	apiVariable.IsDeleted = constants.DeleteYes
	apiVariable.GmtModified = times.Now()
	apiVariable.Modifier = user.AdAccount
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &apiVariable)
}

func (a ApiEnvService) addEnvVars(ctx *commoncontext.MantisContext, user commondto.UserInfo, apiEnv models.ApiEnv, addVariableDTOS []models.ApiVariable, orderNo int32) []models.ApiVariable {
	varKeySet := apiVariableDao.GetVarKeySet(ctx, apiEnv.LibId, apiEnv.Id)
	if addVariableDTOS != nil && len(addVariableDTOS) != 0 {
		for i, v := range addVariableDTOS {
			if varKeySet.Contain(v.VarKey) {
				logger.Logger.Panicf("%+v", errors.New("变量名在当前环境下或全局环境中存在，请修改"))
			}
			v.Creator = user.AdAccount
			v.Modifier = user.AdAccount
			v.GmtCreated = times.Now()
			v.GmtModified = times.Now()
			v.LibId = apiEnv.LibId
			v.Type = variable.ENV_VAR.GetCode()
			v.IsDeleted = constants.DeleteNo
			v.EnvId = apiEnv.Id
			v.OrderNo = orderNo + int32(i) + 1
			addVariableDTOS[i] = v
		}
		gormx.InsertBatchX(ctx, addVariableDTOS)
		return addVariableDTOS
	}
	return make([]models.ApiVariable, 0)
}

func (a ApiEnvService) updateVars(ctx *commoncontext.MantisContext, libId int64, userId string, envId int64, updateVariables []models.ApiVariable, code int64) []models.ApiVariable {
	if updateVariables != nil && len(updateVariables) != 0 {
		variableModel := models.ApiVariable{
			LibId: libId,
			EnvId: envId,
			Type:  code,
		}
		variableModel.IsDeleted = constants.DeleteNo
		variables := make([]models.ApiVariable, 0)
		gormx.SelectByParamStructX(ctx, &variableModel, &variables)
		variableMap := make(map[int64]models.ApiVariable)
		for _, variable := range variables {
			variableMap[variable.Id] = variable
		}
		for _, variable := range updateVariables {
			variableMap[variable.Id] = variable
			variable.Modifier = userId
			variable.GmtModified = times.Now()
		}
		varKeyMap := make(map[string]int32)
		for _, v := range variableMap {
			varKeyMap[v.VarKey] += 1
		}
		repeatKey := make([]string, 0)
		for k, v := range varKeyMap {
			if v > 1 {
				repeatKey = append(repeatKey, k)
			}
		}
		if repeatKey != nil && len(repeatKey) > 0 {
			logger.Logger.Panicf("%+v", errors.New("变量名在当前环境下或全局环境中存在，请修改"))
		}
		for _, variable := range updateVariables {
			gormx.UpdateOneByConditionX(ctx, &variable)
		}
		return updateVariables
	}
	return make([]models.ApiVariable, 0)
}

func (a ApiEnvService) deleteVars(ctx *commoncontext.MantisContext, varIds []int64, userId string) {
	if varIds != nil && len(varIds) != 0 {
		paramBuilder := gormx.NewParamBuilder().Model(&models.ApiVariable{}).In("id", varIds).Eq("is_deleted", constants.DeleteNo)
		variable := models.ApiVariable{}
		variable.IsDeleted = constants.DeleteYes
		variable.Modifier = userId
		variable.GmtModified = times.Now()
		gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, &variable)
	}
}

func (a ApiEnvService) UpdateEnv(ctx *commoncontext.MantisContext, req dto.ApiEnvReq, user commondto.UserInfo) dto.ApiEnvReq {
	gormx.Transaction(ctx, func() error {
		apiEnv := models.ApiEnv{}
		apiEnv.Id = req.Id
		apiEnv.Name = req.Name
		apiEnv.Code = req.Code
		apiEnv.PreUrl = req.PreUrl
		apiEnv.LibId = req.LibId
		addVariables := req.AddVariables
		if apiEnv.Id != 0 {
			apiEnv.Modifier = user.AdAccount
			apiEnv.GmtModified = times.Now()
			gormx.UpdateBatchByParamBuilderAndMapX(ctx, gormx.NewParamBuilder().Model(&models.ApiEnv{}).
				Eq("id", apiEnv.Id),
				map[string]any{
					"name":         req.Name,
					"code":         req.Code,
					"pre_url":      req.PreUrl,
					"lib_id":       req.LibId,
					"modifier":     apiEnv.Modifier,
					"gmt_modified": apiEnv.GmtModified,
				})
			if addVariables != nil && len(addVariables) != 0 {
				varKeyMap := make(map[string]int32)
				for _, variable := range addVariables {
					varKeyMap[variable.VarKey] += 1
				}
				repeatKey := make([]string, 0)
				for k, v := range varKeyMap {
					if v > 1 {
						repeatKey = append(repeatKey, k)
					}
				}
				if len(repeatKey) != 0 {
					logger.Logger.Panicf("%+v", xerror.Wrap(errors.New("变量重复"), "变量重复"))
				}
				maxOrderNo := apiVariableDao.GetMaxOrderNo(ctx, apiEnv.Id, variable.ENV_VAR.GetCode(), req.LibId)
				apiVariables := a.addEnvVars(ctx, user, apiEnv, addVariables, maxOrderNo+1)
				req.AddVariables = apiVariables
			}
			updateVariables := req.UpdateVariables
			a.updateVars(ctx, req.LibId, user.AdAccount, apiEnv.Id, updateVariables, variable.ENV_VAR.GetCode())
			a.deleteVars(ctx, req.DeleteIds, user.AdAccount)
		} else {
			varKeyMap := make(map[string]int32)
			for _, variable := range addVariables {
				varKeyMap[variable.VarKey] += 1
			}
			repeatKey := make([]string, 0)
			for k, v := range varKeyMap {
				if v > 1 {
					repeatKey = append(repeatKey, k)
				}
			}
			if len(repeatKey) != 0 {
				err := xerror.New("变量名在当前环境下或全局环境中存在，请修改")
				logger.Logger.Errorf("%+v", err)
				return err
			}
			apiEnv.IsDeleted = constants.DeleteNo
			maxOrderNo := apiEnvDao.GetMaxOrderNo(ctx, apiEnv.LibId)
			apiEnv.OrderNo = maxOrderNo + 1
			apiEnv.Modifier = user.AdAccount
			apiEnv.GmtModified = times.Now()
			apiEnv.Creator = user.AdAccount
			apiEnv.GmtCreated = times.Now()
			gormx.InsertUpdateOneX(ctx, &apiEnv)
			apiVariables := a.addEnvVars(ctx, user, apiEnv, addVariables, 0)
			req.AddVariables = apiVariables
			req.Id = apiEnv.Id
		}
		return nil
	})
	return req
}

func (a ApiEnvService) SaveGlobalVars(ctx *commoncontext.MantisContext, req dto.ApiGlobalVarReq, user commondto.UserInfo) []models.ApiVariable {
	apiVariables := make([]models.ApiVariable, 0)
	gormx.Transaction(ctx, func() error {
		libId := req.LibId
		maxOrderNo := apiVariableDao.GetMaxOrderNo(ctx, 0, variable.GLOBAL_VAR.GetCode(), libId)
		addGlobalVars := req.AddGlobalVars
		if addGlobalVars != nil && len(addGlobalVars) != 0 {
			keySet := apiVariableDao.GetAllVarKeySet(ctx, req.LibId)
			param := models.ApiVariable{}
			param.IsDeleted = constants.DeleteNo
			param.Type = variable.GLOBAL_VAR.GetCode()
			param.LibId = libId
			varList := make([]models.ApiVariable, 0)
			gormx.SelectByParamStructX(ctx, &param, &varList)
			for _, variable := range varList {
				keySet.Add(variable.VarKey)
			}
			for _, v := range addGlobalVars {
				if keySet.Contain(v.VarKey) {
					err := xerror.New("变量名在当前接口库的环境下或全局环境中存在，请修改")
					logger.Logger.Errorf("%+v", err)
					return err
				}
				keySet.Add(v.VarKey)
				v.GmtCreated = times.Now()
				v.GmtModified = times.Now()
				v.Creator = user.AdAccount
				v.Modifier = user.AdAccount
				v.IsDeleted = constants.DeleteNo
				v.OrderNo = maxOrderNo + 1
				v.LibId = libId
				v.Type = variable.GLOBAL_VAR.GetCode()
				apiVariables = append(apiVariables, v)
			}
			gormx.InsertBatchX(ctx, apiVariables)
		}
		updateGlobalVars := req.UpdateGlobalVars
		updateVariables := a.updateVars(ctx, libId, user.AdAccount, 0, updateGlobalVars, variable.GLOBAL_VAR.GetCode())
		apiVariables = append(apiVariables, updateVariables...)
		a.deleteVars(ctx, req.DeletedIds, user.AdAccount)
		return nil
	})
	return apiVariables
}

func (a ApiEnvService) GetGlobalVars(ctx *commoncontext.MantisContext, libId int64) []models.ApiVariable {
	param := models.ApiVariable{}
	param.IsDeleted = constants.DeleteNo
	param.LibId = libId
	param.Type = variable.GLOBAL_VAR.GetCode()
	res := make([]models.ApiVariable, 0)
	gormx.SelectByParamStructX(ctx, &param, &res)
	return res
}

func (a ApiEnvService) GetCurrentVars(ctx *commoncontext.MantisContext, libId int64, envId int64) []commondto.CodeEnumDTO {
	vars := apiVariableDao.GetCurrentVars(ctx, libId, envId)
	res := make([]commondto.CodeEnumDTO, 0, len(vars))
	for _, v := range vars {
		enumDTO := commondto.CodeEnumDTO{
			Value: v.VarValue,
			Label: v.VarKey,
		}
		if v.Type == 1 {
			enumDTO.Extend = "全局变量"
		} else {
			enumDTO.Extend = "环境变量"
		}
		res = append(res, enumDTO)
	}
	return res
}

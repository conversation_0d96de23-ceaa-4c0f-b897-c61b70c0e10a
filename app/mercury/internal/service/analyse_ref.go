package service

import (
	"errors"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

var (
	apiDataModelService ApiDataModelService
	apiDocDirService    ApiDocDirService
)

// AnalyseSchemaRef 一些转换ref与refPah 的函数, 将ref转换为ref path，供前端使用
func AnalyseSchemaRef(ctx *commoncontext.MantisContext, schema *dto.ApiBaseSchema, outId int64) {
	if schema == nil {
		return
	}
	if schema.Properties != nil && len(schema.Properties) != 0 {
		for k, v := range schema.Properties {
			v.RefPath = dealWithRef(ctx, v.Ref)
			if v.Items != nil {
				v.Items.RefPath = dealWithRef(ctx, v.Items.Ref)
			} else if v.Position == constants.BaseSchemaOuterPos {
				dealWithRef2Propertied(ctx, &v, outId)
			} else {
				AnalyseSchemaRef(ctx, &v, outId)
			}
			schema.Properties[k] = v
		}
	}
	if schema.Items != nil {
		schema.Items.RefPath = dealWithRef(ctx, schema.Items.Ref)
	}
	if schema.Ref != "" {
		schema.RefPath = dealWithRef(ctx, schema.Ref)
	}
}

func dealWithRef(ctx *commoncontext.MantisContext, ref string) []int64 {
	res := make([]int64, 0)
	if ref == "" {
		return res
	}
	dataModel := apiDataModelService.GetApiDataModelByRef(ctx, ref)
	if dataModel.Id == 0 {
		return res
	}
	if dataModel.FolderId != 0 {
		folder := apiDocDirService.GetApiFolder(ctx, dataModel.FolderId)
		split := strings.Split(folder.Path, ",")
		for _, s := range split {
			if s == "" {
				continue
			}
			parseInt, err := strconv.ParseInt(s, 10, 64)
			if err != nil {
				logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in parse string to int64"))
			}
			res = append(res, parseInt)
		}
		res = append(res, folder.Id)
		res = append(res, dataModel.Id)
	} else {
		res = append(res, 0, dataModel.Id)
	}
	return res
}

func dealWithRef2Propertied(ctx *commoncontext.MantisContext, schema *dto.ApiBaseSchema, outId int64) {
	if schema.Ref == "" {
		return
	}
	dataModel := apiDataModelService.GetApiDataModelByRef(ctx, schema.Ref)
	if dataModel.Id == outId {
		logger.Logger.Panicf("%+v", errors.New("存在外层循环引用，添加失败"))
	}
	if dataModel.Id == 0 {
		return
	}
	modelSchema := dto.ApiBaseSchema{}
	jsonx.UnMarshal([]byte(dataModel.Content), &modelSchema)
	for k, v := range modelSchema.Properties {
		v.RefPath = dealWithRef(ctx, v.Ref)
		if v.Items != nil {
			v.Items.RefPath = dealWithRef(ctx, v.Items.Ref)
		} else if v.Position == constants.BaseSchemaOuterPos {
			dealWithRef2Propertied(ctx, &v, outId)
		} else {
			AnalyseSchemaRef(ctx, &v, outId)
		}
		modelSchema.Properties[k] = v
	}
	schema.Properties = modelSchema.Properties
}

// 将ref path转换为ref，以便级联删除
func AnalyseSchemaRefPath(ctx *commoncontext.MantisContext, schema *dto.ApiBaseSchema) {
	if schema == nil {
		return
	}
	if schema.Properties != nil && len(schema.Properties) != 0 {
		for k, v := range schema.Properties {
			if v.RefPath != nil {
				v.Ref = dealWithRefPath(ctx, v.RefPath)
			} else if v.Items != nil && v.Items.RefPath != nil {
				v.Items.Ref = dealWithRefPath(ctx, v.Items.RefPath)
			} else {
				AnalyseSchemaRefPath(ctx, &v)
			}
			schema.Properties[k] = v
		}
	}
	if schema.Items != nil && schema.Items.RefPath != nil {
		schema.Items.Ref = dealWithRefPath(ctx, schema.Items.RefPath)
	}
}

func dealWithRefPath(ctx *commoncontext.MantisContext, refPath []int64) string {
	if refPath == nil || len(refPath) == 0 {
		return ""
	}
	dataModelId := refPath[len(refPath)-1]
	model := apiDataModelDao.GetApiModelById(ctx, dataModelId)
	return strconv.FormatInt(model.LibId, 10) + "+" + model.Name
}

func GetDefaultValueByType(typeStr string) string {
	switch typeStr {
	case "引用", "object":
		return "{}"
	case "boolean":
		return "false"
	case "string":
		return `""`
	case "integer", "number":
		return "0"
	case "array":
		return "[]"
	default:
		return "null"
	}
}

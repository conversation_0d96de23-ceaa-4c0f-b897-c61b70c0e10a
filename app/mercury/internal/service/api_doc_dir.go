package service

import (
	"fmt"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	set "github.com/duke-git/lancet/v2/datastructure/set"
	"github.com/duke-git/lancet/v2/xerror"
)

type ApiDocDirService struct{}

func (a ApiDocDirService) GetApiFolder(ctx *commoncontext.MantisContext, folderId int64) models.ApiFolder {
	folder := models.ApiFolder{}
	folder.Id = folderId
	folder.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &folder)
	return folder
}

func (a ApiDocDirService) GetApiGroups(ctx *commoncontext.MantisContext, libId int64, folderId int64) []commondto.CodeEnumDTO {
	apiFolders := make([]models.ApiFolder, 0)
	paramBuilder := gormx.NewParamBuilder().Select("id, name, path").Eq("lib_id", libId).Eq("type", "api").Eq("is_deleted", constants.DeleteNo).OrderByAsc("path")
	if folderId != 0 {
		paramBuilder.Like("path", `%,`+fmt.Sprintf(`%d`, folderId)+`,%`)
	}
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiFolders)
	codeEnumList := make([]commondto.CodeEnumDTO, 0, len(apiFolders))
	for _, apiFolder := range apiFolders {
		folderIds := strings.Split(apiFolder.Path, ",")
		flag := false
		var str string
		for _, foldId := range folderIds {
			if foldId == "" {
				break
			}
			id, err := strconv.ParseInt(foldId, 10, 64)
			if err != nil {
				logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in convert string to int64"))
			}
			if folderId == id {
				flag = true
			}
			if flag {
				str += "/"
				if id == 0 {
					str += "根目录"
				} else {
					str += apiFolder.Name
				}
			}
		}
		codeEnumList = append(codeEnumList, commondto.CodeEnumDTO{
			Value: apiFolder.Id,
			Label: str,
		})
	}
	if folderId != 0 {
		folder := models.ApiFolder{}
		folder.Id = folderId
		gormx.SelectOneByConditionX(ctx, &folder)
		codeEnumList = append(codeEnumList, commondto.CodeEnumDTO{Value: folder.Id, Label: folder.Name})
	}
	return codeEnumList
}

func (a ApiDocDirService) DeleteApis(ctx *commoncontext.MantisContext, ids []int64, user commondto.UserInfo) {
	apiDocBase := models.ApiDocBase{}
	paramBuilder := gormx.NewParamBuilder().Model(&apiDocBase).In("id", ids).Eq("is_deleted", constants.DeleteNo)
	apiDocBase.Modifier = user.AdAccount
	apiDocBase.GmtModified = times.Now()
	apiDocBase.IsDeleted = constants.DeleteYes
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, apiDocBase)
}

func (a ApiDocDirService) UpdateDocs(ctx *commoncontext.MantisContext, batchDTO dto.BatchDTO, user commondto.UserInfo) {
	apiDocBase := models.ApiDocBase{}
	paramBuilder := gormx.NewParamBuilder().Model(&apiDocBase).Eq("is_deleted", constants.DeleteNo).In("id", batchDTO.ApiIds)
	if batchDTO.StatusId != 0 {
		apiDocBase.Status = batchDTO.StatusId
	}
	apiDocBase.ResponsibleId = user.AdAccount
	apiDocBase.GmtModified = times.Now()
	apiDocBase.Modifier = user.AdAccount
	if batchDTO.ResponsibleId != "" {
		apiDocBase.ResponsibleId = batchDTO.ResponsibleId
	}
	gormx.UpdateBatchByParamBuilderX(ctx, paramBuilder, apiDocBase)
}

func (a ApiDocDirService) AddLabels(ctx *commoncontext.MantisContext, batchDTO dto.BatchDTO, user commondto.UserInfo) {
	a.updateLabels(ctx, batchDTO, user, true)
}

func (a ApiDocDirService) DeleteLabels(ctx *commoncontext.MantisContext, batchDTO dto.BatchDTO, user commondto.UserInfo) {
	a.updateLabels(ctx, batchDTO, user, false)
}

func (a ApiDocDirService) updateLabels(ctx *commoncontext.MantisContext, batchDTO dto.BatchDTO, user commondto.UserInfo, addFlag bool) {
	paramBuilder := gormx.NewParamBuilder().Select("bind_label_id, id").In("id", batchDTO.ApiIds).Eq("is_deleted", constants.DeleteNo)
	apiDocBaseList := make([]models.ApiDocBase, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &apiDocBaseList)
	for _, apiDocBase := range apiDocBaseList {
		bindLabelId := ""
		if apiDocBase.BindLabelId != "" {
			bindLabelId = apiDocBase.BindLabelId
		}
		labelIdSet := set.New[string]()
		for _, labelId := range strings.Split(bindLabelId, ",") {
			if labelId == "" {
				continue
			}
			labelIdSet.Add(labelId)
		}
		labelIds := batchDTO.LabelIds
		if labelIds != nil {
			if addFlag {
				for _, labelId := range labelIds {
					labelIdSet.Add(strconv.FormatInt(labelId, 10))
				}
			} else {
				for _, labelId := range labelIds {
					labelIdSet.Delete(strconv.FormatInt(labelId, 10))
				}
			}
		}
		if labelIdSet.Size() != 0 {
			bindLabelIdList := ""
			for _, labelId := range labelIdSet.ToSlice() {
				bindLabelIdList += labelId + ","
			}
			apiDocBase.BindLabelId = bindLabelIdList
		}
		apiDocBase.GmtModified = times.Now()
		apiDocBase.Modifier = user.AdAccount
		gormx.UpdateOneByConditionX(ctx, &apiDocBase)
	}
}

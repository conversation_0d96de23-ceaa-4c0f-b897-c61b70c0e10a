package service

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

type ApiDocHistoryService struct{}

func (a ApiDocHistoryService) SaveToHistory(ctx *commoncontext.MantisContext, docDTO dto.ApiDocSaveDTO, user commondto.UserInfo) {
	history := a.ConvertDoc2DocHistory(docDTO, user)
	history.ResponseList = string(jsonx.Marshal(&(docDTO.ApiResponseList)))
	history.ResponseCaseList = string(jsonx.Marshal(&(docDTO.ApiResponseCaseList)))
	gormx.InsertUpdateOneX(ctx, &history)
}

func (a ApiDocHistoryService) SaveBatchHistory(ctx *commoncontext.MantisContext, batch []models.ApiDocBaseHistory) {
	gormx.InsertBatchX(ctx, &batch)
}

func (a ApiDocHistoryService) ConvertDoc2DocHistory(dto dto.ApiDocSaveDTO, user commondto.UserInfo) models.ApiDocBaseHistory {
	history := models.ApiDocBaseHistory{
		ApiDocBaseId: dto.ApiDocBase.Id,
	}
	history.ApiDocBase = dto.ApiDocBase
	if dto.ApiDocBase.Request == "" {
		history.Request = string(jsonx.Marshal(&(dto.DocRequestDTO)))
	}
	history.Id = 0
	history.ResponseList = string(jsonx.Marshal(&(dto.ApiResponseList)))
	history.ResponseCaseList = string(jsonx.Marshal(&(dto.ApiResponseCaseList)))
	history.Creator = user.AdAccount
	history.Modifier = user.AdAccount
	history.GmtCreated = times.Now()
	history.GmtModified = times.Now()
	return history
}

func (a ApiDocHistoryService) GetHistoryListByDocId(ctx *commoncontext.MantisContext, docId int64) []models.ApiDocBaseHistory {
	paramBuilder := gormx.NewParamBuilder().Model(&models.ApiDocBaseHistory{}).Select("id, creator, gmt_created").Eq("api_doc_base_id", docId).Eq("is_deleted", constants.DeleteNo).OrderByDesc("gmt_created")
	res := make([]models.ApiDocBaseHistory, 0)
	gormx.SelectByParamBuilderX(ctx, paramBuilder, &res)
	userAccountNameMap := make(map[string]string)
	for _, history := range res {
		userAccountNameMap[history.Creator] = ""
	}
	usercenterRemoteApi.GetUserAdAccountNameMap(userAccountNameMap, utils.IDString(ctx.User.CompanyID))
	for i := 0; i < len(res); i++ {
		res[i].CreatorName = userAccountNameMap[res[i].Creator]
	}
	return res
}

func (a ApiDocHistoryService) GetDetailById(ctx *commoncontext.MantisContext, id int64, projectNo string) dto.ApiDocSaveDTO {
	history := models.ApiDocBaseHistory{}
	history.Id = id
	history.IsDeleted = constants.DeleteNo
	gormx.SelectOneByConditionX(ctx, &history)
	base := models.ApiDocBase{}
	base.Id = history.ApiDocBaseId
	gormx.SelectOneByConditionX(ctx, &base)
	creator, err := usercenterRemoteApi.GetUserInfo(history.Creator, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	modifier, err := usercenterRemoteApi.GetUserInfo(base.Creator, utils.IDString(ctx.User.CompanyID))
	if err != nil {
		logger.Logger.Panic(err)
	}
	history.ModifierName = creator.Name
	history.CreatorName = modifier.Name
	return a.ConvertModel2DTO(ctx, history)
}

func (a ApiDocHistoryService) ConvertModel2DTO(ctx *commoncontext.MantisContext, apiDocById models.ApiDocBaseHistory) dto.ApiDocSaveDTO {
	var apiDocSaveDTO dto.ApiDocSaveDTO
	if apiDocById.Status != 0 {
		apiDocById.StatusName = apiStatusDao.GetApiStatusById(ctx, apiDocById.Status).Name
	}
	if apiDocById.ResponsibleId != "" {
		user, err := usercenterRemoteApi.GetUserInfo(apiDocById.ResponsibleId, utils.IDString(ctx.User.CompanyID))
		if err != nil {
			logger.Logger.Panic(err)
		}
		apiDocById.ResponsibleName = user.Name
	}
	if apiDocById.BindLabelId != "" {
		tags := cubeRemoteApi.GetTagsByIds(ctx, apiDocById.BindLabelId)
		apiDocById.Labels = tags
	}
	if apiDocById.Request != "" {
		requestDTO := dto.ApiDocRequestDTO{}
		jsonx.UnMarshal([]byte(apiDocById.Request), &requestDTO)
		if requestDTO.RequestBody != nil && requestDTO.RequestBody.JsonSchema != nil {
			schema := requestDTO.RequestBody.JsonSchema
			AnalyseSchemaRef(ctx, schema, -1)
			requestDTO.RequestBody.JsonSchema = schema
		}
		apiDocSaveDTO.DocRequestDTO = requestDTO
	}
	apiDocById.Id = apiDocById.ApiDocBaseId
	apiDocSaveDTO.ApiDocBase = apiDocById.ApiDocBase
	apiResponses := make([]dto.ApiResponseDTO, 0)
	if apiDocById.ResponseList != "" {
		jsonx.UnMarshal([]byte(apiDocById.ResponseList), &apiResponses)
	}
	apiDocSaveDTO.ApiResponseList = apiResponses
	apiResponseCaseList := make([]models.ApiResponseCase, 0)
	if apiDocById.ResponseCaseList != "" {
		jsonx.UnMarshal([]byte(apiDocById.ResponseCaseList), &apiResponseCaseList)
	}
	apiDocSaveDTO.ApiResponseCaseList = apiResponseCaseList
	return apiDocSaveDTO
}

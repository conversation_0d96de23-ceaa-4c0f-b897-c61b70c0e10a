package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/tree_node"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ApiTreeViewRequest struct {
	Type  string `path:"type"`
	LibId int64  `path:"libId"`
}

type ApiTreeLibIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type ApiTreeRemoveNodeReq struct {
	LibId int64 `path:"libId"`
	Id    int64 `path:"id"`
}

type ApiTreeController struct {
	*controller.BaseController
}

var DefaultApiTreeController ApiTreeController

var apiTreeService tree_node.ApiTreeService

func (a *ApiTreeController) ViewTreeHandler(req ApiTreeViewRequest) {
	typeStr := req.Type
	libId := req.LibId
	a.ResSuccessResult(apiTreeService.View(a.MantisContext, typeStr, libId))
}

func (a *ApiTreeController) AddApiFolderHandler(apiFolderDTO dto.ApiFolderDTO, req ApiTreeLibIdInPathReq) {
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiFolderDTO.LibId = libId
	a.ResSuccessResult(apiTreeService.AddFolder(a.MantisContext, apiFolderDTO, userInfo))
}

func (a *ApiTreeController) AddModelFolderHandler(apiFolderDTO dto.ApiFolderDTO, req ApiTreeLibIdInPathReq) {
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiFolderDTO.LibId = libId
	a.ResSuccessResult(apiTreeService.AddFolder(a.MantisContext, apiFolderDTO, userInfo))
}

func (a *ApiTreeController) AddCaseFolderHandler(apiFolderDTO dto.ApiFolderDTO, req ApiTreeLibIdInPathReq) {
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiFolderDTO.LibId = libId
	a.ResSuccessResult(apiTreeService.AddFolder(a.MantisContext, apiFolderDTO, userInfo))
}

func (a *ApiTreeController) UpdateApiFolderHandler(apiFolderDTO dto.ApiFolderDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.UpdateFolder(a.MantisContext, apiFolderDTO, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) UpdateModelFolderHandler(apiFolderDTO dto.ApiFolderDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.UpdateFolder(a.MantisContext, apiFolderDTO, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) UpdateCaseFolderHandler(apiFolderDTO dto.ApiFolderDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.UpdateFolder(a.MantisContext, apiFolderDTO, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveApiNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeApi
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveApiFolderNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeFolder
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveModelFolderNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeFolder
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveCaseFolderNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeFolder
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveModelNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeModel
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) RemoveCaseNodeHandler(req ApiTreeRemoveNodeReq) {
	libId := req.LibId
	id := req.Id
	nodeType := constants.NodeTypeCase
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.RemoveNode(a.MantisContext, libId, nodeType, id, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) ApiNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeApi
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) ApiFolderNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeFolder
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) ModelFolderNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeFolder
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) CaseFolderNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeFolder
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) ModelNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeModel
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) CaseNodeCopyHandler(req ApiTreeRemoveNodeReq) {
	id := req.Id
	nodeType := constants.NodeTypeCase
	libId := req.LibId
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiTreeService.Copy(a.MantisContext, nodeType, id, libId, userInfo, a.GetProjectNo()))
}

func (a *ApiTreeController) ApiTreeOrderingHandler(ordering dto.TreeOrdering) {
	ordering.TreeType = constants.FolderTypeApi
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.Ordering(a.MantisContext, ordering, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) ModelTreeOrderingHandler(ordering dto.TreeOrdering) {
	ordering.TreeType = constants.FolderTypeModel
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.Ordering(a.MantisContext, ordering, userInfo)
	a.ResSuccess()
}

func (a *ApiTreeController) CaseTreeOrderingHandler(ordering dto.TreeOrdering) {
	ordering.TreeType = constants.FolderTypeCase
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiTreeService.Ordering(a.MantisContext, ordering, userInfo)
	a.ResSuccess()
}

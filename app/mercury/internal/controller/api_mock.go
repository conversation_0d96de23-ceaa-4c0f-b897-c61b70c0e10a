package controller

import (
	"io"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/mocker"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type ApiMockDocIdInPathReq struct {
	DocId int64 `path:"docId"`
}

type ApiMockIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiMockUpdateStatusReq struct {
	Id     int64  `path:"id"`
	Status string `path:"status"`
}

type ApiMockDragReq struct {
	DragId int64 `json:"dragId"`
	DropId int64 `json:"dropId"`
	Pos    int64 `json:"pos"`
}

type ApiMockController struct {
	*controller.BaseController
}

var DefaultApiMockController ApiMockController

var apiMockService mocker.ApiMockService

func (a *ApiMockController) GetMocksByDocId(req ApiMockDocIdInPathReq) {
	docId := req.DocId
	a.ResSuccessResult(apiMockService.GetMocksByDocId(a.MantisContext, docId))
}

func (a *ApiMockController) GetMockById(req ApiMockIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiMockService.GetMockDetailById(a.MantisContext, id))
}

func (a *ApiMockController) GetMockUrl(req ApiMockDocIdInPathReq) {
	res := configs.Config.Domain.Cube + "/magic/api/mercury/v1/mock/mockApi/"
	res += strconv.FormatInt(req.DocId, 10)
	a.ResSuccessResult(res)
}

func (a *ApiMockController) DeleteMockById(req ApiMockIdInPathReq) {
	id := req.Id
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiMockService.DeleteMock(a.MantisContext, id, user)
	a.ResSuccess()
}

func (a *ApiMockController) CopyMockById(req ApiMockIdInPathReq) {
	id := req.Id
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiMockService.CopyMock(a.MantisContext, id, user)
	a.ResSuccess()
}

func (a *ApiMockController) SaveMock(mockDTO dto.MockDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiMockService.SaveMock(a.MantisContext, mockDTO, user)
	a.ResSuccess()
}

func (a *ApiMockController) DragMock(req ApiMockDragReq) {
	apiMockService.DragMock(a.MantisContext, req.DragId, req.DropId, req.Pos)
	a.ResSuccess()
}

func (a *ApiMockController) UpdateStatus(req ApiMockUpdateStatusReq) {
	id := req.Id
	status, err := strconv.ParseBool(req.Status)
	if err != nil {
		logger.Logger.Panic("error in getting param")
	}
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiMockService.UpdateStatus(a.MantisContext, id, status, user)
	a.ResSuccess()
}

func (a *ApiMockController) Mock(req ApiMockDocIdInPathReq) {
	docId := req.DocId
	apiMockService.Mock(a.MantisContext, a.Request, a.ResponseWriter, docId)
}

func (a *ApiMockController) GetMockParamDataTree() {
	a.ResSuccessResult(apiMockService.GetMockParamDataTree(a.MantisContext))
}

func (a *ApiMockController) GetMockPreview() {
	bytes, err := io.ReadAll(a.Body)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	a.ResSuccessResult(apiMockService.GetMockPreview(string(bytes)))
}

package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type GetHistoryListReq struct {
	DocId int64 `path:"docId"`
}

type GetHistoryDetailReq struct {
	Id int64 `path:"id"`
}

type ApiDocHistoryController struct {
	*controller.BaseController
}

var DefaultApiDocHistoryController ApiDocHistoryController

var apiDocHistoryService service.ApiDocHistoryService

func (a *ApiDocHistoryController) GetHistoryListByDocId(req GetHistoryListReq) {
	listByDocId := apiDocHistoryService.GetHistoryListByDocId(a.<PERSON>ontext, req.DocId)
	a.ResSuccessResult(listByDocId)
}

func (a *ApiDocHistoryController) GetHistoryDetailById(req GetHistoryDetailReq) {
	detail := apiDocHistoryService.GetDetailById(a.<PERSON>, req.Id, a.GetProjectNo())
	a.ResSuccessResult(detail)
}

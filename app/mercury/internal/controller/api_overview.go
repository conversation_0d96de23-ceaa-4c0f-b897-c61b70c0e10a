package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type ApiOverviewLibIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type ApiOverviewController struct {
	*controller.BaseController
}

var DefaultApiOverviewController ApiOverviewController

var apiOverviewService service.ApiOverviewService

func (a *ApiOverviewController) GetData(req ApiOverviewLibIdInPathReq) {
	libId := req.LibId
	a.ResSuccessResult(map[string]interface{}{
		"apiCount":            apiOverviewService.GetApiCount(a.MantisContext, libId),
		"passRecordCount":     apiOverviewService.GetPassRecordCount(a.MantisContext, libId),
		"passRecordPercent":   apiOverviewService.GetPassRecordPercent(a.MantisContext, libId),
		"completedApiPercent": apiOverviewService.GetCompletedApiPercent(a.MantisContext, libId),
		"shareCount":          apiOverviewService.GetShareCount(a.MantisContext, libId),
		"apiCoverPercent":     apiOverviewService.GetApiCoverPercent(a.MantisContext, libId),
	})
}

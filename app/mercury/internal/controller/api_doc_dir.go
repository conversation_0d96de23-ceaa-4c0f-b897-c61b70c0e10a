package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type ApiDocDirGroupSearchReq struct {
	LibId    int64 `schema:"libId"`
	FolderId int64 `schema:"folderId"`
}

type ApiDocDirController struct {
	*controller.BaseController
}

var DefaultApiDocDirController ApiDocDirController

var apiDocDirService service.ApiDocDirService

func (a *ApiDocDirController) GetApiGroupsHandler(req ApiDocDirGroupSearchReq) {
	a.ResSuccessResult(apiDocDirService.GetApiGroups(a.MantisContext, req.LibId, req.FolderId))
}

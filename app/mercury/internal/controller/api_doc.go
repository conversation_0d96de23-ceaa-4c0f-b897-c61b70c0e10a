package controller

import (
	"io"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ApiDocIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiDocPageSearchReq struct {
	FolderId int64  `schema:"folderId"`
	LibId    int64  `schema:"libId"`
	Search   string `schema:"search"`
	Method   string `schema:"method"`
	Page     int64  `schema:"page"`
	PageSize int64  `schema:"pageSize"`
}

type ApiDocUpdateResponseIdReq struct {
	Ids           []int64 `json:"ids"`
	ResponsibleId string  `json:"responsibleId"`
}

type ApiDocUpdateStatusReq struct {
	Status int64 `json:"status"`
}

type ApiDocUpdateBatchStatusReq struct {
	DocIds []int64 `json:"docIds"`
	Status int64   `json:"status"`
}

type ApiDocAppIdInPathReq struct {
	AppId int64 `path:"appId"`
}

type ApiDocController struct {
	*controller.BaseController
}

var DefaultApiDocController ApiDocController

var apiDocService service.ApiDocService

func (a *ApiDocController) GetDocDetailByIdHandler(req ApiDocIdInPathReq) {
	apiDocSaveDTO := apiDocService.GetDocDetailById(a.MantisContext, req.Id, a.GetProjectNo())
	a.ResSuccessResult(apiDocSaveDTO)
}

func (a *ApiDocController) RemoveDocByIdHandler(req ApiDocIdInPathReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.RemoveDocById(a.MantisContext, req.Id, userInfo)
	a.ResSuccess()
}

func (a *ApiDocController) RemoveDocByIds() error {
	ids := make([]int64, 0)
	body, err := a.GetBody()
	if err != nil {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
	defer body.Close()
	b, err := io.ReadAll(body)
	if err != nil {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
	jsonx.UnMarshal(b, &ids)
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.RemoveBatchByIds(a.MantisContext, ids, user)
	a.ResSuccess()
	return nil
}

func (a *ApiDocController) SaveDocHandler(saveDTO dto.ApiDocSaveDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	id := apiDocService.SaveDoc(a.MantisContext, saveDTO, userInfo, a.GetProjectNo())
	a.ResSuccessResult(id)
}

func (a *ApiDocController) UpdateDocHandler(saveDTO dto.ApiDocSaveDTO) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	id := apiDocService.SaveDoc(a.MantisContext, saveDTO, userInfo, a.GetProjectNo())
	a.ResSuccessResult(id)
}

func (a *ApiDocController) SearchDocPage(req ApiDocPageSearchReq) {
	a.ResSuccessResult(apiDocService.SearchByFolderId(a.MantisContext, req.FolderId,
		req.LibId, req.Search, req.Method, gormx.PageRequest{
			Page:     req.Page,
			PageSize: req.PageSize,
		}))
}

func (a *ApiDocController) UpdateResponseId(req ApiDocUpdateResponseIdReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.UpdateResponseId(a.MantisContext, req.Ids, req.ResponsibleId, user)
	a.ResSuccess()
}

func (a *ApiDocController) UpdateStatus(idReq ApiDocIdInPathReq, req ApiDocUpdateStatusReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.UpdateStatus(a.MantisContext, idReq.Id, req.Status, user)
	a.ResSuccess()
}

func (a *ApiDocController) UpdateBatchStatus(req ApiDocUpdateBatchStatusReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.UpdateBatchStatus(a.MantisContext, req.DocIds, req.Status, user)
	a.ResSuccess()
}

func (a *ApiDocController) Convert2Jupiter(req ApiDocIdInPathReq) {
	a.ResSuccessResult(apiDocService.Convert2JupiterDTO(a.MantisContext, req.Id))
}

func (a *ApiDocController) Sync2Jupiter(req ApiDocIdInPathReq) {
	apiDocService.Sync2Jupiter(a.MantisContext, req.Id)
	a.ResSuccess()
}

func (a *ApiDocController) Save2Jupiter(req ApiDocIdInPathReq) error {
	doc := make(map[string]any)
	b, err := io.ReadAll(a.Request.Body)
	if err != nil {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
	jsonx.UnMarshal(b, &doc)
	apiDocService.Save2Jupiter(a.MantisContext, req.Id, doc)
	a.ResSuccess()
	return nil
}

func (a *ApiDocController) SaveBatch2Jupiter(req ApiDocAppIdInPathReq) error {
	spaceIdStr := a.Request.Header.Get("spaceId")
	spaceId, err := strconv.ParseInt(spaceIdStr, 10, 64)
	if err != nil {
		return err
	}
	ids := make([]int64, 0)
	b, err := io.ReadAll(a.Request.Body)
	if err != nil {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
	jsonx.UnMarshal(b, &ids)
	apiDocService.SaveBatch2JupiterByIds(a.MantisContext, ids, req.AppId, spaceId)
	a.ResSuccess()
	return nil
}

func (a *ApiDocController) SyncBatch2Jupiter() error {
	ids := make([]int64, 0)
	b, err := io.ReadAll(a.Request.Body)
	if err != nil {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
	jsonx.UnMarshal(b, &ids)
	apiDocService.SyncBatch2JupiterByIds(a.MantisContext, ids)
	a.ResSuccess()
	return nil
}

func (a *ApiDocController) UnbindJupiter(req ApiDocIdInPathReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocService.OneDocUnbindJupiter(a.MantisContext, req.Id, user)
	a.ResSuccess()
}

package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/share"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ApiShareIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiShareLibIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type ApiShareAppIdInPathReq struct {
	AppId string `path:"appId"`
}

type ApiShareController struct {
	*controller.BaseController
}

var DefaultApiShareController ApiShareController

var apiShareService share.ApiShareService

func (a *ApiShareController) SaveShare(shareDTO dto.ApiShareDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiShareService.SaveShare(a.MantisContext, shareDTO, user)
	a.ResSuccess()
}

func (a *ApiShareController) UpdateShare(shareDTO dto.ApiShareDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiShareService.SaveShare(a.MantisContext, shareDTO, user)
	a.ResSuccess()
}

func (a *ApiShareController) GetShareById(req ApiShareIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiShareService.GetShareById(a.MantisContext, id))
}

func (a *ApiShareController) GetShares(req ApiShareLibIdInPathReq) {
	libId := req.LibId
	a.ResSuccessResult(apiShareService.GetShares(a.MantisContext, libId))
}

func (a *ApiShareController) RemoveShare(req ApiShareIdInPathReq) {
	id := req.Id
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiShareService.RemoveShare(a.MantisContext, id, user)
	a.ResSuccess()
}

func (a *ApiShareController) GetShareData(req ApiShareIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiShareService.GetShareData(a.MantisContext, id))
}

func (a *ApiShareController) GetDataInHub(req ApiShareAppIdInPathReq) {
	a.ResSuccessResult(apiShareService.GetDataInHubByAppId(a.MantisContext, req.AppId))
}

func (a *ApiShareController) GetDocDetailByIdHandler(req ApiShareIdInPathReq) {
	apiDocSaveDTO := apiDocService.GetDocDetailById(a.MantisContext, req.Id, a.GetProjectNo())
	a.ResSuccessResult(apiDocSaveDTO)
}

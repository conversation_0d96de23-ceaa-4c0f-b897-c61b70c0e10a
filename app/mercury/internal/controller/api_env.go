package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ApiEnvLibraryIdInSchemaReq struct {
	LibId int64 `schema:"libId"`
}

type ApiEnvLibraryIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type ApiEnvIdInSchemaReq struct {
	EnvId int64 `schema:"envId"`
}

type ApiEnvIdInPathReq struct {
	EnvId int64 `path:"envId"`
}

type ApiEnvController struct {
	*controller.BaseController
}

var DefaultApiEnvController ApiEnvController

var apiEnvService service.ApiEnvService

func (a *ApiEnvController) GetLibraryEnvsHandler(req ApiEnvLibraryIdInSchemaReq) {
	a.ResSuccessResult(apiEnvService.GetLibraryEnvs(a.MantisContext, req.LibId))
}

func (a *ApiEnvController) GetEnvVarsHandler(req ApiEnvIdInSchemaReq) {
	a.ResSuccessResult(apiEnvService.GetEnvVars(a.MantisContext, req.EnvId))
}

func (a *ApiEnvController) DeleteEnvHandler(req ApiEnvIdInSchemaReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiEnvService.DeleteEnv(a.MantisContext, req.EnvId, userInfo)
	a.ResSuccess()
}

func (a *ApiEnvController) SaveEnv(req dto.ApiEnvReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiEnvService.UpdateEnv(a.MantisContext, req, userInfo))
}

func (a *ApiEnvController) UpdateEnv(req dto.ApiEnvReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiEnvService.UpdateEnv(a.MantisContext, req, userInfo))
}

func (a *ApiEnvController) SaveGlobalVars(req dto.ApiGlobalVarReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiEnvService.SaveGlobalVars(a.MantisContext, req, userInfo))
}

func (a *ApiEnvController) GetGlobalVars(req ApiEnvLibraryIdInSchemaReq) {
	a.ResSuccessResult(apiEnvService.GetGlobalVars(a.MantisContext, req.LibId))
}

func (a *ApiEnvController) GetVars(req ApiEnvLibraryIdInPathReq, envReq ApiEnvIdInPathReq) {
	a.ResSuccessResult(apiEnvService.GetCurrentVars(a.MantisContext, req.LibId, envReq.EnvId))
}

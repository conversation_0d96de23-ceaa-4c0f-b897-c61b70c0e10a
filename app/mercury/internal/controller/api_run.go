package controller

import (
	"io"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type ApiRunDocIdInPathReq struct {
	DocId int64 `path:"docId"`
}

type ApiRunLibIdInSchemaReq struct {
	LibId int64 `schema:"libId"`
}

type ApiRunIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiRunSaveToApiReq struct {
	FolderId int64  `path:"folderId"`
	Name     string `path:"name"`
}

type ApiRunController struct {
	*controller.BaseController
}

var DefaultApiRunController ApiRunController

var apiRunService service.ApiRunService

func (a *ApiRunController) GetRunByDocIdHandler(req ApiRunDocIdInPathReq) {
	docId := req.DocId
	a.ResSuccessResult(apiRunService.GetApiDocRuntimeByDocId(a.MantisContext, docId))
}

func (a *ApiRunController) SaveRunDocHandler(runtimeDTO dto.ApiRuntimeDTO) {
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiRunService.SaveApiDocRuntime(a.MantisContext, runtimeDTO, info)
	a.ResSuccess()
}

func (a *ApiRunController) SyncRunDocHandler(req ApiRunDocIdInPathReq) {
	docId := req.DocId
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiRunService.SyncFromDocBase(a.MantisContext, docId, info)
	a.ResSuccess()
}

func (a *ApiRunController) GetAssertTypeHandler() {
	a.ResSuccessResult(apiRunService.GetAssertTypeList())
}

func (a *ApiRunController) RunApi(runtimeDTO dto.ApiRuntimeDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	a.ResSuccessResult(apiRunService.RunApi(a.MantisContext, runtimeDTO, user))
}

func (a *ApiRunController) GetRecordsList(req ApiRunLibIdInSchemaReq) {
	libId := req.LibId
	a.ResSuccessResult(apiRunService.GetRunRecords(a.MantisContext, libId))
}

func (a *ApiRunController) GetRecord(req ApiRunIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiRunService.GetRunRecord(a.MantisContext, id))
}

func (a *ApiRunController) DeleteRecords() {
	bytes, err := io.ReadAll(a.Body)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	ids := make([]int64, 0)
	jsonx.UnMarshal(bytes, &ids)
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiRunService.DeleteByIds(a.MantisContext, ids, user)
	a.ResSuccess()
}

func (a *ApiRunController) SaveToApiDoc(runtimeDTO dto.ApiRuntimeDTO, req ApiRunSaveToApiReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	folderId := req.FolderId
	name := req.Name
	apiRunService.SaveToApiDoc(a.MantisContext, runtimeDTO, user, folderId, name)
	a.ResSuccess()
}

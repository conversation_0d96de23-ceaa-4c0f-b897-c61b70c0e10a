package controller

import (
	"fmt"
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/doc_export"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type ApiExportReq struct {
	ShareId int64  `path:"shareId"`
	DocType string `path:"docType"`
}

type ApiExportController struct {
	*controller.BaseController
}

var DefaultApiExportController ApiExportController

var apiDocExportService doc_export.ApiDocExportService

func (a *ApiExportController) Export(exportDTO dto.ApiExportDTO) {
	export, fileName := apiDocExportService.Export(a.MantisContext, exportDTO, a.GetProjectNo())
	contentType := "application/octet-stream"
	a.ResponseWriter.Header().Set("Content-Type", contentType)
	fileName = url.QueryEscape(fileName)
	a.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	a.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	a.ResponseWriter.Write([]byte(export))
}

func (a *ApiExportController) ExportByShareId(req ApiExportReq) {
	var export, fileName string
	if a.Request.Header.Get(commonconstants.HubUrlKey) != "" {
		export, fileName = apiDocExportService.ExportInHub(a.MantisContext, req.ShareId, req.DocType, a.GetProjectNo())
	} else {
		export, fileName = apiDocExportService.ExportByShare(a.MantisContext, req.ShareId, req.DocType, a.GetProjectNo())
	}
	contentType := "application/octet-stream"
	a.ResponseWriter.Header().Set("Content-Type", contentType)
	fileName = url.QueryEscape(fileName)
	a.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	a.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	a.ResponseWriter.Write([]byte(export))
}

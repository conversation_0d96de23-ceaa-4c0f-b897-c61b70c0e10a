package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type UpdateDataModelReq struct {
	Id int64 `path:"id"`
}

type RemoveDataModelReq struct {
	Id int64 `path:"id"`
}

type GetDataModelDetailReq struct {
	Id    int64 `path:"id"`
	OutId int64 `path:"outId"`
}

type ApiDataModelController struct {
	*controller.BaseController
}

var DefaultApiDataModelController ApiDataModelController

var apiDataModelService service.ApiDataModelService

func (a *ApiDataModelController) AddDataModelHandler(req dto.ApiDataModelDTO) {
	apiDataModelService.InsertAndUpdateApiDataModel(a.MantisContext, &req, a.GetUser())
	a.ResSuccess()
}

func (a *ApiDataModelController) UpdateDataModelHandler(req UpdateDataModelReq, data dto.ApiDataModelDTO) {
	data.Id = req.Id
	apiDataModelService.InsertAndUpdateApiDataModel(a.MantisContext, &data, a.GetUser())
	a.ResSuccess()
}

func (a *ApiDataModelController) RemoveDataModelHandler(req RemoveDataModelReq) {
	apiDataModelService.RemoveDataModel(a.MantisContext, req.Id, a.GetUser())
	a.ResSuccess()
}

func (a *ApiDataModelController) GetDataModelDetailByIdHandler(req GetDataModelDetailReq) {
	dataModel := apiDataModelService.GetApiModelById(a.MantisContext, req.Id, req.OutId)
	a.ResSuccessResult(dataModel)
}

package controller

import (
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ApiDocQuickIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiDocQuickSave2DocReq struct {
	FolderId int64  `path:"folderId"`
	Name     string `path:"name"`
}

type ApiDocQuickController struct {
	*controller.BaseController
}

var DefaultApiDocQuickController ApiDocQuickController

var apiDocQuickService service.ApiDocQuickRequestService

func (a *ApiDocQuickController) SaveQuickRequestHandler(docDTO dto.ApiQuickDocDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocQuickService.SaveDocQuickRequest(a.MantisContext, docDTO, user)
	a.ResSuccess()
}

func (a *ApiDocQuickController) UpdateQuickRequestHandler(docDTO dto.ApiQuickDocDTO) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocQuickService.SaveDocQuickRequest(a.MantisContext, docDTO, user)
	a.ResSuccess()
}

func (a *ApiDocQuickController) RemoveById(req ApiDocQuickIdInPathReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocQuickService.RemoveById(a.MantisContext, req.Id, user)
	a.ResSuccess()
}

func (a *ApiDocQuickController) GetById(req ApiDocQuickIdInPathReq) {
	a.ResSuccessResult(apiDocQuickService.GetDetailById(a.MantisContext, req.Id))
}

func (a *ApiDocQuickController) SaveToApi(docDTO dto.ApiQuickDocDTO, req ApiDocQuickSave2DocReq) {
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	unescape, err := url.PathUnescape(req.Name)
	if err != nil {
		logger.Logger.Panicf("error in unencode name")
	}
	apiDocQuickService.SaveToApi(a.MantisContext, docDTO, user, req.FolderId, unescape)
	a.ResSuccess()
}

package controller

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeApiCommonController(rw http.ResponseWriter, req *http.Request) (*ApiCommonController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiCommonController := &ApiCommonController{
		baseController,
	}
	return apiCommonController, nil
}

func InitializeApiDataModelController(rw http.ResponseWriter, req *http.Request) (*ApiDataModelController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiDataModelController := &ApiDataModelController{
		BaseController: baseController,
	}
	return apiDataModelController, nil
}

func InitializeApiDocController(rw http.ResponseWriter, req *http.Request) (*ApiDocController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiDocController := &ApiDocController{
		BaseController: baseController,
	}
	return apiDocController, nil
}

func InitializeApiDocDirController(rw http.ResponseWriter, req *http.Request) (*ApiDocDirController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiDocDirController := &ApiDocDirController{
		BaseController: baseController,
	}
	return apiDocDirController, nil
}

func InitializeApiDocHistoryController(rw http.ResponseWriter, req *http.Request) (*ApiDocHistoryController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiDocHistoryController := &ApiDocHistoryController{
		BaseController: baseController,
	}
	return apiDocHistoryController, nil
}

func InitializeApiDocQuickController(rw http.ResponseWriter, req *http.Request) (*ApiDocQuickController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiDocQuickController := &ApiDocQuickController{
		BaseController: baseController,
	}
	return apiDocQuickController, nil
}

func InitializeApiEnvController(rw http.ResponseWriter, req *http.Request) (*ApiEnvController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiEnvController := &ApiEnvController{
		BaseController: baseController,
	}
	return apiEnvController, nil
}

func InitializeApiExportController(rw http.ResponseWriter, req *http.Request) (*ApiExportController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiExportController := &ApiExportController{
		BaseController: baseController,
	}
	return apiExportController, nil
}

func InitializeApiImportController(rw http.ResponseWriter, req *http.Request) (*ApiImportController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiImportController := &ApiImportController{
		BaseController: baseController,
	}
	return apiImportController, nil
}

func InitializeApiLibraryController(rw http.ResponseWriter, req *http.Request) (*ApiLibraryController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiLibraryController := &ApiLibraryController{
		BaseController: baseController,
	}
	return apiLibraryController, nil
}

func InitializeApiMockController(rw http.ResponseWriter, req *http.Request) (*ApiMockController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiMockController := &ApiMockController{
		BaseController: baseController,
	}
	return apiMockController, nil
}

func InitializeApiOverviewController(rw http.ResponseWriter, req *http.Request) (*ApiOverviewController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiOverviewController := &ApiOverviewController{
		BaseController: baseController,
	}
	return apiOverviewController, nil
}

func InitializeApiRunController(rw http.ResponseWriter, req *http.Request) (*ApiRunController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiRunController := &ApiRunController{
		BaseController: baseController,
	}
	return apiRunController, nil
}

func InitializeApiShareController(rw http.ResponseWriter, req *http.Request) (*ApiShareController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiShareController := &ApiShareController{
		BaseController: baseController,
	}
	return apiShareController, nil
}

func InitializeApiTreeController(rw http.ResponseWriter, req *http.Request) (*ApiTreeController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	apiTreeController := &ApiTreeController{
		BaseController: baseController,
	}
	return apiTreeController, nil
}

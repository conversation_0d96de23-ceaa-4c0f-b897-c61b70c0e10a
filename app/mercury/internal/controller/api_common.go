package controller

import (
	"io"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/api_request_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/enums/data_type"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ApiCommonController struct {
	*controller.BaseController
}

var DefaultApiCommonController ApiCommonController

var (
	apiStatusService service.ApiStatusService
	apiCommonService service.ApiCommonService
)

func (a *ApiCommonController) ApiStatusListHandler() {
	list := apiStatusService.ApiStatusList(a.MantisContext)
	a.ResSuccessResult(list)
}

func (a *ApiCommonController) DataTypeListHandler() {
	a.<PERSON>s<PERSON>uccessResult(data_type.DataTypeList)
}

func (a *ApiCommonController) ApiTypeListHandler() {
	a.ResSuccessResult(api_request_type.ApiRequestTypeList)
}

func (a *ApiCommonController) TransferJson2SchemaHandler() error {
	b, err := io.ReadAll(a.Body)
	if err == nil {
		a.ResSuccessResult(apiCommonService.TransferJson2Schema(a.MantisContext, string(b)))
		return nil
	} else {
		logger.Logger.Errorf("error reading body, err=%+v", err)
		return err
	}
}

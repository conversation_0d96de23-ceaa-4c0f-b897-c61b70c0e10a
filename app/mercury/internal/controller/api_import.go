package controller

import (
	"encoding/json"
	"io"
	"strconv"
	"strings"
	"time"

	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/common_util"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto/doc_import_export"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/doc_import"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/mitchellh/mapstructure"
)

type ApiImportLibIdInPathReq struct {
	LibId int64 `path:"libId"`
}

type ApiImportIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiImportUpdateTaskStatusReq struct {
	Id      int64  `path:"id"`
	Running string `path:"running"`
}

type ApiImportGetTaskPageReq struct {
	Name        string `schema:"name"`
	DataFormat  string `schema:"dataFormat"`
	Creator     string `schema:"creator"`
	GmtCreated  string `schema:"gmtCreated"` // example: 2017-01-02,2017-01-02
	GmtModified string `schema:"gmtModified"`
	ExecuteTime string `schema:"executeTime"`
	Page        int64  `schema:"page"`
	PageSize    int64  `schema:"pageSize"`
}

type ApiImportGetTaskRecordPageReq struct {
	TaskId   int64 `schema:"taskId"`
	Page     int64 `schema:"page"`
	PageSize int64 `schema:"pageSize"`
}

type ApiImportController struct {
	*controller.BaseController
}

var DefaultApiImportController ApiImportController

var apiDocImportService doc_import.ApiDocImportService

func (a *ApiImportController) ImportDocHandler() {
	logger.Logger.Info("a")
	loadDocDTO, docType := a.analyseFormParam()
	apiDocImportService.Analyse(a.MantisContext, loadDocDTO, docType)
	a.ResSuccess()
}

func (a *ApiImportController) ImportDocTaskHandler() {
	request := a.Request
	loadDocRequestMap := make(map[string]any)
	bytes, err := io.ReadAll(request.Body)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	jsonx.UnMarshal(bytes, &loadDocRequestMap)
	loadDocDTORequest := doc_import_export.LoadDocDTORequest{}
	err = mapstructure.Decode(&loadDocRequestMap, &loadDocDTORequest)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	dataModelFolderId := loadDocDTORequest.DataModelFolderId[len(loadDocDTORequest.DataModelFolderId)-1]
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	apiDocFolderId := loadDocDTORequest.ApiDocFolderId[len(loadDocDTORequest.ApiDocFolderId)-1]
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	spaceId := request.Header.Get("Spaceid")
	XServiceName := request.Header.Get("X-Service-Name")
	XUsercenterSession := request.Header.Get("X-Usercenter-Session")
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	apiImportTask := models.ApiImportTask{
		Name:     loadDocDTORequest.Name,
		LibId:    loadDocDTORequest.LibId,
		DocType:  loadDocDTORequest.DataFormat,
		Url:      loadDocDTORequest.Url,
		UserName: loadDocDTORequest.UserName,
		Password: loadDocDTORequest.Password,
	}
	apiImportTask.Id = loadDocDTORequest.Id
	loadDocDTOContent := doc_import_export.LoadAndExportDocDTOContent{}
	loadDocDTOContent.SetDataModelFolderId(dataModelFolderId)
	loadDocDTOContent.DataModelFolderIdStr = loadDocDTORequest.DataModelFolderId
	loadDocDTOContent.SetApiDocFolderId(apiDocFolderId)
	loadDocDTOContent.ApiDocFolderIdStr = loadDocDTORequest.ApiDocFolderId
	loadDocDTOContent.SetStrategy(loadDocDTORequest.Strategy)
	loadDocDTOContent.SetSpaceId(spaceId)
	loadDocDTOContent.SetXServiceName(XServiceName)
	loadDocDTOContent.SetXUserCenterSession(XUsercenterSession)
	loadDocDTOContent.SetUserAccount(info.AdAccount)
	loadDocDTOContent.NoticeType = loadDocDTORequest.NoticeType
	if loadDocDTORequest.NoticeUsers != "" {
		splitUsers := strings.Split(loadDocDTORequest.NoticeUsers, ",")
		for _, user := range splitUsers {
			if user != "" {
				loadDocDTOContent.NoticeUsers = append(loadDocDTOContent.NoticeUsers, user)
			}
		}
	}
	if loadDocDTORequest.NoticeTemplates != "" {
		splitTemplates := strings.Split(loadDocDTORequest.NoticeTemplates, ",")
		for _, template := range splitTemplates {
			if template != "" {
				loadDocDTOContent.NoticeTemplates = append(loadDocDTOContent.NoticeTemplates, template)
			}
		}
	}
	loadDocDTOContent.Type = loadDocDTORequest.Type
	loadDocDTOContent.CronContent = loadDocDTORequest.CronContent
	jsonx.UnMarshal([]byte(loadDocDTOContent.CronContent), &(loadDocDTOContent.CronContentStruct))
	loadDocDTOContent.TriggerType = loadDocDTORequest.TriggerType
	if loadDocDTORequest.LibId == 0 {
		logger.Logger.Panicf("%+v", xerror.New("error getting libId"))
	}
	loadDocDTOContent.LibId = loadDocDTORequest.LibId
	apiImportTask.Auth = loadDocDTORequest.Auth
	if loadDocDTORequest.TriggerType == commonconstants.TaskTypeScheduled ||
		loadDocDTORequest.TriggerType == commonconstants.TaskTypeManual ||
		loadDocDTORequest.TimeLimit == nil || len(loadDocDTORequest.TimeLimit) == 0 {
		// 定时或者手动或者没选起止时间，赋默认值
		loadDocDTORequest.TimeLimit = []int64{time.Now().UnixMilli(), time.Now().AddDate(100, 0, 0).UnixMilli()}
	}
	apiDocImportService.ImportTask(a.MantisContext, loadDocDTORequest.ScheduleTime, times.UnixToTime(loadDocDTORequest.TimeLimit[0]),
		times.UnixToTime(loadDocDTORequest.TimeLimit[1]), apiImportTask, loadDocDTOContent)
	a.ResSuccess()
}

func (a *ApiImportController) UpdateImportTask() {
	a.ImportDocHandler()
}

func (a *ApiImportController) GetTasksPage(libIdReq ApiImportLibIdInPathReq, req ApiImportGetTaskPageReq) {
	libId := libIdReq.LibId
	taskRequestDTO := doc_import_export.TaskRequestDTO{
		AddonTimeDTO: commondto.AddonTimeDTO{
			GmtCreated:           req.GmtCreated,
			GmtModified:          req.GmtModified,
			LastScanTestExecTime: req.ExecuteTime,
		},
		Name:       req.Name,
		DataFormat: req.DataFormat,
		Creator:    req.Creator,
	}
	pageRequest := gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	a.ResSuccessResult(apiDocImportService.GetTaskPages(a.MantisContext, libId, taskRequestDTO, pageRequest))
}

func (a *ApiImportController) GetTaskById(req ApiImportIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiDocImportService.GetTaskById(a.MantisContext, id))
}

func (a *ApiImportController) DeleteTaskById(req ApiImportIdInPathReq) {
	id := req.Id
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocImportService.DeleteTaskById(a.MantisContext, id, info)
	a.ResSuccess()
}

func (a *ApiImportController) GetTaskRecordsByTaskId(req ApiImportGetTaskRecordPageReq) {
	taskId := req.TaskId
	pageRequest := gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	a.ResSuccessResult(apiDocImportService.GetRecordsByTaskId(a.MantisContext, taskId, pageRequest))
}

func (a *ApiImportController) UpdateTaskStatus(req ApiImportUpdateTaskStatusReq) {
	id := req.Id
	running, _ := strconv.ParseBool(req.Running)
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocImportService.UpdateTaskStatusById(a.MantisContext, id, info, running)
	a.ResSuccess()
}

func (a *ApiImportController) RunImmediately(req ApiImportIdInPathReq) {
	id := req.Id
	user := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiDocImportService.RunImmediately(a.MantisContext, id, user)
	a.ResSuccess()
}

func (a *ApiImportController) analyseFormParam() (doc_import_export.LoadDocDTO, string) {
	request := a.Request
	importType := request.PostFormValue("importType")
	var docJson string
	if importType == constants.ImportDocTypeUrl {
		var username, password string
		auth, err := strconv.ParseBool(request.PostFormValue("auth"))
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
		}
		if auth {
			username = request.PostFormValue("userName")
			password = request.PostFormValue("password")
		}
		url := request.PostFormValue("url")
		docJson = doc_import.GetSwaggerByUrl(url, auth, username, password)
	} else if importType == constants.ImportDocTypeFile {
		_, file, err := request.FormFile("file")
		if err != nil {
			logger.Logger.Panicf("%+v", xerror.New("error in get parameter"))
		}
		docJson = common_util.ReadFileToString(file)
	}
	docType := request.PostFormValue("dataFormat")
	dataModelFolderIdStr := request.PostFormValue("dataModelFolderId")
	dataModelFolderIdSplit := strings.Split(dataModelFolderIdStr, ",")
	dataModelFolderId, err := strconv.ParseInt(dataModelFolderIdSplit[len(dataModelFolderIdSplit)-1], 10, 64)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	apiDocFolderIdStr := request.PostFormValue("apiDocFolderId")
	apiDocFolderIdSplit := strings.Split(apiDocFolderIdStr, ",")
	apiDocFolderId, err := strconv.ParseInt(apiDocFolderIdSplit[len(apiDocFolderIdSplit)-1], 10, 64)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	libId, err := strconv.ParseInt(request.PostFormValue("libId"), 10, 64)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	strategy, err := strconv.ParseInt(request.PostFormValue("importMode"), 10, 32)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	loadDocDTO := doc_import_export.GetLoadDocDTO(docType)
	loadDocDTOMap := make(map[string]any)
	err = json.Unmarshal([]byte(docJson), &loadDocDTOMap)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.New("文档格式错误"))
	}
	err = mapstructure.Decode(loadDocDTOMap, &loadDocDTO)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.New("文档格式错误"))
	}
	info := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	loadDocDTO.SetDataModelFolderId(dataModelFolderId)
	loadDocDTO.SetApiDocFolderId(apiDocFolderId)
	loadDocDTO.SetLibId(libId)
	loadDocDTO.SetUserAccount(info.AdAccount)
	spaceId := request.Header.Get("Spaceid")
	loadDocDTO.SetSpaceId(spaceId)
	loadDocDTO.SetXServiceName(request.Header.Get("X-Service-Name"))
	loadDocDTO.SetXUserCenterSession(request.Header.Get("X-Usercenter-Session"))
	loadDocDTO.SetStrategy(int32(strategy))
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in get parameter"))
	}
	return loadDocDTO, docType
}

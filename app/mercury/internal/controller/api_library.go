package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/library"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type ApiLibrarySearchReq struct {
	GmtCreated  string `schema:"gmtCreated"` // example: 2017-01-02,2017-01-02
	GmtModified string `schema:"gmtModified"`
	ExecuteTime string `schema:"executeTime"`
	Name        string `schema:"name"`
	AppId       string `schema:"appId"`
	Creator     string `schema:"creator"`
	Page        int64  `schema:"page"`
	PageSize    int64  `schema:"pageSize"`
}

type ApiLibraryIdInPathReq struct {
	Id int64 `path:"id"`
}

type ApiLibraryController struct {
	*controller.BaseController
}

var DefaultApiLibraryController ApiLibraryController

var apiLibraryService library.ApiLibraryService

func (a *ApiLibraryController) SearchLibrary(req ApiLibrarySearchReq) {
	libraryRequest := dto.ApiLibraryRequest{
		AddonTimeDTO: commondto.AddonTimeDTO{
			GmtCreated:           req.GmtCreated,
			GmtModified:          req.GmtModified,
			LastScanTestExecTime: req.ExecuteTime,
		},
		Name:    req.Name,
		AppId:   req.AppId,
		Creator: req.Creator,
	}
	pageRequest := gormx.PageRequest{
		Page:     req.Page,
		PageSize: req.PageSize,
	}
	if pageRequest.Page == 0 {
		pageRequest.Page = 1
	}
	if pageRequest.PageSize == 0 {
		pageRequest.PageSize = 100
	}
	spaceId := a.Request.Header.Get("Spaceid")
	a.ResSuccessResult(apiLibraryService.SearchLibrary(a.MantisContext, libraryRequest, pageRequest, spaceId))
}

func (a *ApiLibraryController) AddLibrary(apiLibrary models.ApiLibrary) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	spaceId := a.Request.Header.Get("Spaceid")
	a.ResSuccessResult(apiLibraryService.AddLibrary(a.MantisContext, apiLibrary, userInfo, spaceId))
}

func (a *ApiLibraryController) ModifyLibrary(apiLibrary models.ApiLibrary, req ApiLibraryIdInPathReq) {
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	id := req.Id
	spaceId := a.Request.Header.Get("Spaceid")
	apiLibrary.Id = id
	a.ResSuccessResult(apiLibraryService.ModifyLibrary(a.MantisContext, apiLibrary, userInfo, spaceId))
}

func (a *ApiLibraryController) RemoveLibrary(req ApiLibraryIdInPathReq) {
	id := req.Id
	userInfo := commondto.UserInfo{
		AdAccount: a.GetUser(),
	}
	apiLibraryService.RemoveLibrary(a.MantisContext, id, userInfo)
	a.ResSuccess()
}

func (a *ApiLibraryController) GetDocStatus(req ApiLibraryIdInPathReq) {
	id := req.Id
	a.ResSuccessResult(apiLibraryService.GetDocStatus(a.MantisContext, id))
}

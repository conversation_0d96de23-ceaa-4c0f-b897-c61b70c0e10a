package mercury

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/service/doc_import"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/migration"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron/queue"
)

func Init() {
	// 初始化数据库迁移
	migration.Init()

	// 定时任务导入初始化
	doc_import.Init()

	// 加入定时任务的反序列化器
	queue.AddUnmarshalMap(constants.MercuryImportJob, doc_import.DealMercuryImportJob)
}

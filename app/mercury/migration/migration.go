package migration

import (
	. "git.zhonganinfo.com/zainfo/cube-mantis/app/mercury/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	. "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"github.com/duke-git/lancet/v2/xerror"
)

// Init 初始化数据
func Init() {
	ctx := &commoncontext.MantisContext{}
	if !configs.Config.Db.Migrate {
		return
	}
	err := gormx.GetDB(ctx).AutoMigrate(
		&ApiDataModel{},
		&ApiDocBase{},
		&ApiDocBaseHistory{},
		&ApiDocQuickRequest{},
		&ApiDocRuntime{},
		&ApiEnv{},
		&ApiFolder{},
		&ApiImportTask{},
		&ApiImportTaskRecords{},
		&ApiLibrary{},
		&ApiMock{},
		&ApiResponse{},
		&ApiResponseCase{},
		&ApiRunRecord{},
		&ApiShare{},
		&ApiStatus{},
		&ApiVariable{},
	)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "error in auto migrate table"))
	}
	InitApiStatus(ctx)
}

func InitApiStatus(ctx *commoncontext.MantisContext) {
	// 清空预制数据
	gormx.DeleteAllRecords(ctx, &ApiStatus{})
	// 批量插入初始化数据
	apiStatusSlice := []ApiStatus{
		{Addons: NewAddonsWithId(1), Name: "设计中", LibId: 0},
		{Addons: NewAddonsWithId(2), Name: "联调中", LibId: 0},
		{Addons: NewAddonsWithId(3), Name: "已发布", LibId: 0},
		{Addons: NewAddonsWithId(4), Name: "待确定", LibId: 0},
		{Addons: NewAddonsWithId(5), Name: "测试中", LibId: 0},
		{Addons: NewAddonsWithId(6), Name: "开发中", LibId: 0},
	}
	gormx.InsertBatchX(ctx, apiStatusSlice)
}

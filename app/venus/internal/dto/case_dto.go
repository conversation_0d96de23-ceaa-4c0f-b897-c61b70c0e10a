package dto

import (
	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pocketbase"
)

type ElementCaseDto struct {
	Id       string `json:"id"` // 用例的id
	Name     string `json:"name" `
	Modifier string `json:"modifier"`
	Updated  int64  `json:"updated"`
}

type AgentCaseDto struct {
	Id       string           `json:"id"`
	Name     string           `json:"name"`
	SpaceId  string           `json:"spaceId"`
	ParentId string           `json:"parent_id"`
	Steps    []map[string]any `json:"steps"`
}

type DebugCaseDto struct {
	ReportId  string              `json:"reportId"`
	Steps     []models.UiCaseStep `json:"steps"`
	AgentCase *cases.Case         `json:"agentCase"`
}

type CaseRunResDTO struct {
	Res       map[string][]models.ReportRes `json:"res" yaml:"res"`
	Finished  bool                          `json:"finished" yaml:"finished"`
	StartTime int64                         `json:"start_time" yaml:"start_time"`
	Duration  int64                         `json:"duration" yaml:"duration"`
	Type      string                        `json:"type"`
}

type CaseStepDTO struct {
	Finished  bool   `json:"finished" yaml:"finished"`
	StartTime int64  `json:"start_time" yaml:"start_time"`
	Duration  int64  `json:"duration" yaml:"duration"`
	Type      string `json:"type"`
}

type CaseStepsDTO struct {
	Finished  bool                                                    `json:"finished"`
	StartTime int64                                                   `json:"start_time"`
	Duration  int64                                                   `json:"duration"`
	ExecType  string                                                  `json:"exec_type"`
	Operator  string                                                  `json:"operator"`
	ReportId  string                                                  `json:"report_id"`
	Recording bool                                                    `json:"recording"`
	Steps     *pocketbase.PaginationRecord[models.UiReportStepDetail] `json:"steps"`
}

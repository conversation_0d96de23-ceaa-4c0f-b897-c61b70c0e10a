package dto

type PathIdDto struct {
	Id string `path:"id"`
}

type MoveDto struct {
	Id       string `json:"id"`
	ParentId string `json:"parent_id"`
}

type CountDto struct {
	Count int `json:"count"`
}

type ResCountDto struct {
	All     int `json:"all"`
	Pass    int `json:"pass"`
	Fail    int `json:"fail"`
	UnExec  int `json:"un_exec"`
	Running int `json:"running"`
}

type IdDto struct {
	Id string `json:"id"`
}

// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// UiReportStepDetailQueryDTO represents query parameters for UiReportStepDetail
type UiReportStepDetailQueryDTO struct {
	ReportIdEqual *string `json:"reportidequal"`
	ReportIdLike *string `json:"reportidlike"`
	ReportIdNotEqual *string `json:"reportidnotequal"`
	ReportIdNotLike *string `json:"reportidnotlike"`
	ReportIdExpand bool `json:"reportidexpand"`
	ReportCaseIdEqual *string `json:"reportcaseidequal"`
	ReportCaseIdLike *string `json:"reportcaseidlike"`
	ReportCaseIdNotEqual *string `json:"reportcaseidnotequal"`
	ReportCaseIdNotLike *string `json:"reportcaseidnotlike"`
	ReportCaseIdExpand bool `json:"reportcaseidexpand"`
	StatusEqual *string `json:"statusequal"`
	StatusLike *string `json:"statuslike"`
	StatusNotEqual *string `json:"statusnotequal"`
	StatusNotLike *string `json:"statusnotlike"`
	IsCountEqual *bool `json:"iscountequal"`
	CaseIdEqual *string `json:"caseidequal"`
	CaseIdLike *string `json:"caseidlike"`
	CaseIdNotEqual *string `json:"caseidnotequal"`
	CaseIdNotLike *string `json:"caseidnotlike"`
	CaseIdExpand bool `json:"caseidexpand"`
	StepIdEqual *string `json:"stepidequal"`
	StepIdLike *string `json:"stepidlike"`
	StepIdNotEqual *string `json:"stepidnotequal"`
	StepIdNotLike *string `json:"stepidnotlike"`
	StepIdExpand bool `json:"stepidexpand"`
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	ParentIdEqual *string `json:"parentidequal"`
	ParentIdLike *string `json:"parentidlike"`
	ParentIdNotEqual *string `json:"parentidnotequal"`
	ParentIdNotLike *string `json:"parentidnotlike"`
	ParentIdExpand bool `json:"parentidexpand"`
	ActionTypeEqual *string `json:"actiontypeequal"`
	ActionTypeLike *string `json:"actiontypelike"`
	ActionTypeNotEqual *string `json:"actiontypenotequal"`
	ActionTypeNotLike *string `json:"actiontypenotlike"`
	SettingEnableEqual *bool `json:"settingenableequal"`
	GenSourceEqual *string `json:"gensourceequal"`
	GenSourceLike *string `json:"gensourcelike"`
	GenSourceNotEqual *string `json:"gensourcenotequal"`
	GenSourceNotLike *string `json:"gensourcenotlike"`
	DisableEqual *string `json:"disableequal"`
	DisableLike *string `json:"disablelike"`
	DisableNotEqual *string `json:"disablenotequal"`
	DisableNotLike *string `json:"disablenotlike"`
	SiblingOrderEqual *int64 `json:"siblingorderequal"`
	SiblingOrderNotEqual *int64 `json:"siblingordernotequal"`
	SiblingOrderGreater *int64 `json:"siblingordergreater"`
	SiblingOrderLess *int64 `json:"siblingorderless"`
	SiblingOrderGreaterEqual *int64 `json:"siblingordergreaterequal"`
	SiblingOrderLessEqual *int64 `json:"siblingorderlessequal"`
	IsDirEqual *bool `json:"isdirequal"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *UiReportStepDetailQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "report_id":
				dto.ReportIdExpand = true
			case "report_case_id":
				dto.ReportCaseIdExpand = true
			case "case_id":
				dto.CaseIdExpand = true
			case "step_id":
				dto.StepIdExpand = true
			case "parent_id":
				dto.ParentIdExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "report_id":
					dto.ReportIdEqual = &value
					
				case "report_case_id":
					dto.ReportCaseIdEqual = &value
					
				case "status":
					dto.StatusEqual = &value
					
				case "is_count":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for is_count: %v", err)
					}
					dto.IsCountEqual = &boolVal
					
				case "case_id":
					dto.CaseIdEqual = &value
					
				case "step_id":
					dto.StepIdEqual = &value
					
				case "name":
					dto.NameEqual = &value
					
				case "parent_id":
					dto.ParentIdEqual = &value
					
				case "action_type":
					dto.ActionTypeEqual = &value
					
				case "setting_enable":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for setting_enable: %v", err)
					}
					dto.SettingEnableEqual = &boolVal
					
				case "gen_source":
					dto.GenSourceEqual = &value
					
				case "element_ids":
					
				case "disable":
					dto.DisableEqual = &value
					
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderEqual = &int64Val
					
				case "is_dir":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for is_dir: %v", err)
					}
					dto.IsDirEqual = &boolVal
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "report_id":
					dto.ReportIdNotEqual = &value
					
				case "report_case_id":
					dto.ReportCaseIdNotEqual = &value
					
				case "status":
					dto.StatusNotEqual = &value
					
				case "is_count":
					
				case "case_id":
					dto.CaseIdNotEqual = &value
					
				case "step_id":
					dto.StepIdNotEqual = &value
					
				case "name":
					dto.NameNotEqual = &value
					
				case "parent_id":
					dto.ParentIdNotEqual = &value
					
				case "action_type":
					dto.ActionTypeNotEqual = &value
					
				case "setting_enable":
					
				case "gen_source":
					dto.GenSourceNotEqual = &value
					
				case "element_ids":
					
				case "disable":
					dto.DisableNotEqual = &value
					
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderNotEqual = &int64Val
					
				case "is_dir":
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "report_id":
					dto.ReportIdLike = &value
				case "report_case_id":
					dto.ReportCaseIdLike = &value
				case "status":
					dto.StatusLike = &value
				case "case_id":
					dto.CaseIdLike = &value
				case "step_id":
					dto.StepIdLike = &value
				case "name":
					dto.NameLike = &value
				case "parent_id":
					dto.ParentIdLike = &value
				case "action_type":
					dto.ActionTypeLike = &value
				case "gen_source":
					dto.GenSourceLike = &value
				case "disable":
					dto.DisableLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "report_id":
					dto.ReportIdNotLike = &value
				case "report_case_id":
					dto.ReportCaseIdNotLike = &value
				case "status":
					dto.StatusNotLike = &value
				case "case_id":
					dto.CaseIdNotLike = &value
				case "step_id":
					dto.StepIdNotLike = &value
				case "name":
					dto.NameNotLike = &value
				case "parent_id":
					dto.ParentIdNotLike = &value
				case "action_type":
					dto.ActionTypeNotLike = &value
				case "gen_source":
					dto.GenSourceNotLike = &value
				case "disable":
					dto.DisableNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderGreaterEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderLessEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderGreater = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderLess = &int64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *UiReportStepDetailQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.ReportIdEqual != nil {
		db = db.Where("report_id = ?", *dto.ReportIdEqual)
	}
	if dto.ReportIdLike != nil {
		db = db.Where("report_id ILIKE ?", "%"+*dto.ReportIdLike+"%")
	}
	if dto.ReportIdNotEqual != nil {
		db = db.Where("report_id != ?", *dto.ReportIdNotEqual)
	}
	if dto.ReportIdNotLike != nil {
		db = db.Where("report_id NOT ILIKE ?", "%"+*dto.ReportIdNotLike+"%")
	}
	
	
	
	if dto.ReportCaseIdEqual != nil {
		db = db.Where("report_case_id = ?", *dto.ReportCaseIdEqual)
	}
	if dto.ReportCaseIdLike != nil {
		db = db.Where("report_case_id ILIKE ?", "%"+*dto.ReportCaseIdLike+"%")
	}
	if dto.ReportCaseIdNotEqual != nil {
		db = db.Where("report_case_id != ?", *dto.ReportCaseIdNotEqual)
	}
	if dto.ReportCaseIdNotLike != nil {
		db = db.Where("report_case_id NOT ILIKE ?", "%"+*dto.ReportCaseIdNotLike+"%")
	}
	
	
	
	if dto.StatusEqual != nil {
		db = db.Where("status = ?", *dto.StatusEqual)
	}
	if dto.StatusLike != nil {
		db = db.Where("status ILIKE ?", "%"+*dto.StatusLike+"%")
	}
	if dto.StatusNotEqual != nil {
		db = db.Where("status != ?", *dto.StatusNotEqual)
	}
	if dto.StatusNotLike != nil {
		db = db.Where("status NOT ILIKE ?", "%"+*dto.StatusNotLike+"%")
	}
	
	
	
	if dto.IsCountEqual != nil {
		db = db.Where("is_count = ?", *dto.IsCountEqual)
	}
	
	
	
	if dto.CaseIdEqual != nil {
		db = db.Where("case_id = ?", *dto.CaseIdEqual)
	}
	if dto.CaseIdLike != nil {
		db = db.Where("case_id ILIKE ?", "%"+*dto.CaseIdLike+"%")
	}
	if dto.CaseIdNotEqual != nil {
		db = db.Where("case_id != ?", *dto.CaseIdNotEqual)
	}
	if dto.CaseIdNotLike != nil {
		db = db.Where("case_id NOT ILIKE ?", "%"+*dto.CaseIdNotLike+"%")
	}
	
	
	
	if dto.StepIdEqual != nil {
		db = db.Where("step_id = ?", *dto.StepIdEqual)
	}
	if dto.StepIdLike != nil {
		db = db.Where("step_id ILIKE ?", "%"+*dto.StepIdLike+"%")
	}
	if dto.StepIdNotEqual != nil {
		db = db.Where("step_id != ?", *dto.StepIdNotEqual)
	}
	if dto.StepIdNotLike != nil {
		db = db.Where("step_id NOT ILIKE ?", "%"+*dto.StepIdNotLike+"%")
	}
	
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.ParentIdEqual != nil {
		db = db.Where("parent_id = ?", *dto.ParentIdEqual)
	}
	if dto.ParentIdLike != nil {
		db = db.Where("parent_id ILIKE ?", "%"+*dto.ParentIdLike+"%")
	}
	if dto.ParentIdNotEqual != nil {
		db = db.Where("parent_id != ?", *dto.ParentIdNotEqual)
	}
	if dto.ParentIdNotLike != nil {
		db = db.Where("parent_id NOT ILIKE ?", "%"+*dto.ParentIdNotLike+"%")
	}
	
	
	
	if dto.ActionTypeEqual != nil {
		db = db.Where("action_type = ?", *dto.ActionTypeEqual)
	}
	if dto.ActionTypeLike != nil {
		db = db.Where("action_type ILIKE ?", "%"+*dto.ActionTypeLike+"%")
	}
	if dto.ActionTypeNotEqual != nil {
		db = db.Where("action_type != ?", *dto.ActionTypeNotEqual)
	}
	if dto.ActionTypeNotLike != nil {
		db = db.Where("action_type NOT ILIKE ?", "%"+*dto.ActionTypeNotLike+"%")
	}
	
	
	
	if dto.SettingEnableEqual != nil {
		db = db.Where("setting_enable = ?", *dto.SettingEnableEqual)
	}
	
	
	
	if dto.GenSourceEqual != nil {
		db = db.Where("gen_source = ?", *dto.GenSourceEqual)
	}
	if dto.GenSourceLike != nil {
		db = db.Where("gen_source ILIKE ?", "%"+*dto.GenSourceLike+"%")
	}
	if dto.GenSourceNotEqual != nil {
		db = db.Where("gen_source != ?", *dto.GenSourceNotEqual)
	}
	if dto.GenSourceNotLike != nil {
		db = db.Where("gen_source NOT ILIKE ?", "%"+*dto.GenSourceNotLike+"%")
	}
	
	
	
	
	
	if dto.DisableEqual != nil {
		db = db.Where("disable = ?", *dto.DisableEqual)
	}
	if dto.DisableLike != nil {
		db = db.Where("disable ILIKE ?", "%"+*dto.DisableLike+"%")
	}
	if dto.DisableNotEqual != nil {
		db = db.Where("disable != ?", *dto.DisableNotEqual)
	}
	if dto.DisableNotLike != nil {
		db = db.Where("disable NOT ILIKE ?", "%"+*dto.DisableNotLike+"%")
	}
	
	
	
	if dto.SiblingOrderEqual != nil {
		db = db.Where("sibling_order = ?", *dto.SiblingOrderEqual)
	}
	if dto.SiblingOrderNotEqual != nil {
		db = db.Where("sibling_order != ?", *dto.SiblingOrderNotEqual)
	}
	
	if dto.SiblingOrderGreater != nil {
		db = db.Where("sibling_order > ?", *dto.SiblingOrderGreater)
	}
	if dto.SiblingOrderLess != nil {
		db = db.Where("sibling_order < ?", *dto.SiblingOrderLess)
	}
	if dto.SiblingOrderGreaterEqual != nil {
		db = db.Where("sibling_order >= ?", *dto.SiblingOrderGreaterEqual)
	}
	if dto.SiblingOrderLessEqual != nil {
		db = db.Where("sibling_order <= ?", *dto.SiblingOrderLessEqual)
	}
	
	
	
	
	if dto.IsDirEqual != nil {
		db = db.Where("is_dir = ?", *dto.IsDirEqual)
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

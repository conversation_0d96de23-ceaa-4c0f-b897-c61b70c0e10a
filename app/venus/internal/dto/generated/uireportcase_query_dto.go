// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// UiReportCaseQueryDTO represents query parameters for UiReportCase
type UiReportCaseQueryDTO struct {
	StatusEqual *string `json:"statusequal"`
	StatusLike *string `json:"statuslike"`
	StatusNotEqual *string `json:"statusnotequal"`
	StatusNotLike *string `json:"statusnotlike"`
	StepTotalCountEqual *int `json:"steptotalcountequal"`
	StepTotalCountNotEqual *int `json:"steptotalcountnotequal"`
	StepTotalCountGreater *int `json:"steptotalcountgreater"`
	StepTotalCountLess *int `json:"steptotalcountless"`
	StepTotalCountGreaterEqual *int `json:"steptotalcountgreaterequal"`
	StepTotalCountLessEqual *int `json:"steptotalcountlessequal"`
	StepPassCountEqual *int `json:"steppasscountequal"`
	StepPassCountNotEqual *int `json:"steppasscountnotequal"`
	StepPassCountGreater *int `json:"steppasscountgreater"`
	StepPassCountLess *int `json:"steppasscountless"`
	StepPassCountGreaterEqual *int `json:"steppasscountgreaterequal"`
	StepPassCountLessEqual *int `json:"steppasscountlessequal"`
	StepFailCountEqual *int `json:"stepfailcountequal"`
	StepFailCountNotEqual *int `json:"stepfailcountnotequal"`
	StepFailCountGreater *int `json:"stepfailcountgreater"`
	StepFailCountLess *int `json:"stepfailcountless"`
	StepFailCountGreaterEqual *int `json:"stepfailcountgreaterequal"`
	StepFailCountLessEqual *int `json:"stepfailcountlessequal"`
	StepUnexecCountEqual *int `json:"stepunexeccountequal"`
	StepUnexecCountNotEqual *int `json:"stepunexeccountnotequal"`
	StepUnexecCountGreater *int `json:"stepunexeccountgreater"`
	StepUnexecCountLess *int `json:"stepunexeccountless"`
	StepUnexecCountGreaterEqual *int `json:"stepunexeccountgreaterequal"`
	StepUnexecCountLessEqual *int `json:"stepunexeccountlessequal"`
	ExecutorEqual *string `json:"executorequal"`
	ExecutorLike *string `json:"executorlike"`
	ExecutorNotEqual *string `json:"executornotequal"`
	ExecutorNotLike *string `json:"executornotlike"`
	ExecutorExpand bool `json:"executorexpand"`
	ExecStartTimeEqual *int64 `json:"execstarttimeequal"`
	ExecStartTimeNotEqual *int64 `json:"execstarttimenotequal"`
	ExecStartTimeGreater *int64 `json:"execstarttimegreater"`
	ExecStartTimeLess *int64 `json:"execstarttimeless"`
	ExecStartTimeGreaterEqual *int64 `json:"execstarttimegreaterequal"`
	ExecStartTimeLessEqual *int64 `json:"execstarttimelessequal"`
	ExecEndTimeEqual *int64 `json:"execendtimeequal"`
	ExecEndTimeNotEqual *int64 `json:"execendtimenotequal"`
	ExecEndTimeGreater *int64 `json:"execendtimegreater"`
	ExecEndTimeLess *int64 `json:"execendtimeless"`
	ExecEndTimeGreaterEqual *int64 `json:"execendtimegreaterequal"`
	ExecEndTimeLessEqual *int64 `json:"execendtimelessequal"`
	DurationEqual *int64 `json:"durationequal"`
	DurationNotEqual *int64 `json:"durationnotequal"`
	DurationGreater *int64 `json:"durationgreater"`
	DurationLess *int64 `json:"durationless"`
	DurationGreaterEqual *int64 `json:"durationgreaterequal"`
	DurationLessEqual *int64 `json:"durationlessequal"`
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	ReportIdEqual *string `json:"reportidequal"`
	ReportIdLike *string `json:"reportidlike"`
	ReportIdNotEqual *string `json:"reportidnotequal"`
	ReportIdNotLike *string `json:"reportidnotlike"`
	ReportIdExpand bool `json:"reportidexpand"`
	CaseIdEqual *string `json:"caseidequal"`
	CaseIdLike *string `json:"caseidlike"`
	CaseIdNotEqual *string `json:"caseidnotequal"`
	CaseIdNotLike *string `json:"caseidnotlike"`
	CaseIdExpand bool `json:"caseidexpand"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *UiReportCaseQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "executor":
				dto.ExecutorExpand = true
			case "report_id":
				dto.ReportIdExpand = true
			case "case_id":
				dto.CaseIdExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "status":
					dto.StatusEqual = &value
					
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountEqual = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountEqual = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountEqual = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountEqual = &intVal
					
				case "executor":
					dto.ExecutorEqual = &value
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationEqual = &int64Val
					
				case "name":
					dto.NameEqual = &value
					
				case "report_id":
					dto.ReportIdEqual = &value
					
				case "case_id":
					dto.CaseIdEqual = &value
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "status":
					dto.StatusNotEqual = &value
					
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountNotEqual = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountNotEqual = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountNotEqual = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountNotEqual = &intVal
					
				case "executor":
					dto.ExecutorNotEqual = &value
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeNotEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeNotEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationNotEqual = &int64Val
					
				case "name":
					dto.NameNotEqual = &value
					
				case "report_id":
					dto.ReportIdNotEqual = &value
					
				case "case_id":
					dto.CaseIdNotEqual = &value
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "status":
					dto.StatusLike = &value
				case "executor":
					dto.ExecutorLike = &value
				case "name":
					dto.NameLike = &value
				case "report_id":
					dto.ReportIdLike = &value
				case "case_id":
					dto.CaseIdLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "status":
					dto.StatusNotLike = &value
				case "executor":
					dto.ExecutorNotLike = &value
				case "name":
					dto.NameNotLike = &value
				case "report_id":
					dto.ReportIdNotLike = &value
				case "case_id":
					dto.CaseIdNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountGreaterEqual = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountGreaterEqual = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountGreaterEqual = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountGreaterEqual = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeGreaterEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeGreaterEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationGreaterEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountLessEqual = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountLessEqual = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountLessEqual = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountLessEqual = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeLessEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeLessEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationLessEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountGreater = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountGreater = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountGreater = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountGreater = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeGreater = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeGreater = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationGreater = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "step_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_total_count: %v", err)
					}
					dto.StepTotalCountLess = &intVal
					
				case "step_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_pass_count: %v", err)
					}
					dto.StepPassCountLess = &intVal
					
				case "step_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_fail_count: %v", err)
					}
					dto.StepFailCountLess = &intVal
					
				case "step_unexec_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for step_unexec_count: %v", err)
					}
					dto.StepUnexecCountLess = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeLess = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeLess = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationLess = &int64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *UiReportCaseQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.StatusEqual != nil {
		db = db.Where("status = ?", *dto.StatusEqual)
	}
	if dto.StatusLike != nil {
		db = db.Where("status ILIKE ?", "%"+*dto.StatusLike+"%")
	}
	if dto.StatusNotEqual != nil {
		db = db.Where("status != ?", *dto.StatusNotEqual)
	}
	if dto.StatusNotLike != nil {
		db = db.Where("status NOT ILIKE ?", "%"+*dto.StatusNotLike+"%")
	}
	
	
	
	if dto.StepTotalCountEqual != nil {
		db = db.Where("step_total_count = ?", *dto.StepTotalCountEqual)
	}
	if dto.StepTotalCountNotEqual != nil {
		db = db.Where("step_total_count != ?", *dto.StepTotalCountNotEqual)
	}
	
	if dto.StepTotalCountGreater != nil {
		db = db.Where("step_total_count > ?", *dto.StepTotalCountGreater)
	}
	if dto.StepTotalCountLess != nil {
		db = db.Where("step_total_count < ?", *dto.StepTotalCountLess)
	}
	if dto.StepTotalCountGreaterEqual != nil {
		db = db.Where("step_total_count >= ?", *dto.StepTotalCountGreaterEqual)
	}
	if dto.StepTotalCountLessEqual != nil {
		db = db.Where("step_total_count <= ?", *dto.StepTotalCountLessEqual)
	}
	
	
	
	
	if dto.StepPassCountEqual != nil {
		db = db.Where("step_pass_count = ?", *dto.StepPassCountEqual)
	}
	if dto.StepPassCountNotEqual != nil {
		db = db.Where("step_pass_count != ?", *dto.StepPassCountNotEqual)
	}
	
	if dto.StepPassCountGreater != nil {
		db = db.Where("step_pass_count > ?", *dto.StepPassCountGreater)
	}
	if dto.StepPassCountLess != nil {
		db = db.Where("step_pass_count < ?", *dto.StepPassCountLess)
	}
	if dto.StepPassCountGreaterEqual != nil {
		db = db.Where("step_pass_count >= ?", *dto.StepPassCountGreaterEqual)
	}
	if dto.StepPassCountLessEqual != nil {
		db = db.Where("step_pass_count <= ?", *dto.StepPassCountLessEqual)
	}
	
	
	
	
	if dto.StepFailCountEqual != nil {
		db = db.Where("step_fail_count = ?", *dto.StepFailCountEqual)
	}
	if dto.StepFailCountNotEqual != nil {
		db = db.Where("step_fail_count != ?", *dto.StepFailCountNotEqual)
	}
	
	if dto.StepFailCountGreater != nil {
		db = db.Where("step_fail_count > ?", *dto.StepFailCountGreater)
	}
	if dto.StepFailCountLess != nil {
		db = db.Where("step_fail_count < ?", *dto.StepFailCountLess)
	}
	if dto.StepFailCountGreaterEqual != nil {
		db = db.Where("step_fail_count >= ?", *dto.StepFailCountGreaterEqual)
	}
	if dto.StepFailCountLessEqual != nil {
		db = db.Where("step_fail_count <= ?", *dto.StepFailCountLessEqual)
	}
	
	
	
	
	if dto.StepUnexecCountEqual != nil {
		db = db.Where("step_unexec_count = ?", *dto.StepUnexecCountEqual)
	}
	if dto.StepUnexecCountNotEqual != nil {
		db = db.Where("step_unexec_count != ?", *dto.StepUnexecCountNotEqual)
	}
	
	if dto.StepUnexecCountGreater != nil {
		db = db.Where("step_unexec_count > ?", *dto.StepUnexecCountGreater)
	}
	if dto.StepUnexecCountLess != nil {
		db = db.Where("step_unexec_count < ?", *dto.StepUnexecCountLess)
	}
	if dto.StepUnexecCountGreaterEqual != nil {
		db = db.Where("step_unexec_count >= ?", *dto.StepUnexecCountGreaterEqual)
	}
	if dto.StepUnexecCountLessEqual != nil {
		db = db.Where("step_unexec_count <= ?", *dto.StepUnexecCountLessEqual)
	}
	
	
	
	
	if dto.ExecutorEqual != nil {
		db = db.Where("executor = ?", *dto.ExecutorEqual)
	}
	if dto.ExecutorLike != nil {
		db = db.Where("executor ILIKE ?", "%"+*dto.ExecutorLike+"%")
	}
	if dto.ExecutorNotEqual != nil {
		db = db.Where("executor != ?", *dto.ExecutorNotEqual)
	}
	if dto.ExecutorNotLike != nil {
		db = db.Where("executor NOT ILIKE ?", "%"+*dto.ExecutorNotLike+"%")
	}
	
	
	
	if dto.ExecStartTimeEqual != nil {
		db = db.Where("exec_start_time = ?", *dto.ExecStartTimeEqual)
	}
	if dto.ExecStartTimeNotEqual != nil {
		db = db.Where("exec_start_time != ?", *dto.ExecStartTimeNotEqual)
	}
	
	if dto.ExecStartTimeGreater != nil {
		db = db.Where("exec_start_time > ?", *dto.ExecStartTimeGreater)
	}
	if dto.ExecStartTimeLess != nil {
		db = db.Where("exec_start_time < ?", *dto.ExecStartTimeLess)
	}
	if dto.ExecStartTimeGreaterEqual != nil {
		db = db.Where("exec_start_time >= ?", *dto.ExecStartTimeGreaterEqual)
	}
	if dto.ExecStartTimeLessEqual != nil {
		db = db.Where("exec_start_time <= ?", *dto.ExecStartTimeLessEqual)
	}
	
	
	
	
	if dto.ExecEndTimeEqual != nil {
		db = db.Where("exec_end_time = ?", *dto.ExecEndTimeEqual)
	}
	if dto.ExecEndTimeNotEqual != nil {
		db = db.Where("exec_end_time != ?", *dto.ExecEndTimeNotEqual)
	}
	
	if dto.ExecEndTimeGreater != nil {
		db = db.Where("exec_end_time > ?", *dto.ExecEndTimeGreater)
	}
	if dto.ExecEndTimeLess != nil {
		db = db.Where("exec_end_time < ?", *dto.ExecEndTimeLess)
	}
	if dto.ExecEndTimeGreaterEqual != nil {
		db = db.Where("exec_end_time >= ?", *dto.ExecEndTimeGreaterEqual)
	}
	if dto.ExecEndTimeLessEqual != nil {
		db = db.Where("exec_end_time <= ?", *dto.ExecEndTimeLessEqual)
	}
	
	
	
	
	if dto.DurationEqual != nil {
		db = db.Where("duration = ?", *dto.DurationEqual)
	}
	if dto.DurationNotEqual != nil {
		db = db.Where("duration != ?", *dto.DurationNotEqual)
	}
	
	if dto.DurationGreater != nil {
		db = db.Where("duration > ?", *dto.DurationGreater)
	}
	if dto.DurationLess != nil {
		db = db.Where("duration < ?", *dto.DurationLess)
	}
	if dto.DurationGreaterEqual != nil {
		db = db.Where("duration >= ?", *dto.DurationGreaterEqual)
	}
	if dto.DurationLessEqual != nil {
		db = db.Where("duration <= ?", *dto.DurationLessEqual)
	}
	
	
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.ReportIdEqual != nil {
		db = db.Where("report_id = ?", *dto.ReportIdEqual)
	}
	if dto.ReportIdLike != nil {
		db = db.Where("report_id ILIKE ?", "%"+*dto.ReportIdLike+"%")
	}
	if dto.ReportIdNotEqual != nil {
		db = db.Where("report_id != ?", *dto.ReportIdNotEqual)
	}
	if dto.ReportIdNotLike != nil {
		db = db.Where("report_id NOT ILIKE ?", "%"+*dto.ReportIdNotLike+"%")
	}
	
	
	
	if dto.CaseIdEqual != nil {
		db = db.Where("case_id = ?", *dto.CaseIdEqual)
	}
	if dto.CaseIdLike != nil {
		db = db.Where("case_id ILIKE ?", "%"+*dto.CaseIdLike+"%")
	}
	if dto.CaseIdNotEqual != nil {
		db = db.Where("case_id != ?", *dto.CaseIdNotEqual)
	}
	if dto.CaseIdNotLike != nil {
		db = db.Where("case_id NOT ILIKE ?", "%"+*dto.CaseIdNotLike+"%")
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

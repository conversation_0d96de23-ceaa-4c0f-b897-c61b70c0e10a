// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// UiReportQueryDTO represents query parameters for UiReport
type UiReportQueryDTO struct {
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	RelationIdEqual *string `json:"relationidequal"`
	RelationIdLike *string `json:"relationidlike"`
	RelationIdNotEqual *string `json:"relationidnotequal"`
	RelationIdNotLike *string `json:"relationidnotlike"`
	ExecTypeEqual *string `json:"exectypeequal"`
	ExecTypeLike *string `json:"exectypelike"`
	ExecTypeNotEqual *string `json:"exectypenotequal"`
	ExecTypeNotLike *string `json:"exectypenotlike"`
	EnvEqual *string `json:"envequal"`
	EnvLike *string `json:"envlike"`
	EnvNotEqual *string `json:"envnotequal"`
	EnvNotLike *string `json:"envnotlike"`
	EnvExpand bool `json:"envexpand"`
	StatusEqual *string `json:"statusequal"`
	StatusLike *string `json:"statuslike"`
	StatusNotEqual *string `json:"statusnotequal"`
	StatusNotLike *string `json:"statusnotlike"`
	CaseTotalCountEqual *int `json:"casetotalcountequal"`
	CaseTotalCountNotEqual *int `json:"casetotalcountnotequal"`
	CaseTotalCountGreater *int `json:"casetotalcountgreater"`
	CaseTotalCountLess *int `json:"casetotalcountless"`
	CaseTotalCountGreaterEqual *int `json:"casetotalcountgreaterequal"`
	CaseTotalCountLessEqual *int `json:"casetotalcountlessequal"`
	CasePassCountEqual *int `json:"casepasscountequal"`
	CasePassCountNotEqual *int `json:"casepasscountnotequal"`
	CasePassCountGreater *int `json:"casepasscountgreater"`
	CasePassCountLess *int `json:"casepasscountless"`
	CasePassCountGreaterEqual *int `json:"casepasscountgreaterequal"`
	CasePassCountLessEqual *int `json:"casepasscountlessequal"`
	CaseFailCountEqual *int `json:"casefailcountequal"`
	CaseFailCountNotEqual *int `json:"casefailcountnotequal"`
	CaseFailCountGreater *int `json:"casefailcountgreater"`
	CaseFailCountLess *int `json:"casefailcountless"`
	CaseFailCountGreaterEqual *int `json:"casefailcountgreaterequal"`
	CaseFailCountLessEqual *int `json:"casefailcountlessequal"`
	PassRateEqual *string `json:"passrateequal"`
	PassRateLike *string `json:"passratelike"`
	PassRateNotEqual *string `json:"passratenotequal"`
	PassRateNotLike *string `json:"passratenotlike"`
	ExecStartTimeEqual *int64 `json:"execstarttimeequal"`
	ExecStartTimeNotEqual *int64 `json:"execstarttimenotequal"`
	ExecStartTimeGreater *int64 `json:"execstarttimegreater"`
	ExecStartTimeLess *int64 `json:"execstarttimeless"`
	ExecStartTimeGreaterEqual *int64 `json:"execstarttimegreaterequal"`
	ExecStartTimeLessEqual *int64 `json:"execstarttimelessequal"`
	ExecEndTimeEqual *int64 `json:"execendtimeequal"`
	ExecEndTimeNotEqual *int64 `json:"execendtimenotequal"`
	ExecEndTimeGreater *int64 `json:"execendtimegreater"`
	ExecEndTimeLess *int64 `json:"execendtimeless"`
	ExecEndTimeGreaterEqual *int64 `json:"execendtimegreaterequal"`
	ExecEndTimeLessEqual *int64 `json:"execendtimelessequal"`
	DurationEqual *int64 `json:"durationequal"`
	DurationNotEqual *int64 `json:"durationnotequal"`
	DurationGreater *int64 `json:"durationgreater"`
	DurationLess *int64 `json:"durationless"`
	DurationGreaterEqual *int64 `json:"durationgreaterequal"`
	DurationLessEqual *int64 `json:"durationlessequal"`
	ExecutorEqual *string `json:"executorequal"`
	ExecutorLike *string `json:"executorlike"`
	ExecutorNotEqual *string `json:"executornotequal"`
	ExecutorNotLike *string `json:"executornotlike"`
	ExecutorExpand bool `json:"executorexpand"`
	SpaceIdEqual *string `json:"spaceidequal"`
	SpaceIdLike *string `json:"spaceidlike"`
	SpaceIdNotEqual *string `json:"spaceidnotequal"`
	SpaceIdNotLike *string `json:"spaceidnotlike"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *UiReportQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "env":
				dto.EnvExpand = true
			case "executor":
				dto.ExecutorExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameEqual = &value
					
				case "relation_id":
					dto.RelationIdEqual = &value
					
				case "exec_type":
					dto.ExecTypeEqual = &value
					
				case "env":
					dto.EnvEqual = &value
					
				case "status":
					dto.StatusEqual = &value
					
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountEqual = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountEqual = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountEqual = &intVal
					
				case "pass_rate":
					dto.PassRateEqual = &value
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationEqual = &int64Val
					
				case "executor":
					dto.ExecutorEqual = &value
					
				case "space_id":
					dto.SpaceIdEqual = &value
					
				case "k8s_task_ids":
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotEqual = &value
					
				case "relation_id":
					dto.RelationIdNotEqual = &value
					
				case "exec_type":
					dto.ExecTypeNotEqual = &value
					
				case "env":
					dto.EnvNotEqual = &value
					
				case "status":
					dto.StatusNotEqual = &value
					
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountNotEqual = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountNotEqual = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountNotEqual = &intVal
					
				case "pass_rate":
					dto.PassRateNotEqual = &value
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeNotEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeNotEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationNotEqual = &int64Val
					
				case "executor":
					dto.ExecutorNotEqual = &value
					
				case "space_id":
					dto.SpaceIdNotEqual = &value
					
				case "k8s_task_ids":
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameLike = &value
				case "relation_id":
					dto.RelationIdLike = &value
				case "exec_type":
					dto.ExecTypeLike = &value
				case "env":
					dto.EnvLike = &value
				case "status":
					dto.StatusLike = &value
				case "pass_rate":
					dto.PassRateLike = &value
				case "executor":
					dto.ExecutorLike = &value
				case "space_id":
					dto.SpaceIdLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotLike = &value
				case "relation_id":
					dto.RelationIdNotLike = &value
				case "exec_type":
					dto.ExecTypeNotLike = &value
				case "env":
					dto.EnvNotLike = &value
				case "status":
					dto.StatusNotLike = &value
				case "pass_rate":
					dto.PassRateNotLike = &value
				case "executor":
					dto.ExecutorNotLike = &value
				case "space_id":
					dto.SpaceIdNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountGreaterEqual = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountGreaterEqual = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountGreaterEqual = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeGreaterEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeGreaterEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationGreaterEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountLessEqual = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountLessEqual = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountLessEqual = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeLessEqual = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeLessEqual = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationLessEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountGreater = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountGreater = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountGreater = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeGreater = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeGreater = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationGreater = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "case_total_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_total_count: %v", err)
					}
					dto.CaseTotalCountLess = &intVal
					
				case "case_pass_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_pass_count: %v", err)
					}
					dto.CasePassCountLess = &intVal
					
				case "case_fail_count":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for case_fail_count: %v", err)
					}
					dto.CaseFailCountLess = &intVal
					
				case "exec_start_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_start_time: %v", err)
					}
					dto.ExecStartTimeLess = &int64Val
					
				case "exec_end_time":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for exec_end_time: %v", err)
					}
					dto.ExecEndTimeLess = &int64Val
					
				case "duration":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for duration: %v", err)
					}
					dto.DurationLess = &int64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *UiReportQueryDTO) ToGORMQuery(db *gorm.DB, isCount bool) (*gorm.DB, error) {
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.RelationIdEqual != nil {
		db = db.Where("relation_id = ?", *dto.RelationIdEqual)
	}
	if dto.RelationIdLike != nil {
		db = db.Where("relation_id ILIKE ?", "%"+*dto.RelationIdLike+"%")
	}
	if dto.RelationIdNotEqual != nil {
		db = db.Where("relation_id != ?", *dto.RelationIdNotEqual)
	}
	if dto.RelationIdNotLike != nil {
		db = db.Where("relation_id NOT ILIKE ?", "%"+*dto.RelationIdNotLike+"%")
	}
	
	
	
	if dto.ExecTypeEqual != nil {
		db = db.Where("exec_type = ?", *dto.ExecTypeEqual)
	}
	if dto.ExecTypeLike != nil {
		db = db.Where("exec_type ILIKE ?", "%"+*dto.ExecTypeLike+"%")
	}
	if dto.ExecTypeNotEqual != nil {
		db = db.Where("exec_type != ?", *dto.ExecTypeNotEqual)
	}
	if dto.ExecTypeNotLike != nil {
		db = db.Where("exec_type NOT ILIKE ?", "%"+*dto.ExecTypeNotLike+"%")
	}
	
	
	
	if dto.EnvEqual != nil {
		db = db.Where("env = ?", *dto.EnvEqual)
	}
	if dto.EnvLike != nil {
		db = db.Where("env ILIKE ?", "%"+*dto.EnvLike+"%")
	}
	if dto.EnvNotEqual != nil {
		db = db.Where("env != ?", *dto.EnvNotEqual)
	}
	if dto.EnvNotLike != nil {
		db = db.Where("env NOT ILIKE ?", "%"+*dto.EnvNotLike+"%")
	}
	
	
	
	if dto.StatusEqual != nil {
		db = db.Where("status = ?", *dto.StatusEqual)
	}
	if dto.StatusLike != nil {
		db = db.Where("status ILIKE ?", "%"+*dto.StatusLike+"%")
	}
	if dto.StatusNotEqual != nil {
		db = db.Where("status != ?", *dto.StatusNotEqual)
	}
	if dto.StatusNotLike != nil {
		db = db.Where("status NOT ILIKE ?", "%"+*dto.StatusNotLike+"%")
	}
	
	
	
	if dto.CaseTotalCountEqual != nil {
		db = db.Where("case_total_count = ?", *dto.CaseTotalCountEqual)
	}
	if dto.CaseTotalCountNotEqual != nil {
		db = db.Where("case_total_count != ?", *dto.CaseTotalCountNotEqual)
	}
	
	if dto.CaseTotalCountGreater != nil {
		db = db.Where("case_total_count > ?", *dto.CaseTotalCountGreater)
	}
	if dto.CaseTotalCountLess != nil {
		db = db.Where("case_total_count < ?", *dto.CaseTotalCountLess)
	}
	if dto.CaseTotalCountGreaterEqual != nil {
		db = db.Where("case_total_count >= ?", *dto.CaseTotalCountGreaterEqual)
	}
	if dto.CaseTotalCountLessEqual != nil {
		db = db.Where("case_total_count <= ?", *dto.CaseTotalCountLessEqual)
	}
	
	
	
	
	if dto.CasePassCountEqual != nil {
		db = db.Where("case_pass_count = ?", *dto.CasePassCountEqual)
	}
	if dto.CasePassCountNotEqual != nil {
		db = db.Where("case_pass_count != ?", *dto.CasePassCountNotEqual)
	}
	
	if dto.CasePassCountGreater != nil {
		db = db.Where("case_pass_count > ?", *dto.CasePassCountGreater)
	}
	if dto.CasePassCountLess != nil {
		db = db.Where("case_pass_count < ?", *dto.CasePassCountLess)
	}
	if dto.CasePassCountGreaterEqual != nil {
		db = db.Where("case_pass_count >= ?", *dto.CasePassCountGreaterEqual)
	}
	if dto.CasePassCountLessEqual != nil {
		db = db.Where("case_pass_count <= ?", *dto.CasePassCountLessEqual)
	}
	
	
	
	
	if dto.CaseFailCountEqual != nil {
		db = db.Where("case_fail_count = ?", *dto.CaseFailCountEqual)
	}
	if dto.CaseFailCountNotEqual != nil {
		db = db.Where("case_fail_count != ?", *dto.CaseFailCountNotEqual)
	}
	
	if dto.CaseFailCountGreater != nil {
		db = db.Where("case_fail_count > ?", *dto.CaseFailCountGreater)
	}
	if dto.CaseFailCountLess != nil {
		db = db.Where("case_fail_count < ?", *dto.CaseFailCountLess)
	}
	if dto.CaseFailCountGreaterEqual != nil {
		db = db.Where("case_fail_count >= ?", *dto.CaseFailCountGreaterEqual)
	}
	if dto.CaseFailCountLessEqual != nil {
		db = db.Where("case_fail_count <= ?", *dto.CaseFailCountLessEqual)
	}
	
	
	
	
	if dto.PassRateEqual != nil {
		db = db.Where("pass_rate = ?", *dto.PassRateEqual)
	}
	if dto.PassRateLike != nil {
		db = db.Where("pass_rate ILIKE ?", "%"+*dto.PassRateLike+"%")
	}
	if dto.PassRateNotEqual != nil {
		db = db.Where("pass_rate != ?", *dto.PassRateNotEqual)
	}
	if dto.PassRateNotLike != nil {
		db = db.Where("pass_rate NOT ILIKE ?", "%"+*dto.PassRateNotLike+"%")
	}
	
	
	
	if dto.ExecStartTimeEqual != nil {
		db = db.Where("exec_start_time = ?", *dto.ExecStartTimeEqual)
	}
	if dto.ExecStartTimeNotEqual != nil {
		db = db.Where("exec_start_time != ?", *dto.ExecStartTimeNotEqual)
	}
	
	if dto.ExecStartTimeGreater != nil {
		db = db.Where("exec_start_time > ?", *dto.ExecStartTimeGreater)
	}
	if dto.ExecStartTimeLess != nil {
		db = db.Where("exec_start_time < ?", *dto.ExecStartTimeLess)
	}
	if dto.ExecStartTimeGreaterEqual != nil {
		db = db.Where("exec_start_time >= ?", *dto.ExecStartTimeGreaterEqual)
	}
	if dto.ExecStartTimeLessEqual != nil {
		db = db.Where("exec_start_time <= ?", *dto.ExecStartTimeLessEqual)
	}
	
	
	
	
	if dto.ExecEndTimeEqual != nil {
		db = db.Where("exec_end_time = ?", *dto.ExecEndTimeEqual)
	}
	if dto.ExecEndTimeNotEqual != nil {
		db = db.Where("exec_end_time != ?", *dto.ExecEndTimeNotEqual)
	}
	
	if dto.ExecEndTimeGreater != nil {
		db = db.Where("exec_end_time > ?", *dto.ExecEndTimeGreater)
	}
	if dto.ExecEndTimeLess != nil {
		db = db.Where("exec_end_time < ?", *dto.ExecEndTimeLess)
	}
	if dto.ExecEndTimeGreaterEqual != nil {
		db = db.Where("exec_end_time >= ?", *dto.ExecEndTimeGreaterEqual)
	}
	if dto.ExecEndTimeLessEqual != nil {
		db = db.Where("exec_end_time <= ?", *dto.ExecEndTimeLessEqual)
	}
	
	
	
	
	if dto.DurationEqual != nil {
		db = db.Where("duration = ?", *dto.DurationEqual)
	}
	if dto.DurationNotEqual != nil {
		db = db.Where("duration != ?", *dto.DurationNotEqual)
	}
	
	if dto.DurationGreater != nil {
		db = db.Where("duration > ?", *dto.DurationGreater)
	}
	if dto.DurationLess != nil {
		db = db.Where("duration < ?", *dto.DurationLess)
	}
	if dto.DurationGreaterEqual != nil {
		db = db.Where("duration >= ?", *dto.DurationGreaterEqual)
	}
	if dto.DurationLessEqual != nil {
		db = db.Where("duration <= ?", *dto.DurationLessEqual)
	}
	
	
	
	
	if dto.ExecutorEqual != nil {
		db = db.Where("executor = ?", *dto.ExecutorEqual)
	}
	if dto.ExecutorLike != nil {
		db = db.Where("executor ILIKE ?", "%"+*dto.ExecutorLike+"%")
	}
	if dto.ExecutorNotEqual != nil {
		db = db.Where("executor != ?", *dto.ExecutorNotEqual)
	}
	if dto.ExecutorNotLike != nil {
		db = db.Where("executor NOT ILIKE ?", "%"+*dto.ExecutorNotLike+"%")
	}
	
	
	
	if dto.SpaceIdEqual != nil {
		db = db.Where("space_id = ?", *dto.SpaceIdEqual)
	}
	if dto.SpaceIdLike != nil {
		db = db.Where("space_id ILIKE ?", "%"+*dto.SpaceIdLike+"%")
	}
	if dto.SpaceIdNotEqual != nil {
		db = db.Where("space_id != ?", *dto.SpaceIdNotEqual)
	}
	if dto.SpaceIdNotLike != nil {
		db = db.Where("space_id NOT ILIKE ?", "%"+*dto.SpaceIdNotLike+"%")
	}
	
	
	
	
	if !isCount {
		// Apply sorting
		if dto.SortBy != "" {
			if dto.SortOrder == "DESC" {
				db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
			} else {
				db = db.Order(dto.SortBy)
			}
		}

		// Apply pagination
		if dto.PerPage > 0 {
			db = db.Limit(dto.PerPage)
			if dto.Page > 0 {
				offset := (dto.Page - 1) * dto.PerPage
				db = db.Offset(offset)
			}
		}
	}

	return db, nil
}

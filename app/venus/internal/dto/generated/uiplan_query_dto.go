// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// UiPlanQueryDTO represents query parameters for UiPlan
type UiPlanQueryDTO struct {
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	DescriptionEqual *string `json:"descriptionequal"`
	DescriptionLike *string `json:"descriptionlike"`
	DescriptionNotEqual *string `json:"descriptionnotequal"`
	DescriptionNotLike *string `json:"descriptionnotlike"`
	EnableTimerEqual *bool `json:"enabletimerequal"`
	EnablePipelineEqual *bool `json:"enablepipelineequal"`
	NoticeRuleEqual *string `json:"noticeruleequal"`
	NoticeRuleLike *string `json:"noticerulelike"`
	NoticeRuleNotEqual *string `json:"noticerulenotequal"`
	NoticeRuleNotLike *string `json:"noticerulenotlike"`
	NoticeUsersExpand bool `json:"noticeusersexpand"`
	EnvEqual *string `json:"envequal"`
	EnvLike *string `json:"envlike"`
	EnvNotEqual *string `json:"envnotequal"`
	EnvNotLike *string `json:"envnotlike"`
	EnvExpand bool `json:"envexpand"`
	RetryTimesEqual *int `json:"retrytimesequal"`
	RetryTimesNotEqual *int `json:"retrytimesnotequal"`
	RetryTimesGreater *int `json:"retrytimesgreater"`
	RetryTimesLess *int `json:"retrytimesless"`
	RetryTimesGreaterEqual *int `json:"retrytimesgreaterequal"`
	RetryTimesLessEqual *int `json:"retrytimeslessequal"`
	CaseIdsExpand bool `json:"caseidsexpand"`
	CaseCountEqual *int64 `json:"casecountequal"`
	CaseCountNotEqual *int64 `json:"casecountnotequal"`
	CaseCountGreater *int64 `json:"casecountgreater"`
	CaseCountLess *int64 `json:"casecountless"`
	CaseCountGreaterEqual *int64 `json:"casecountgreaterequal"`
	CaseCountLessEqual *int64 `json:"casecountlessequal"`
	SpaceIdEqual *string `json:"spaceidequal"`
	SpaceIdLike *string `json:"spaceidlike"`
	SpaceIdNotEqual *string `json:"spaceidnotequal"`
	SpaceIdNotLike *string `json:"spaceidnotlike"`
	AppIdEqual *string `json:"appidequal"`
	AppIdLike *string `json:"appidlike"`
	AppIdNotEqual *string `json:"appidnotequal"`
	AppIdNotLike *string `json:"appidnotlike"`
	AppIdExpand bool `json:"appidexpand"`
	LastExecInfoEqual *string `json:"lastexecinfoequal"`
	LastExecInfoLike *string `json:"lastexecinfolike"`
	LastExecInfoNotEqual *string `json:"lastexecinfonotequal"`
	LastExecInfoNotLike *string `json:"lastexecinfonotlike"`
	LastExecInfoExpand bool `json:"lastexecinfoexpand"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *UiPlanQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "notice_users":
				dto.NoticeUsersExpand = true
			case "env":
				dto.EnvExpand = true
			case "case_ids":
				dto.CaseIdsExpand = true
			case "app_id":
				dto.AppIdExpand = true
			case "last_exec_info":
				dto.LastExecInfoExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameEqual = &value
					
				case "description":
					dto.DescriptionEqual = &value
					
				case "enable_timer":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for enable_timer: %v", err)
					}
					dto.EnableTimerEqual = &boolVal
					
				case "enable_pipeline":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for enable_pipeline: %v", err)
					}
					dto.EnablePipelineEqual = &boolVal
					
				case "notice_rule":
					dto.NoticeRuleEqual = &value
					
				case "notice_users":
					
				case "notice_type":
					
				case "env":
					dto.EnvEqual = &value
					
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesEqual = &intVal
					
				case "case_ids":
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountEqual = &int64Val
					
				case "space_id":
					dto.SpaceIdEqual = &value
					
				case "app_id":
					dto.AppIdEqual = &value
					
				case "last_exec_info":
					dto.LastExecInfoEqual = &value
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotEqual = &value
					
				case "description":
					dto.DescriptionNotEqual = &value
					
				case "enable_timer":
					
				case "enable_pipeline":
					
				case "notice_rule":
					dto.NoticeRuleNotEqual = &value
					
				case "notice_users":
					
				case "notice_type":
					
				case "env":
					dto.EnvNotEqual = &value
					
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesNotEqual = &intVal
					
				case "case_ids":
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountNotEqual = &int64Val
					
				case "space_id":
					dto.SpaceIdNotEqual = &value
					
				case "app_id":
					dto.AppIdNotEqual = &value
					
				case "last_exec_info":
					dto.LastExecInfoNotEqual = &value
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameLike = &value
				case "description":
					dto.DescriptionLike = &value
				case "notice_rule":
					dto.NoticeRuleLike = &value
				case "env":
					dto.EnvLike = &value
				case "space_id":
					dto.SpaceIdLike = &value
				case "app_id":
					dto.AppIdLike = &value
				case "last_exec_info":
					dto.LastExecInfoLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotLike = &value
				case "description":
					dto.DescriptionNotLike = &value
				case "notice_rule":
					dto.NoticeRuleNotLike = &value
				case "env":
					dto.EnvNotLike = &value
				case "space_id":
					dto.SpaceIdNotLike = &value
				case "app_id":
					dto.AppIdNotLike = &value
				case "last_exec_info":
					dto.LastExecInfoNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesGreaterEqual = &intVal
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountGreaterEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesLessEqual = &intVal
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountLessEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesGreater = &intVal
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountGreater = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "retry_times":
					intVal, err := strconv.Atoi(value)
					if err != nil {
						return fmt.Errorf("invalid int value for retry_times: %v", err)
					}
					dto.RetryTimesLess = &intVal
					
				case "case_count":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for case_count: %v", err)
					}
					dto.CaseCountLess = &int64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *UiPlanQueryDTO) ToGORMQuery(db *gorm.DB, isCount bool) (*gorm.DB, error) {
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.DescriptionEqual != nil {
		db = db.Where("description = ?", *dto.DescriptionEqual)
	}
	if dto.DescriptionLike != nil {
		db = db.Where("description ILIKE ?", "%"+*dto.DescriptionLike+"%")
	}
	if dto.DescriptionNotEqual != nil {
		db = db.Where("description != ?", *dto.DescriptionNotEqual)
	}
	if dto.DescriptionNotLike != nil {
		db = db.Where("description NOT ILIKE ?", "%"+*dto.DescriptionNotLike+"%")
	}
	
	
	
	if dto.EnableTimerEqual != nil {
		db = db.Where("enable_timer = ?", *dto.EnableTimerEqual)
	}
	
	
	
	if dto.EnablePipelineEqual != nil {
		db = db.Where("enable_pipeline = ?", *dto.EnablePipelineEqual)
	}
	
	
	
	if dto.NoticeRuleEqual != nil {
		db = db.Where("notice_rule = ?", *dto.NoticeRuleEqual)
	}
	if dto.NoticeRuleLike != nil {
		db = db.Where("notice_rule ILIKE ?", "%"+*dto.NoticeRuleLike+"%")
	}
	if dto.NoticeRuleNotEqual != nil {
		db = db.Where("notice_rule != ?", *dto.NoticeRuleNotEqual)
	}
	if dto.NoticeRuleNotLike != nil {
		db = db.Where("notice_rule NOT ILIKE ?", "%"+*dto.NoticeRuleNotLike+"%")
	}
	
	
	
	
	
	
	
	if dto.EnvEqual != nil {
		db = db.Where("env = ?", *dto.EnvEqual)
	}
	if dto.EnvLike != nil {
		db = db.Where("env ILIKE ?", "%"+*dto.EnvLike+"%")
	}
	if dto.EnvNotEqual != nil {
		db = db.Where("env != ?", *dto.EnvNotEqual)
	}
	if dto.EnvNotLike != nil {
		db = db.Where("env NOT ILIKE ?", "%"+*dto.EnvNotLike+"%")
	}
	
	
	
	if dto.RetryTimesEqual != nil {
		db = db.Where("retry_times = ?", *dto.RetryTimesEqual)
	}
	if dto.RetryTimesNotEqual != nil {
		db = db.Where("retry_times != ?", *dto.RetryTimesNotEqual)
	}
	
	if dto.RetryTimesGreater != nil {
		db = db.Where("retry_times > ?", *dto.RetryTimesGreater)
	}
	if dto.RetryTimesLess != nil {
		db = db.Where("retry_times < ?", *dto.RetryTimesLess)
	}
	if dto.RetryTimesGreaterEqual != nil {
		db = db.Where("retry_times >= ?", *dto.RetryTimesGreaterEqual)
	}
	if dto.RetryTimesLessEqual != nil {
		db = db.Where("retry_times <= ?", *dto.RetryTimesLessEqual)
	}
	
	
	
	
	
	
	if dto.CaseCountEqual != nil {
		db = db.Where("case_count = ?", *dto.CaseCountEqual)
	}
	if dto.CaseCountNotEqual != nil {
		db = db.Where("case_count != ?", *dto.CaseCountNotEqual)
	}
	
	if dto.CaseCountGreater != nil {
		db = db.Where("case_count > ?", *dto.CaseCountGreater)
	}
	if dto.CaseCountLess != nil {
		db = db.Where("case_count < ?", *dto.CaseCountLess)
	}
	if dto.CaseCountGreaterEqual != nil {
		db = db.Where("case_count >= ?", *dto.CaseCountGreaterEqual)
	}
	if dto.CaseCountLessEqual != nil {
		db = db.Where("case_count <= ?", *dto.CaseCountLessEqual)
	}
	
	
	
	
	if dto.SpaceIdEqual != nil {
		db = db.Where("space_id = ?", *dto.SpaceIdEqual)
	}
	if dto.SpaceIdLike != nil {
		db = db.Where("space_id ILIKE ?", "%"+*dto.SpaceIdLike+"%")
	}
	if dto.SpaceIdNotEqual != nil {
		db = db.Where("space_id != ?", *dto.SpaceIdNotEqual)
	}
	if dto.SpaceIdNotLike != nil {
		db = db.Where("space_id NOT ILIKE ?", "%"+*dto.SpaceIdNotLike+"%")
	}
	
	
	
	if dto.AppIdEqual != nil {
		db = db.Where("app_id = ?", *dto.AppIdEqual)
	}
	if dto.AppIdLike != nil {
		db = db.Where("app_id ILIKE ?", "%"+*dto.AppIdLike+"%")
	}
	if dto.AppIdNotEqual != nil {
		db = db.Where("app_id != ?", *dto.AppIdNotEqual)
	}
	if dto.AppIdNotLike != nil {
		db = db.Where("app_id NOT ILIKE ?", "%"+*dto.AppIdNotLike+"%")
	}
	
	
	
	if dto.LastExecInfoEqual != nil {
		db = db.Where("last_exec_info = ?", *dto.LastExecInfoEqual)
	}
	if dto.LastExecInfoLike != nil {
		db = db.Where("last_exec_info ILIKE ?", "%"+*dto.LastExecInfoLike+"%")
	}
	if dto.LastExecInfoNotEqual != nil {
		db = db.Where("last_exec_info != ?", *dto.LastExecInfoNotEqual)
	}
	if dto.LastExecInfoNotLike != nil {
		db = db.Where("last_exec_info NOT ILIKE ?", "%"+*dto.LastExecInfoNotLike+"%")
	}
	
	
	if (!isCount) {
		// Apply sorting
		if dto.SortBy != "" {
			if dto.SortOrder == "DESC" {
				db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
			} else {
				db = db.Order(dto.SortBy)
			}
		}

		// Apply pagination
		if dto.PerPage > 0 {
			db = db.Limit(dto.PerPage)
			if dto.Page > 0 {
				offset := (dto.Page - 1) * dto.PerPage
				db = db.Offset(offset)
			}
		}
	}

	return db, nil
}

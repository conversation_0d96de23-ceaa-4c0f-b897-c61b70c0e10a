// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// UiCaseQueryDTO represents query parameters for UiCase
type UiCaseQueryDTO struct {
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	ParentIdEqual *string `json:"parentidequal"`
	ParentIdLike *string `json:"parentidlike"`
	ParentIdNotEqual *string `json:"parentidnotequal"`
	ParentIdNotLike *string `json:"parentidnotlike"`
	ParentIdExpand bool `json:"parentidexpand"`
	DescriptionEqual *string `json:"descriptionequal"`
	DescriptionLike *string `json:"descriptionlike"`
	DescriptionNotEqual *string `json:"descriptionnotequal"`
	DescriptionNotLike *string `json:"descriptionnotlike"`
	SiblingOrderEqual *int64 `json:"siblingorderequal"`
	SiblingOrderNotEqual *int64 `json:"siblingordernotequal"`
	SiblingOrderGreater *int64 `json:"siblingordergreater"`
	SiblingOrderLess *int64 `json:"siblingorderless"`
	SiblingOrderGreaterEqual *int64 `json:"siblingordergreaterequal"`
	SiblingOrderLessEqual *int64 `json:"siblingorderlessequal"`
	IsDirEqual *bool `json:"isdirequal"`
	LastReportIdEqual *string `json:"lastreportidequal"`
	LastReportIdLike *string `json:"lastreportidlike"`
	LastReportIdNotEqual *string `json:"lastreportidnotequal"`
	LastReportIdNotLike *string `json:"lastreportidnotlike"`
	LastReportIdExpand bool `json:"lastreportidexpand"`
	SpaceIdEqual *string `json:"spaceidequal"`
	SpaceIdLike *string `json:"spaceidlike"`
	SpaceIdNotEqual *string `json:"spaceidnotequal"`
	SpaceIdNotLike *string `json:"spaceidnotlike"`
	RecordingEqual *bool `json:"recordingequal"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *UiCaseQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "parent_id":
				dto.ParentIdExpand = true
			case "last_report_id":
				dto.LastReportIdExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameEqual = &value
					
				case "parent_id":
					dto.ParentIdEqual = &value
					
				case "description":
					dto.DescriptionEqual = &value
					
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderEqual = &int64Val
					
				case "is_dir":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for is_dir: %v", err)
					}
					dto.IsDirEqual = &boolVal
					
				case "last_report_id":
					dto.LastReportIdEqual = &value
					
				case "space_id":
					dto.SpaceIdEqual = &value
					
				case "recording":
					boolVal, err := strconv.ParseBool(value)
					if err != nil {
						return fmt.Errorf("invalid boolean value for recording: %v", err)
					}
					dto.RecordingEqual = &boolVal
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotEqual = &value
					
				case "parent_id":
					dto.ParentIdNotEqual = &value
					
				case "description":
					dto.DescriptionNotEqual = &value
					
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderNotEqual = &int64Val
					
				case "is_dir":
					
				case "last_report_id":
					dto.LastReportIdNotEqual = &value
					
				case "space_id":
					dto.SpaceIdNotEqual = &value
					
				case "recording":
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameLike = &value
				case "parent_id":
					dto.ParentIdLike = &value
				case "description":
					dto.DescriptionLike = &value
				case "last_report_id":
					dto.LastReportIdLike = &value
				case "space_id":
					dto.SpaceIdLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotLike = &value
				case "parent_id":
					dto.ParentIdNotLike = &value
				case "description":
					dto.DescriptionNotLike = &value
				case "last_report_id":
					dto.LastReportIdNotLike = &value
				case "space_id":
					dto.SpaceIdNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderGreaterEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderLessEqual = &int64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderGreater = &int64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "sibling_order":
					int64Val, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						return fmt.Errorf("invalid int64 value for sibling_order: %v", err)
					}
					dto.SiblingOrderLess = &int64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *UiCaseQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.ParentIdEqual != nil {
		db = db.Where("parent_id = ?", *dto.ParentIdEqual)
	}
	if dto.ParentIdLike != nil {
		db = db.Where("parent_id ILIKE ?", "%"+*dto.ParentIdLike+"%")
	}
	if dto.ParentIdNotEqual != nil {
		db = db.Where("parent_id != ?", *dto.ParentIdNotEqual)
	}
	if dto.ParentIdNotLike != nil {
		db = db.Where("parent_id NOT ILIKE ?", "%"+*dto.ParentIdNotLike+"%")
	}
	
	
	
	if dto.DescriptionEqual != nil {
		db = db.Where("description = ?", *dto.DescriptionEqual)
	}
	if dto.DescriptionLike != nil {
		db = db.Where("description ILIKE ?", "%"+*dto.DescriptionLike+"%")
	}
	if dto.DescriptionNotEqual != nil {
		db = db.Where("description != ?", *dto.DescriptionNotEqual)
	}
	if dto.DescriptionNotLike != nil {
		db = db.Where("description NOT ILIKE ?", "%"+*dto.DescriptionNotLike+"%")
	}
	
	
	
	if dto.SiblingOrderEqual != nil {
		db = db.Where("sibling_order = ?", *dto.SiblingOrderEqual)
	}
	if dto.SiblingOrderNotEqual != nil {
		db = db.Where("sibling_order != ?", *dto.SiblingOrderNotEqual)
	}
	
	if dto.SiblingOrderGreater != nil {
		db = db.Where("sibling_order > ?", *dto.SiblingOrderGreater)
	}
	if dto.SiblingOrderLess != nil {
		db = db.Where("sibling_order < ?", *dto.SiblingOrderLess)
	}
	if dto.SiblingOrderGreaterEqual != nil {
		db = db.Where("sibling_order >= ?", *dto.SiblingOrderGreaterEqual)
	}
	if dto.SiblingOrderLessEqual != nil {
		db = db.Where("sibling_order <= ?", *dto.SiblingOrderLessEqual)
	}
	
	
	
	
	if dto.IsDirEqual != nil {
		db = db.Where("is_dir = ?", *dto.IsDirEqual)
	}
	
	
	
	if dto.LastReportIdEqual != nil {
		db = db.Where("last_report_id = ?", *dto.LastReportIdEqual)
	}
	if dto.LastReportIdLike != nil {
		db = db.Where("last_report_id ILIKE ?", "%"+*dto.LastReportIdLike+"%")
	}
	if dto.LastReportIdNotEqual != nil {
		db = db.Where("last_report_id != ?", *dto.LastReportIdNotEqual)
	}
	if dto.LastReportIdNotLike != nil {
		db = db.Where("last_report_id NOT ILIKE ?", "%"+*dto.LastReportIdNotLike+"%")
	}
	
	
	
	if dto.SpaceIdEqual != nil {
		db = db.Where("space_id = ?", *dto.SpaceIdEqual)
	}
	if dto.SpaceIdLike != nil {
		db = db.Where("space_id ILIKE ?", "%"+*dto.SpaceIdLike+"%")
	}
	if dto.SpaceIdNotEqual != nil {
		db = db.Where("space_id != ?", *dto.SpaceIdNotEqual)
	}
	if dto.SpaceIdNotLike != nil {
		db = db.Where("space_id NOT ILIKE ?", "%"+*dto.SpaceIdNotLike+"%")
	}
	
	
	
	if dto.RecordingEqual != nil {
		db = db.Where("recording = ?", *dto.RecordingEqual)
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

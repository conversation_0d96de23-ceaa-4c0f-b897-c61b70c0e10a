package dto

type SimpleTreeDto struct {
	Id       string `json:"id" gorm:"column:id;primary_key;type:varchar(32)"`
	ParentId string `json:"parent_id" gorm:"column:parent_id;primary_key;type:varchar(32)"`
	Name     string `json:"name" gorm:"column:name;type:text;"`
	Order    int64  `json:"order" gorm:"column:order;type:int8;"`

	// 子节点
	Children []*SimpleTreeDto `json:"children" gorm:"-:all"`
}

func BuildSimpleTree(resTemp []*SimpleTreeDto) []*SimpleTreeDto {
	m := make(map[string]*SimpleTreeDto, len(resTemp))
	for _, element := range resTemp {
		m[element.Id] = element
	}
	resp := make([]*SimpleTreeDto, 0)
	for _, element := range resTemp {
		p, exist := m[element.ParentId]
		if !exist {
			resp = append(resp, m[element.Id])
		} else {
			p.Children = append(p.Children, element)
		}
	}
	return resp
}

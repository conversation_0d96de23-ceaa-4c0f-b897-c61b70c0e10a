package dao

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type UiCaseDao struct{}

func (UiCaseDao) CountByElementId(ctx *commoncontext.MantisContext, eId string) (int, error) {
	var res dto.CountDto
	sql := `select count( distinct case_id  ) as count  from ui_case_step where element_ids @> ?::jsonb and is_deleted ='false'`
	err := gormx.Raw(ctx, sql, &res, `["`+eId+`"]`)
	return res.Count, err
}

func (UiCaseDao) GetCaseByElementId(ctx *commoncontext.MantisContext, eId string, page, perPage int64) (*[]dto.ElementCaseDto, error) {
	offset := (page - 1) * perPage
	var res []dto.ElementCaseDto
	sql := `select t2.name,t2.modifier ,t2.updated, t2.id from (
		      select distinct case_id from ui_case_step where element_ids @> ?::jsonb and is_deleted ='false'
		    ) t1 left join ui_case t2 on t1.case_id=t2.id order by t2.updated  offset ? limit ?`
	err := gormx.Raw(ctx, sql, &res, `["`+eId+`"]`, offset, perPage)
	if err != nil {
		return nil, err
	}
	return &res, err
}

func (UiCaseDao) FindCaseById(ctx *commoncontext.MantisContext, id string) (*models.UiCase, error) {
	record := &models.UiCase{}
	record.Id = id
	record.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, record)
	return record, err
}

func (UiCaseDao) GetReferenceCaseByComponentIdWithPage(ctx *commoncontext.MantisContext, id string, page, perPage int64) ([]models.UiCase, error) {
	offset := (page - 1) * perPage
	res := make([]models.UiCase, 0)
	sql := `select t2.name,t2.modifier ,t2.updated, t2.id from (
		select distinct case_id from ui_case_step where reference_case_id = ? and is_deleted ='false'
	  ) t1 left join ui_case t2 on t1.case_id=t2.id order by t2.updated  offset ? limit ?`
	err := gormx.Raw(ctx, sql, &res, id, offset, perPage)
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (UiCaseDao) GetReferencedComponentIdsByCaseId(ctx *commoncontext.MantisContext, id string) ([]string, error) {
	sql := `select distinct reference_case_id from ui_case_step where case_id = ? and is_deleted = 'false'`
	res := make([]string, 0)
	err := gormx.Raw(ctx, sql, &res, id)
	return res, err
}

func (UiCaseDao) CountCaseByComponentIds(ctx *commoncontext.MantisContext, caseIds []string) (int, error) {
	query := `select count (distinct case_id) as count from ui_case_step where reference_case_id in ? and is_deleted ='false'`
	var res dto.CountDto
	err := gormx.Raw(ctx, query, &res, caseIds)
	if err != nil {
		return 0, err
	}
	return res.Count, nil
}

func (UiCaseDao) RecursiveGetCasesByParentId(ctx *commoncontext.MantisContext, parentId string) ([]models.UiCase, error) {
	res := make([]models.UiCase, 0)
	sql := `WITH RECURSIVE case_hierarchy AS (
			SELECT *, 0 as level
			FROM public.ui_case
			WHERE id = ?

			UNION ALL

			SELECT uc.*, ch.level + 1
			FROM public.ui_case uc
			INNER JOIN case_hierarchy ch ON uc.parent_id = ch.id
			WHERE uc.is_deleted = false
		)
		SELECT * FROM case_hierarchy;`
	err := gormx.Raw(ctx, sql, &res, parentId)
	if err != nil {
		return nil, err
	}
	return res, nil
}

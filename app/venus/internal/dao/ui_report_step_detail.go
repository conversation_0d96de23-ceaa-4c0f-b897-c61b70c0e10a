package dao

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type UiReportStepDetailDao struct{}

func (UiReportStepDetailDao) CountByReportIdAndCaseId(ctx *commoncontext.MantisContext, reportId, caseId string) (int, error) {
	sql := `select count(1) from ui_report_step_detail where report_id = ? and case_id = ? and is_dir = 'false' and is_deleted = 'false'`
	var res int
	err := gormx.Raw(ctx, sql, &res, reportId, caseId)
	return res, err
}

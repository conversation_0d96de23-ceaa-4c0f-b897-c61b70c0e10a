package dao

import (
	"encoding/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type UiElementDao struct{}

func (UiElementDao) FindElementById(ctx *commoncontext.MantisContext, id string) (*models.UiElement, error) {
	record := &models.UiElement{}
	record.Id = id
	record.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, record)
	return record, err
}

func (UiElementDao) BatchDeleteCheck(ctx *commoncontext.MantisContext, elementIds []string) (int, error) {
	query := `SELECT count(1) as count FROM ui_case_step
			WHERE element_ids @> ?::jsonb AND is_deleted = 'false'`
	var res dto.CountDto
	elementIdsJson, err := json.Marshal(&elementIds)
	if err != nil {
		return 0, err
	}
	err = gormx.Raw(ctx, query, &res, string(elementIdsJson))
	if err != nil {
		return 0, err
	}
	return res.Count, nil
}

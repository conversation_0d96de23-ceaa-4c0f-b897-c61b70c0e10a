package executor

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/message/payload"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/uuid"
	corev1 "k8s.io/api/core/v1"
	"knative.dev/pkg/apis"
)

func RunUiExecutorTask(ctx *commoncontext.MantisContext, uiExecutePayload payload.UiExecuteTaskPayload) error {
	req := request.RunRequest{
		UUID: fmt.Sprintf("cube-mantis-ui-%s", uuid.NewUUIDSha256()),
		Type: commonconstants.UiTaskType,
		Labels: map[string]string{
			commonconstants.PlatformLabel: "cube-mantis",
			commonconstants.ReportIdLabel: uiExecutePayload.ReportId,
		},
		Params: map[string]any{
			"callback":        uiExecutePayload.Callback,
			"input":           uiExecutePayload.Input,
			"reportid":        uiExecutePayload.ReportId,
			"filedownloadurl": uiExecutePayload.FileDownloadUrl,
			"variableurl":     uiExecutePayload.VariableUrl,
			"env":             uiExecutePayload.Env,
		},
	}
	dp, err := driver.NewDriverProvider()
	if err != nil {
		// 启动pod失败, 发送消息
		reportId := uiExecutePayload.ReportId
		report := models.UiReport{}
		report.Id = reportId
		report.Status = "fail"
		_, err = gormx.UpdateOneByCondition(ctx, &report)
		service.NoticeService{}.PlanNotice(uiExecutePayload.ReportId)
		return err
	}
	err = dp.Run(ctx.Context, req)
	if err != nil {
		return err
	}
	// 设置report的task id
	reportId := uiExecutePayload.ReportId
	report := models.UiReport{}
	report.Id = reportId
	if report.K8sTaskIds == nil {
		report.K8sTaskIds = make([]string, 0)
	}
	report.K8sTaskIds = append(report.K8sTaskIds, req.UUID)
	_, err = gormx.UpdateOneByCondition(ctx, &report)
	if err != nil {
		return err
	}
	return nil
}

func DealUiExecutorTask(condition apis.Condition, labels map[string]string) error {
	status := ""
	switch condition.Status {
	case corev1.ConditionTrue:
		status = constants.ReportStatusSuccess
	case corev1.ConditionFalse:
		status = constants.ReportStatusFail
	case corev1.ConditionUnknown:
	default:
		return fmt.Errorf("syncTaskRunRecord invaild taskrun status")
	}
	if status == constants.ReportStatusFail {
		reportId, ok := labels[commonconstants.ReportIdLabel]
		if !ok {
			return fmt.Errorf("error in getting report id")
		}
		report := models.UiReport{}
		report.Id = reportId
		report.Status = status
		_, err := gormx.UpdateOneByCondition(&commoncontext.MantisContext{}, &report)
		service.NoticeService{}.PlanNotice(reportId)
		return err
	}
	return nil
}

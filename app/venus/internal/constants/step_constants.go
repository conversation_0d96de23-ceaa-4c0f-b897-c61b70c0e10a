package constants

const (
	// ClosePageTypeFirst 第一个
	ClosePageTypeFirst = "first"
	// ClosePageTypeNext 下一个
	ClosePageTypeNext = "post"
	// ClosePageTypePrevious 上一个
	ClosePageTypePrevious = "pre"
	// ClosePageTypeLatest 最后一个
	ClosePageTypeLatest = "last"
	// ClosePageTypeCustomIndex 自定义Index
	ClosePageTypeCustomIndex = "customIndex"
	// ClosePageTypeCustomHandle 自定义句柄
	ClosePageTypeCustomHandle = "customHandle"
	// ClosePageAll all
	ClosePageAll = "all"
)

const (
	MouseLeftClick   = "leftClick"
	MouseRightClick  = "rightClick"
	MouseDoubleClick = "doubleClick"
)

// 等待事件的下拉列表
const (
	// time, element_exist, element_not_exist, visible, hidden, editable, not_editable
	WaitEventTime            = "time"
	WaitEventElementExist    = "element_exist"
	WaitEventElementNotExist = "element_not_exist"
	WaitEventVisible         = "visible"
	WaitEventHidden          = "hidden"
	WaitEventEditable        = "editable"
	WaitEventNotEditable     = "not_editable"
)

const (
	// StepDisableTrue 禁用状态
	StepDisableTrue = "1"
	// StepDisableFalse 非禁用状态
	StepDisableFalse = "0"
)

const (
	// ElementTypeRef 应用类型元素
	ElementTypeRef = 1
	// ElementTypeCustom 自定义类型元素
	ElementTypeCustom   = 2
	ElementTypeLocator  = "locator"
	ElementTypeSelector = "selector"
)

const (
	MouseDragTypePos       = "pos"
	MouseDragTypeElement   = "element"
	MouseScrollTypeElement = "element"
	MouseScrollTypePage    = "page"
)

const (
	InputTypeElement = "element"
	InputTypePress   = "press"
)

const (
	LoopConditionTimes = "times"
	LoopConditionData  = "data"
)

package front

import (
	"maps"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type FrontEnum struct {
	Label string `json:"label"` // 对应的中文
	Value string `json:"value"` // 对应的code
}

func configDropdownList() map[string][]FrontEnum {
	return map[string][]FrontEnum{
		"browserType": {
			FrontEnum{Value: constants.BrowserChrome, Label: "谷歌浏览器"},
			FrontEnum{Value: constants.BrowserFirefox, Label: "火狐浏览器"},
			FrontEnum{Value: constants.BrowserEdge, Label: "Edge浏览器"},
			FrontEnum{Value: constants.BrowserSafari, Label: "Safari浏览器"},
		},
		"browserViewMode": {
			FrontEnum{Value: constants.BrowserViewNoTrace, Label: "无跟踪"},
			FrontEnum{Value: constants.BrowserViewTrace, Label: "有跟踪"},
		},
		"dealError": {
			FrontEnum{Value: constants.DealErrorStop, Label: "停止"},
			FrontEnum{Value: constants.DealErrorRetry, Label: "重试"},
			FrontEnum{Value: constants.DealErrorIgnore, Label: "忽略"},
		},
		"screenShot": {
			FrontEnum{Value: constants.ScreenshotAlways, Label: "总是"},
			FrontEnum{Value: constants.ScreenshotErr, Label: "错误时"},
			FrontEnum{Value: constants.ScreenshotNone, Label: "从不"},
		},
		"window": {
			FrontEnum{Value: constants.WindowPC, Label: "PC窗口"},
			FrontEnum{Value: constants.WindowMobile, Label: "移动窗口"},
		},
	}
}

func actionsDropdownList() map[string][]FrontEnum {
	return map[string][]FrontEnum{
		"browser_group": {
			FrontEnum{Value: constants.ActionBrowserOpen, Label: "打开页面"},
			FrontEnum{Value: constants.ActionBrowserClose, Label: "关闭页面"},
			FrontEnum{Value: constants.ActionBrowserToggleWindow, Label: "切换窗口"},
			FrontEnum{Value: constants.ActionBrowserForward, Label: "前进"},
			FrontEnum{Value: constants.ActionBrowserBack, Label: "后退"},
			FrontEnum{Value: constants.ActionBrowserRefresh, Label: "刷新"},
		},
		"mouse_group": {
			FrontEnum{Value: constants.ActionMouseClick, Label: "点击"},
			FrontEnum{Value: constants.ActionMouseScroll, Label: "滚动"},
			FrontEnum{Value: constants.ActionMouseMove, Label: "悬浮/移动"},
			FrontEnum{Value: constants.ActionMouseDrag, Label: "拖拽"},
			FrontEnum{Value: constants.ActionMouseHold, Label: "长按"},
		},
		"input_group": {
			FrontEnum{Value: constants.ActionInput, Label: "输入"},
			FrontEnum{Value: constants.ActionInputSelect, Label: "选择"},
			FrontEnum{Value: constants.ActionInputUploadFiles, Label: "上传文件"},
		},
		"advanced_group": {
			FrontEnum{Value: constants.ActionAdvancedAssert, Label: "断言"},
			FrontEnum{Value: constants.ActionAdvancedCodeOperation, Label: "代码操作"},
			FrontEnum{Value: constants.ActionAdvancedCallApi, Label: "调用API"},
			FrontEnum{Value: constants.ActionAdvancedCallDatabase, Label: "数据库调用"},
			FrontEnum{Value: constants.ActionAdvancedWait, Label: "等待事件"},
			FrontEnum{Value: constants.ActionAdvancedDataWithdraw, Label: "关联提取"},
			FrontEnum{Value: constants.ActionAdvancedIfOperate, Label: "判断操作"},
			FrontEnum{Value: constants.ActionAdvancedLoopOperate, Label: "循环操作"},
		},
	}
}

func elementDropdownList() map[string][]FrontEnum {
	return map[string][]FrontEnum{
		"locatorType": {
			FrontEnum{Value: constants.LocatorTypeSelector, Label: "选择器"},
			FrontEnum{Value: constants.LocatorTypeLocator, Label: "定位器"},
		},
		"locatorMethod": {
			FrontEnum{Value: constants.LocatorMethodRole, Label: "角色"},
			FrontEnum{Value: constants.LocatorMethodText, Label: "文本"},
			FrontEnum{Value: constants.LocatorMethodLabel, Label: "标签"},
			FrontEnum{Value: constants.LocatorMethodPlaceholder, Label: "占位符"},
			FrontEnum{Value: constants.LocatorMethodAlt, Label: "alt"},
			FrontEnum{Value: constants.LocatorMethodTitle, Label: "标题"},
			FrontEnum{Value: constants.LocatorMethodCustomArr, Label: "自定义数组"},
		},
	}
}

func stepDropdownList() map[string][]FrontEnum {
	return map[string][]FrontEnum{
		"windowOperateType": {
			FrontEnum{Value: constants.ClosePageTypeFirst, Label: "第一个"},
			FrontEnum{Value: constants.ClosePageTypeNext, Label: "下一个"},
			FrontEnum{Value: constants.ClosePageTypePrevious, Label: "上一个"},
			FrontEnum{Value: constants.ClosePageTypeLatest, Label: "最后一个"},
			FrontEnum{Value: constants.ClosePageTypeCustomIndex, Label: "自定义Index"},
			FrontEnum{Value: constants.ClosePageTypeCustomHandle, Label: "自定义句柄"},
			FrontEnum{Value: constants.ClosePageAll, Label: "关闭所有"},
		},
		"mouseClickType": {
			FrontEnum{Value: constants.MouseLeftClick, Label: "左击"},
			FrontEnum{Value: constants.MouseDoubleClick, Label: "双击"},
			FrontEnum{Value: constants.MouseRightClick, Label: "右键"},
		},
		"mouseScrollType": {
			FrontEnum{Value: constants.MouseScrollTypeElement, Label: "页面中元素"},
			FrontEnum{Value: constants.MouseScrollTypePage, Label: "整个页面窗口"},
		},
		"mouseDragType": {
			FrontEnum{Value: constants.MouseDragTypeElement, Label: "推拽到目标元素"},
			FrontEnum{Value: constants.MouseDragTypePos, Label: "推拽到这个坐标"},
		},
	}
}

func other() map[string][]FrontEnum {
	return map[string][]FrontEnum{
		"operationName": {
			FrontEnum{Value: "equal", Label: "等于"},
			FrontEnum{Value: "notEqual", Label: "不等于"},
			FrontEnum{Value: "exist", Label: "存在"},
			FrontEnum{Value: "notExist", Label: "不存在"},
			FrontEnum{Value: "lessThan", Label: "小于"},
			FrontEnum{Value: "lessThanEqual", Label: "小于等于"},
			FrontEnum{Value: "greaterThan", Label: "大于"},
			FrontEnum{Value: "greaterThanEqual", Label: "大于等于"},
			FrontEnum{Value: "contain", Label: "包含"},
			FrontEnum{Value: "notContain", Label: "不包含"},
			FrontEnum{Value: "regularExpression", Label: "正则表达式"},
		},
	}
}

var cache map[string][]FrontEnum

func GetDropdownList(key string) []FrontEnum {
	if len(cache) == 0 {
		cache = make(map[string][]FrontEnum)
		maps.Copy(cache, configDropdownList())
		maps.Copy(cache, actionsDropdownList())
		maps.Copy(cache, elementDropdownList())
		maps.Copy(cache, stepDropdownList())
		maps.Copy(cache, other())
		logger.Logger.Info("初始化前端枚举缓存，len=", len(cache))
	}
	return cache[key]
}

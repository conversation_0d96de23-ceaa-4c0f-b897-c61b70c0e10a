// CRUD methods generated by pb-api-generator.

package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"gorm.io/gorm"
)

type UiCaseIdRequest struct {
	Id string `path:"id"`
}

type UiCaseController struct {
	*controller.BaseController
}

var DefaultUiCaseController UiCaseController

func (c *UiCaseController) List() (*commondto.PaginationRecord[models.UiCase], error) {
	query := dto.UiCaseQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.UiCase, 0)
	err = db.Model(&models.UiCase{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.ParentIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ParentId)
		}
		f, ok := expand.GetExpandFunc("ui_case")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["parent_id"] = res[items[i].ParentId]

			}
		}
	}

	if query.LastReportIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.LastReportId)
		}
		f, ok := expand.GetExpandFunc("ui_report")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["last_report_id"] = res[items[i].LastReportId]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where space_id = ? and is_deleted = false`, models.UiCase{}.TableName()), &total, c.Request.Header.Get("spaceid"))
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiCase]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiCaseController) Get(req *UiCaseIdRequest) (*models.UiCase, error) {
	res := &models.UiCase{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *UiCaseController) Delete(req *UiCaseIdRequest) error {
	err := caseService.Delete(c.MantisContext, req.Id)
	if err != nil {
		return err
	}
	count, err := gormx.Exec(c.MantisContext, `update ui_case_step set is_deleted=? where case_id = ? and is_deleted=false`,
		true, req.Id)
	if err != nil {
		return err
	}
	logger.Logger.Infof("删除用例(id=%s)下的 %d 条步骤", req.Id, count)
	return err
}

func (c *UiCaseController) Create(req *models.UiCase) (*models.UiCase, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiCaseController) Update(req *models.UiCase, idReq *UiCaseIdRequest) (*models.UiCase, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiCaseController) Move(req commondto.MoveTreeNodeRequest) error {
	tableName := models.UiCase{}.TableName()
	sql := fmt.Sprintf(`WITH RECURSIVE res AS (
		-- 非递归数据
		SELECT id,name,parent_id,is_root FROM "%s" WHERE id=?
		-- 递归
		UNION
		SELECT t2.id,t2.name,t2.parent_id,t2.is_root FROM res t1 INNER JOIN "%s" t2 ON t1.parent_id = t2.id
	)
	SELECT id FROM res WHERE (parent_id IS NULL OR parent_id = '') AND is_root=true AND name='根节点'`, tableName, tableName)
	for _, node := range req.Nodes {
		if node.NewParentId == "" {
			var res commondto.MoveDto
			// 查询此节点的虚拟父节点
			err := gormx.Raw(c.MantisContext, sql, &res, node.Id)
			if err != nil {
				return err
			}
			node.NewParentId = res.Id
		}
		if err := gormx.Move(c.MantisContext, tableName, node.Id, node.NewParentId, node.NewSiblingId); err != nil {
			return err
		}
	}
	return nil
}

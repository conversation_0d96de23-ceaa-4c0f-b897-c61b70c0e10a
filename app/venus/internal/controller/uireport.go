// CRUD methods generated by pb-api-generator.

package controller

import (
	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiReportIdRequest struct {
	Id string `path:"id"`
}

type UiReportController struct {
	*controller.BaseController
}

var DefaultUiReportController UiReportController

func (c *UiReportController) List() (*commondto.PaginationRecord[models.UiReport], error) {
	query := dto.UiReportQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext), false)
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.UiReport, 0)
	err = db.Model(&models.UiReport{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.EnvExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Env)
		}
		f, ok := expand.GetExpandFunc("env")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["env"] = res[items[i].Env]

			}
		}
	}

	if query.ExecutorExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Executor)
		}
		f, ok := expand.GetExpandFunc("user")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["executor"] = res[items[i].Executor]

			}
		}
	}

	var total int64
	dbc, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext), true)
	if err != nil {
		return nil, err
	}
	dbc = dbc.Select("count(1)").Where("is_deleted = ?", false).Where("space_id = ?", c.Request.Header.Get("spaceid")).Model(&models.UiReport{})
	err = dbc.Find(&total).Error
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiReport]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiReportController) Get(req *UiReportIdRequest) (*models.UiReport, error) {
	res := &models.UiReport{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	query := dto.UiReportQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	// Expand fields

	if query.EnvExpand {
		ids := make([]string, 0)
		ids = append(ids, res.Env)
		f, ok := expand.GetExpandFunc("env")
		if ok {
			expRes, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			if res.Expand == nil {
				res.Expand = make(map[string]any)
			}

			res.Expand["env"] = expRes[res.Env]

		}
	}

	if query.ExecutorExpand {
		ids := make([]string, 0)
		ids = append(ids, res.Executor)
		f, ok := expand.GetExpandFunc("user")
		if ok {
			expRes, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			if res.Expand == nil {
				res.Expand = make(map[string]any)
			}

			res.Expand["executor"] = expRes[res.Executor]

		}
	}
	return res, nil
}

func (c *UiReportController) Delete(req *UiReportIdRequest) error {
	model := &models.UiReport{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *UiReportController) BatchDelete(req commondto.BatchDeleteReq) error {
	del := &models.UiReport{}
	del.IsDeleted = true
	_, err := gormx.UpdateBatchByParamBuilder(c.MantisContext, gormx.NewParamBuilder().Model(&models.UiReport{}).In("id", req.Ids), del)
	return err
}

func (c *UiReportController) Create(req *models.UiReport) (*models.UiReport, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiReportController) Update(req *models.UiReport, idReq *UiReportIdRequest) (*models.UiReport, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

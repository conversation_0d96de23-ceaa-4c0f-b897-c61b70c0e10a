package controller

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeFrontController(rw http.ResponseWriter, req *http.Request) (*FrontController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &FrontController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeTypesController(rw http.ResponseWriter, req *http.Request) (*TypesController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &TypesController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeElementController(rw http.ResponseWriter, req *http.Request) (*ElementController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ElementController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeCaseController(rw http.ResponseWriter, req *http.Request) (*CaseController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &CaseController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeCaseStepController(rw http.ResponseWriter, req *http.Request) (*CaseStepController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &CaseStepController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeReportController(rw http.ResponseWriter, req *http.Request) (*ReportController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &ReportController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializePlanController(rw http.ResponseWriter, req *http.Request) (*PlanController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &PlanController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiCaseController(rw http.ResponseWriter, req *http.Request) (*UiCaseController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiCaseController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiCaseStepController(rw http.ResponseWriter, req *http.Request) (*UiCaseStepController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiCaseStepController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiElementController(rw http.ResponseWriter, req *http.Request) (*UiElementController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiElementController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiPlanController(rw http.ResponseWriter, req *http.Request) (*UiPlanController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiPlanController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiReportController(rw http.ResponseWriter, req *http.Request) (*UiReportController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiReportController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiReportCaseController(rw http.ResponseWriter, req *http.Request) (*UiReportCaseController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiReportCaseController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeUiReportStepDetailController(rw http.ResponseWriter, req *http.Request) (*UiReportStepDetailController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &UiReportStepDetailController{
		BaseController: baseController,
	}
	return c, nil
}

// CRUD methods generated by pb-api-generator.

package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiReportCaseIdRequest struct {
	Id string `path:"id"`
}

type UiReportCaseController struct {
	*controller.BaseController
}

var DefaultUiReportCaseController UiReportCaseController

func (c *UiReportCaseController) List() (*commondto.PaginationRecord[models.UiReportCase], error) {
	query := dto.UiReportCaseQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)

	items := make([]models.UiReportCase, 0)
	err = db.Model(&models.UiReportCase{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.ExecutorExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Executor)
		}
		f, ok := expand.GetExpandFunc("user")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["executor"] = res[items[i].Executor]

			}
		}
	}

	if query.ReportIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ReportId)
		}
		f, ok := expand.GetExpandFunc("ui_report")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["report_id"] = res[items[i].ReportId]

			}
		}
	}

	if query.CaseIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.CaseId)
		}
		f, ok := expand.GetExpandFunc("case_id")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["case_id"] = res[items[i].CaseId]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.UiReportCase{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiReportCase]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiReportCaseController) Get(req *UiReportCaseIdRequest) (*models.UiReportCase, error) {
	res := &models.UiReportCase{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *UiReportCaseController) Delete(req *UiReportCaseIdRequest) error {
	model := &models.UiReportCase{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *UiReportCaseController) Create(req *models.UiReportCase) (*models.UiReportCase, error) {
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiReportCaseController) Update(req *models.UiReportCase, idReq *UiReportCaseIdRequest) (*models.UiReportCase, error) {
	req.Id = idReq.Id
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

// CRUD methods generated by pb-api-generator.

package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiReportStepDetailIdRequest struct {
	Id string `path:"id"`
}

type UiReportStepDetailController struct {
	*controller.BaseController
}

var DefaultUiReportStepDetailController UiReportStepDetailController

func (c *UiReportStepDetailController) List() (*commondto.PaginationRecord[models.UiReportStepDetail], error) {
	query := dto.UiReportStepDetailQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)

	items := make([]models.UiReportStepDetail, 0)
	err = db.Model(&models.UiReportStepDetail{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.ReportIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ReportId)
		}
		f, ok := expand.GetExpandFunc("ui_report")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["report_id"] = res[items[i].ReportId]

			}
		}
	}

	if query.ReportCaseIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ReportCaseId)
		}
		f, ok := expand.GetExpandFunc("ui_report_case_detail")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["report_case_id"] = res[items[i].ReportCaseId]

			}
		}
	}

	if query.CaseIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.CaseId)
		}
		f, ok := expand.GetExpandFunc("ui_case")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["case_id"] = res[items[i].CaseId]

			}
		}
	}

	if query.StepIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.StepId)
		}
		f, ok := expand.GetExpandFunc("step_id")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["step_id"] = res[items[i].StepId]

			}
		}
	}

	if query.ParentIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ParentId)
		}
		f, ok := expand.GetExpandFunc("ui_report_step_detail")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["parent_id"] = res[items[i].ParentId]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.UiReportStepDetail{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiReportStepDetail]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiReportStepDetailController) Get(req *UiReportStepDetailIdRequest) (*models.UiReportStepDetail, error) {
	res := &models.UiReportStepDetail{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *UiReportStepDetailController) Delete(req *UiReportStepDetailIdRequest) error {
	model := &models.UiReportStepDetail{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *UiReportStepDetailController) Create(req *models.UiReportStepDetail) (*models.UiReportStepDetail, error) {
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiReportStepDetailController) Update(req *models.UiReportStepDetail, idReq *UiReportStepDetailIdRequest) (*models.UiReportStepDetail, error) {
	req.Id = idReq.Id
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiReportStepDetailController) Move(req commondto.MoveTreeNodeRequest) error {
	tableName := models.UiReportStepDetail{}.TableName()
	sql := fmt.Sprintf(`WITH RECURSIVE res AS (
		-- 非递归数据
		SELECT id,name,parent_id,is_root FROM "%s" WHERE id=?
		-- 递归
		UNION
		SELECT t2.id,t2.name,t2.parent_id,t2.is_root FROM res t1 INNER JOIN "%s" t2 ON t1.parent_id = t2.id
	)
	SELECT id FROM res WHERE (parent_id IS NULL OR parent_id = '') AND is_root=true AND name='根节点'`, tableName, tableName)
	for _, node := range req.Nodes {
		if node.NewParentId == "" {
			var res commondto.MoveDto
			// 查询此节点的虚拟父节点
			err := gormx.Raw(c.MantisContext, sql, &res, node.Id)
			if err != nil {
				return err
			}
			node.NewParentId = res.Id
		}
		if err := gormx.Move(c.MantisContext, tableName, node.Id, node.NewParentId, node.NewSiblingId); err != nil {
			return err
		}
	}
	return nil
}

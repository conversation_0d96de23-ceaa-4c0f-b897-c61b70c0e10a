// CRUD methods generated by pb-api-generator.

package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type UiCaseStepIdRequest struct {
	Id string `path:"id"`
}

type UiCaseStepController struct {
	*controller.BaseController
}

var DefaultUiCaseStepController UiCaseStepController

func (c *UiCaseStepController) List() (*commondto.PaginationRecord[models.UiCaseStep], error) {
	query := dto.UiCaseStepQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)

	items := make([]models.UiCaseStep, 0)
	err = db.Model(&models.UiCaseStep{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.CaseIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.CaseId)
		}
		f, ok := expand.GetExpandFunc("ui_case")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["case_id"] = res[items[i].CaseId]

			}
		}
	}

	if query.ParentIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ParentId)
		}
		f, ok := expand.GetExpandFunc("ui_case_step")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["parent_id"] = res[items[i].ParentId]

			}
		}
	}

	if query.ElementIdsExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.ElementIds...)
		}
		f, ok := expand.GetExpandFunc("ui_element")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				expands := make([]any, 0)
				for _, id := range items[i].ElementIds {
					if val, exists := res[id]; exists {
						expands = append(expands, val)
					}
				}
				items[i].Expand["element_ids"] = expands

			}
		}
	}

	if query.LatestReportStepIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.LatestReportStepId)
		}
		f, ok := expand.GetExpandFunc("ui_report_step_detail")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["latest_report_step_id"] = res[items[i].LatestReportStepId]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.UiCaseStep{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiCaseStep]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiCaseStepController) Get(req *UiCaseStepIdRequest) (*models.UiCaseStep, error) {
	res := &models.UiCaseStep{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *UiCaseStepController) Delete(req *UiCaseStepIdRequest) error {
	// 如果是引用，先修改用例
	model := &models.UiCaseStep{}
	model.Id = req.Id
	err := gormx.SelectOneByCondition(c.MantisContext, &model)
	if err != nil {
		return err
	}
	model.IsDeleted = true
	_, err = gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *UiCaseStepController) BatchDelete(req commondto.BatchDeleteReq) error {
	del := &models.UiCaseStep{}
	del.IsDeleted = true
	_, err := gormx.UpdateBatchByParamBuilder(c.MantisContext, gormx.NewParamBuilder().Model(&models.UiCaseStep{}).In("id", req.Ids), del)
	return err
}

func (c *UiCaseStepController) Create(req *models.UiCaseStep) (*models.UiCaseStep, error) {
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiCaseStepController) Update(req *models.UiCaseStep, idReq *UiCaseStepIdRequest) (*models.UiCaseStep, error) {
	req.Id = idReq.Id
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiCaseStepController) Move(req commondto.MoveTreeNodeRequest) error {
	tableName := models.UiCaseStep{}.TableName()
	sql := fmt.Sprintf(`WITH RECURSIVE res AS (
		-- 非递归数据
		SELECT id,name,parent_id,is_root FROM "%s" WHERE id=?
		-- 递归
		UNION
		SELECT t2.id,t2.name,t2.parent_id,t2.is_root FROM res t1 INNER JOIN "%s" t2 ON t1.parent_id = t2.id
	)
	SELECT id FROM res WHERE (parent_id IS NULL OR parent_id = '') AND is_root=true AND name='根节点'`, tableName, tableName)
	for _, node := range req.Nodes {
		if node.NewParentId == "" {
			var res commondto.MoveDto
			// 查询此节点的虚拟父节点
			err := gormx.Raw(c.MantisContext, sql, &res, node.Id)
			if err != nil {
				return err
			}
			node.NewParentId = res.Id
		}
		if err := gormx.Move(c.MantisContext, tableName, node.Id, node.NewParentId, node.NewSiblingId); err != nil {
			return err
		}
	}
	return nil
}

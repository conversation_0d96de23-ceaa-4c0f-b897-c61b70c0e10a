package controller

import (
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants/front"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type FrontController struct {
	*controller.BaseController
}

var DefaultFrontController FrontController

type DropdownRep struct {
	DropdownType string `schema:"dropdownType"`
}

func (c *FrontController) DropdownList(req *DropdownRep) ([]front.FrontEnum, error) {
	list := front.GetDropdownList(req.DropdownType)
	if list == nil {
		return nil, errors.New("未配置此枚举,请联系开发同学配置")
	}
	return list, nil
}

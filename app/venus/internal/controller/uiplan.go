// CRUD methods generated by pb-api-generator.

package controller

import (
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	"gorm.io/gorm"
)

type UiPlanIdRequest struct {
	Id string `path:"id"`
}

type UiPlanController struct {
	*controller.BaseController
}

var DefaultUiPlanController UiPlanController

func (c *UiPlanController) List() (*commondto.PaginationRecord[models.UiPlan], error) {
	query := dto.UiPlanQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext), false)
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.UiPlan, 0)
	err = db.Model(&models.UiPlan{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.NoticeUsersExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.NoticeUsers...)
		}
		f, ok := expand.GetExpandFunc("user")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				expands := make([]any, 0)
				for _, id := range items[i].NoticeUsers {
					if val, exists := res[id]; exists {
						expands = append(expands, val)
					}
				}
				items[i].Expand["notice_users"] = expands

			}
		}
	}

	if query.EnvExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Env)
		}
		f, ok := expand.GetExpandFunc("env")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["env"] = res[items[i].Env]

			}
		}
	}

	if query.CaseIdsExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.CaseIds...)
		}
		f, ok := expand.GetExpandFunc("ui_case")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				expands := make([]any, 0)
				for _, id := range items[i].CaseIds {
					if val, exists := res[id]; exists {
						expands = append(expands, val)
					}
				}
				items[i].Expand["case_ids"] = expands

			}
		}
	}

	if query.AppIdExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.AppId)
		}
		f, ok := expand.GetExpandFunc("app")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["app_id"] = res[items[i].AppId]

			}
		}
	}

	if query.LastExecInfoExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.LastExecInfo)
		}
		f, ok := expand.GetExpandFunc("ui_report")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["last_exec_info"] = res[items[i].LastExecInfo]

			}
		}
	}

	var total int64
	dbc, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext), true)
	if err != nil {
		return nil, err
	}
	dbc = dbc.Select("count(1)").Where("is_deleted = ?", false).Where("space_id = ?", c.Request.Header.Get("spaceid")).Model(&models.UiPlan{})
	err = dbc.Find(&total).Error
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.UiPlan]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *UiPlanController) Get(req *UiPlanIdRequest) (*models.UiPlan, error) {
	res := &models.UiPlan{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	// 校验用例
	cases := make([]models.UiCase, 0)
	caseIds := make([]string, 0)
	caseIds = append(caseIds, res.CaseIds...)
	err := gormx.SelectByParamBuilder(c.MantisContext,
		gormx.NewParamBuilder().Model(&models.UiCase{}).In("id", caseIds).Eq("is_deleted", false),
		&cases,
	)
	if err != nil {
		return nil, err
	}
	newCaseIds := make([]string, 0)
	for _, c := range cases {
		if !c.IsDeleted {
			newCaseIds = append(newCaseIds, c.Id)
		}
	}
	res.CaseIds = newCaseIds
	return res, nil
}

func (c *UiPlanController) Delete(req *UiPlanIdRequest) error {
	model := &models.UiPlan{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *UiPlanController) BatchDelete(req commondto.BatchDeleteReq) error {
	del := &models.UiPlan{}
	del.IsDeleted = true
	_, err := gormx.UpdateBatchByParamBuilder(c.MantisContext, gormx.NewParamBuilder().Model(&models.UiPlan{}).In("id", req.Ids), del)
	return err
}

func (c *UiPlanController) Create(req *models.UiPlan) (*models.UiPlan, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.CompanyId = utils.IDString(c.GetCompany())
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	if err := c.saveJob(req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiPlanController) Update(req *models.UiPlan, idReq *UiPlanIdRequest) (*models.UiPlan, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	if err := c.saveJob(req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *UiPlanController) saveJob(m *models.UiPlan) (err error) {
	if *m.EnableTimer {
		startTime := time.UnixMilli(m.TimingInfo.StartTime)
		endTime := time.UnixMilli(m.TimingInfo.EndTime)
		executeTime := time.UnixMilli(m.TimingInfo.FixedTime)
		t := cron.Task{
			StartTime:   &startTime,
			EndTime:     &endTime,
			Cron:        m.TimingInfo.Cron,
			JobName:     constants.UiExecuteJob,
			Key:         constants.UiExecuteJob + m.Id,
			Type:        int64(m.TimingInfo.TimingType),
			ExecuteTime: &executeTime,
			Job:         service.NewUiExecuteJob(m.Id),
		}
		return t.RegisterJob()
	} else {
		return cron.RemoveJob(constants.UiExecuteJob + m.Id)
	}
}

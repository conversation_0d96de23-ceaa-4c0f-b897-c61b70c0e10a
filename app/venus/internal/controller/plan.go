package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type PlanController struct {
	*controller.BaseController
}

var (
	DefaultPlanController PlanController
	planService           service.PlanService
)

type ExecuteReq struct {
	PlanId   string   `json:"plan_id"`
	AppId    string   `json:"app_id"`
	Env      string   `json:"env"`
	Webhooks []string `json:"webhooks"`
}

type SearchReq struct {
	IdList []string `schema:"idList"`
}

type ExecuteRes struct {
	ReportUrl string `json:"report_url"`
}

type ManualExecuteResp struct {
	ReportId string `json:"report_id"`
}

func (c *PlanController) ManualExecute(req *ExecuteReq) (*ManualExecuteResp, error) {
	reportId, err := planService.ManualExecute(c.<PERSON>ontext, req.PlanId)
	if err != nil {
		return nil, err
	}
	return &ManualExecuteResp{ReportId: reportId}, nil
}

func (c *PlanController) OpenExecute(req *ExecuteReq) (*ExecuteRes, error) {
	reportUrl, err := planService.OpenExecute(c.MantisContext, req.AppId, req.PlanId, req.Env, req.Webhooks)
	return &ExecuteRes{
		ReportUrl: reportUrl,
	}, err
}

func (c *PlanController) Search(req *SearchReq) ([]models.UiPlan, error) {
	plans, err := planService.Search(c.MantisContext, req.IdList)
	if err != nil {
		return nil, err
	}
	return plans, nil
}

package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/result"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type ReportController struct {
	*controller.BaseController
}

type StopReportExecuteReq struct {
	ReportId string `json:"report_id"`
}

var (
	DefaultReportController ReportController
	reportService           service.ReportService
)

func (c *ReportController) Callback(req *result.StepResult) {
	if req.Type == "case" {
		reportService.DealCaseFinish(c.<PERSON>, req.ReportId, req.CaseId, req.Success)
		return
	} else {
		reportService.DealReportRes(c.<PERSON>onte<PERSON>, req)
	}
}

func (c *ReportController) StopExecute(req *StopReportExecuteReq) error {
	return reportService.StopExecute(c<PERSON>, req.ReportId)
}

package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

type CaseStepController struct {
	*controller.BaseController
}

type AddBrotherRequest struct {
	models.UiCaseStep
	CurrentNodeId string `json:"current_node_id"`
	Direction     string `json:"direction"`
}

type ReferenceComponentRequest struct {
	models.UiCaseStep
	ComponentId   string `json:"component_id"`
	CurrentNodeId string `json:"current_node_id"`
	Direction     string `json:"direction"`
}

type RecursiveUpdateDisableRequest struct {
	StepIds []string `json:"step_ids"`
	Disable string   `json:"disable"`
}

type RecursiveCopyStepRequest struct {
	Id string `json:"step_id"`
}

type GetCaseStepsReq struct {
	CaseId   string `schema:"case_id"`
	ReportId string `schema:"report_id"`
}

type ExtractStepsReq struct {
	Component *models.UiCase `json:"component"`
	StepIds   []string       `json:"step_ids"`
	Replace   bool           `json:"replace"`
}

var (
	DefaultCaseStepController CaseStepController
	caseStepService           service.CaseStepService
)

func (c *CaseStepController) AddBrotherNode(req *AddBrotherRequest) (*models.UiCaseStep, error) {
	return caseStepService.AddBrotherNode(c.MantisContext, &req.UiCaseStep, req.CurrentNodeId, req.Direction)
}

func (c *CaseStepController) RecursiveUpdateDisable(req *RecursiveUpdateDisableRequest) error {
	return caseStepService.RecursiveUpdateDisable(c.MantisContext, req.StepIds, req.Disable)
}

func (c *CaseStepController) RecursiveCopy(req *RecursiveCopyStepRequest) error {
	return caseStepService.RecursiveCopy(c.MantisContext, req.Id)
}

func (c *CaseStepController) GetCaseSteps(req *GetCaseStepsReq) (*dto.CaseStepsDTO, error) {
	return caseStepService.GetCaseSteps(c.MantisContext, req.CaseId, req.ReportId)
}

func (c *CaseStepController) ReferenceComponent(req *ReferenceComponentRequest) (*models.UiCaseStep, error) {
	return caseStepService.ReferenceComponent(c.MantisContext, &req.UiCaseStep, req.ComponentId, req.CurrentNodeId, req.Direction)
}

func (c *CaseStepController) Dereference(req *RecursiveCopyStepRequest) error {
	return caseStepService.DereferenceStepComponent(c.MantisContext, req.Id)
}

func (c *CaseStepController) ExtractSteps(req *ExtractStepsReq) error {
	req.Component.SpaceId = c.Request.Header.Get("spaceid")
	return caseStepService.ExtractSteps(c.MantisContext, *req.Component, req.StepIds, req.Replace)
}

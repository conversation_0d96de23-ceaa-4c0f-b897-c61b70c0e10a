package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
)

type ElementController struct {
	*controller.BaseController
}

var (
	DefaultElementController ElementController
	elementService           service.ElementService
)

type ElementReq struct {
	Name    string `schema:"name"`
	Page    int64  `schema:"page"`    // 当前页
	PerPage int64  `schema:"perPage"` // 页大小
}

type CheckDeleteRes struct {
	Res bool `json:"res"`
}
type BatchDeleteIds struct {
	Ids []string `json:"ids" validate:"required"`
}

func (c *ElementController) BindCaseList(req *ElementReq, idDto dto.PathIdDto) (*commondto.PaginationRecord[dto.ElementCaseDto], error) {
	return elementService.BindCaseList(c.<PERSON>, idDto.Id, req.Page, req.PerPage)
}

func (c *ElementController) BatchDeleteCheck(req BatchDeleteIds) (*CheckDeleteRes, error) {
	check, err := elementService.BatchDeleteCheck(c.MantisContext, req.Ids)
	if err != nil {
		return nil, err
	}
	return &CheckDeleteRes{Res: check}, nil
}

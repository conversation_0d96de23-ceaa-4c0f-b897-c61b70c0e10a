package payload

// UiExecuteTaskPayload UI用例执行任务载体
type UiExecuteTaskPayload struct {
	Input           string `json:"input"`
	Callback        string `json:"callback"`
	FileDownloadUrl string `json:"filedownloadurl"`
	ReportId        string `json:"reportId"`
	VariableUrl     string `json:"variableurl"`
	Env             string `json:"env"`
}

func (UiExecuteTaskPayload) Kind() string {
	return "magic-ui-executor"
}

package consumer

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/executor"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/message/payload"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/factory"
)

func Init() {
	// 监听UI用例执行任务
	factory.Sub(HandleUiExecuteTask)
}

// HandleUiExecuteTask 执行UI用例任务
func HandleUiExecuteTask(ctx context.Context, executeTask payload.UiExecuteTaskPayload) error {
	logger.Logger.Infof("消息消费-执行UI用例任务: %+v", executeTask)
	err := executor.RunUiExecutorTask(&commoncontext.MantisContext{Context: ctx}, executeTask)
	if err != nil {
		return err
	}
	return nil
}

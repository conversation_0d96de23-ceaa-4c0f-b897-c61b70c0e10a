package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
)

//type ActionDetail struct {
//	// 浏览器操作
//	OpenPage     *OpenPageDTO    `json:"open_page"`
//	ClosePage    *OperatePageDTO `json:"close_page"`
//	ToggleWindow *OperatePageDTO `json:"toggle_window"`
//	Forward      *OperatePageDTO `json:"forward"`
//	Back         *OperatePageDTO `json:"back"`
//	Refresh      *OperatePageDTO `json:"refresh"`
//	// 鼠标操作
//	MouseClick  *MouseClickDTO  `json:"mouse_click"`
//	MouseMove   *MouseMoveDTO   `json:"mouse_move"`
//	MouseHold   *MouseHoldDTO   `json:"mouse_hold"`
//	MouseDrag   *MouseDragDTO   `json:"mouse_drag"`
//	MouseScroll *MouseScrollDTO `json:"mouse_scroll"`
//	// 输入操作
//	InputOperations *InputOperationsDTO `json:"input_operations"`
//	UploadFiles     *UploadFilesDTO     `json:"upload_files"`
//	Select          *SelectDTO          `json:"select"`
//	// 高级操作
//	// 等待事件
//	WaitEvents *WaitEventsDTO `json:"wait"`
//	// 断言
//	Assert *AssertDTO `json:"assert"`
//	// 关联提取
//	DataWithdraw *DataWithdrawDTO `json:"data_withdraw"`
//	// 代码操作
//	CodeOperate *CodeOperateDTO `json:"code_operate"`
//	// 循环操作（for loop）
//	LoopOperate *LoopOperateDTO `json:"loop_operate"`
//	// if判断
//	IfOperate *IfOperateDTO `json:"if_operate"`
//}

// CodeOperateDTO 代码操作对象
type CodeOperateDTO struct {
	CodeType    string          `json:"code_type"`    // 目前只支持 javascript
	OperateType string          `json:"operate_type"` // page,element
	CodeContent string          `json:"code_content"` // 代码内容
	Element     *StepElementDTO `json:"element"`      // 操作的元素
}

// LoopOperateDTO 循环操作对象
type LoopOperateDTO struct {
	LoopType      string `json:"loop_type"`      // for
	LoopCondition string `json:"loop_condition"` // times循环次数，data循环数据
	LoopTimes     uint   `json:"loop_times"`     // 循环次数 3
	DatasourceId  string `json:"datasource_id"`  // 循环的数据源id
}

// IfOperateDTO if判断对象
type IfOperateDTO struct {
	Relationship string      `json:"relationship"` // and , or
	Condition    []AssertDTO `json:"condition"`    // 条件: 元素存在，元素不存在
}
type OpenPageDTO struct {
	Url       string `json:"url"`
	IsNewPage bool   `json:"is_new_page"`
}

type OperatePageDTO struct {
	WindowAction   string `json:"window_action"`    // 窗口操作 // first, pre, post, last, customIndex, all
	CustomIndex    int    `json:"custom_index"`     // 自定义索引
	CustomHandleId string `json:"custom_handle_id"` // 自定义句柄id
}

// MouseClickDTO 鼠标点击对象
type MouseClickDTO struct {
	Type    string          `json:"type"` // leftClick,rightClick,doubleClick
	Element *StepElementDTO `json:"element"`
}

// MouseMoveDTO 鼠标移动到元素/鼠标悬浮在某个元素
type MouseMoveDTO struct {
	Type    string          `json:"type"` // 目前只支持元素 element
	Element *StepElementDTO `json:"element"`
}

// MouseHoldDTO 鼠标按住对象
type MouseHoldDTO struct {
	Type    string          `json:"type"` // 目前只支持元素 element
	Element *StepElementDTO `json:"element"`
	Hold    uint64          `json:"hold"` // 毫秒
}

// MouseScrollDTO 鼠标滚动对象
type MouseScrollDTO struct {
	Type          string          `json:"type"`           // page element
	Element       *StepElementDTO `json:"element"`        // 在元素上滚动时,被滚动的元素
	ScrollElement *StepElementDTO `json:"scroll_element"` //  滚动到出现的元素
	Target        string          `json:"target"`         // 滚动目标  pos:滚到到这个坐标  element:滚动到元素出现
	Distance      *DistanceDTO    `json:"distance"`       // 滚动坐标
}

// MouseDragDTO 鼠标拖拽对象
type MouseDragDTO struct {
	Target        string          `json:"target"`         // 滚动目标  pos:推拽到这个坐标   element:推拽到目标元素
	Element       *StepElementDTO `json:"element"`        // 被推拽的元素
	TargetElement *StepElementDTO `json:"target_element"` // 推拽到出现的元素
	Distance      *DistanceDTO    `json:"distance"`       // 拖拽到坐标
}

// InputOperationsDTO 输入操作对象
type InputOperationsDTO struct {
	Type            string          `json:"type"` // 目前只能在元素上输入或按键盘,element,press
	InputContent    string          `json:"input_content"`
	IsAppendContent string          `json:"is_append_content"` // 是否追加输入
	Element         *StepElementDTO `json:"element"`
}

type PressDTO struct {
	PressKey   string   `json:"press_key"`  // 回车 Enter,空格 Space,回退 Backspace
	PressMilli *float64 `json:"pressMilli"` // 按压时间
}

type UploadFilesDTO struct {
	Element *StepElementDTO `json:"element"`
	Files   []struct {
		FileName string `json:"name"`
		MimeType string `json:"mime_type"`
		OssKey   string `json:"obj_key"` // base64
	} `json:"fileOssPaths"`
}

// SelectDTO 下拉对象
type SelectDTO struct {
	Type       string          `json:"type"`    // text 文本、value 值、index 下标
	Element    *StepElementDTO `json:"element"` // 操作的元素
	Text       []string        `json:"text"`
	Value      []string        `json:"value"`
	Index      []int           `json:"index"`
	IsMultiple bool            `json:"isMultiple"` // 是否支持多选
}

// AssertDTO 断言对象
type AssertDTO struct {
	Type          string                 `json:"type"` // element_exist,element_not_exist, text_exist,text_not_exist, variable, page,element
	Element       *StepElementDTO        `json:"element"`
	Text          []string               `json:"text"`
	Page          *action.PageAssert     `json:"page"`
	Variable      *action.VariableAssert `json:"variable"`
	AssertElement *AssertElement         `json:"assertElement"`
}

type AssertElement struct {
	Type    string `json:"type"`    // 元素的断言类型 attr(属性), text(元素的文本), tagName(标签名称), value(输入框的值), count(元素的个数)
	AttrKey string `json:"attrKey"` // 元素属性
	// 操作符号: equal/notEqual/contain(包含)/notContain(不包含)/regularExpression(正则)
	// lessThan(小于)/lessThanEqual(小于等于)/greaterThan(大于)/greaterThanEqual(大于等于)
	Operator string `json:"operator"`
	Expected string `json:"expected"` // 期望值
}

// DataWithdrawDTO 数据提取对象
type DataWithdrawDTO struct {
	Type         string                  `json:"type"` // 提取类型: page element
	VariableName string                  `json:"name"`
	Element      *WithdrawElement        `json:"element"`
	Webpage      *action.WithdrawWebpage `json:"webpage"`
	RegexEnable  bool                    `json:"regexEnable"` // 开启二次加工
	Regex        string                  `json:"regex"`       // 正则表达式
}

type WithdrawElement struct {
	Element       *StepElementDTO `json:"element"`
	Type          string          `json:"type"` // text, value, attr, source
	AttributeName string          `json:"attributeName"`
}

// WaitEventsDTO 等待事件对象
type WaitEventsDTO struct {
	Type     string          `json:"type"`      // time, element_exist, element_not_exist, visible, hidden, editable, not_editable
	WaitTime int64           `json:"wait_time"` // 等待时常 ms
	Element  *StepElementDTO `json:"element"`
}

type ConditionDTO struct {
	ConditionType string `json:"condition_type"` // 条件类型:element_text,element_tag_name,element_count,input_value
	OperationName string `json:"operation_name"` // 操作符:operation_enum.GetOperationNames
	ExpectValue   string `json:"expect_value"`   // 期望值
}

type StepElementDTO struct {
	TargetType    int                     `json:"target_type"`    // 区分引用元素(1)还是自定义元素(2)
	RefElementId  string                  `json:"ref_element_id"` // 引用元素的id
	CustomElement *element.ElementLocator `json:"custom_element"` // 自定义元素
	ViewElement   *element.ElementLocator `json:"view_element"`   // 页面显示元素
}

type DistanceDTO struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// BrowserConfigInfo 浏览器配置
type BrowserConfigInfo struct {
	Browser                     string `json:"browserType"`                 // 浏览器类型
	Mode                        string `json:"mode"`                        // 模式
	Window                      string `json:"window"`                      // 窗口大小 1080*1920
	IgnoreHttpsCertificateError *bool  `json:"ignoreHttpsCertificateError"` //  默认true
}

func (d *BrowserConfigInfo) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *BrowserConfigInfo) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

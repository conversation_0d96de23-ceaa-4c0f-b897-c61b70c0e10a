package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

// UiReportStepDetail 步骤报告详情
type UiReportStepDetail struct {
	models.Addons2
	ReportId      string         `json:"report_id" gorm:"column:report_id;type:text" expand:"ui_report"`                       // 步骤id
	ReportCaseId  string         `json:"report_case_id" gorm:"column:report_case_id;type:text" expand:"ui_report_case_detail"` // 用例报告id
	Status        string         `json:"status" gorm:"column:status;type:text"`                                                // 步骤状态
	IsCount       bool           `json:"is_count" gorm:"column:is_count;type:bool"`                                            // 是否统计
	ReportMessage ReportResSlice `json:"report_message" gorm:"column:report_message;type:jsonb"`                               // 步骤报告信息
	// 以下为步骤的字段
	CaseId          string             `json:"case_id" gorm:"column:case_id;type:text;comment:用例id" expand:"ui_case"`
	StepId          string             `json:"step_id" gorm:"column:step_id;type:text" expand:"step_id"`
	Name            string             `json:"name" gorm:"column:name;type:text;comment:步骤名称"`
	ParentId        string             `json:"parent_id" gorm:"column:parent_id;type:text;comment:父节点id" expand:"ui_report_step_detail"`
	ActionType      string             `json:"action_type" gorm:"column:action_type;type:text;comment:操作类型" `
	Action          models.JSONAny     `json:"action" gorm:"column:action;type:jsonb;comment:操作内容" `
	SettingsContent *scene.StepConfig  `json:"settings_content" gorm:"column:settings_content;type:jsonb;comment:设置信息"`
	SettingEnable   *bool              `json:"setting_enable" gorm:"column:setting_enable;type:bool;comment:设置开关"`
	GenSource       string             `json:"gen_source" gorm:"column:gen_source;type:text;comment:生成来源"`
	ElementIds      models.StringSlice `json:"element_ids" gorm:"column:element_ids;type:jsonb;comment:绑定的元素id"`
	Disable         string             `json:"disable" gorm:"column:disable;type:text;comment:禁用状态:1是0否" `
	SiblingOrder    int64              `json:"sibling_order" gorm:"column:sibling_order;type:int8;comment:元素顺序"`
	IsDir           bool               `json:"is_dir" gorm:"column:is_dir;type:bool;comment:是否是文件夹"` // 组其实就是文件夹
	IsReference     *bool              `json:"is_reference" gorm:"-"`
	ReferenceCaseId string             `json:"reference_case_id" gorm:"-"`
	Expand          map[string]any     `json:"expand" gorm:"-"`
}

func (UiReportStepDetail) TableName() string {
	return "ui_report_step_detail"
}

func (UiReportStepDetail) Tree() {}

type ReportRes struct {
	Id         string `json:"id" yaml:"id"`
	Success    bool   `json:"success" yaml:"success"`
	Res        any    `json:"res" yaml:"res"`
	Error      string `json:"error" yaml:"error"`
	Screenshot string `json:"screenshot" yaml:"screenshot"`
	Duration   int64  `json:"duration"`
}

type ReportResSlice []ReportRes

func (d ReportResSlice) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *ReportResSlice) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

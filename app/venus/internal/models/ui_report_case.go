package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

// UiReportCase  用例报告详情
type UiReportCase struct {
	models.Addons2
	Status          string         `json:"status" gorm:"column:status;type:text"`                       // 状态
	StepTotalCount  int            `json:"step_total_count" gorm:"column:step_total_count;type:int4"`   // 步骤数量
	StepPassCount   int            `json:"step_pass_count" gorm:"columnm:step_pass_count;type:int4"`    // 步骤通过数
	StepFailCount   int            `json:"step_fail_count" gorm:"column:step_fail_count;type:int4"`     // 步骤失败数
	StepUnexecCount int            `json:"step_unexec_count" gorm:"column:step_unexec_count;type:int4"` // 步骤未执行数
	Executor        string         `json:"executor" gorm:"column:executor;type:text" expand:"user"`     // 执行人
	ExecStartTime   int64          `json:"exec_start_time" gorm:"column:exec_start_time;type:int8"`     // 执行开始时间
	ExecEndTime     int64          `json:"exec_end_time" gorm:"column:exec_end_time;type:int8"`         // 执行结束时间
	Duration        int64          `json:"duration" gorm:"column:duration;type:int8"`                   // 执行时长 ms
	Name            string         `json:"name" gorm:"column:name;type:text"`
	ReportId        string         `json:"report_id" gorm:"column:report_id;type:text" expand:"ui_report"` // 报告id
	CaseId          string         `json:"case_id" gorm:"column:case_id;type:text" expand:"case_id"`       // 用例id
	IsComponent     bool           `json:"is_component" gorm:"column:is_component;type:bool;default:false"`
	Expand          map[string]any `json:"expand" gorm:"-"`
}

func (UiReportCase) TableName() string {
	return "ui_report_case_detail"
}

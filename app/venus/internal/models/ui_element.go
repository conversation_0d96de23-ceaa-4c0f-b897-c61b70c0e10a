package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"gorm.io/gorm"
)

// UiElement 如果pocketbase 字段类型是json, 则需要使用指针类型
type UiElement struct {
	models.Addons2
	Name         string         `json:"name" gorm:"column:name;type:text;comment:元素名称" `
	ParentId     string         `json:"parent_id" gorm:"column:parent_id;type:text;comment:父节点id" expand:"ui_element"`
	Locator      LocatorInfo    `json:"locator" gorm:"column:locator;type:jsonb;comment:定位元素str"`
	SiblingOrder int64          `json:"sibling_order" gorm:"column:sibling_order;type:int8;comment:元素顺序"`
	IsDir        bool           `json:"is_dir" gorm:"column:is_dir;type:bool;comment:是否是文件夹"`
	IsRoot       bool           `json:"is_root" gorm:"column:is_root;type:bool;not null;default:false"`
	SpaceId      string         `json:"space_id" gorm:"column:space_id;type:text;comment:所属项目id"`
	Expand       map[string]any `json:"expand" gorm:"-"`
}

func (UiElement) TableName() string {
	return "ui_element"
}

func (UiElement) Tree() {}

func (m *UiElement) BeforeCreate(tx *gorm.DB) (err error) {
	m.SetNewId()
	if m.ParentId == "" && m.Name != "根节点" && !m.IsRoot {
		vm := UiElement{
			IsRoot:  true,
			IsDir:   true,
			Name:    "根节点",
			SpaceId: m.SpaceId,
		}
		res, err := addVirtualNode(vm, gormx.NewParamBuilder().Model(&UiElement{}).Eq("space_id", m.SpaceId).Eq("is_deleted", false).Eq("parent_id", ""))
		if err != nil {
			return err
		}
		m.ParentId = res.Id
	}
	return nil
}

type LocatorInfo struct {
	element.ElementLocator
}

func (d LocatorInfo) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *LocatorInfo) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

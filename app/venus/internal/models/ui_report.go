package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

// UiReport  报告
type UiReport struct {
	models.Addons2
	Name           string             `json:"name" gorm:"column:name;type:text"`
	RelationId     string             `json:"relation_id" gorm:"column:relation_id;type:text"` // 关联的外部表 id,ui_plan/ui_case
	ExecType       string             `json:"exec_type" gorm:"column:exec_type;type:text"`
	Env            string             `json:"env" gorm:"column:env;type:text" expand:"env"`
	Status         string             `json:"status" gorm:"column:status;type:text"` // 状态
	Msg            string             `json:"msg" gorm:"column:msg;type:text"`
	CaseTotalCount int                `json:"case_total_count" gorm:"column:case_total_count;type:int4"` // 用例数量
	CasePassCount  int                `json:"case_pass_count" gorm:"column:case_pass_count;type:int4"`   // 用例通过数
	CaseFailCount  int                `json:"case_fail_count" gorm:"column:case_fail_count;type:int4"`   // 用例失败数
	PassRate       string             `json:"pass_rate" gorm:"column:pass_rate;type:text"`               // 用例通过率
	ExecStartTime  int64              `json:"exec_start_time" gorm:"column:exec_start_time;type:int8"`   // 执行开始时间
	ExecEndTime    int64              `json:"exec_end_time" gorm:"column:exec_end_time;type:int8"`       // 执行结束时间
	Duration       int64              `json:"duration" gorm:"column:duration;type:int8"`                 // 执行时长 ms
	Executor       string             `json:"executor" gorm:"column:executor;type:text" expand:"user"`   // 执行人
	SpaceId        string             `json:"space_id" gorm:"column:space_id;type:text"`
	K8sTaskIds     models.StringSlice `json:"k8s_task_ids" gorm:"column:k8s_task_ids;type:jsonb"`
	Webhooks       models.StringSlice `json:"webhooks" gorm:"column:webhooks;type:jsonb"`
	WebhookResults WebhookResultSlice `json:"webhook_results" gorm:"column:webhook_results;type:jsonb"`
	EnableConfig   *bool              `json:"enable_config" gorm:"column:config_open;type:bool"`
	BrowserConfig  *BrowserConfigInfo `json:"browser_config" gorm:"column:browser_config;type:jsonb"`
	StepConfig     *scene.StepConfig  `json:"step_config" gorm:"column:step_config;type:jsonb"`
	Expand         map[string]any     `json:"expand" gorm:"-"`
}

func (UiReport) TableName() string {
	return "ui_report"
}

const (
	WebhookResultPlan   = "plan"
	WebhookResultReport = "report"
)

type WebhookResult struct {
	Url     string `json:"url"`
	Type    string `json:"type"` // plan or report
	Success bool   `json:"success"`
	Msg     string `json:"msg"`
	Request struct {
		Body   map[string]any `json:"body"`
		Method string         `json:"method"`
	} `json:"request"`
	Response struct {
		StatusCode int    `json:"status_code"`
		Body       string `json:"body"`
	} `json:"response"`
}

type WebhookResultSlice []WebhookResult

func (d WebhookResultSlice) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *WebhookResultSlice) Scan(value any) error {
	if value == nil || value == "" {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

package models

import (
	"context"
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"gorm.io/gorm"
)

type UiPlan struct {
	models.Addons2
	Name          string             `json:"name" gorm:"column:name;type:text"`
	Description   string             `json:"description" gorm:"column:description;type:text"`
	EnableTimer   *bool              `json:"enable_timer" gorm:"column:enable_timer;type:bool;not null;default:false"`
	NoticeRule    string             `json:"notice_rule" gorm:"column:notice_rule;type:text"`
	NoticeUsers   models.StringSlice `json:"notice_users" gorm:"column:notice_users;type:jsonb" expand:"user"`
	NoticeType    models.StringSlice `json:"notice_type" gorm:"column:notice_type;type:jsonb"`
	Env           string             `json:"env" gorm:"column:env;type:text" expand:"env"`
	RetryTimes    int                `json:"retry_times" gorm:"column:retry_times;type:int4"`
	CaseIds       models.StringSlice `json:"case_ids" gorm:"column:case_ids;type:jsonb" expand:"ui_case"`
	CaseCount     int64              `json:"case_count" gorm:"column:case_count;type:int8"`
	SpaceId       string             `json:"space_id" gorm:"column:space_id;type:text"`
	AppId         string             `json:"app_id" gorm:"column:app_id;type:text" expand:"app"`
	TimingInfo    TimingInfo         `json:"timing_info" gorm:"column:timing_info;type:jsonb"`
	LastExecInfo  string             `json:"last_exec_info" gorm:"column:last_exec_info;type:text" expand:"ui_report"` // 最近执行报告信息
	Webhooks      models.StringSlice `json:"webhooks" gorm:"column:webhooks;type:jsonb"`
	EnableConfig  *bool              `json:"enable_config" gorm:"column:enable_config;type:bool;not null;default:false"`
	BrowserConfig *BrowserConfigInfo `json:"browser_config" gorm:"column:browser_config;type:jsonb"`
	StepConfig    *scene.StepConfig  `json:"step_config" gorm:"column:step_config;type:jsonb"`
	Expand        map[string]any     `json:"expand" gorm:"-"`
	CompanyId     string             `json:"company_id" gorm:"column:company_id;type:text"` // 公司 id
}

type TimingInfo struct {
	TimingType       int    `json:"timing_type"`      // 1 定时执行/2 周期任务/4 间隔执行
	StartTime        int64  `json:"start_time"`       // 开始时间
	EndTime          int64  `json:"end_time"`         // 结束时间
	Cron             string `json:"cron"`             // cron
	FixedTime        int64  `json:"fixed_time"`       // 固定时间
	TimeInterval     int64  `json:"timeInterval"`     // 前端字段
	TimeIntervalUnit string `json:"timeIntervalUnit"` // 前端字段
}

func (d TimingInfo) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *TimingInfo) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

func (UiPlan) TableName() string {
	return "ui_plan"
}

func (m *UiPlan) BeforeCreate(tx *gorm.DB) (err error) {
	m.SetNewId()
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	records := make([]UiPlan, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&UiPlan{}).Eq("app_id", m.AppId).Eq("env", m.Env).Eq("is_deleted", false),
		&records,
	)
	if err != nil {
		return err
	}
	if len(records) > 0 {
		record := records[0]
		app, err := cmdb.Client.GetAppById(ctx, record.AppId)
		if err != nil {
			return err
		}
		project, err := remote.CubeBaseRemoteApi{}.GetProjectInfo(record.SpaceId)
		if err != nil {
			return err
		}
		return fmt.Errorf("应用[%s]已在项目[%s]下被[%s]计划绑定", app.Name, project.Name, record.Name)
	}
	return nil
}

func (m *UiPlan) BeforeUpdate(tx *gorm.DB) (err error) {
	if m.IsDeleted {
		return nil
	}
	m.Updated = time.Now().UnixMilli()
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	records := make([]UiPlan, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&UiPlan{}).Eq("app_id", m.AppId).Eq("env", m.Env).Eq("is_deleted", false),
		&records,
	)
	if err != nil {
		return err
	}
	if len(records) > 0 && !(len(records) == 1 && records[0].Id == m.Id) {
		var record UiPlan
		for _, re := range records {
			if re.Id != m.Id {
				record = re
			}
		}
		app, err := cmdb.Client.GetAppById(ctx, record.AppId)
		if err != nil {
			return err
		}
		project, err := remote.CubeBaseRemoteApi{}.GetProjectInfo(record.SpaceId)
		if err != nil {
			return err
		}
		return fmt.Errorf("应用[%s]已在项目[%s]下被[%s]计划绑定", app.Name, project.Name, record.Name)
	}
	return nil
}

// 删除后清理任务
func (m *UiPlan) AfterUpdate(tx *gorm.DB) (err error) {
	if m.IsDeleted {
		return cron.RemoveJob(constants.UiExecuteJob + m.Id)
	}
	return nil
}

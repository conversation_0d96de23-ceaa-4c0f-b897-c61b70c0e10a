package models

import (
	"context"

	scene "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

// UiCase 如果pocketbase 字段类型是json, 则需要使用指针类型
type UiCase struct {
	models.Addons2
	Name          string             `json:"name" gorm:"column:name;type:text;"`
	ParentId      string             `json:"parent_id" gorm:"column:parent_id;type:text;" expand:"ui_case"`
	Description   string             `json:"description" gorm:"column:description;type:text;"`
	BrowserConfig *BrowserConfigInfo `json:"browser_config" gorm:"column:browser_config;type:jsonb"`
	StepsConfig   *scene.StepConfig  `json:"step_config" gorm:"column:step_config;type:jsonb"`
	SiblingOrder  int64              `json:"sibling_order" gorm:"column:sibling_order;type:int8"`
	IsDir         bool               `json:"is_dir" gorm:"column:is_dir;type:bool"`
	IsRoot        bool               `json:"is_root" gorm:"column:is_root;type:bool;not null;default:false"`
	LastReportId  string             `json:"last_report_id" gorm:"column:last_report_id;type:text" expand:"ui_report"`
	SpaceId       string             `json:"space_id" gorm:"column:space_id;type:text"`
	Recording     *bool              `json:"recording" gorm:"column:recording;type:bool;not null;default:false"`
	IsComponent   *bool              `json:"is_component" gorm:"column:is_component;type:bool;not null;default:false"`
	Expand        map[string]any     `json:"expand" gorm:"-"`
}

func (UiCase) TableName() string {
	return "ui_case"
}

func (UiCase) Tree() {}

func (m *UiCase) BeforeCreate(tx *gorm.DB) (err error) {
	m.SetNewId()
	if m.ParentId == "" && m.Name != "根节点" && !m.IsRoot {
		vm := UiCase{
			IsRoot:  true,
			IsDir:   true,
			Name:    "根节点",
			SpaceId: m.SpaceId,
		}
		res, err := addVirtualNode(vm, gormx.NewParamBuilder().Model(&UiCase{}).Eq("space_id", m.SpaceId).Eq("is_deleted", false).Eq("parent_id", ""))
		if err != nil {
			return err
		}
		m.ParentId = res.Id
	}
	if m.IsDir {
		return nil
	}
	if m.BrowserConfig == nil {
		m.BrowserConfig = &BrowserConfigInfo{
			Browser: constants.BrowserChrome,
			Mode:    constants.BrowserViewNoTrace,
			Window:  constants.WindowPC,
		}
	}
	if m.BrowserConfig.Browser == "" {
		m.BrowserConfig.Browser = constants.BrowserChrome
	}
	if m.BrowserConfig.Mode == "" {
		m.BrowserConfig.Mode = constants.BrowserViewNoTrace
	}
	if m.BrowserConfig.IgnoreHttpsCertificateError == nil {
		m.BrowserConfig.IgnoreHttpsCertificateError = new(bool)
		*(m.BrowserConfig.IgnoreHttpsCertificateError) = true
	}
	if m.StepsConfig == nil {
		m.StepsConfig = &scene.StepConfig{
			EndsWhenFail:    constants.DealErrorStop,
			RetryTimes:      0,
			ScreenShotTime:  constants.ScreenshotAlways,
			TimeoutMilli:    15000,
			WaitBeforeMilli: 0,
		}
	}
	return nil
}

func addVirtualNode[T schema.Tabler](m T, filter *gormx.ParamBuilder) (*T, error) {
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	records := make([]T, 0)
	err := gormx.SelectByParamBuilder(ctx, filter, &records)
	if err != nil {
		return nil, err
	}
	if len(records) > 0 {
		return &records[0], nil
	}
	_, err = gormx.InsertOne(ctx, &m)
	return &m, err
}

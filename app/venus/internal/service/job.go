package service

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
	"github.com/reugn/go-quartz/quartz"
)

type UiExecuteJob struct {
	PlanId string `json:"plan_id"`
}

func NewUiExecuteJob(planId string) *UiExecuteJob {
	return &UiExecuteJob{
		PlanId: planId,
	}
}

func (job *UiExecuteJob) Execute(c context.Context) (err error) {
	logger.Logger.Infof("ui task execute, plan id: %s", job.PlanId)
	ctx := &commoncontext.MantisContext{Context: c}
	// 查询plan
	plan := models.UiPlan{}
	plan.Id = job.PlanId
	plan.IsDeleted = false
	err = gormx.SelectOneByCondition(ctx, &plan)
	if err != nil {
		return err
	}
	_, err = Execute(ctx, plan, types.Cron, plan.Webhooks)
	if err != nil {
		return err
	}
	return err
}

func (job *UiExecuteJob) Description() string {
	return job.PlanId
}

func DealUiExecuteJob(description string) (quartz.Job, error) {
	return NewUiExecuteJob(description), nil
}

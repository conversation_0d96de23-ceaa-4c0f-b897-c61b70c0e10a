package service

import (
	"encoding/json"
	"errors"
	"slices"
	"strconv"

	agentAction "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	agentElement "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	commonmodels "git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"github.com/iancoleman/strcase"
)

// agentStepToUIStep 转换步骤
func agentStepToUIStep(ctx *commoncontext.MantisContext, step map[string]any, caseId, genSource string) (*models.UiCaseStep, error) {
	_, ok := step["actionType"]
	if !ok {
		return nil, nil
	}
	actionType := strcase.ToSnake(step["actionType"].(string))
	uiStep := &models.UiCaseStep{
		Name:       step["name"].(string),
		ActionType: actionType,
		GenSource:  genSource,
		Disable:    constants.StepDisableFalse,
		CaseId:     caseId,
	}
	uiStep.IsDir = false
	uiStep.Creator = ctx.User.Name
	uiStep.Modifier = ctx.User.Name

	actionBytes, err := json.Marshal(step["action"])
	if err != nil {
		logger.Logger.Errorf("action 转换 json str 失败,%s", err.Error())
		return nil, errors.New("action 转换 json str 失败," + err.Error())
	}
	// 浏览器操作
	err, typeExit := ofUIActionBrowser(actionType, actionBytes, uiStep)
	if typeExit {
		return uiStep, err
	}
	// 鼠标操作
	err, typeExit = ofUIActionMouse(actionType, actionBytes, uiStep)
	if typeExit {
		return uiStep, err
	}
	// 输入操作
	err, typeExit = ofUIActionInput(actionType, actionBytes, uiStep)
	if typeExit {
		return uiStep, err
	}
	// 高级操作
	err, typeExit = ofUIActionAdvanced(actionType, actionBytes, uiStep)
	if typeExit {
		return uiStep, err
	}
	logger.Logger.Warnf("未识别的操作类型%s", actionType)
	return nil, errors.New("未识别的操作类型:" + actionType)
}

// 浏览器操作
func ofUIActionBrowser(actionType string, actionBytes []byte, uiCaseStep *models.UiCaseStep) (error, bool) {
	arr := []string{
		constants.ActionBrowserOpen, constants.ActionBrowserClose, constants.ActionBrowserToggleWindow,
		constants.ActionBrowserForward, constants.ActionBrowserBack, constants.ActionBrowserRefresh,
	}
	boolX := slices.Contains(arr, actionType)
	if !boolX {
		return nil, false
	}
	switch actionType {
	// 1 浏览器打开
	case constants.ActionBrowserOpen:
		openPage := agentAction.OpenPage{}
		err := json.Unmarshal(actionBytes, &openPage)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.OpenPageDTO{
				IsNewPage: openPage.IsNewPage,
				Url:       openPage.Url,
			},
		}
	// 2 浏览器关闭
	case constants.ActionBrowserClose:
		closePage := agentAction.ClosePage{}
		err := json.Unmarshal(actionBytes, &closePage)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.OperatePageDTO{
				WindowAction: closePage.Type,
				CustomIndex:  closePage.CustomIndex,
			},
		}
	// 3 切换窗口
	case constants.ActionBrowserToggleWindow:
		tw := agentAction.ToggleWindow{}
		err := json.Unmarshal(actionBytes, &tw)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.OperatePageDTO{
				WindowAction: tw.Type,
				CustomIndex:  tw.CustomIndex,
			},
		}
	// 4 前进
	case constants.ActionBrowserForward:
		uiCaseStep.Action = commonmodels.JSONAny{Data: &models.OperatePageDTO{}}
	// 5 后退
	case constants.ActionBrowserBack:
		uiCaseStep.Action = commonmodels.JSONAny{Data: &models.OperatePageDTO{}}
	// 6 刷新
	case constants.ActionBrowserRefresh:
		uiCaseStep.Action = commonmodels.JSONAny{Data: &models.OperatePageDTO{}}
	}
	return nil, true
}

// 鼠标操作
func ofUIActionMouse(actionType string, actionBytes []byte, uiCaseStep *models.UiCaseStep) (error, bool) {
	arr := []string{constants.ActionMouseMove, constants.ActionMouseClick, constants.ActionMouseScroll, constants.ActionMouseDrag, constants.ActionMouseHold}
	boolX := slices.Contains(arr, actionType)
	if !boolX {
		return nil, false
	}
	var err error
	switch actionType {
	// 1 点击
	case constants.ActionMouseClick:
		logger.Logger.Info("agent step to ", constants.ActionMouseClick)
		mc := agentAction.MouseClick{}
		err = json.Unmarshal(actionBytes, &mc)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		clickType := ""
		if mc.Button == "left" && mc.Type == "single" {
			clickType = constants.MouseLeftClick
		} else if mc.Button == "right" && mc.Type == "single" {
			clickType = constants.MouseRightClick
		} else if mc.Button == "left" && mc.Type == "double" {
			clickType = constants.MouseDoubleClick
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.MouseClickDTO{
				Type:    clickType,
				Element: agentElementToUiElement(mc.Element),
			},
		}
	// 2 鼠标移动/悬浮
	case constants.ActionMouseMove:
		logger.Logger.Info("agent step to ", constants.ActionMouseMove)
		mm := agentAction.MouseMove{}
		err = json.Unmarshal(actionBytes, &mm)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.MouseMoveDTO{
				Type:    mm.Type,
				Element: agentElementToUiElement(mm.Element),
			},
		}
	// 3 鼠标滚动
	case constants.ActionMouseScroll:
		logger.Logger.Info("agent step to ", constants.ActionMouseScroll)
		ms := agentAction.MouseScroll{}
		err = json.Unmarshal(actionBytes, &ms)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		target := "element"
		if ms.ScrollType == "position" {
			target = "pos"
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.MouseScrollDTO{
				Type:          ms.Type,
				Element:       agentElementToUiElement(ms.Element),
				ScrollElement: agentElementToUiElement(ms.ScrollElement),
				Target:        target,
				Distance:      agentPositionToDistance(ms.Position),
			},
		}
	// 4 鼠标拖拽
	case constants.ActionMouseDrag:
		logger.Logger.Info("agent step to ", constants.ActionMouseDrag)
		md := agentAction.MouseDrag{}
		err = json.Unmarshal(actionBytes, &md)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		var distanceDTO *models.DistanceDTO
		if md.Type == constants.MouseDragTypePos {
			distanceDTO = &models.DistanceDTO{
				X: md.EndPointCoordinates.X,
				Y: md.EndPointCoordinates.Y,
			}
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.MouseDragDTO{
				Target:        md.Type,
				TargetElement: agentElementToUiElement(md.TargetElement),
				Element:       agentElementToUiElement(md.Element),
				Distance:      distanceDTO,
			},
		}
	// 5 鼠标按住
	case constants.ActionMouseHold:
		logger.Logger.Info("agent step to ", constants.ActionMouseHold)
		mh := agentAction.MouseHold{}
		err = json.Unmarshal(actionBytes, &mh)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.MouseHoldDTO{
				Element: agentElementToUiElement(mh.Element),
			},
		}
	}
	return err, true
}

// 输入操作
func ofUIActionInput(actionType string, actionBytes []byte, uiCaseStep *models.UiCaseStep) (error, bool) {
	arr := []string{constants.ActionInput, constants.ActionInputSelect, constants.ActionInputUploadFiles, constants.ActionInputPress}
	boolX := slices.Contains(arr, actionType)
	if !boolX {
		return nil, false
	}
	var err error
	switch actionType {
	// 输入操作
	case constants.ActionInput:
		logger.Logger.Info("agent step to ", constants.ActionInput)
		input := agentAction.Input{}
		err = json.Unmarshal(actionBytes, &input)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.InputOperationsDTO{
				Type:            constants.InputTypeElement,
				Element:         agentElementToUiElement(input.Element),
				InputContent:    input.InputContent,
				IsAppendContent: strconv.FormatBool(input.IsAppendContent),
			},
		}
	case constants.ActionInputPress:
		logger.Logger.Info("agent step to ", constants.ActionInputPress)
		press := agentAction.Press{}
		err = json.Unmarshal(actionBytes, &press)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.PressDTO{
				PressKey:   press.Key,
				PressMilli: press.Delay,
			},
		}
	// 选择操作
	case constants.ActionInputSelect:
		logger.Logger.Info("agent step to ", constants.ActionInputSelect)
		selectX := agentAction.Select{}
		err = json.Unmarshal(actionBytes, &selectX)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.SelectDTO{
				Type:    selectX.Type,
				Element: agentElementToUiElement(selectX.Element),
				Text:    selectX.Text,
				Value:   selectX.Value,
				Index:   selectX.Index,
			},
		}
	// 文件上传
	case constants.ActionInputUploadFiles:
		logger.Logger.Info("agent step to ", constants.ActionInputUploadFiles)
		uf := agentAction.UploadFiles{}
		err = json.Unmarshal(actionBytes, &uf)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		files := make([]struct {
			FileName string `json:"name"`
			MimeType string `json:"mime_type"`
			OssKey   string `json:"obj_key"`
		}, 0)
		for _, f := range uf.Files {
			files = append(files, struct {
				FileName string "json:\"name\""
				MimeType string "json:\"mime_type\""
				OssKey   string "json:\"obj_key\""
			}{
				FileName: f.FileName,
				MimeType: f.MimeType,
				OssKey:   f.OssKey,
			})
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.UploadFilesDTO{
				Element: agentElementToUiElement(uf.Element),
				Files:   files,
			},
		}
	}

	return err, true
}

// 高级操作
func ofUIActionAdvanced(actionType string, actionBytes []byte, uiCaseStep *models.UiCaseStep) (error, bool) {
	arr := []string{
		constants.ActionAdvancedAssert, constants.ActionAdvancedCodeOperation,
		constants.ActionAdvancedCallDatabase, constants.ActionAdvancedIfOperate,
		constants.ActionAdvancedLoopOperate,
		constants.ActionAdvancedWait, constants.ActionAdvancedDataWithdraw,
	}
	boolX := slices.Contains(arr, actionType)
	if !boolX {
		return nil, false
	}
	var err error
	switch actionType {
	// 断言
	case constants.ActionAdvancedAssert:
		logger.Logger.Info("agent step to ", constants.ActionAdvancedAssert)
		assert := agentAction.Assert{}
		err = json.Unmarshal(actionBytes, &assert)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		if assert.Type == "element" {
			uiAssert := &models.AssertDTO{
				Element: agentElementToUiElement(assert.Element.Element),
			}
			uiCaseStep.Action = commonmodels.JSONAny{Data: uiAssert}
			if assert.Element.Type == "exist" {
				uiAssert.Type = "element_exist"
			} else if assert.Element.Type == "notexist" {
				uiAssert.Type = "element_not_exist"
			} else if assert.Element.Type == "text" || assert.Element.Type == "tagName" ||
				assert.Element.Type == "attr" || assert.Element.Type == "count" || assert.Element.Type == "value" {
				uiAssert.Type = "element"
				uiAssert.AssertElement = &models.AssertElement{
					Expected: assert.Element.Expected,
					Operator: assert.Element.Operator,
					Type:     assert.Element.Type,
					AttrKey:  assert.Element.AttrKey,
				}
			}
		} else if assert.Type == "page" {
			uiCaseStep.Action = commonmodels.JSONAny{
				Data: &models.AssertDTO{
					Type: assert.Type,
					Page: assert.Page,
				},
			}
		} else if assert.Type == "text" {
			if assert.Text.Type == "exist" {
				uiCaseStep.Action = commonmodels.JSONAny{
					Data: &models.AssertDTO{
						Type: "text_exist",
						Text: assert.Text.Texts,
					},
				}
			} else {
				uiCaseStep.Action = commonmodels.JSONAny{
					Data: &models.AssertDTO{
						Type: "text_not_exist",
						Text: assert.Text.Texts,
					},
				}
			}
		} else if assert.Type == "variable" {
			uiCaseStep.Action = commonmodels.JSONAny{
				Data: &models.AssertDTO{
					Type:     assert.Type,
					Variable: assert.Variable,
				},
			}
		} else {
			logger.Logger.Error("未知的断言类型", assert.Type)
			return errors.New("未知的断言类型"), true
		}
	// 代码操作
	case constants.ActionAdvancedCodeOperation:
		logger.Logger.Info("agent step to ", constants.ActionAdvancedCodeOperation)
		code := agentAction.CodeOperation{}
		err = json.Unmarshal(actionBytes, &code)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.CodeOperateDTO{
				CodeType:    code.Type,
				Element:     agentElementToUiElement(code.Element),
				OperateType: code.OperationType,
				CodeContent: code.CodeText,
			},
		}
	// 数据库调用
	case constants.ActionAdvancedCallDatabase:

	// 条件操作
	case constants.ActionAdvancedIfOperate:

	// 循环操作
	case constants.ActionAdvancedLoopOperate:

	// 等待事件
	case constants.ActionAdvancedWait:
		logger.Logger.Info("agent step to ", constants.ActionAdvancedWait)
		w := agentAction.Wait{}
		err = json.Unmarshal(actionBytes, &w)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.WaitEventsDTO{
				Element:  agentElementToUiElement(w.Element),
				Type:     w.Type,
				WaitTime: w.WaitMilli,
			},
		}
	// 数据提取
	case constants.ActionAdvancedDataWithdraw:
		logger.Logger.Info("agent step to ", constants.ActionAdvancedDataWithdraw)
		w := agentAction.DataWithdraw{}
		err = json.Unmarshal(actionBytes, &w)
		if err != nil {
			return errors.New("action 转换 json str 失败," + err.Error()), true
		}
		drawType := "page"
		var we *models.WithdrawElement
		if w.Element != nil {
			drawType = "element"
			we = &models.WithdrawElement{
				Element:       agentElementToUiElement(w.Element.Element),
				Type:          w.Element.Type,
				AttributeName: w.Element.AttributeName,
			}
		}
		uiCaseStep.Action = commonmodels.JSONAny{
			Data: &models.DataWithdrawDTO{
				Type:         drawType,
				VariableName: w.VariableName,
				Webpage:      w.Webpage,
				Element:      we,
				RegexEnable:  w.Regex != "",
				Regex:        w.Regex,
			},
		}
	}
	return err, true
}

func agentPositionToDistance(pos *agentAction.ScrollPosition) *models.DistanceDTO {
	if pos == nil {
		return nil
	}
	return &models.DistanceDTO{
		X: pos.X,
		Y: pos.Y,
	}
}

func agentElementToUiElement(element *agentElement.Element) *models.StepElementDTO {
	if element == nil {
		return nil
	}
	locatorType := constants.LocatorTypeLocator
	var selector *agentElement.Selector
	var locator *agentElement.Locator
	if element.Selector != nil {
		locatorType = constants.LocatorTypeSelector
		selector = &agentElement.Selector{
			Value: *element.Selector,
			Index: element.Index,
		}
	} else {
		locatorType = constants.LocatorTypeLocator
		locator = element.Locator
	}
	e := models.StepElementDTO{
		TargetType: constants.ElementTypeCustom,
		CustomElement: &agentElement.ElementLocator{
			Type:     locatorType,
			Selector: selector,
			Locator:  locator,
		},
	}
	return &e
}

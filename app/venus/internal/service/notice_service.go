package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/goroutine"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/notification"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

type NoticeService struct{}

const (
	uiTitle     = `UI 测试报告: %s`
	uiContent   = "%s\n执行时间: %s \n通过率: %s\n耗　时: %d ms\n成功数: %d\n失败数: %d\n总　计: %d\n\n报告详情: %s"
	uiReportUrl = "%s/project/%s/ui-test/history-report/detail/%s"
)

// PlanNotice 按计划规则通知
func (s NoticeService) PlanNotice(reportId string) {
	goroutine.Run(func() {
		ctx := &commoncontext.MantisContext{Context: context.Background()}
		start := time.Now()
		logger.Logger.Infof("PlanNotice...开始执行计划通知,reportId=%s", reportId)
		report := models.UiReport{}
		report.Id = reportId
		report.IsDeleted = false
		err := gormx.SelectOneByCondition(ctx, &report)
		if err != nil {
			logger.Logger.Errorf("PlanNotice...获取报告失败: %s", err)
			return
		}
		if report.ExecType == string(types.Debug) || report.ExecType == string(types.Cloud) {
			logger.Logger.Infof("PlanNotice...报告类型是 debug 或者 cloud, 不发送通知")
			return
		}
		plan := models.UiPlan{}
		plan.Id = report.RelationId
		plan.IsDeleted = false
		err = gormx.SelectOneByCondition(ctx, &plan)
		if err != nil {
			logger.Logger.Errorf("PlanNotice...获取计划 planId=%s 失败: %s", report.RelationId, err)
			return
		}
		// 查询companyId
		project, err := remote.CubeBaseRemoteApi{}.GetProjectInfo(plan.SpaceId)
		if err != nil || project == nil {
			logger.Logger.Errorf("PlanNotice...获取项目 spaceId=%s 失败: %s", plan.SpaceId, err)
			return
		}
		reportUrl := fmt.Sprintf(uiReportUrl, configs.Config.Domain.Cube, plan.SpaceId, reportId)
		title := fmt.Sprintf(uiTitle, plan.Name)
		content := ""
		success := report.Status == constants.ReportStatusSuccess
		msg := ""
		if success {
			msg = "执行成功"
			content = fmt.Sprintf(uiContent, title, timeToStr(report.ExecStartTime), floatToPercentage(report.PassRate), report.Duration,
				report.CasePassCount, report.CaseFailCount, report.CaseTotalCount, reportUrl)
		} else {
			msg = "执行机错误"
			content = fmt.Sprintf("%s\n执行失败。", title)
		}
		webhookResults := make(models.WebhookResultSlice, 0)
		// 失败通知
		var noticeErr error
		if report.Status == constants.ReportStatusFail && plan.NoticeRule == types.NotificationFail {
			noticeErr = notification.Send(plan.NoticeUsers, title,
				content,
				plan.NoticeType, "", project.CompanyId)
			for _, webhook := range plan.Webhooks {
				webhookResult := dialWebhook(webhook, report, reportUrl, plan.AppId, success, msg)
				if webhookResult != nil {
					webhookResult.Type = models.WebhookResultPlan
					webhookResults = append(webhookResults, *webhookResult)
				}
			}
		} else if plan.NoticeRule == types.NotificationAlways {
			// 总是通知
			noticeErr = notification.Send(plan.NoticeUsers, title,
				content,
				plan.NoticeType, "", project.CompanyId)
			for _, webhook := range plan.Webhooks {
				webhookResult := dialWebhook(webhook, report, reportUrl, plan.AppId, success, msg)
				if webhookResult != nil {
					webhookResult.Type = models.WebhookResultPlan
					webhookResults = append(webhookResults, *webhookResult)
				}
			}
		}
		if noticeErr != nil {
			logger.Logger.Errorf("PlanNotice...发送通知失败:planId=%s, %s", plan.Id, noticeErr)
		}
		// 调用report的hook
		for _, webhook := range report.Webhooks {
			webhookResult := dialWebhook(webhook, report, reportUrl, plan.AppId, success, msg)
			if webhookResult != nil {
				webhookResult.Type = models.WebhookResultReport
				webhookResults = append(webhookResults, *webhookResult)
			}
		}
		report.WebhookResults = webhookResults
		_, err = gormx.UpdateOneByCondition(ctx, &report)
		if err != nil {
			logger.Logger.Errorf("PlanNotice...发送通知失败:planId=%s, %s", plan.Id, err)
		}
		logger.Logger.Infof("PlanNotice...执行计划通知结束,planId=%s,reportId=%s,cost=%d ms", plan.Id, reportId, time.Since(start).Milliseconds())
	})
}

func timeToStr(millis int64) string {
	// 将毫秒时间戳转换为time.Time
	t := time.Unix(0, millis*int64(time.Millisecond))
	// 格式化为指定的字符串格式
	formatted := t.Format("2006:01:02 15:04:05")
	return formatted
}

// 小数转百分比
func floatToPercentage(s string) string {
	if s == "" {
		return "0.00%"
	}
	f, err := strconv.ParseFloat(s, 64)
	if err != nil {
		fmt.Println("Error parsing float:", err)
		return "0.00%"
	}
	return fmt.Sprintf("%.2f%%", f*100)
}

func dialWebhook(webhook string, report models.UiReport, reportUrl, appId string, success bool, msg string) *models.WebhookResult {
	webhookRes := models.WebhookResult{
		Url: webhook,
	}
	result := map[string]any{
		"success":          success,
		"msg":              msg,
		"report_id":        report.Id,
		"app_id":           appId,
		"plan_id":          report.RelationId,
		"report_name":      report.Name,
		"env":              report.Env,
		"duration":         report.Duration,
		"start_time":       report.ExecStartTime,
		"pass_rate":        report.PassRate,
		"case_pass_count":  report.CasePassCount,
		"case_fail_count":  report.CaseFailCount,
		"case_total_count": report.CaseTotalCount,
		"report_url":       reportUrl,
	}
	webhookRes.Request.Body = result
	webhookRes.Request.Method = http.MethodPost
	body, err := json.Marshal(&result)
	if err != nil {
		logger.Logger.Warnf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		webhookRes.Success = false
		webhookRes.Msg = fmt.Sprintf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		return &webhookRes
	}
	resp, err := http.Post(webhook, "application/json", bytes.NewBuffer(body))
	if err != nil {
		logger.Logger.Warnf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		webhookRes.Success = false
		webhookRes.Msg = fmt.Sprintf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		return &webhookRes
	}
	webhookRes.Response.StatusCode = resp.StatusCode
	defer resp.Body.Close()
	respJson, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Logger.Warnf("ui测试执行回调失败. 回调地址:%s. err=%s", webhook, err.Error())
		return &webhookRes
	}
	webhookRes.Success = true
	webhookRes.Response.Body = string(respJson)
	return &webhookRes
}

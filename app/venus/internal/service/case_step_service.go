package service

import (
	"encoding/json"
	"fmt"
	"slices"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pocketbase"
)

type CaseStepService struct{}

func (s CaseStepService) AddBrotherNode(ctx *commoncontext.MantisContext, step *models.UiCaseStep, currentNodeId string, direction string) (*models.UiCaseStep, error) {
	_, err := gormx.InsertOne(ctx, step)
	if err != nil {
		return nil, err
	}
	newSiblingId := ""
	if direction == "up" {
		// up
		newSiblingId = currentNodeId
	} else {
		// down
		current := models.UiCaseStep{}
		current.Id = currentNodeId
		current.IsDeleted = false
		err = gormx.SelectOneByCondition(ctx, &current)
		if err != nil {
			return nil, err
		}
		siblingSteps := make([]models.UiCaseStep, 0)
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.UiCaseStep{}).OrderByAsc("sibling_order").Eq("parent_id", current.ParentId).Gt("sibling_order", current.SiblingOrder).Eq("is_deleted", false),
			&siblingSteps,
		)
		if err != nil {
			return nil, err
		}
		if len(siblingSteps) == 0 {
			newSiblingId = "-1"
		} else {
			newSiblingId = siblingSteps[0].Id
		}
	}
	tableName := "ui_case_step"
	if err := gormx.Move(ctx, tableName, step.Id, step.ParentId, newSiblingId); err != nil {
		return nil, err
	}
	return step, nil
}

func (s CaseStepService) ReferenceComponent(ctx *commoncontext.MantisContext, step *models.UiCaseStep, componentId, currentNodeId, direction string) (*models.UiCaseStep, error) {
	// 查询case step
	current := models.UiCaseStep{}
	if currentNodeId != "" {
		current.Id = currentNodeId
		current.IsDeleted = false
		err := gormx.SelectOneByCondition(ctx, &current)
		if err != nil {
			return nil, err
		}
	}
	// 检查是否是open的或者在同一目录下
	currentCase := models.UiCase{}
	currentCase.Id = step.CaseId
	currentCase.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &currentCase)
	if err != nil {
		return nil, err
	}
	component := models.UiCase{}
	component.Id = componentId
	component.IsDeleted = false
	err = gormx.SelectOneByCondition(ctx, &component)
	if err != nil {
		return nil, err
	}
	// 检测是否存在环
	hasCycle, cyclePath, err := s.detectCycleBeforeAddingEdge(ctx, currentCase.Id, componentId)
	if err != nil {
		return nil, err
	}
	if hasCycle {
		return nil, fmt.Errorf("存在引用环，环路径:%v", cyclePath)
	}
	// 增加节点
	step.Name = component.Name
	step.ReferenceCaseId = componentId
	isReference := true
	step.IsReference = &isReference
	step.IsDir = true
	step.Creator = ctx.User.AdAccount
	step.Modifier = ctx.User.AdAccount
	if direction != "" {
		return s.AddBrotherNode(ctx, step, currentNodeId, direction)
	} else {
		step.ParentId = currentNodeId
		_, err = gormx.InsertOne(ctx, step)
		return step, err
	}
}

// detectCycleBeforeAddingEdge 检测在 如果 a 引用 b 后是否形成环，并返回环路径
// 返回值：
// - bool: 是否存在环
// - []string: 如果存在环，返回环的路径（包含起点和终点）
// - error: 查询过程中的错误
func (s CaseStepService) detectCycleBeforeAddingEdge(ctx *commoncontext.MantisContext, aID, bID string) (bool, []string, error) {
	visited := make(map[string]bool)  // 记录所有访问过的节点
	path := make(map[string]bool)     // 记录当前递归路径中的节点
	parent := make(map[string]string) // 记录父节点，用于回溯路径

	if aID == bID {
		return true, []string{aID, bID}, nil
	}

	// 模拟在 a 中添加对 b 的引用
	startCase := models.UiCase{}
	startCase.Id = aID
	err := gormx.SelectOneByCondition(ctx, &startCase)
	if err != nil {
		return false, nil, fmt.Errorf("failed to query UiCase with ID %s. err=%s", aID, err.Error())
	}

	// 递归检测环
	hasCycle, cyclePath, err := s.detectCycle(ctx, aID, visited, path, parent)
	if err != nil {
		return false, nil, err
	}

	return hasCycle, cyclePath, nil
}

// detectCycle 递归检测环
func (s CaseStepService) detectCycle(ctx *commoncontext.MantisContext, caseID string, visited, path map[string]bool, parent map[string]string) (bool, []string, error) {
	// 如果在当前路径中再次遇到，说明发现环
	if path[caseID] {
		// 回溯构建环路径
		cyclePath := []string{caseID}
		current := caseID
		for parent[current] != "" && parent[current] != caseID {
			cyclePath = append([]string{parent[current]}, cyclePath...)
			current = parent[current]
		}
		cyclePath = append([]string{parent[current]}, cyclePath...)
		return true, cyclePath, nil
	}

	// 如果已访问但不在当前路径，说明不是环
	if visited[caseID] {
		return false, nil, nil
	}

	// 标记为当前路径中的节点
	path[caseID] = true
	visited[caseID] = true

	// 查询当前 UiCase
	uiCase := models.UiCase{}
	uiCase.Id = caseID
	err := gormx.SelectOneByCondition(ctx, &uiCase)
	if err != nil {
		return false, nil, fmt.Errorf("failed to query UiCase with ID %s. err=%s", caseID, err.Error())
	}

	// 遍历所有引用的组件ID
	referencedComponentIds, err := caseDao.GetReferencedComponentIdsByCaseId(ctx, uiCase.Id)
	if err != nil {
		return false, nil, err
	}
	for _, refID := range referencedComponentIds {
		if refID == "" {
			continue
		}
		// 记录父节点
		parent[refID] = caseID
		// 递归检查
		hasCycle, cyclePath, err := s.detectCycle(ctx, refID, visited, path, parent)
		if err != nil {
			return false, nil, fmt.Errorf("error checking reference ID %s. err=%s", refID, err.Error())
		}
		if hasCycle {
			return true, cyclePath, nil
		}
	}

	// 当前路径检查完成，移除
	path[caseID] = false
	return false, nil, nil
}

func (s CaseStepService) DereferenceStepComponent(ctx *commoncontext.MantisContext, stepId string) error {
	return gormx.TransactionReturnErr(ctx, func() error {
		return s.dereferenceStepComponent(ctx, stepId)
	})
}

// dereferenceStep 解引用函数
// 接收引用步骤的ID，在内存中组装步骤树，然后分层插入数据库，并更新同级步骤的 SiblingOrder
// 返回 error 表示操作过程中的错误
func (s CaseStepService) dereferenceStepComponent(ctx *commoncontext.MantisContext, stepID string) error {
	// 查询引用步骤
	var refStep models.UiCaseStep
	refStep.Id = stepID
	refStep.IsDeleted = false
	err := gormx.SelectOneByCondition(ctx, &refStep)
	if err != nil {
		return fmt.Errorf("failed to query step with ID %s: %w", stepID, err)
	}

	// 检查是否为引用步骤
	if refStep.IsReference == nil || !*refStep.IsReference || refStep.ReferenceCaseId == "" {
		return fmt.Errorf("step %s is not a reference step", stepID)
	}

	// 获取被引用用例的步骤
	componentSteps := make([]models.UiCaseStep, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", refStep.ReferenceCaseId).Eq("is_root", false).Eq("is_deleted", false),
		&componentSteps,
	)
	if err != nil {
		return err
	}
	newSteps := make([]models.UiCaseStep, 0)
	stepOldNewMap := make(map[string]string)
	for _, step := range componentSteps {
		newStep := step
		newStep.Id = ""
		newStep.SetNewId()
		newStep.CaseId = refStep.CaseId
		stepOldNewMap[step.Id] = newStep.Id
		newSteps = append(newSteps, newStep)
	}
	var maxSibling int64 = 0
	for i, step := range newSteps {
		newParentId, ok := stepOldNewMap[step.ParentId]
		if !ok {
			newSteps[i].ParentId = refStep.ParentId
			newSteps[i].SiblingOrder += refStep.SiblingOrder - 1
			if newSteps[i].SiblingOrder > maxSibling {
				maxSibling = newSteps[i].SiblingOrder
			}
		} else {
			newSteps[i].ParentId = newParentId
		}
	}

	// 更新同级后续步骤的 SiblingOrder
	err = caseStepDao.UpdateSiblingOrderAfter(ctx, refStep.ParentId, refStep.CaseId, refStep.SiblingOrder-1, maxSibling)
	if err != nil {
		return fmt.Errorf("failed to update sibling order after step %s: %w", stepID, err)
	}

	// 插入新步骤
	_, err = gormx.InsertBatch(ctx, newSteps)
	if err != nil {
		return err
	}

	// 删除原始引用步骤
	err = caseStepDao.Delete(ctx, stepID)
	if err != nil {
		return fmt.Errorf("failed to delete reference step %s: %w", stepID, err)
	}

	return nil
}

// ExtractSteps 提取步骤为组件
func (s CaseStepService) ExtractSteps(ctx *commoncontext.MantisContext, component models.UiCase, stepIds []string, replace bool) error {
	return gormx.TransactionReturnErr(ctx, func() error {
		// 插入component
		isComponent := true
		component.IsComponent = &isComponent
		component.Creator = ctx.User.AdAccount
		component.Modifier = ctx.User.AdAccount
		component.SetNewId()
		_, err := gormx.InsertOne(ctx, &component)
		if err != nil {
			return err
		}
		// 复制被选中的步骤以及根节点
		caseId := ""
		oriSteps := make([]models.UiCaseStep, 0)
		err = gormx.SelectByParamBuilder(ctx,
			gormx.NewParamBuilder().Model(&models.UiCaseStep{}).In("id", stepIds),
			&oriSteps)
		if err != nil {
			return err
		}
		oldNewStepMap := make(map[string]string)
		newSteps := make([]*models.UiCaseStep, 0)
		newStepMap := make(map[string]*models.UiCaseStep)
		oldStepMap := make(map[string]models.UiCaseStep)
		for _, step := range oriSteps {
			if caseId == "" {
				caseId = step.CaseId
			}
			newStep := step
			newStep.Id = ""
			newStep.SetNewId()
			oldNewStepMap[step.Id] = newStep.Id
			newStep.Creator = ctx.User.AdAccount
			newStep.Modifier = ctx.User.AdAccount
			newSteps = append(newSteps, &newStep)
			oldStepMap[step.Id] = step
			newStepMap[step.Id] = &newStep
		}
		newStepIds, err := caseStepDao.SortNodeIds(ctx, caseId, stepIds)
		if err != nil {
			return err
		}
		for i := range newStepIds {
			newStep := newStepMap[newStepIds[i]]
			newStep.SiblingOrder = int64(i + 1)
		}
		for i := range newSteps {
			newSteps[i].CaseId = component.Id
			newSteps[i].ParentId = oldNewStepMap[newSteps[i].ParentId]
		}
		_, err = gormx.InsertBatch(ctx, &newSteps)
		if err != nil {
			return err
		}
		if replace {
			// 找到要被替换的步骤
			replaceStep := oldStepMap[newStepIds[0]]
			// 如果要替换删除原步骤
			_, err = gormx.UpdateBatchByParamBuilderAndMap(ctx,
				gormx.NewParamBuilder().Model(&models.UiCaseStep{}).In("id", stepIds),
				map[string]any{"is_deleted": true, "modifier": ctx.User.AdAccount},
			)
			if err != nil {
				return err
			}
			// 替换步骤
			t := true
			referenceStep := models.UiCaseStep{}
			referenceStep.IsReference = &t
			referenceStep.IsDir = true
			referenceStep.ReferenceCaseId = component.Id
			referenceStep.Creator = ctx.User.AdAccount
			referenceStep.Modifier = ctx.User.AdAccount
			referenceStep.CaseId = replaceStep.CaseId
			referenceStep.ParentId = replaceStep.ParentId
			referenceStep.SiblingOrder = replaceStep.SiblingOrder
			gormx.InsertOne(ctx, &referenceStep)
		}
		return nil
	})
}

func (s CaseStepService) RecursiveUpdateDisable(ctx *commoncontext.MantisContext, stepIds []string, disable string) error {
	return caseStepDao.RecursiveUpdateDisable(ctx, stepIds, disable)
}

func (s CaseStepService) RecursiveCopy(ctx *commoncontext.MantisContext, id string) error {
	steps, err := caseStepDao.RecursiveGetStepsByParentId(ctx, id)
	if err != nil {
		return err
	}
	parentRecordMap := make(map[string][]models.UiCaseStep)
	dirOldNewMap := make(map[string]string)
	for _, step := range steps {
		if step.Id == id {
			step.Id = ""
			step.SiblingOrder = 0
			step.Name += "(复制)"
			step.LatestReportStepId = ""
			res, err := s.AddBrotherNode(ctx, &step, id, "down")
			if err != nil {
				return err
			}
			dirOldNewMap[id] = res.Id
		} else {
			if _, ok := parentRecordMap[step.ParentId]; ok {
				parentRecordMap[step.ParentId] = append(parentRecordMap[step.ParentId], step)
			} else {
				list := make([]models.UiCaseStep, 0)
				list = append(list, step)
				parentRecordMap[step.ParentId] = list
			}
		}
	}
	// 从根节点开始一层一层新增
	currentList := parentRecordMap[id]
	for {
		if len(currentList) == 0 {
			break
		}
		newList := make([]models.UiCaseStep, 0)
		oriIds := make([]string, 0)
		insertList := make([]*models.UiCaseStep, 0)
		for _, step := range currentList {
			oriIds = append(oriIds, step.Id)
			res := step
			res.Id = ""
			res.ParentId = dirOldNewMap[step.ParentId]
			res.LatestReportStepId = ""
			insertList = append(insertList, &res)
		}
		_, err := gormx.InsertBatch(ctx, &insertList)
		if err != nil {
			return err
		}
		for i, res := range insertList {
			if res.IsDir {
				dirOldNewMap[oriIds[i]] = res.Id
				newList = append(newList, parentRecordMap[oriIds[i]]...)
			}
		}
		currentList = newList
	}
	return nil
}

func (s CaseStepService) recursiveCopyByCase(ctx *commoncontext.MantisContext, caseId string, newCaseId string) error {
	// 先复制虚拟根节点
	parentRecordMap := make(map[string][]models.UiCaseStep)
	dirOldNewMap := make(map[string]string)
	virtualRootList := make([]models.UiCaseStep, 0)
	err := gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseId).Eq("sibling_order", 0).Eq("parent_id", "").Eq("is_deleted", false),
		&virtualRootList,
	)
	if err != nil {
		return err
	}
	if len(virtualRootList) == 0 {
		return nil
	}
	virtualRoot := virtualRootList[0]
	oriVRootId := virtualRoot.Id
	newVirtualRoot := virtualRoot
	newVirtualRoot.Id = ""
	newVirtualRoot.CaseId = newCaseId
	_, err = gormx.InsertOne(ctx, &newVirtualRoot)
	if err != nil {
		return err
	}
	dirOldNewMap[oriVRootId] = newVirtualRoot.Id
	// 复制其他节点
	steps := make([]models.UiCaseStep, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiCaseStep{}).Eq("case_id", caseId).Eq("is_deleted", false).Gt("sibling_order", 0),
		&steps,
	)
	if err != nil {
		return err
	}
	for _, step := range steps {
		if _, ok := parentRecordMap[step.ParentId]; ok {
			parentRecordMap[step.ParentId] = append(parentRecordMap[step.ParentId], step)
		} else {
			list := make([]models.UiCaseStep, 0)
			list = append(list, step)
			parentRecordMap[step.ParentId] = list
		}
	}
	// 从根节点开始一层一层新增
	currentList := parentRecordMap[oriVRootId]
	for {
		if len(currentList) == 0 {
			break
		}
		newList := make([]models.UiCaseStep, 0)
		oriIds := make([]string, 0)
		insertList := make([]*models.UiCaseStep, 0)
		for _, step := range currentList {
			oriIds = append(oriIds, step.Id)
			res := step
			res.CaseId = newCaseId
			res.Id = ""
			res.ParentId = dirOldNewMap[step.ParentId]
			res.LatestReportStepId = ""
			insertList = append(insertList, &res)
		}
		_, err := gormx.InsertBatch(ctx, &insertList)
		if err != nil {
			return err
		}
		for i, res := range insertList {
			if res.IsDir {
				dirOldNewMap[oriIds[i]] = res.Id
				newList = append(newList, parentRecordMap[oriIds[i]]...)
			}
		}
		currentList = newList
	}
	return nil
}

func (S CaseStepService) GetCaseSteps(ctx *commoncontext.MantisContext, caseId, reportId string) (*dto.CaseStepsDTO, error) {
	res := &dto.CaseStepsDTO{}
	// 查询steps
	steps, err := caseStepDao.FlattenStepArrayByCaseID(ctx, caseId)
	if err != nil {
		return nil, err
	}
	// 查询elements
	elementIds := make([]string, 0)
	for _, step := range steps {
		elementIds = append(elementIds, step.ElementIds...)
	}
	elements := make([]models.UiElement, 0)
	err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.UiElement{}).In("id", elementIds).Eq("is_deleted", false), &elements)
	if err != nil {
		return nil, err
	}
	elementMap := make(map[string]models.UiElement)
	for _, ele := range elements {
		elementMap[ele.Id] = ele
	}
	realSteps := make([]models.UiReportStepDetail, 0)
	for _, step := range steps {
		// 处理步骤
		eleMap := make(map[string]*element.ElementLocator)
		for _, eid := range step.ElementIds {
			loc := elementMap[eid].Locator.ElementLocator
			eleMap[eid] = &loc
		}
		dealElementRef(&step, eleMap)
		detail := models.UiReportStepDetail{
			Addons2:         step.Addons2,
			CaseId:          step.CaseId,
			StepId:          step.Id,
			Name:            step.Name,
			ParentId:        step.ParentId,
			ActionType:      step.ActionType,
			Action:          step.Action,
			SettingsContent: step.SettingsContent,
			SettingEnable:   step.SettingEnable,
			ElementIds:      step.ElementIds,
			SiblingOrder:    step.SiblingOrder,
			IsDir:           step.IsDir,
			ReportId:        reportId,
			Disable:         step.Disable,
			IsReference:     step.IsReference,
			ReferenceCaseId: step.ReferenceCaseId,
		}
		detail.Id = step.DistinctId
		realSteps = append(realSteps, detail)
	}
	res.Steps = &pocketbase.PaginationRecord[models.UiReportStepDetail]{
		Items: realSteps,
	}
	uiCase := models.UiCase{}
	uiCase.Id = caseId
	uiCase.IsDeleted = false
	err = gormx.SelectOneByCondition(ctx, &uiCase)
	if err != nil {
		return nil, err
	}
	res.Recording = *uiCase.Recording
	// 查询report
	if reportId == "" {
		reportId = uiCase.LastReportId
	}
	// 没有执行过
	if reportId == "" {
		res.Finished = true
		return res, nil
	}
	report := models.UiReport{}
	report.Id = reportId
	report.IsDeleted = false
	err = gormx.SelectOneByCondition(ctx, &report)
	if err != nil {
		res.Finished = true
		res.ReportId = ""
		return res, nil
	}
	res.ExecType = report.ExecType
	res.StartTime = report.ExecStartTime
	res.Finished = report.Status == constants.ReportStatusSuccess || report.Status == constants.ReportStatusFail
	res.Operator = report.Executor
	res.ReportId = report.Id
	if res.Finished {
		res.Duration = report.Duration
	} else {
		res.Duration = time.Now().UnixMilli() - res.StartTime
	}
	// 查询step结果
	stepReports := make([]models.UiReportStepDetail, 0)
	err = gormx.SelectByParamBuilder(ctx,
		gormx.NewParamBuilder().Model(&models.UiReportStepDetail{}).Eq("report_id", reportId).Eq("case_id", caseId).Eq("is_deleted", false),
		&stepReports,
	)
	if err != nil {
		return nil, err
	}
	stepReportMap := make(map[string]models.UiReportStepDetail)
	for _, stepReport := range stepReports {
		stepReportMap[stepReport.StepId] = stepReport
	}
	for i := range res.Steps.Items {
		id := res.Steps.Items[i].Id
		stepReport := stepReportMap[id]
		res.Steps.Items[i].ReportCaseId = stepReport.ReportCaseId
		res.Steps.Items[i].Status = stepReport.Status
		res.Steps.Items[i].IsCount = stepReport.IsCount
		res.Steps.Items[i].ReportMessage = stepReport.ReportMessage
	}
	return res, nil
}

// 处理步骤,如果包含引用元素,则需要更新 view_element 及 ref_element_id
func dealElementRef(step *models.UiCaseStep, eleMap map[string]*element.ElementLocator) {
	if !slices.Contains(models.NeedDealActionType, step.ActionType) {
		return
	}
	if step.Action.Data == nil {
		return
	}
	marshal, err := json.Marshal(step.Action)
	if err != nil {
		return
	}
	switch step.ActionType {
	case constants.ActionMouseClick:
		var action models.MouseClickDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionMouseMove:
		var action models.MouseMoveDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionMouseScroll:
		var action models.MouseScrollDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		doElementRef(action.ScrollElement, eleMap)
		step.Action.Data = action
	case constants.ActionMouseDrag:
		var action models.MouseDragDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionMouseHold:
		var action models.MouseHoldDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionInput:
		var action models.InputOperationsDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionInputUploadFiles:
		var action models.UploadFilesDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionInputSelect:
		var action models.SelectDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionAdvancedWait:
		var action models.WaitEventsDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionAdvancedAssert:
		var action models.AssertDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionAdvancedDataWithdraw:
		var action models.DataWithdrawDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		if action.Element == nil {
			return
		}
		doElementRef(action.Element.Element, eleMap)
		step.Action.Data = action
	case constants.ActionAdvancedCodeOperation:
		var action models.CodeOperateDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		doElementRef(action.Element, eleMap)
		step.Action.Data = action
	case constants.ActionAdvancedIfOperate:
		var action models.IfOperateDTO
		err = json.Unmarshal(marshal, &action)
		if err != nil {
			return
		}
		if len(action.Condition) < 1 {
			return
		}
		for _, con := range action.Condition {
			doElementRef(con.Element, eleMap)
		}
		step.Action.Data = action
	}
}

func doElementRef(ele *models.StepElementDTO, eleMap map[string]*element.ElementLocator) {
	if ele == nil || ele.RefElementId == "" {
		return
	}
	if locator, ok := eleMap[ele.RefElementId]; ok {
		ele.ViewElement = locator
	} else {
		ele.ViewElement = nil
		ele.RefElementId = ""
	}
}

package service

import (
	"encoding/json"
	"fmt"
	"testing"

	"github.com/iancoleman/strcase"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
)

func Test_aiStepToUIStep(t *testing.T) {
	str := "{\n    \t\"actionType\": \"mouseClick\",\n        \"action\": {\n            \"actionType\": \"mouseClick\",\n            \"type\": \"single\",\n            \"button\": \"left\",\n            \"element\": {\n                \"name\": \"Tables display sets of data. They can be fully customized.\",\n                \"selector\": \"html > body > div > div > main > div > div > div > p\",\n                \"index\": 0\n            },\n            \"name\": \"左键单击 Tables display sets of data. They can be fully customized.\",\n            \"elementDescription\": {\n                \"locator\": \"\",\n                \"selector\": \"html > body > div > div > main > div > div > div > p\"\n            }\n        },\n        \"name\": \"左键单击 Tables display sets of data. They can be fully customized.\"\n    }"
	m := make(map[string]any)
	err := json.Unmarshal([]byte(str), &m)
	if err != nil {
		fmt.Printf("err:%v", err)
		return
	}
	fmt.Printf("%+v \n", m)
	actionType := m["actionType"].(string)
	fmt.Printf("actionType= %v \n", actionType)
	if actionType == "mouseClick" {
		actionByte, _ := json.Marshal(m["action"])
		mc := action.MouseClick{}
		err := json.Unmarshal(actionByte, &mc)
		if err != nil {
			return
		}
		fmt.Printf("mouse-click %+v \n", mc)
		fmt.Printf("element %+v \n", mc.Element)

	}
}

func Test_camelToSnake(t *testing.T) {
	camelCaseString := "mouseClick"
	snakeCaseString := strcase.ToSnake(camelCaseString)
	println(snakeCaseString) // 输出: camel_case_string
}

package service

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type ElementService struct{}

// BindCaseList 获取元素绑定的用例
func (e *ElementService) BindCaseList(ctx *commoncontext.MantisContext, id string, page, pageSize int64) (*commondto.PaginationRecord[dto.ElementCaseDto], error) {
	total, err := caseDao.CountByElementId(ctx, id)
	if err != nil {
		logger.Logger.Panicf("%+v", err)
	}
	if total == 0 {
		return &commondto.PaginationRecord[dto.ElementCaseDto]{
			Page:    int(page),
			PerPage: int(pageSize),
			Items:   []dto.ElementCaseDto{},
		}, nil
	}
	items, err := caseDao.GetCaseByElementId(ctx, id, page, pageSize)
	if err != nil {
		return nil, err
	}
	return &commondto.PaginationRecord[dto.ElementCaseDto]{
		Page:       int(page),
		PerPage:    int(pageSize),
		TotalItems: int64(total),
		Items:      *items,
	}, nil
}

func (e *ElementService) BatchDeleteCheck(ctx *commoncontext.MantisContext, ids []string) (bool, error) {
	check, err := elementDao.BatchDeleteCheck(ctx, ids)
	if err != nil {
		return false, err
	}
	return !(check > 0), nil
}

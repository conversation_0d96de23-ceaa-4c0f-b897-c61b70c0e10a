// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// GET Path("/ui/api/v1/biz/ui_element/bindCaseList/{id}") -> controller.ElementController.BindCaseList
func controllerElementControllerBindCaseListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "BindCaseList",
		Patten:            "/ui/api/v1/biz/ui_element/bindCaseList/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ElementController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeElementController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ElementReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idDto := dto.PathIdDto{}
		if i, ok := decode.Implements(&idDto); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idDto); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idDto); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_paginationRecord, err1 := ctrl.BindCaseList(&req1, idDto)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call BindCaseList failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _paginationRecord)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_element/batchDeleteCheck") -> controller.ElementController.BatchDeleteCheck
func controllerElementControllerBatchDeleteCheckHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "BatchDeleteCheck",
		Patten:            "/ui/api/v1/biz/ui_element/batchDeleteCheck",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "ElementController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeElementController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.BatchDeleteIds{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_checkDeleteRes, err1 := ctrl.BatchDeleteCheck(req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call BatchDeleteCheck failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _checkDeleteRes)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

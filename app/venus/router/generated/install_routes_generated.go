// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: controller

	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/copy").Hand<PERSON>(controllerCaseControllerCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/startRecord").Hand<PERSON>(controllerCaseControllerStartRecordHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/stopRecord").Handler(controllerCaseControllerStopRecordHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/startDebug").Handler(controllerCaseControllerDebugPrepareHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/run").Handler(controllerCaseControllerRunHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/save/record").Handler(controllerCaseControllerSaveRecordHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/save/aiGenerate").Handler(controllerCaseControllerSaveAiGenerateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/extract").Handler(controllerCaseControllerExtractHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case/stopExecute").Handler(controllerCaseControllerStopExecuteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/biz/ui_case/getLastRunReport").Handler(controllerCaseControllerGetLastRunResultHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/biz/ui_case/getReferenceCases/{id}").Handler(controllerCaseControllerFindReferenceCasesHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/addBrotherNode").Handler(controllerCaseStepControllerAddBrotherNodeHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/recursiveDisable").Handler(controllerCaseStepControllerRecursiveUpdateDisableHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/recursiveCopy").Handler(controllerCaseStepControllerRecursiveCopyHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/reference").Handler(controllerCaseStepControllerReferenceComponentHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/dereference").Handler(controllerCaseStepControllerDereferenceHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_case_step/extractSteps").Handler(controllerCaseStepControllerExtractStepsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/biz/ui_case_step/records").Handler(controllerCaseStepControllerGetCaseStepsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/ui/api/v1/biz/ui_element/bindCaseList/{id}").Handler(controllerElementControllerBindCaseListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_element/batchDeleteCheck").Handler(controllerElementControllerBatchDeleteCheckHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/ui/api/v1/biz/front/dropdownList").Handler(controllerFrontControllerDropdownListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_plan/execute").Handler(controllerPlanControllerManualExecuteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/biz/ui_plan/list").Handler(controllerPlanControllerSearchHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/ui/openapi/v1/plan/execute").Handler(controllerPlanControllerOpenExecuteHandleFunc(alice.New(_recovery, _trace, _accessLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/biz/ui_report/stopExecute").Handler(controllerReportControllerStopExecuteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/openapi/v1/agent/callback").Handler(controllerReportControllerCallbackHandleFunc(alice.New(_recovery, _trace, _accessLog), opt))

	//
	mux.Methods("GET").Path("/ui/api/v1/biz/types/exec").Handler(controllerTypesControllerExecuteTypeListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_case/records").Handler(controllerUiCaseControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_case/records/{id}").Handler(controllerUiCaseControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_case/records/{id}").Handler(controllerUiCaseControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_case/move").Handler(controllerUiCaseControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_case/records/{id}").Handler(controllerUiCaseControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_case/records").Handler(controllerUiCaseControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_case_step/records").Handler(controllerUiCaseStepControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_case_step/records/{id}").Handler(controllerUiCaseStepControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_case_step/records/{id}").Handler(controllerUiCaseStepControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_case_step/records/batchDelete").Handler(controllerUiCaseStepControllerBatchDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_case_step/move").Handler(controllerUiCaseStepControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_case_step/records/{id}").Handler(controllerUiCaseStepControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_case_step/records").Handler(controllerUiCaseStepControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_element/records").Handler(controllerUiElementControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_element/records/{id}").Handler(controllerUiElementControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_element/records/{id}").Handler(controllerUiElementControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_element/records/batchDelete").Handler(controllerUiElementControllerBatchDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_element/move").Handler(controllerUiElementControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_element/records/{id}").Handler(controllerUiElementControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_element/records").Handler(controllerUiElementControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_plan/records").Handler(controllerUiPlanControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_plan/records/{id}").Handler(controllerUiPlanControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_plan/records/{id}").Handler(controllerUiPlanControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_plan/records/batchDelete").Handler(controllerUiPlanControllerBatchDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_plan/records/{id}").Handler(controllerUiPlanControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_plan/records").Handler(controllerUiPlanControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_report_case_detail/records").Handler(controllerUiReportCaseControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report_case_detail/records/{id}").Handler(controllerUiReportCaseControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_report_case_detail/records/{id}").Handler(controllerUiReportCaseControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_report_case_detail/records/{id}").Handler(controllerUiReportCaseControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report_case_detail/records").Handler(controllerUiReportCaseControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_report/records/{id}").Handler(controllerUiReportControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_report/records/{id}").Handler(controllerUiReportControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report/records/{id}").Handler(controllerUiReportControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report/records").Handler(controllerUiReportControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_report/records/batchDelete").Handler(controllerUiReportControllerBatchDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_report_step_detail/records").Handler(controllerUiReportStepDetailControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report_step_detail/records/{id}").Handler(controllerUiReportStepDetailControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("PUT").Path("/ui/api/v1/collections/ui_report_step_detail/records/{id}").Handler(controllerUiReportStepDetailControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("DELETE").Path("/ui/api/v1/collections/ui_report_step_detail/records/{id}").Handler(controllerUiReportStepDetailControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/ui/api/v1/collections/ui_report_step_detail/records").Handler(controllerUiReportStepDetailControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/ui/api/v1/collections/ui_report_step_detail/move").Handler(controllerUiReportStepDetailControllerMoveHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

}

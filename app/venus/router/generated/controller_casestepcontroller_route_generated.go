// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/ui/api/v1/biz/ui_case_step/addBrotherNode") -> controller.CaseStepController.AddBrotherNode
func controllerCaseStepControllerAddBrotherNodeHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "AddBrotherNode",
		Patten:            "/ui/api/v1/biz/ui_case_step/addBrotherNode",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-STEP-CREATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.AddBrotherRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_uiCaseStep, err1 := ctrl.AddBrotherNode(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call AddBrotherNode failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _uiCaseStep)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case_step/recursiveDisable") -> controller.CaseStepController.RecursiveUpdateDisable
func controllerCaseStepControllerRecursiveUpdateDisableHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RecursiveUpdateDisable",
		Patten:            "/ui/api/v1/biz/ui_case_step/recursiveDisable",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-STEP-ACTIVATE",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RecursiveUpdateDisableRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.RecursiveUpdateDisable(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call RecursiveUpdateDisable failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case_step/recursiveCopy") -> controller.CaseStepController.RecursiveCopy
func controllerCaseStepControllerRecursiveCopyHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "RecursiveCopy",
		Patten:            "/ui/api/v1/biz/ui_case_step/recursiveCopy",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-STEP-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RecursiveCopyStepRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.RecursiveCopy(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call RecursiveCopy failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case_step/reference") -> controller.CaseStepController.ReferenceComponent
func controllerCaseStepControllerReferenceComponentHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ReferenceComponent",
		Patten:            "/ui/api/v1/biz/ui_case_step/reference",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-COMPONENT-LINK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ReferenceComponentRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_uiCaseStep, err1 := ctrl.ReferenceComponent(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call ReferenceComponent failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _uiCaseStep)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case_step/dereference") -> controller.CaseStepController.Dereference
func controllerCaseStepControllerDereferenceHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Dereference",
		Patten:            "/ui/api/v1/biz/ui_case_step/dereference",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-COMPONENT-UNLINK",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.RecursiveCopyStepRequest{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Dereference(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Dereference failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case_step/extractSteps") -> controller.CaseStepController.ExtractSteps
func controllerCaseStepControllerExtractStepsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "ExtractSteps",
		Patten:            "/ui/api/v1/biz/ui_case_step/extractSteps",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-COMPONENT-EXTRACT",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.ExtractStepsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.ExtractSteps(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call ExtractSteps failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/ui/api/v1/biz/ui_case_step/records") -> controller.CaseStepController.GetCaseSteps
func controllerCaseStepControllerGetCaseStepsHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetCaseSteps",
		Patten:            "/ui/api/v1/biz/ui_case_step/records",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseStepController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseStepController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.GetCaseStepsReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_caseStepsDTO, err1 := ctrl.GetCaseSteps(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call GetCaseSteps failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _caseStepsDTO)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/dto"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/ui/api/v1/biz/ui_case/copy") -> controller.CaseController.Copy
func controllerCaseControllerCopyHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Copy",
		Patten:            "/ui/api/v1/biz/ui_case/copy",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-COPY",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Copy(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Copy failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/startRecord") -> controller.CaseController.StartRecord
func controllerCaseControllerStartRecordHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "StartRecord",
		Patten:            "/ui/api/v1/biz/ui_case/startRecord",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-RECORD",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.StartRecord(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call StartRecord failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/stopRecord") -> controller.CaseController.StopRecord
func controllerCaseControllerStopRecordHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "StopRecord",
		Patten:            "/ui/api/v1/biz/ui_case/stopRecord",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-RECORD",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.StopRecord(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call StopRecord failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/startDebug") -> controller.CaseController.DebugPrepare
func controllerCaseControllerDebugPrepareHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DebugPrepare",
		Patten:            "/ui/api/v1/biz/ui_case/startDebug",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-RUN",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.TransParam{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_debugCaseDto, err1 := ctrl.DebugPrepare(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call DebugPrepare failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _debugCaseDto)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/run") -> controller.CaseController.Run
func controllerCaseControllerRunHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Run",
		Patten:            "/ui/api/v1/biz/ui_case/run",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-RUN-CLOUD",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.TransParam{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_runRes, err1 := ctrl.Run(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Run failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _runRes)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/save/record") -> controller.CaseController.SaveRecord
func controllerCaseControllerSaveRecordHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveRecord",
		Patten:            "/ui/api/v1/biz/ui_case/save/record",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-RECORD",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		agentCase := dto.AgentCaseDto{}
		if i, ok := decode.Implements(&agentCase); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &agentCase); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(agentCase); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.SaveRecord(&agentCase)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call SaveRecord failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/save/aiGenerate") -> controller.CaseController.SaveAiGenerate
func controllerCaseControllerSaveAiGenerateHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "SaveAiGenerate",
		Patten:            "/ui/api/v1/biz/ui_case/save/aiGenerate",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-CASE-AI",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		agentSteps := dto.AgentCaseDto{}
		if i, ok := decode.Implements(&agentSteps); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &agentSteps); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(agentSteps); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_respId, err1 := ctrl.SaveAiGenerate(&agentSteps)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call SaveAiGenerate failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _respId)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/extract") -> controller.CaseController.Extract
func controllerCaseControllerExtractHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Extract",
		Patten:            "/ui/api/v1/biz/ui_case/extract",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog", "authorization"},
		Label: map[string]string{
			"auditlog.id":       "$resp.traceId",
			"auditlog.resource": "UI测试",
			"auth.code":         "MANTIS-VENUS-COMPONENT-TRANSFER",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.CaseCopyReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Extract(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Extract failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// POST Path("/ui/api/v1/biz/ui_case/stopExecute") -> controller.CaseController.StopExecute
func controllerCaseControllerStopExecuteHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "StopExecute",
		Patten:            "/ui/api/v1/biz/ui_case/stopExecute",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.StopDebugReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.StopExecute(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call StopExecute failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/ui/api/v1/biz/ui_case/getLastRunReport") -> controller.CaseController.GetLastRunResult
func controllerCaseControllerGetLastRunResultHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "GetLastRunResult",
		Patten:            "/ui/api/v1/biz/ui_case/getLastRunReport",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.GetLastRunResultReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_caseRunResDTO, err1 := ctrl.GetLastRunResult(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call GetLastRunResult failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _caseRunResDTO)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/ui/api/v1/biz/ui_case/getReferenceCases/{id}") -> controller.CaseController.FindReferenceCases
func controllerCaseControllerFindReferenceCasesHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "FindReferenceCases",
		Patten:            "/ui/api/v1/biz/ui_case/getReferenceCases/{id}",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "CaseController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "UI测试",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeCaseController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.FindReferenceCaseReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		idReq := dto.PathIdDto{}
		if i, ok := decode.Implements(&idReq); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := path.Decode(req, &idReq); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(idReq); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_paginationRecord, err1 := ctrl.FindReferenceCases(&req1, &idReq)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call FindReferenceCases failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _paginationRecord)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

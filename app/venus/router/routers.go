package router

import (
	"context"

	"github.com/gorilla/handlers"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/router/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/accesslog"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
	authPkg "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authentication"
	authoPkg "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authorization"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/tracing"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/audit"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/authority/ua"
	"github.com/go-playground/validator/v10"
	"github.com/gorilla/mux"
	"github.com/sirupsen/logrus"
)

func InstallRouter(ctx context.Context, env *configs.Configs, router *mux.Router) *mux.Router {
	handlerOpt := types.Option{
		Codec:        &codec.DefaultJsonEncoder,
		Validator:    validator.New(),
		LogRouteFunc: types.DefaultLogRouterFunc,
	}
	setMiddleware(ctx, &handlerOpt, env)
	generated.InstallRoutes(router, handlerOpt)
	return router
}

func setMiddleware(ctx context.Context, handlerOpt *types.Option, env *configs.Configs) {
	generated.SetAccessLogMiddleware(accesslog.WrapLoggingHandler(logrus.StandardLogger().Out))
	generated.SetRecoveryMiddleware(handlers.RecoveryHandler(handlers.PrintRecoveryStack(true),
		handlers.RecoveryLogger(logrus.StandardLogger())))
	generated.SetTraceMiddleware(tracing.New)
	generated.SetAuthenticationMiddleware(authPkg.WrapAuthenticationHandler(ua.GetDefaultUA(), authPkg.SsoConfig{
		UserCenterServer: env.Auth.UaUrl,
	}))
	generated.SetAuthorizationMiddleware(authoPkg.WrapAuthorizationHandler())
	ids := []string{"id", "publish_id", "task_id", "app_id", "auditId", "publishId"}
	generated.SetAuditLogMiddleware(auditlog.WrapAuditLogHandler(auditlog.NewCubeAuditLog(audit.GetDefaultAudit(), auditlog.WithDefaultIdSources(ids))))
}

package router

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/controller"
	. "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codegen/router/decl"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
)

const (
	accessLog            = "accessLog"
	recovery             = "recovery"
	trace                = "trace"
	authentication       = "authentication"
	authorization        = "authorization"
	auditLog             = "auditLog"
	authenticationx      = "authentication-"
	authorizationx       = "authorization-"
	auditLogx            = "auditLog-"
	pocketBaseReqHandler = "pocketBaseReqHandler"
)

const (
	VenusAuditResourceLabelKey = auditlog.AuditResourceLabelKey + "=" + "UI测试"
	VenusAuditId               = "auditlog.id=$resp.traceId"
)

var _ = Path("/ui",
	Path("/api/v1",
		Middlewares([]string{recovery, trace, accessLog, authentication, auditLog},
			Labels([]string{VenusAuditResourceLabelKey},
				Path("/collections",
					Constructor(controller.InitializeUiCaseController,
						Middleware(authorization,
							Path("/ui_case/records", POST(controller.DefaultUiCaseController.Create, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-CREATE")),
							Path("/ui_case/records/{id}", PUT(controller.DefaultUiCaseController.Update, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RENAME")),
							Path("/ui_case/records/{id}", DELETE(controller.DefaultUiCaseController.Delete, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-DELETE")),
							Path("/ui_case/move", POST(controller.DefaultUiCaseController.Move, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-MOVE")),
						),
						Path("/ui_case/records/{id}", GET(controller.DefaultUiCaseController.Get)),
						Path("/ui_case/records", GET(controller.DefaultUiCaseController.List)),
					),
					Constructor(controller.InitializeUiCaseStepController,
						Middleware(authorization,
							Path("/ui_case_step/records", POST(controller.DefaultUiCaseStepController.Create, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-CREATE")),
							Path("/ui_case_step/records/{id}", PUT(controller.DefaultUiCaseStepController.Update, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-EDIT")),
							Path("/ui_case_step/records/{id}", DELETE(controller.DefaultUiCaseStepController.Delete, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-DELETE")),
							Path("/ui_case_step/records/batchDelete", POST(controller.DefaultUiCaseStepController.BatchDelete, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-DELETE-BATCH")),
							Path("/ui_case_step/move", POST(controller.DefaultUiCaseStepController.Move, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-MOVE")),
						),
						Path("/ui_case_step/records/{id}", GET(controller.DefaultUiCaseStepController.Get)),
						Path("/ui_case_step/records", GET(controller.DefaultUiCaseStepController.List)),
					),
					Constructor(controller.InitializeUiElementController,
						Middleware(authorization,
							Path("/ui_element/records", POST(controller.DefaultUiElementController.Create, VenusAuditId, "auth.code=MANTIS-VENUS-ELEMENT-CREATE")),
							Path("/ui_element/records/{id}", PUT(controller.DefaultUiElementController.Update, VenusAuditId, "auth.code=MANTIS-VENUS-ELEMENT-EDIT")),
							Path("/ui_element/records/{id}", DELETE(controller.DefaultUiElementController.Delete, VenusAuditId, "auth.code=MANTIS-VENUS-ELEMENT-DELETE")),
							Path("/ui_element/records/batchDelete", POST(controller.DefaultUiElementController.BatchDelete, VenusAuditId, "auth.code=MANTIS-VENUS-ELEMENT-DELETE-BATCH")),
							Path("/ui_element/move", POST(controller.DefaultUiElementController.Move, VenusAuditId, "auth.code=MANTIS-VENUS-ELEMENT-MOVE")),
						),
						Path("/ui_element/records/{id}", GET(controller.DefaultUiElementController.Get)),
						Path("/ui_element/records", GET(controller.DefaultUiElementController.List)),
					),
					Constructor(controller.InitializeUiPlanController,
						Middleware(authorization,
							Path("/ui_plan/records", POST(controller.DefaultUiPlanController.Create, VenusAuditId, "auth.code=MANTIS-VENUS-PLAN-CREATE")),
							Path("/ui_plan/records/{id}", PUT(controller.DefaultUiPlanController.Update, VenusAuditId, "auth.code=MANTIS-VENUS-PLAN-EDIT")),
							Path("/ui_plan/records/{id}", DELETE(controller.DefaultUiPlanController.Delete, VenusAuditId, "auth.code=MANTIS-VENUS-PLAN-DELETE")),
							Path("/ui_plan/records/batchDelete", POST(controller.DefaultUiPlanController.BatchDelete, VenusAuditId, "auth.code=MANTIS-VENUS-PLAN-DELETE-BATCH")),
						),
						Path("/ui_plan/records/{id}", GET(controller.DefaultUiPlanController.Get)),
						Path("/ui_plan/records", GET(controller.DefaultUiPlanController.List)),
					),
					Constructor(controller.InitializeUiReportController,
						Middleware(authorization,
							Path("/ui_report/records/{id}", PUT(controller.DefaultUiReportController.Update, VenusAuditId, "auth.code=MANTIS-VENUS-REPORT-EDIT")),
							Path("/ui_report/records/{id}", DELETE(controller.DefaultUiReportController.Delete, VenusAuditId, "auth.code=MANTIS-VENUS-REPORT-DELETE")),
						),
						Path("/ui_report/records/{id}", GET(controller.DefaultUiReportController.Get)),
						Path("/ui_report/records", GET(controller.DefaultUiReportController.List)),
						Path("/ui_report/records/batchDelete", POST(controller.DefaultUiReportController.BatchDelete)),
					),
					Constructor(controller.InitializeUiReportCaseController,
						Path("/ui_report_case_detail/records", POST(controller.DefaultUiReportCaseController.Create)),
						Path("/ui_report_case_detail/records/{id}", GET(controller.DefaultUiReportCaseController.Get)),
						Path("/ui_report_case_detail/records/{id}", PUT(controller.DefaultUiReportCaseController.Update)),
						Path("/ui_report_case_detail/records/{id}", DELETE(controller.DefaultUiReportCaseController.Delete)),
						Path("/ui_report_case_detail/records", GET(controller.DefaultUiReportCaseController.List)),
					),
					Constructor(controller.InitializeUiReportStepDetailController,
						Path("/ui_report_step_detail/records", POST(controller.DefaultUiReportStepDetailController.Create)),
						Path("/ui_report_step_detail/records/{id}", GET(controller.DefaultUiReportStepDetailController.Get)),
						Path("/ui_report_step_detail/records/{id}", PUT(controller.DefaultUiReportStepDetailController.Update)),
						Path("/ui_report_step_detail/records/{id}", DELETE(controller.DefaultUiReportStepDetailController.Delete)),
						Path("/ui_report_step_detail/records", GET(controller.DefaultUiReportStepDetailController.List)),
						Path("/ui_report_step_detail/move", POST(controller.DefaultUiReportStepDetailController.Move)),
					),
				),
				Path("/biz",
					Constructor(controller.InitializeFrontController,
						Path("/front",
							Path("/dropdownList", GET(controller.DefaultFrontController.DropdownList)),
						),
					),
					Constructor(controller.InitializeTypesController,
						Path("/types",
							Path("/exec", GET(controller.DefaultTypesController.ExecuteTypeList)),
						),
					),
					Constructor(controller.InitializeElementController,
						Path("/ui_element",
							Path("/bindCaseList/{id}", GET(controller.DefaultElementController.BindCaseList)),
							Path("/batchDeleteCheck", POST(controller.DefaultElementController.BatchDeleteCheck)),
						),
					),
					Constructor(controller.InitializeCaseController,
						Path("/ui_case",
							Middleware(authorization,
								Path("/copy", POST(controller.DefaultCaseController.Copy, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-COPY")),
								Path("/startRecord", POST(controller.DefaultCaseController.StartRecord, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RECORD")),
								Path("/stopRecord", POST(controller.DefaultCaseController.StopRecord, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RECORD")),
								Path("/startDebug", POST(controller.DefaultCaseController.DebugPrepare, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RUN")),
								Path("/run", POST(controller.DefaultCaseController.Run, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RUN-CLOUD")),
								Path("/save/record", POST(controller.DefaultCaseController.SaveRecord, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-RECORD")),
								Path("/save/aiGenerate", POST(controller.DefaultCaseController.SaveAiGenerate, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-AI")),
								Path("/extract", POST(controller.DefaultCaseController.Extract, VenusAuditId, "auth.code=MANTIS-VENUS-COMPONENT-TRANSFER")),
							),
							Path("/stopExecute", POST(controller.DefaultCaseController.StopExecute)),
							Path("/getLastRunReport", GET(controller.DefaultCaseController.GetLastRunResult)),
							Path("/getReferenceCases/{id}", GET(controller.DefaultCaseController.FindReferenceCases)),
						),
					),
					Constructor(controller.InitializeCaseStepController,
						Path("/ui_case_step",
							Middleware(authorization,
								Path("/addBrotherNode", POST(controller.DefaultCaseStepController.AddBrotherNode, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-CREATE")),
								Path("/recursiveDisable", POST(controller.DefaultCaseStepController.RecursiveUpdateDisable, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-ACTIVATE")),
								Path("/recursiveCopy", POST(controller.DefaultCaseStepController.RecursiveCopy, VenusAuditId, "auth.code=MANTIS-VENUS-CASE-STEP-COPY")),
								Path("/reference", POST(controller.DefaultCaseStepController.ReferenceComponent, VenusAuditId, "auth.code=MANTIS-VENUS-COMPONENT-LINK")),
								Path("/dereference", POST(controller.DefaultCaseStepController.Dereference, VenusAuditId, "auth.code=MANTIS-VENUS-COMPONENT-UNLINK")),
								Path("/extractSteps", POST(controller.DefaultCaseStepController.ExtractSteps, VenusAuditId, "auth.code=MANTIS-VENUS-COMPONENT-EXTRACT")),
							),
							Path("/records", GET(controller.DefaultCaseStepController.GetCaseSteps)),
						),
					),
					Constructor(controller.InitializePlanController,
						Path("/ui_plan",
							Middleware(authorization,
								Path("/execute", POST(controller.DefaultPlanController.ManualExecute, VenusAuditId, "auth.code=MANTIS-VENUS-PLAN-RUN")),
							),
							Path("/list", GET(controller.DefaultPlanController.Search)),
						),
					),
					Constructor(controller.InitializeReportController,
						Path("/ui_report",
							Middleware(authorization,
								Path("/stopExecute", POST(controller.DefaultReportController.StopExecute, VenusAuditId, "auth.code=MANTIS-VENUS-REPORT-STOP")),
							),
						),
					),
				),
			),
		),
	),
	Path("/openapi/v1",
		Middlewares([]string{recovery, trace, accessLog},
			Constructor(controller.InitializeReportController,
				Path("/agent",
					Path("/callback", POST(controller.DefaultReportController.Callback)),
				),
			),
			Constructor(controller.InitializePlanController,
				Path("/plan",
					Path("/execute", POST(controller.DefaultPlanController.OpenExecute)),
				),
			),
		),
	),
)

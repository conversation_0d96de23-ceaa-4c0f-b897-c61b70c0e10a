package logs

import (
	"errors"
	"io"
	"log"
	"os"
	"os/exec"
	"strings"
)

func SyncOutLog(cmdInfo *exec.Cmd) error {
	cmdStdoutPipe, _ := cmdInfo.StdoutPipe()
	cmdStderrPipe, _ := cmdInfo.StderrPipe()
	err := cmdInfo.Start()
	if err != nil {
		log.Printf("start cmd err: %v", err)
		return err
	}

	logger := log.New(os.Stdout, "", log.LstdFlags)
	oldFlags := logger.Flags()
	logger.SetFlags(0)
	go syncLog(logger, cmdStdoutPipe)
	go syncLog(logger, cmdStderrPipe)
	err = cmdInfo.Wait()
	logger.SetFlags(oldFlags)
	if err != nil {
		log.Printf("execute cmd err: %v", err)
		return err
	}
	return nil
}

func syncLog(logger *log.Logger, reader io.ReadCloser) {
	cache := make([]byte, 0)
	enterCache := make([]byte, 0)
	buf := make([]byte, 1024)
	for {
		strNum, err := reader.Read(buf)
		if strNum > 0 {
			cache = append(cache, buf[:strNum]...)
			if len(cache) > 1024 {
				split := strings.Split(string(cache), "\n")
				cache = append(enterCache, []byte(strings.Join(split[:len(split)-1], "\n"))...)
				logger.Printf("%s", cache)
				enterCache = []byte(split[len(split)-1])
				cache = make([]byte, 0)
			}
		}
		if err != nil {
			if errors.Is(err, io.EOF) {
				logger.Printf("%s", cache)
				break
			}
		}
	}
}

package uiagent

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"

	cases "git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/result"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
)

func Run(input string, callBack string, reportId string, fileDownloadUrl string, variableurl string, env string) error {
	if callBack == "" {
		return errors.New("can not execute without callback")
	}
	if fileDownloadUrl == "" {
		return errors.New("can not execute without file download url")
	}
	configs.CallBackUrl = callBack
	configs.ReportId = reportId
	configs.FileDownloadUrl = fileDownloadUrl
	if err := variable.Init(variableurl, env); err != nil {
		return err
	}
	datas := make([]map[string]any, 0)
	err := json.Unmarshal([]byte(input), &datas)
	if err != nil {
		return err
	}
	runCases := make([]cases.Case, 0)
	for _, data := range datas {
		c, err := convertJSONToCase(data)
		if err != nil {
			return err
		}
		runCases = append(runCases, c)
	}
	var resErr error
	for _, c := range runCases {
		// 重置panic flag
		cases.PanicFlag = false
		execute := func() {
			defer func() {
				if err1 := recover(); err1 != nil {
					resErr = fmt.Errorf("%v", err1)
				}
				caseCallBack(c.Id, reportId, (resErr == nil && !cases.PanicFlag))
			}()
			resErr = c.ExecuteWithPW(nil, nil, nil)
		}
		execute()
	}
	return nil
}

var dDefualtStepConfig *cases.StepConfig = nil

func convertJSONToCase(data map[string]any) (cases.Case, error) {
	var caseData cases.Case
	// convert id
	id, ok := data["id"].(string)
	if !ok {
		return caseData, fmt.Errorf("id is not a string")
	}
	caseData.Id = id
	// Convert config
	configData, ok := data["config"].(map[string]any)
	if !ok {
		return caseData, fmt.Errorf("config is not a map")
	}
	configJSON, err := json.Marshal(configData)
	if err != nil {
		return caseData, err
	}
	var config cases.Config
	err = json.Unmarshal(configJSON, &config)
	if err != nil {
		return caseData, err
	}
	caseData.Config = config

	// Convert defaultStepConfig
	defaultStepConfigData, ok := data["defaultStepConfig"].(map[string]any)
	if ok && defaultStepConfigData != nil {
		defaultStepConfigJSON, err := json.Marshal(defaultStepConfigData)
		if err != nil {
			return caseData, err
		}
		var defaultStepConfig cases.StepConfig
		err = json.Unmarshal(defaultStepConfigJSON, &defaultStepConfig)
		if err != nil {
			return caseData, err
		}
		caseData.DefaultStepConfig = &defaultStepConfig
		dDefualtStepConfig = &defaultStepConfig
	} else {
		caseData.DefaultStepConfig = dDefualtStepConfig
	}

	// Convert steps
	stepsData, ok := data["steps"].([]any)
	if !ok {
		return caseData, fmt.Errorf("steps is not a slice")
	}
	var steps cases.Steps = make([]*cases.Step, len(stepsData))
	for i, stepData := range stepsData {
		stepMap, ok := stepData.(map[string]any)
		if !ok {
			return caseData, fmt.Errorf("step is not a map")
		}

		id, ok := stepMap["id"].(string)
		if !ok {
			return caseData, fmt.Errorf("id is not a string")
		}

		stepReportId, ok := stepMap["stepReportId"].(string)

		if !ok {
			return caseData, fmt.Errorf("stepReportId is not a string")
		}

		actionType, ok := stepMap["actionType"].(string)
		if !ok {
			return caseData, fmt.Errorf("actionType is not a string")
		}

		actionData, ok := stepMap["action"].(map[string]interface{})
		if !ok {
			return caseData, fmt.Errorf("action is not a map")
		}
		actionJSON, err := json.Marshal(actionData)
		if err != nil {
			return caseData, err
		}

		act, err := convertJSONToAction(actionType, actionJSON)
		if err != nil {
			return caseData, err
		}

		var config *cases.StepConfig = nil
		if configData, ok := stepMap["config"]; ok && configData != nil {
			if configJSON, err := json.Marshal(configData); err == nil {
				var configD cases.StepConfig
				if err := json.Unmarshal(configJSON, &configD); err != nil {
					return caseData, err
				} else {
					config = &configD
				}
			} else {
				return caseData, err
			}
		}
		steps[i] = &cases.Step{Id: id, StepReportId: stepReportId, ActionType: actionType, Action: act, Config: config}
	}
	caseData.Steps = steps

	return caseData, nil
}

func convertJSONToAction(actionType string, actionJSON []byte) (action.Action, error) {
	var act action.Action
	var err error
	switch actionType {
	case "assert":
		var assert action.Assert
		err = json.Unmarshal(actionJSON, &assert)
		act = &assert
	case "input":
		var input action.Input
		err = json.Unmarshal(actionJSON, &input)
		act = &input
	case "press":
		var press action.Press
		err = json.Unmarshal(actionJSON, &press)
		act = &press
	case "select":
		var selectAct action.Select
		err = json.Unmarshal(actionJSON, &selectAct)
		act = &selectAct
	case "uploadFiles":
		var upload action.UploadFiles
		err = json.Unmarshal(actionJSON, &upload)
		act = &upload
	case "mouseClick":
		var click action.MouseClick
		err = json.Unmarshal(actionJSON, &click)
		act = &click
	case "mouseScroll":
		var scroll action.MouseScroll
		err = json.Unmarshal(actionJSON, &scroll)
		act = &scroll
	case "mouseMove":
		var move action.MouseMove
		err = json.Unmarshal(actionJSON, &move)
		act = &move
	case "mouseDrag":
		var drag action.MouseDrag
		err = json.Unmarshal(actionJSON, &drag)
		act = &drag
	case "mouseHold":
		var hold action.MouseHold
		err = json.Unmarshal(actionJSON, &hold)
		act = &hold
	case "wait":
		var wait action.Wait
		err = json.Unmarshal(actionJSON, &wait)
		act = &wait
	case "codeOperation":
		var codeOperation action.CodeOperation
		err = json.Unmarshal(actionJSON, &codeOperation)
		act = &codeOperation
	case "dataWithdraw":
		var dataWithdraw action.DataWithdraw
		err = json.Unmarshal(actionJSON, &dataWithdraw)
		act = &dataWithdraw
	case "callApi":
		var callApi action.CallApi
		err = json.Unmarshal(actionJSON, &callApi)
		act = &callApi
	case "callDatabase":
		var callDatabase action.CallDatabase
		err = json.Unmarshal(actionJSON, &callDatabase)
		act = &callDatabase
	case "openPage":
		var openPage action.OpenPage
		err = json.Unmarshal(actionJSON, &openPage)
		act = &openPage
	case "closePage":
		var closePage action.ClosePage
		err = json.Unmarshal(actionJSON, &closePage)
		act = &closePage
	case "toggleWindow":
		var toggleWindow action.ToggleWindow
		err = json.Unmarshal(actionJSON, &toggleWindow)
		act = &toggleWindow
	case "forward":
		var forward action.Forward
		err = json.Unmarshal(actionJSON, &forward)
		act = &forward
	case "back":
		var back action.Back
		err = json.Unmarshal(actionJSON, &back)
		act = &back
	case "refresh":
		var refresh action.Refresh
		err = json.Unmarshal(actionJSON, &refresh)
		act = &refresh
	case "setWindowSize":
		var setWindowSize action.SetWindowSize
		err = json.Unmarshal(actionJSON, &setWindowSize)
		act = &setWindowSize
	case "if":
		data := make(map[string]any)
		err = json.Unmarshal(actionJSON, &data)
		if err != nil {
			return nil, err
		}
		var condition cases.Condition
		conditionJSON, err := json.Marshal(data["condition"])
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(conditionJSON, &condition)
		if err != nil {
			return nil, err
		}
		caseJSON, ok := data["child"].(map[string]any)
		if !ok {
			return nil, errors.New("child is not a map")
		}
		c, err := convertJSONToCase(caseJSON)
		if err != nil {
			return nil, err
		}
		act = &cases.If{Condition: condition, Child: &c}
	case "while":
		data := make(map[string]any)
		err = json.Unmarshal(actionJSON, &data)
		if err != nil {
			return nil, err
		}
		var condition cases.Condition
		conditionJSON, err := json.Marshal(data["condition"])
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(conditionJSON, &condition)
		if err != nil {
			return nil, err
		}
		caseJSON, ok := data["child"].(map[string]any)
		if !ok {
			return nil, errors.New("child is not a map")
		}
		c, err := convertJSONToCase(caseJSON)
		if err != nil {
			return nil, err
		}
		act = &cases.While{Condition: condition, Child: &c}
	case "for":
		data := make(map[string]any)
		err = json.Unmarshal(actionJSON, &data)
		if err != nil {
			return nil, err
		}
		caseJSON, ok := data["child"].(map[string]any)
		if !ok {
			return nil, errors.New("child is not a map")
		}
		c, err := convertJSONToCase(caseJSON)
		if err != nil {
			return nil, err
		}
		var tp string
		if tpR, ok := data["type"]; ok {
			if tp1, ok1 := tpR.(string); ok1 {
				tp = tp1
			}
		}
		var times int
		if timesR, ok := data["times"]; ok {
			if timesI, ok1 := timesR.(float64); ok1 {
				times = int(timesI)
			}
		}
		datas := make([]map[string]string, 0)
		if datasR, ok := data["data"]; ok {
			datasJ, err := json.Marshal(&datasR)
			if err != nil {
				return nil, err
			}
			err = json.Unmarshal(datasJ, &datas)
			if err != nil {
				return nil, err
			}
		}
		act = &cases.For{Type: tp, Times: times, Data: datas, Child: &c}
	default:
		return nil, fmt.Errorf("unknown action type: %s", actionType)
	}
	return act, err
}

func caseCallBack(caseId, reportId string, success bool) {
	stepRes := result.StepResult{
		Type:     "case",
		CaseId:   caseId,
		ReportId: reportId,
		Success:  success,
	}
	jsonData, err := json.Marshal(&stepRes)
	log.Printf("回调case, case id: %s", caseId)
	if err != nil {
		log.Printf("call back case err, case id: %s, err: %v", caseId, err)
	}
	req, err := http.NewRequest("POST", configs.CallBackUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("call back case err, case id: %s, err: %v", caseId, err)
	}
	_, err = (&http.Client{}).Do(req)
	if err != nil {
		log.Printf("call back case err, case id: %s, err: %v", caseId, err)
	}
}

package result

type StepResult struct {
	Id           string `json:"id" yaml:"id"`
	StepReportId string `json:"stepReportId" yaml:"stepReportId"`
	CaseId       string `json:"caseId"`
	Type         string `json:"type"` // step or case
	ReportId     string `json:"reportId" yaml:"report_id"`
	Success      bool   `json:"success" yaml:"success"`
	Res          any    `json:"res" yaml:"res"`
	Error        string `json:"error" yaml:"error"`
	Screenshot   string `json:"screenshot" yaml:"screenshot"`
	ConsumeMilli int64  `json:"duration" yaml:"duration"`
}

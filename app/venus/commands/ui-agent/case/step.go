package cases

import (
	"bytes"
	"database/sql/driver"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/result"
	"github.com/playwright-community/playwright-go"
)

type StepConfig struct {
	WaitBeforeMilli int64  `json:"waitBeforeMilli"`
	WaitAfterMilli  int64  `json:"waitAfterMilli"`
	TimeoutMilli    int64  `json:"timeoutMilli"`
	EndsWhenFail    string `json:"endsWhenFail"`   // stop ignore retry
	RetryTimes      int64  `json:"retryTimes"`     // 1 max 5
	ScreenShotTime  string `json:"screenShotTime"` // always, error, no
}

func (d *StepConfig) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *StepConfig) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

type Step struct {
	Id           string        `json:"id"`
	StepReportId string        `json:"stepReportId"`
	Config       *StepConfig   `json:"config"`
	Action       action.Action `json:"action"`
	ActionType   string        `json:"actionType"`
}

func (s *Step) executeActionWithPW(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (res any, err error) {
	defer func() {
		if err1 := recover(); err1 != nil {
			err = fmt.Errorf("%v", err1)
		}
	}()
	if s.Action == nil {
		err = errors.New("操作为空")
	} else {
		act, ok := s.Action.(action.PlaywrightExecutor)
		if !ok {
			err = errors.New("该步骤不能使用playwright执行")
		} else {
			res, err = act.Execute(browser, pages, pageIndex)
		}
	}
	return res, err
}

func (s *Step) executePostOperate(start time.Time, pages *[]playwright.Page, pageIndex *int, e error) (screenshot string, consumeTime int64, err error) {
	defer func() {
		if err1 := recover(); err1 != nil {
			err = fmt.Errorf("%v", err1)
		}
	}()
	// 截图处理
	if s.Config.ScreenShotTime == "always" {
		// 截图
		ss, _ := (*pages)[*pageIndex].Screenshot(playwright.PageScreenshotOptions{FullPage: playwright.Bool(false)})
		screenshot = s.byteToBase64URL(ss)
	} else if s.Config.ScreenShotTime == "error" && e != nil {
		// 错误截图
		ss, _ := (*pages)[*pageIndex].Screenshot(playwright.PageScreenshotOptions{FullPage: playwright.Bool(false)})
		screenshot = s.byteToBase64URL(ss)
	} else if s.Config.ScreenShotTime == "no" {
		// 不截图
	}
	consumeTime = time.Since(start).Milliseconds()
	time.Sleep(time.Duration(s.Config.WaitAfterMilli) * time.Millisecond)
	return screenshot, consumeTime, err
}

func (s *Step) executeWithPW(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (err error) {
	browser.SetDefaultTimeout(float64(s.Config.TimeoutMilli))
	browser.SetDefaultNavigationTimeout(float64(s.Config.TimeoutMilli))
	time.Sleep(time.Duration(s.Config.WaitBeforeMilli) * time.Millisecond)
	start := time.Now()
	var res any
	var screenshot string
	var consumeTime int64
	defer func() {
		if err1 := recover(); err1 != nil {
			err = fmt.Errorf("%v", err1)
		}
		if err == nil {
			s.callBack(true, res, nil, screenshot, consumeTime)
		} else {
			s.callBack(false, res, err, screenshot, consumeTime)
		}
	}()
	res, err = s.executeActionWithPW(browser, pages, pageIndex)
	if err != nil && s.Config.EndsWhenFail == "retry" {
		// 遇到错误重试
		for range int(s.Config.RetryTimes) {
			time.Sleep(time.Duration(s.Config.WaitBeforeMilli) * time.Millisecond)
			res, err = s.executeActionWithPW(browser, pages, pageIndex)
			if err == nil {
				break
			}
		}
	} else if err != nil && s.Config.EndsWhenFail == "ignore" {
		// 遇到错误忽略
	} else if err != nil && s.Config.EndsWhenFail == "stop" {
		// 遇到错误停止
		PanicFlag = true
	}
	screenshot, consumeTime, _ = s.executePostOperate(start, pages, pageIndex, err)
	return err
}

func (s *Step) ExecuteWithPW(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) error {
	err := s.executeWithPW(browser, pages, pageIndex)
	return err
}

func (s *Step) callBack(success bool, res any, err error, ss string, consume int64) {
	stepRes := result.StepResult{
		Id:           s.Id,
		StepReportId: s.StepReportId,
		Type:         "step",
		ReportId:     configs.ReportId,
		Success:      success,
		Res:          res,
		Screenshot:   ss,
		ConsumeMilli: consume,
	}
	if err != nil {
		stepRes.Error = err.Error()
	}
	jsonData, err := json.Marshal(&stepRes)
	log.Printf("回调step, res: %s", jsonData)
	if err != nil {
		log.Printf("call back step err, step id: %s, err: %v", s.Id, err)
	}
	req, err := http.NewRequest("POST", configs.CallBackUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		log.Printf("call back step err, step id: %s, err: %v", s.Id, err)
	}
	_, err = (&http.Client{}).Do(req)
	if err != nil {
		log.Printf("call back step err, step id: %s, err: %v", s.Id, err)
	}
}

func (s *Step) byteToBase64URL(data []byte) string {
	base64String := base64.StdEncoding.EncodeToString(data)
	return fmt.Sprintf("data:image/png;base64,%s", base64String)
}

package element

import "github.com/playwright-community/playwright-go"

type Element struct {
	Name     string   `json:"name"`
	Locator  *Locator `json:"locator"`
	Selector *string  `json:"selector"`
	Index    int      `json:"index"`
}

func (e *Element) GetElementByPW(page playwright.Page) playwright.Locator {
	var tmp playwright.Locator
	if e.Locator != nil {
		locator := e.Locator
		switch locator.Type {
		case "role":
			tmp = page.GetByRole(playwright.AriaR<PERSON>(locator.Key), playwright.PageGetByRoleOptions{
				Name: locator.Value,
			})
		case "text":
			tmp = page.GetByText(locator.Value)
		case "label":
			tmp = page.GetByLabel(locator.Value)
		case "placeholder":
			tmp = page.GetByPlaceholder(locator.Value)
		case "alt":
			tmp = page.GetByAltText(locator.Value)
		case "title":
			tmp = page.GetByTitle(locator.Value)
		}
	} else if e.Selector != nil {
		tmp = page.Locator(*e.Selector)
	}
	if e.Index < 0 {
		// 当下标<0时，返回最后一个元素
		return tmp.Last()
	}
	return tmp.Nth(e.Index)
}

type ElementLocator struct {
	Type          string         `json:"type"` // selector or locator
	Selector      *Selector      `json:"selector"`
	Locator       *Locator       `json:"locator"`
	IframeLocator *IframeLocator `json:"iframeLocator"`
}

type Selector struct {
	Value string `json:"value"`
	Index int    `json:"index"`
}

type Locator struct {
	Type  string `json:"type"` // role,text,label,placeholder,alt,title
	Key   string `json:"key"`
	Value string `json:"value"`
	Index int    `json:"index"`
}

type IframeLocator struct {
	Type  string `json:"type"` // name,index,url
	Value string `json:"value"`
}

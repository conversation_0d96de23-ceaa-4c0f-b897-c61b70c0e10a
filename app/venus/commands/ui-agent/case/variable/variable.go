package variable

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
)

var stepDataMap map[string]map[string]string = make(map[string]map[string]string)

var currentSteps = make([]string, 0)

func RegisterDatasource(id string, data map[string]string) {
	stepDataMap[id] = data
	currentSteps = append(currentSteps, id)
}

func DeregisterDatasource(id string) {
	delete(stepDataMap, id)
	newCurrentSteps := make([]string, len(currentSteps)-1)
	for _, cur := range currentSteps {
		if cur == id {
			continue
		}
		newCurrentSteps = append(newCurrentSteps, cur)
	}
	currentSteps = newCurrentSteps
}

func RegisterWithdrawVariable(id string, name string, value string) {
	stepDataMap[id] = map[string]string{
		name: value,
	}
	currentSteps = append(currentSteps, id)
}

var globalAndEnvVarMap map[string]string = make(map[string]string)

func Init(variableurl string, env string) error {
	vars, err := getVars(variableurl, env)
	if err != nil {
		return err
	}
	for _, v := range vars {
		if v.VarType == "global" {
			globalAndEnvVarMap[v.Name] = v.Value
		}
	}
	for _, v := range vars {
		if v.VarType == "env" {
			globalAndEnvVarMap[v.Name] = v.Value
		}
	}
	return nil
}

type variable struct {
	Name    string `json:"name"`
	Value   string `json:"value"`
	VarType string `json:"var_type"`
}

func getVars(variableurl string, env string) ([]variable, error) {
	// 发起 GET 请求
	resp, err := http.Get(variableurl + fmt.Sprintf("?env=%s", url.QueryEscape(env)))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// 读取响应内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	vars := make([]variable, 0)
	err = json.Unmarshal(data, &vars)
	if err != nil {
		return nil, err
	}

	return vars, nil
}

func GetVariables() map[string]string {
	res := make(map[string]string)
	for k, v := range globalAndEnvVarMap {
		res[k] = v
	}
	for _, id := range currentSteps {
		for k, v := range stepDataMap[id] {
			res[k] = v
		}
	}
	return res
}

func DealWithVariableAndMock(input string) (string, error) {
	output := input
	for k, v := range GetVariables() {
		output = strings.ReplaceAll(output, `{{`+k+`}}`, v)
	}
	// return output, nil
	res, err := getMockData(output)
	if err != nil {
		return "", err
	}
	return res, nil
}

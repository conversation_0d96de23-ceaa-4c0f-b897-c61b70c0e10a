package action

import (
	"errors"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
	optenum "git.zhonganinfo.com/zainfo/cube-mantis/pkg/enum/operation_enum"
	"github.com/playwright-community/playwright-go"
)

type Assert struct {
	Type     string          `json:"type"` // element, text, variable, page
	Element  *ElementAssert  `json:"element"`
	Text     *TextAssert     `json:"text"`
	Page     *PageAssert     `json:"page"`
	Variable *VariableAssert `json:"variable"`
}

type ElementAssert struct {
	Type     string           `json:"type"` // exist, notexist, attr, text, tagName, value, count
	Element  *element.Element `json:"element"`
	AttrKey  string           `json:"attrKey"`
	Operator string           `json:"operator"`
	Expected string           `json:"expected"`
}

func (a *ElementAssert) assert(page playwright.Page) error {
	if a.Element == nil {
		return errors.New("未选择目标元素")
	}
	locator := a.Element.GetElementByPW(page)
	switch a.Type {
	case "exist":
		if err := locator.WaitFor(playwright.LocatorWaitForOptions{
			State: playwright.WaitForSelectorStateVisible,
		}); err != nil {
			return errors.New("元素不可见, 期望可见")
		}
	case "notexist":
		if locator.WaitFor(playwright.LocatorWaitForOptions{
			State: playwright.WaitForSelectorStateVisible,
		}) == nil {
			return errors.New("元素可见, 期望不可见")
		}
	case "attr":
		attr, err := locator.GetAttribute(a.AttrKey)
		if err != nil {
			return fmt.Errorf("未提取到元素属性, err=%s", err.Error())
		}
		f := optenum.GetOperationByName(a.Operator).Operate(attr, a.Expected)
		if !f {
			return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, attr)
		}
	case "text":
		text, err := locator.InnerText()
		if err != nil {
			return fmt.Errorf("未提取到元素文本, err=%s", err.Error())
		}
		f := optenum.GetOperationByName(a.Operator).Operate(text, a.Expected)
		if !f {
			return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, text)
		}
	case "value":
		value, err := locator.InputValue()
		if err != nil {
			return fmt.Errorf("未提取到元素输入值, err=%s", err.Error())
		}
		f := optenum.GetOperationByName(a.Operator).Operate(value, a.Expected)
		if !f {
			return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, value)
		}
	case "tagName":
		value, err := locator.Evaluate("el => el.tagName", "")
		if err != nil {
			return fmt.Errorf("未获取到标签名称, err=%s", err.Error())
		}
		f := optenum.GetOperationByName(a.Operator).Operate(fmt.Sprintf("%v", value), a.Expected)
		if !f {
			return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, value)
		}
	case "count":
		count, err := locator.Count()
		if err != nil {
			return err
		}
		f := optenum.GetOperationByName(a.Operator).Operate(fmt.Sprintf("%d", count), a.Expected)
		if !f {
			return fmt.Errorf("断言失败, 期望:%s, 实际值:%d", a.Expected, count)
		}
	}
	return nil
}

type TextAssert struct {
	Type  string   `json:"type"`
	Texts []string `json:"texts"`
}

func (a *TextAssert) assert(page playwright.Page) error {
	t, err := page.Locator("body").InnerText()
	if err != nil {
		return fmt.Errorf("获取page内文本信息错误, err=%s", err.Error())
	}
	if a.Type == "exist" {
		for _, text := range a.Texts {
			if !strings.Contains(t, text) {
				return fmt.Errorf("断言失败, %s在页面中不存在", text)
			}
		}
	} else {
		for _, text := range a.Texts {
			if strings.Contains(t, text) {
				return fmt.Errorf("断言失败, %s在页面中存在", text)
			}
		}
	}
	return nil
}

type PageAssert struct {
	Type     string `json:"type"` // url, title
	Operator string `json:"operator"`
	Expected string `json:"expected"`
}

func (a *PageAssert) assert(page playwright.Page) error {
	actual := ""
	if a.Type == "url" {
		actual = page.URL()
	} else if a.Type == "title" {
		title, err := page.Title()
		if err != nil {
			return fmt.Errorf("获取页面标题错误, err=%s", err.Error())
		}
		actual = title
	}
	f := optenum.GetOperationByName(a.Operator).Operate(actual, a.Expected)
	if !f {
		return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, actual)
	}
	return nil
}

type VariableAssert struct {
	Key      string `json:"key"`
	Operator string `json:"operator"`
	Expected string `json:"expected"`
}

func (a *VariableAssert) assert() error {
	actual, ok := variable.GetVariables()[a.Key]
	if !ok {
		return fmt.Errorf("不存在名为%s的变量", a.Key)
	}
	f := optenum.GetOperationByName(a.Operator).Operate(actual, a.Expected)
	if !f {
		return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", a.Expected, actual)
	}
	return nil
}

func (a *Assert) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	switch a.Type {
	case "element":
		return nil, a.Element.assert((*pages)[*pageIndex])
	case "text":
		return nil, a.Text.assert((*pages)[*pageIndex])
	case "page":
		return nil, a.Page.assert((*pages)[*pageIndex])
	case "variable":
		return nil, a.Variable.assert()
	}
	return nil, nil
}

func (a *Assert) Name() string {
	return "assert"
}

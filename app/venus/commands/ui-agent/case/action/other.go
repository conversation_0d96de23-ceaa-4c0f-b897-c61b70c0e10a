package action

import (
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"github.com/playwright-community/playwright-go"
)

type Wait struct {
	Type      string           `json:"type"` // time, attached, detached, visible, hidden, editable, noneditable
	WaitMilli int64            `json:"waitMilli"`
	Element   *element.Element `json:"element"`
}

func (a *Wait) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	switch a.Type {
	case "time":
		time.Sleep(time.Duration(a.WaitMilli) * time.Millisecond)
	default:
		if a.Element == nil {
			err = errors.New("未选择目标元素")
		}
		locator := a.Element.GetElementByPW((*pages)[*pageIndex])
		switch a.Type {
		case "attached":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateAttached,
			})
		case "detached":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateDetached,
			})
		case "visible":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateVisible,
			})
		case "hidden":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateHidden,
			})
		case "editable":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateVisible,
			})
		case "noneditable":
			err = locator.WaitFor(playwright.LocatorWaitForOptions{
				State: playwright.WaitForSelectorStateHidden,
			})
		}
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *Wait) Name() string {
	return "wait"
}

type CodeOperation struct {
	Type          string           `json:"type"` // javascript
	Element       *element.Element `json:"element"`
	OperationType string           `json:"operationType"` // element | page
	CodeText      string           `json:"codeText"`
}

func (a *CodeOperation) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	code, err := variable.DealWithVariableAndMock(a.CodeText)
	if err != nil {
		return nil, err
	}
	if a.Type == "javascript" {
		if a.OperationType == "element" {
			if a.Element == nil {
				err = errors.New("未选择目标元素")
			}
			locator := a.Element.GetElementByPW((*pages)[*pageIndex])
			locator.Evaluate(code, nil)
		} else {
			(*pages)[*pageIndex].Evaluate(code, nil)
		}
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *CodeOperation) Name() string {
	return "codeOperation"
}

type DataWithdraw struct {
	VariableName string           `json:"name"`
	Element      *WithdrawElement `json:"element"`
	Webpage      *WithdrawWebpage `json:"webpage"`
	Regex        string           `json:"regex"`
}

type WithdrawElement struct {
	Type          string           `json:"type"` // text, value, attr, source
	Element       *element.Element `json:"element"`
	AttributeName string           `json:"attributeName"`
}

func (a *WithdrawElement) execute(page playwright.Page) (result string, err error) {
	if a.Element == nil {
		err = errors.New("未选择目标元素")
		return "", err
	}
	locator := a.Element.GetElementByPW(page)
	switch a.Type {
	case "text":
		res, err := locator.TextContent()
		if err != nil {
			return "", err
		}
		return res, err
	case "value":
		res, err := locator.InputValue()
		if err != nil {
			return "", err
		}
		return res, err
	case "attr":
		res, err := locator.GetAttribute(a.AttributeName)
		if err != nil {
			return "", err
		}
		return res, err
	case "source":
		res, err := locator.InnerHTML()
		if err != nil {
			return "", err
		}
		return res, err
	}
	return "", err
}

type WithdrawWebpage struct {
	Type      string `json:"type"` // url, title, raw, text, cookie
	CookieKey string `json:"cookieKey"`
}

func (a *WithdrawWebpage) execute(page playwright.Page) (result string, err error) {
	switch a.Type {
	case "url":
		return page.URL(), nil
	case "title":
		return page.Title()
	case "raw":
		return page.Content()
	case "text":
		return page.Locator("body").TextContent()
	case "cookie":
		cookies, err := page.Context().Cookies(page.URL())
		if err != nil {
			return "", err
		}
		for _, cookie := range cookies {
			if cookie.Name == a.CookieKey {
				return cookie.Value, nil
			}
		}
	}
	return "", err
}

func (a *DataWithdraw) Name() string {
	return "dataWithdraw"
}

func (a *DataWithdraw) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	val := ""
	if a.Element != nil {
		v, err := a.Element.execute((*pages)[*pageIndex])
		if err != nil {
			return nil, err
		}
		val = v
	} else if a.Webpage != nil {
		v, err := a.Webpage.execute((*pages)[*pageIndex])
		if err != nil {
			return nil, err
		}
		val = v
	}
	if a.Regex != "" {
		re, err := regexp.Compile(a.Regex)
		if err != nil {
			return nil, fmt.Errorf("无效的正则表达式: %v", err)
		}
		if re.NumSubexp() > 0 {
			return nil, fmt.Errorf("正则表达式不允许包含捕获组")
		}
		// 查找第一个匹配项及其捕获组
		matches := re.FindStringSubmatch(val)
		if matches == nil {
			return nil, fmt.Errorf("正则表达式没有提取到任何内容")
		}
		val = matches[0]
	}
	variable.RegisterWithdrawVariable(strconv.FormatInt(snowflake.GenSnowFlakeId(), 10), a.VariableName, val)
	return nil, nil
}

type CallApi struct{}

func (a *CallApi) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	return nil, nil
}

func (a *CallApi) Name() string {
	return "callApi"
}

type CallDatabase struct{}

func (a *CallDatabase) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	return nil, nil
}

func (a *CallDatabase) Name() string {
	return "callDatabase"
}

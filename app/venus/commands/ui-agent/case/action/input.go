package action

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
	"github.com/playwright-community/playwright-go"
)

type Input struct {
	Element         *element.Element `json:"element"`
	InputContent    string           `json:"inputContent"`
	IsAppendContent bool             `json:"isAppendContent"`
}

func (a *Input) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Element == nil {
		err = errors.New("未选择目标元素")
		return nil, err
	}
	locator := a.Element.GetElementByPW((*pages)[*pageIndex])
	input, err := variable.DealWithVariableAndMock(a.InputContent)
	if err != nil {
		return nil, err
	}
	if a.IsAppendContent {
		content, err1 := locator.InputValue()
		if err1 != nil {
			err = err1
			return nil, err
		}
		content += input
		err = locator.Fill(content)
	} else {
		err = locator.Fill(input)
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *Input) Name() string {
	return "input"
}

type Press struct {
	Key   string   `json:"key"`
	Delay *float64 `json:"delay"`
}

func (a *Press) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Key == "" {
		err = errors.New("未选择按键")
	} else {
		err = (*pages)[*pageIndex].Keyboard().Press(a.Key, playwright.KeyboardPressOptions{Delay: a.Delay})
	}
	return nil, err
}

func (a *Press) Name() string {
	return "press"
}

type Select struct {
	Type    string           `json:"type"` // text, value, index
	Element *element.Element `json:"element"`
	Text    []string         `json:"text"`
	Value   []string         `json:"value"`
	Index   []int            `json:"index"`
}

func (a *Select) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Element == nil {
		err = errors.New("未选择目标元素")
	}
	locator := a.Element.GetElementByPW((*pages)[*pageIndex])
	switch a.Type {
	case "text":
		result, err = locator.SelectOption(playwright.SelectOptionValues{Labels: playwright.StringSlice(a.Text...)})
	case "value":
		result, err = locator.SelectOption(playwright.SelectOptionValues{Values: playwright.StringSlice(a.Value...)})
	case "index":
		result, err = locator.SelectOption(playwright.SelectOptionValues{Indexes: playwright.IntSlice(a.Index...)})
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return result, err
}

func (a *Select) Name() string {
	return "select"
}

type UploadFiles struct {
	Element *element.Element `json:"element"`
	Files   []struct {
		FileName string `json:"fileName"`
		MimeType string `json:"mimeType"`
		OssKey   string `json:"ossKey"`
	} `json:"files"`
}

func (a *UploadFiles) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Element == nil {
		err = errors.New("未选择目标元素")
		return result, err
	}
	locator := a.Element.GetElementByPW((*pages)[*pageIndex])
	inputFiles := make([]playwright.InputFile, 0)
	for _, file := range a.Files {
		f, err1 := getFileByURL(configs.FileDownloadUrl + "?obj_key=" + url.QueryEscape(file.OssKey))
		if err1 != nil {
			err = err1
			return nil, err
		}
		inputFiles = append(inputFiles, playwright.InputFile{
			Name:     file.FileName,
			MimeType: file.MimeType,
			Buffer:   f,
		})
	}
	err = locator.SetInputFiles(inputFiles)
	*pages = (*pages)[*pageIndex].Context().Pages()
	return result, err
}

func (a *UploadFiles) Name() string {
	return "uploadFiles"
}

func getFileByURL(url string) ([]byte, error) {
	// 发起 GET 请求
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	// 读取响应内容
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return data, nil
}

package action

import (
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/element"
	"github.com/playwright-community/playwright-go"
)

type MouseClick struct {
	Type          string           `json:"type"`   // single, double
	Button        string           `json:"button"` // left, right, middle
	HoldMilli     uint64           `json:"holdMilli"`
	Element       *element.Element `json:"element"`
	ClickPosition *ClickPosition   `json:"clickPosition"`
}

type ClickPosition struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

func (a *MouseClick) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	hold := float64(1)
	if a.HoldMilli > 1 {
		hold = float64(a.HoldMilli)
	}
	if a.Element == nil {
		err = errors.New("未选择目标元素")
	}
	if a.Element != nil { // 点击元素
		locator := a.Element.GetElementByPW((*pages)[*pageIndex])
		switch a.Type {
		case "single":
			switch a.Button {
			case "left":
				err = locator.Click(playwright.LocatorClickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
			case "right":
				err = locator.Click(playwright.LocatorClickOptions{Button: playwright.MouseButtonRight, Delay: playwright.Float(hold)})
			case "middle":
				err = locator.Click(playwright.LocatorClickOptions{Button: playwright.MouseButtonMiddle, Delay: playwright.Float(hold)})
			}
		case "double":
			switch a.Button {
			case "left":
				err = locator.Dblclick(playwright.LocatorDblclickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
			case "right":
				err = locator.Dblclick(playwright.LocatorDblclickOptions{Button: playwright.MouseButtonRight, Delay: playwright.Float(hold)})
			case "middle":
				err = locator.Dblclick(playwright.LocatorDblclickOptions{Button: playwright.MouseButtonMiddle, Delay: playwright.Float(hold)})
			}
		}
	} else if a.ClickPosition != nil {
		switch a.Type {
		case "single":
			switch a.Button {
			case "left":
				err = (*pages)[*pageIndex].Mouse().Click(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseClickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
			case "right":
				err = (*pages)[*pageIndex].Mouse().Click(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseClickOptions{Button: playwright.MouseButtonRight, Delay: playwright.Float(hold)})
			case "middle":
				err = (*pages)[*pageIndex].Mouse().Click(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseClickOptions{Button: playwright.MouseButtonMiddle, Delay: playwright.Float(hold)})
			}
		case "double":
			switch a.Button {
			case "left":
				err = (*pages)[*pageIndex].Mouse().Dblclick(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseDblclickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
			case "right":
				err = (*pages)[*pageIndex].Mouse().Dblclick(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseDblclickOptions{Button: playwright.MouseButtonRight, Delay: playwright.Float(hold)})
			case "middle":
				err = (*pages)[*pageIndex].Mouse().Dblclick(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseDblclickOptions{Button: playwright.MouseButtonMiddle, Delay: playwright.Float(hold)})
			}
		}
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *MouseClick) Name() string {
	return "mouseClick"
}

type MouseScroll struct {
	Type          string           `json:"type"`       // element, page
	ScrollType    string           `json:"scrollType"` // position, element
	Element       *element.Element `json:"element"`
	Position      *ScrollPosition  // 移动的位置
	ScrollElement *element.Element `json:"scrollElement"` //  滚动到出现的元素
}

type ScrollPosition struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

func (a *MouseScroll) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	wheel := func() error {
		if a.ScrollType == "position" {
			err = (*pages)[*pageIndex].Mouse().Wheel(a.Position.X, a.Position.Y)
			return err
		} else if a.ScrollType == "element" {
			if a.ScrollElement == nil {
				err = errors.New("未选择目标元素")
				return err
			}
			locatar := a.ScrollElement.GetElementByPW((*pages)[*pageIndex])
			err = locatar.ScrollIntoViewIfNeeded()
			return err
		}
		return nil
	}
	if a.Type == "element" {
		if a.Element == nil {
			err = errors.New("未选择目标元素")
		}
		locator := a.Element.GetElementByPW((*pages)[*pageIndex])
		err = locator.Hover()
		err = wheel()
	} else if a.Type == "page" {
		err = wheel()
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *MouseScroll) Name() string {
	return "mouseScroll"
}

type MouseMove struct {
	Type                string                `json:"type"` // element, pos
	Element             *element.Element      `json:"element"`
	EndPointCoordinates *DragPointCoordinates `json:"endPointCoordinates"`
}

func (a *MouseMove) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	// 移动到元素
	if a.Type == "element" {
		if a.Element == nil {
			err = errors.New("未选择目标元素")
			return nil, err
		}
		locator := a.Element.GetElementByPW((*pages)[*pageIndex])
		err = locator.Hover()
		return nil, err
	}
	// 移动到位置
	if a.EndPointCoordinates != nil {
		err = (*pages)[*pageIndex].Mouse().Move(a.EndPointCoordinates.X, a.EndPointCoordinates.Y)
	} else {
		err = errors.New("未选择鼠标移动终点坐标")
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *MouseMove) Name() string {
	return "mouseMove"
}

type MouseDrag struct {
	Type                string                `json:"type"` // element, pos
	Element             *element.Element      `json:"element"`
	TargetElement       *element.Element      `json:"targetElement"`
	EndPointCoordinates *DragPointCoordinates `json:"endPointCoordinates"`
}

func (a *MouseDrag) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Element == nil {
		err = errors.New("未选择拖动元素")
	}
	locator := a.Element.GetElementByPW((*pages)[*pageIndex])
	if a.Type == "element" {
		if a.TargetElement == nil {
			err = errors.New("未选择的目标元素")
			return nil, err
		}
		err = locator.DragTo(a.TargetElement.GetElementByPW((*pages)[*pageIndex]))
	} else if a.Type == "pos" {
		if a.EndPointCoordinates == nil {
			err = errors.New("未选择终点坐标")
			return nil, err
		}
		rect, err1 := locator.BoundingBox()
		if err1 != nil {
			err = err1
		}
		err = (*pages)[*pageIndex].Mouse().Move((rect.X+rect.Width)/2, (rect.Y+rect.Height)/2)
		err = (*pages)[*pageIndex].Mouse().Down()
		err = (*pages)[*pageIndex].Mouse().Move(a.EndPointCoordinates.X, a.EndPointCoordinates.Y)
		err = (*pages)[*pageIndex].Mouse().Up()
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *MouseDrag) Name() string {
	return "mouseDrag"
}

type DragPointCoordinates struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

type MouseHold struct {
	HoldMilli     uint64           `json:"holdMilli"`
	Element       *element.Element `json:"element"`
	ClickPosition *ClickPosition   `json:"clickPosition"`
}

func (a *MouseHold) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	hold := float64(1)
	if a.HoldMilli > 1 {
		hold = float64(a.HoldMilli)
	}
	if a.Element != nil { // 点击元素
		locator := a.Element.GetElementByPW((*pages)[*pageIndex])
		locator.Click(playwright.LocatorClickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
	} else if a.ClickPosition != nil {
		(*pages)[*pageIndex].Mouse().Click(a.ClickPosition.X, a.ClickPosition.Y, playwright.MouseClickOptions{Button: playwright.MouseButtonLeft, Delay: playwright.Float(hold)})
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *MouseHold) Name() string {
	return "mouseHold"
}

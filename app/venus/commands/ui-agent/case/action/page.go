package action

import (
	"errors"
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
	"github.com/playwright-community/playwright-go"
)

type OpenPage struct {
	Url       string `json:"url"`
	IsNewPage bool   `json:"isNewPage"`
}

func (a *OpenPage) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	urlR, err := variable.DealWithVariableAndMock(a.Url)
	if err != nil {
		return nil, err
	}
	if a.IsNewPage {
		page, err := browser.NewPage()
		if err != nil {
			return nil, err
		}
		_, err = page.Goto(urlR, playwright.PageGotoOptions{
			WaitUntil: playwright.WaitUntilStateDomcontentloaded,
		})
		if err != nil {
			return nil, err
		}
		*pages = append(*pages, page)
		*pageIndex = len(*pages) - 1
		return nil, nil
	} else {
		_, err := (*pages)[*pageIndex].<PERSON><PERSON>(urlR, playwright.PageGotoOptions{
			WaitUntil: playwright.WaitUntilStateDomcontentloaded,
		})
		return nil, err
	}
}

func (a *OpenPage) Name() string {
	return "openPage"
}

type ClosePage struct {
	Type        string `json:"type"` // first, pre, post, last, customIndex, all
	CustomIndex int    `json:"customIndex"`
}

func (a *ClosePage) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if len(*pages) == 0 {
		return nil, errors.New("当前未打开页面, 无法关闭页面")
	}
	switch a.Type {
	case "first":
		err = (*pages)[0].Close()
	case "pre":
		if *pageIndex-1 < 0 {
			return nil, errors.New("当前处于第一个页面, 无法关闭前一个页面")
		}
		err = (*pages)[*pageIndex-1].Close()
	case "post":
		if *pageIndex+1 >= len(*pages) {
			return nil, errors.New("当前处于最后一个页面, 无法关闭后一个页面")
		}
		err = (*pages)[*pageIndex+1].Close()
	case "last":
		err = (*pages)[len(*pages)-1].Close()
	case "all":
		errs := make([]error, 0)
		errIndex := make([]int, 0)
		for i, p := range *pages {
			errs = append(errs, p.Close())
			errIndex = append(errIndex, i)
		}
		if len(errs) != 0 {
			errMsg := ""
			for i, err := range errs {
				errMsg += fmt.Sprintf("page %d close error: %s", errIndex[i]+1, err.Error())
			}
			err = errors.New(errMsg)
		}
	case "customIndex":
		if a.CustomIndex < 1 {
			return nil, fmt.Errorf("无法关闭第%d个页面", a.CustomIndex)
		}
		if a.CustomIndex > len(*pages) {
			return nil, fmt.Errorf("当前共有%d个页面, 无法关闭第%d个页面", len(*pages), a.CustomIndex)
		}
		err = (*pages)[a.CustomIndex-1].Close()
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, err
}

func (a *ClosePage) Name() string {
	return "closePage"
}

type ToggleWindow struct {
	Type        string `json:"type"` // first, pre, post, last, customIndex
	CustomIndex int    `json:"customIndex"`
}

func (a *ToggleWindow) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if len(*pages) == 0 {
		return nil, errors.New("当前未打开页面, 无法切换页面")
	}
	switch a.Type {
	case "first":
		*pageIndex = 0
	case "pre":
		if *pageIndex-1 < 0 {
			return nil, errors.New("当前处于第一个页面, 无法切换到前一个页面")
		}
		*pageIndex = *pageIndex - 1
	case "post":
		if *pageIndex+1 >= len(*pages) {
			return nil, errors.New("当前处于最后一个页面, 无法切换到后一个页面")
		}
		*pageIndex = *pageIndex + 1
	case "last":
		*pageIndex = len(*pages) - 1
	case "customIndex":
		if a.CustomIndex < 1 {
			return nil, fmt.Errorf("无法切换到第%d个页面", a.CustomIndex)
		}
		if a.CustomIndex > len(*pages) {
			return nil, fmt.Errorf("当前共有%d个页面, 无法切换到第%d个页面", len(*pages), a.CustomIndex)
		}
		*pageIndex = a.CustomIndex - 1
	default:
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, nil
}

func (a *ToggleWindow) Name() string {
	return "toggleWindow"
}

type Forward struct{}

func (a *Forward) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	_, err = (*pages)[*pageIndex].GoForward(playwright.PageGoForwardOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return nil, err
	}
	*pages = (*pages)[*pageIndex].Context().Pages()
	return nil, nil
}

func (a *Forward) Name() string {
	return "forward"
}

type Back struct{}

func (a *Back) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	_, err = (*pages)[*pageIndex].GoBack(playwright.PageGoBackOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return nil, err
	} else {
		*pages = (*pages)[*pageIndex].Context().Pages()
		return nil, nil
	}
}

func (a *Back) Name() string {
	return "back"
}

type Refresh struct{}

func (a *Refresh) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	_, err = (*pages)[*pageIndex].Reload(playwright.PageReloadOptions{
		WaitUntil: playwright.WaitUntilStateDomcontentloaded,
	})
	if err != nil {
		return nil, err
	} else {
		*pages = (*pages)[*pageIndex].Context().Pages()
		return nil, nil
	}
}

func (a *Refresh) Name() string {
	return "refresh"
}

type SetWindowSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

func (a *SetWindowSize) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	err = (*pages)[*pageIndex].SetViewportSize(a.Width, a.Height)
	if err != nil {
		return nil, err
	} else {
		*pages = (*pages)[*pageIndex].Context().Pages()
		return nil, nil
	}
}

func (a *SetWindowSize) Name() string {
	return "setWindowSize"
}

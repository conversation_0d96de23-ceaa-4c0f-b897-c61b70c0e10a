package cases

import (
	"fmt"
	"log"
	"strconv"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/action"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/commands/ui-agent/case/variable"
	optenum "git.zhonganinfo.com/zainfo/cube-mantis/pkg/enum/operation_enum"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"github.com/playwright-community/playwright-go"
)

var PanicFlag = false

var defaultStepConfig *StepConfig = nil

type Case struct {
	Id                string      `json:"id"`
	Config            Config      `json:"config"`
	DefaultStepConfig *StepConfig `json:"defaultStepConfig"`
	Steps             Steps       `json:"steps"`
}

func (s *Case) ExecuteWithPW(browserCtx playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) error {
	defaultStepConfig = s.DefaultStepConfig
	init := func() {
		if browserCtx == nil {
			var browser playwright.Browser
			pw, err := playwright.Run()
			if err != nil {
				log.Panicf("could not start playwright: %v", err)
			}
			switch s.Config.BrowserType {
			case "chrome":
				browser, err = pw.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
					Args: []string{"--window-size=1920,1080"},
				})
			case "firefox":
				browser, err = pw.Firefox.Launch()
			case "edge":
				browser, err = pw.Chromium.Launch()
			case "safari":
				browser, err = pw.WebKit.Launch()
			}
			if err != nil {
				log.Panicf("could not launch browser: %s", err.Error())
			}
			ctxOpt := playwright.BrowserNewContextOptions{
				Locale: playwright.String("zh-CN"),
			}
			if configs.IgnoreHttpsErrors {
				ctxOpt.IgnoreHttpsErrors = playwright.Bool(true)
			}
			if s.Config.IsMoblie && s.Config.Viewport != nil {
				ctxOpt.Viewport = &playwright.Size{
					Width:  s.Config.Viewport.Width,
					Height: s.Config.Viewport.Height,
				}
				ctxOpt.DeviceScaleFactor = playwright.Float(1)
				ctxOpt.IsMobile = playwright.Bool(true)
				ctxOpt.UserAgent = playwright.String(
					"Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36",
				)
			} else {
			}
			browserCtx, err = browser.NewContext(ctxOpt)
			if err != nil {
				log.Panicf("error creating context, %s", err.Error())
			}
		}
		if pages == nil {
			p := make([]playwright.Page, 0)
			pages = &p
		}
		if pageIndex == nil {
			idx := 0
			pageIndex = &idx
		}
		configs.IgnoreHttpsErrors = s.Config.IgnoreHttpsErrors
	}
	init()
	if s.Steps != nil {
		err := s.Steps.ExecuteWithPW(browserCtx, pages, pageIndex, s.DefaultStepConfig)
		if err != nil {
			return err
		}
	}
	return nil
}

type Steps []*Step

func (steps Steps) ExecuteWithPW(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int, defaultCfg *StepConfig) error {
	var err error
	for _, step := range steps {
		if PanicFlag {
			return err
		} else {
			if step.Config == nil {
				step.Config = defaultCfg
			}
			errc := step.ExecuteWithPW(browser, pages, pageIndex)
			if errc != nil {
				err = errc
			}
		}
	}
	return err
}

type Config struct {
	BrowserType       string `json:"browserType"`       // chrome, firefox, edge, safari
	IgnoreHttpsErrors bool   `json:"ignoreHttpsErrors"` // 是否忽略https错误
	IsMoblie          bool   `json:"isMoble"`
	Viewport          *struct {
		Width  int `json:"width"`
		Height int `json:"height"`
	} `json:"viewport"`
}

type OperateCondition struct {
	Actual   string `json:"actual"`
	Operator string `json:"operator"`
	Expected string `json:"expected"`
}

func (c *OperateCondition) Assert() error {
	actual, err := variable.DealWithVariableAndMock(c.Actual)
	if err != nil {
		return err
	}
	expected, err := variable.DealWithVariableAndMock(c.Expected)
	if err != nil {
		return err
	}
	f := optenum.GetOperationByName(c.Operator).Operate(actual, expected)
	if !f {
		return fmt.Errorf("断言失败, 期望:%s, 实际值:%s", expected, actual)
	}
	return nil
}

type Condition struct {
	Relation   string              `json:"relation"` // and, or
	Asserts    []*action.Assert    `json:"asserts"`
	Conditions []*OperateCondition `json:"conditions"`
}

func (c *Condition) Assert(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) bool {
	if c.Relation == "and" {
		res := true
		for _, a := range c.Asserts {
			_, err := a.Execute(browser, pages, pageIndex)
			res = res && (err == nil)
		}
		for _, a := range c.Conditions {
			err := a.Assert()
			res = res && (err == nil)
		}
		return res
	} else {
		res := false
		for _, a := range c.Asserts {
			_, err := a.Execute(browser, pages, pageIndex)
			res = res || (err == nil)
		}
		for _, a := range c.Conditions {
			err := a.Assert()
			res = res || (err == nil)
		}
		return res
	}
}

type If struct {
	Condition Condition `json:"condition"`
	Child     *Case     `json:"child"`
}

func (a *If) Name() string {
	return "if"
}

func (a *If) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Child.DefaultStepConfig == nil {
		a.Child.DefaultStepConfig = defaultStepConfig
	}
	if a.Condition.Assert(browser, pages, pageIndex) {
		if PanicFlag {
			return
		} else {
			a.Child.ExecuteWithPW(browser, pages, pageIndex)
		}
	}
	return nil, nil
}

type While struct {
	Condition Condition `json:"condition"`
	MaxTimes  int       `json:"maxTimes"`
	Child     *Case     `json:"child"`
}

func (a *While) Name() string {
	return "while"
}

func (a *While) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Child.DefaultStepConfig == nil {
		a.Child.DefaultStepConfig = defaultStepConfig
	}
	i := 0
	for a.Condition.Assert(browser, pages, pageIndex) {
		if i >= a.MaxTimes {
			break
		}
		if PanicFlag {
			return
		} else {
			a.Child.ExecuteWithPW(browser, pages, pageIndex)
			i++
		}
	}
	return nil, nil
}

type For struct {
	Times int                 `json:"times"`
	Child *Case               `json:"child"`
	Data  []map[string]string `json:"data"`
	Type  string              `json:"type"` // times or data
}

func (a *For) Name() string {
	return "for"
}

func (a *For) Execute(browser playwright.BrowserContext, pages *[]playwright.Page, pageIndex *int) (result any, err error) {
	if a.Child.DefaultStepConfig == nil {
		a.Child.DefaultStepConfig = defaultStepConfig
	}
	if a.Type == "times" {
		for range a.Times {
			if PanicFlag {
				return
			} else {
				a.Child.ExecuteWithPW(browser, pages, pageIndex)
			}
		}
	} else if a.Type == "data" {
		id := strconv.FormatInt(snowflake.GenSnowFlakeId(), 10)
		for _, data := range a.Data {
			variable.RegisterDatasource(id, data)
			if PanicFlag {
				return
			} else {
				a.Child.ExecuteWithPW(browser, pages, pageIndex)
			}
			variable.DeregisterDatasource(id)
		}
	}
	return nil, nil
}

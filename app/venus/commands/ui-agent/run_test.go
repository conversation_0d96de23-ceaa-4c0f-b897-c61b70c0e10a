package uiagent

import (
	"log"
	"os"
	"testing"
)

func Test_caseExecuteWithPW(t *testing.T) {
	log.SetOutput(os.Stdout)
	Run(
		// string(cJSON),
		`[{"id":"bje2of2t4a9u1w6","config":{"browserType":"chrome","ignoreHttpsErrors":true,"isMoble":false,"viewport":null},"defaultStepConfig":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"steps":[{"id":"7upsvuv96ciurhn","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"url":"http://713-cube-front.product.poc.za-tech.net","isNewPage":true},"actionType":"openPage"},{"id":"pi0nbeg37p1rz21","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element_exist","waitMilli":0,"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入账号","index":0},"selector":null,"index":0}},"actionType":"wait"},{"id":"qnphu51ym3iq491","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入账号","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"5afjo4ao4fw9nwc","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入账号","index":0},"selector":null,"index":0},"inputContent":"demouser","isAppendContent":false},"actionType":"input"},{"id":"ltx4n3kauodntho","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"role","key":"textbox","value":"请输入密码","index":0},"selector":null,"index":0},"inputContent":"2wsx!QAZ","isAppendContent":false},"actionType":"input"},{"id":"zdvjg97dsxq9a3j","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":null,"selector":"#login-btn","index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"fmlzpoktce5860n","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"time","waitMilli":1,"element":null},"actionType":"wait"},{"id":"6m3xp59jnfekovl","config":null,"action":{"condition":{"relation":"or","asserts":[{"type":"element","element":{"type":"exist","element":{"name":"","locator":null,"selector":"//*[@id=\"temppassword\"]","index":0},"attrKey":"","operator":"","expected":""},"text":null,"page":null,"variable":null},{"type":"element","element":{"type":"exist","element":{"name":"","locator":{"type":"role","key":"button","value":"我知道了","index":0},"selector":null,"index":0},"attrKey":"","operator":"","expected":""},"text":null,"page":null,"variable":null}],"conditions":null},"child":{"id":"","config":{"browserType":"","ignoreHttpsErrors":false,"isMoble":false,"viewport":null},"defaultStepConfig":null,"steps":[{"id":"ejyql0a3be0g7rm","config":{"waitBeforeMilli":3000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"我知道了","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"}]}},"actionType":"if"},{"id":"f6n06v3iqkaojwa","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":null,"selector":"//p[contains(text(),'项目')]/../..","index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"4q0eu5diue3g9cd","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入名称/编号","index":0},"selector":null,"index":0},"inputContent":"Magic测试项目","isAppendContent":false},"actionType":"input"},{"id":"ksk7fe3962i9v3p","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":null,"selector":"#itemsSearch","index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"9jfav3vpcpac4t4","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"link","value":"Magic测试项目","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"z8x3x5ltxflvc2n","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element_exist","waitMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"UI测试(v2.0)","index":0},"selector":null,"index":0}},"actionType":"wait"},{"id":"rukid3ejr32xc5h","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"UI测试(v2.0)","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"iaajurd2cg7ic58","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"元素管理","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"lx35vjwoez5eqi7","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"visible","waitMilli":0,"element":{"name":"","locator":null,"selector":"#mui-tree-view-1-1xkjvr77fvcpbak","index":0}},"actionType":"wait"},{"id":"pgc4vuqjrntg5p0","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":null,"selector":"#mui-tree-view-1-1xkjvr77fvcpbak","index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"uzdtae0dx5rcfpi","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"visible","waitMilli":0,"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入关键字搜索","index":0},"selector":null,"index":0}},"actionType":"wait"},{"id":"uux776gitw5zjwx","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入关键字搜索","index":0},"selector":null,"index":0},"inputContent":"UI测试-元素","isAppendContent":false},"actionType":"input"},{"id":"l2gp5god8n0jqj7","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element_exist","waitMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"编辑","index":0},"selector":null,"index":0}},"actionType":"wait"},{"id":"74x7d8d20iwibtp","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"button","value":"编辑","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"lxwvcen9x3x28rs","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入元素名称","index":0},"selector":null,"index":0},"inputContent":"UI测试-元素{{_randomInt}}","isAppendContent":false},"actionType":"input"},{"id":"uydmdfa5r2uegxq","config":null,"action":{"condition":{"relation":"and","asserts":[{"type":"variable","element":null,"text":null,"page":null,"variable":{"key":"element_attribute","operator":"notEqual","expected":"UI测试"}},{"type":"variable","element":null,"text":null,"page":null,"variable":{"key":"element_text","operator":"notContain","expected":"UI测试"}},{"type":"variable","element":null,"text":null,"page":null,"variable":{"key":"test_url","operator":"contain","expected":"http://713-cube-front.product.poc.za-tech.net/project/8143/ui-test"}},{"type":"variable","element":null,"text":null,"page":null,"variable":{"key":"test_title","operator":"equal","expected":"元素管理"}},{"type":"variable","element":null,"text":null,"page":null,"variable":{"key":"test_cookie","operator":"regularExpression","expected":"[a-z]+-[a-z]+"}},{"type":"element","element":{"type":"exist","element":{"name":"","locator":{"type":"label","key":"","value":"Open","index":0},"selector":null,"index":0},"attrKey":"","operator":"","expected":""},"text":null,"page":null,"variable":null}],"conditions":null},"child":{"id":"","config":{"browserType":"","ignoreHttpsErrors":false,"isMoble":false,"viewport":null},"defaultStepConfig":null,"steps":[{"id":"lgnsg46986ypdrv","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"option","value":"定位器","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"ktpowgz1obr6y1q","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"label","key":"","value":"Open","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"edk1fcq0jfapwnv","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"option","value":"role","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"fsfnbnn2vz9u98z","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":{"type":"role","key":"combobox","value":"定位器类型","index":0},"selector":null,"index":0},"clickPosition":null},"actionType":"mouseClick"},{"id":"wankw3r8svv87la","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"label","key":"","value":"key *","index":0},"selector":null,"index":0},"inputContent":"key","isAppendContent":false},"actionType":"input"},{"id":"wmkwf0ssypvckyt","config":{"waitBeforeMilli":3000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"label","key":"","value":"值 *","index":0},"selector":null,"index":0},"inputContent":"UI测试","isAppendContent":true},"actionType":"input"},{"id":"dkafjdnne8jei3i","config":{"waitBeforeMilli":3000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":0,"screenShotTime":"always"},"action":{"type":"single","button":"left","holdMilli":0,"element":{"name":"","locator":null,"selector":"//button[contains(text(),'确定')]","index":0},"clickPosition":null},"actionType":"mouseClick"}]}},"actionType":"if"},{"id":"epgu5f7cozw6yn2","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"time","waitMilli":1,"element":null},"actionType":"wait"},{"id":"mofqq6xcolkusv3","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"element":{"name":"","locator":{"type":"placeholder","key":"","value":"请输入关键字搜索","index":0},"selector":null,"index":0},"inputContent":"{{element_attribute}}","isAppendContent":false},"actionType":"input"},{"id":"a3nwzpwmocqemq6","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"time","waitMilli":1,"element":null},"actionType":"wait"},{"id":"ise9qrndmr3gm14","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element","element":{"type":"text","element":{"name":"","locator":{"type":"text","key":"","value":"UI测试-元素","index":0},"selector":null,"index":0},"attrKey":"","operator":"contain","expected":"UI测试-元素"},"text":null,"page":null,"variable":null},"actionType":"assert"},{"id":"5c4rh741nrveciu","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element","element":{"type":"count","element":{"name":"","locator":{"type":"text","key":"","value":"UI测试-元素","index":0},"selector":null,"index":0},"attrKey":"","operator":"greaterThanEqual","expected":"1"},"text":null,"page":null,"variable":null},"actionType":"assert"},{"id":"g3j6l981bfilo34","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element","element":{"type":"attr","element":{"name":"","locator":{"type":"text","key":"","value":"UI测试-元素","index":0},"selector":null,"index":0},"attrKey":"class","operator":"contain","expected":"MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineNone css-gxq1ym"},"text":null,"page":null,"variable":null},"actionType":"assert"},{"id":"08qny0yinn6o427","config":{"waitBeforeMilli":1000,"waitAfterMilli":0,"timeoutMilli":15000,"endsWhenFail":"stop","retryTimes":5,"screenShotTime":"always"},"action":{"type":"element","element":{"type":"tagName","element":{"name":"","locator":{"type":"text","key":"","value":"UI测试-元素","index":0},"selector":null,"index":0},"attrKey":"","operator":"equal","expected":"A"},"text":null,"page":null,"variable":null},"actionType":"assert"}]}]`,
		"http://localhost:3000", "", "http://713-cube-mantis.product.poc.za-tech.net/common/api/v1/biz/file/download", "", "")
}

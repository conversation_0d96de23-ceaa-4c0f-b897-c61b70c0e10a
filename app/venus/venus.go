package venus

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/executor"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/message/consumer"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron/queue"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/tekton/tasks"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/migration"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pocketbase"
	"knative.dev/pkg/apis"
)

func AddEventHooks(app pocketbase.App) {
}

func Init() {
	// 初始化数据库迁移
	migration.Init()

	// 初始化消息监听器
	consumer.Init()

	// 任务解析器
	queue.AddUnmarshalMap(constants.UiExecuteJob, service.DealUiExecuteJob)

	// 添加独有的expand
	addExpand()

	tasks.AddTaskRunDealers(map[string]func(condition apis.Condition, labels map[string]string) error{
		"ui-executor": executor.DealUiExecutorTask,
	})
}

func addExpand() {
	expand.SetExpandFunc("ui_case", func(ctx context.Context, ids []string) (map[string]any, error) {
		uiCases := make([]models.UiCase, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiCase{}).In("id", ids).Eq("is_deleted", false),
			&uiCases,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range uiCases {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_case_step", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiCaseStep, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiCaseStep{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_element", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiElement, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiElement{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_plan", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiPlan, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiPlan{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_report", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiReport, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiReport{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_report_case_detail", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiReportCase, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiReportCase{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})

	expand.SetExpandFunc("ui_report_step_detail", func(ctx context.Context, ids []string) (map[string]any, error) {
		data := make([]models.UiReportStepDetail, 0)
		err := gormx.SelectByParamBuilder(&commoncontext.MantisContext{Context: ctx},
			gormx.NewParamBuilder().Model(&models.UiReportStepDetail{}).In("id", ids).Eq("is_deleted", false),
			&data,
		)
		if err != nil {
			return nil, err
		}
		res := make(map[string]any)
		for _, c := range data {
			res[c.Id] = c
		}
		return res, nil
	})
}

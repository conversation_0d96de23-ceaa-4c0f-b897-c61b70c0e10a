# 构建阶段
FROM harbor.zhonganinfo.com/devcube/golang:1.22 AS builder

WORKDIR $GOPATH/src/cube-mantis
COPY . .

ENV GOPROXY=https://goproxy.cn,direct PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=1

# 下载playright-go和ui-executor-cli
RUN go install github.com/playwright-community/playwright-go/cmd/playwright@v0.5200.0 \
    && INSTALL_DIR=/cube-mantis make ui-executor-cli


# 运行阶段
FROM harbor.zhonganinfo.com/devcube/ubuntu:24.04

# 从构建阶段复制command
COPY --from=builder --chmod=a+x /cube-mantis/ui-executor /go/bin/playwright /usr/local/bin/

# 下载playwright
RUN playwright install --with-deps
package migration

import (
	"context"
	"embed"
	"errors"
	"fmt"
	"io/fs"
	"net/url"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/venus/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	"github.com/golang-migrate/migrate/v4/source/iofs"
)

//go:embed postgres/*.sql
var migrations embed.FS

// convertDSNToURL 将键值对格式的 DSN 转换为 go-migrate 兼容的 URL 格式
// 输入示例：host=************ port=5432 user=postgres dbname=test001 password=Cubeinfo_2024 sslmode=disable
// 输出示例：***************************************************/test001?sslmode=disable
func convertDSNToURL(dsn string) (string, error) {
	// 检查 DSN 是否为空
	if dsn == "" {
		return "", errors.New("DSN cannot be empty")
	}

	// 初始化参数映射
	params := make(map[string]string)

	// 按空格分割 DSN 并解析 key=value 对
	pairs := strings.Fields(dsn)
	for _, pair := range pairs {
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) != 2 {
			return "", fmt.Errorf("无效的 DSN 参数格式: %s", pair)
		}
		params[parts[0]] = parts[1]
	}

	// 提取必需参数
	user, ok := params["user"]
	if !ok {
		return "", errors.New("DSN 缺少 user 参数")
	}
	dbname, ok := params["dbname"]
	if !ok {
		return "", errors.New("DSN 缺少 dbname 参数")
	}
	host, ok := params["host"]
	if !ok {
		return "", errors.New("DSN 缺少 host 参数")
	}

	// 提取可选参数
	password, hasPassword := params["password"]
	port := params["port"]
	if port == "" {
		port = "5432" // PostgreSQL 默认端口
	}

	// 构建 URL
	var builder strings.Builder
	builder.WriteString("postgres://")

	// 添加用户名和密码（如果存在）
	builder.WriteString(url.PathEscape(user))
	if hasPassword {
		builder.WriteString(":")
		builder.WriteString(url.PathEscape(password))
	}
	builder.WriteString("@")

	// 添加主机和端口
	builder.WriteString(host)
	builder.WriteString(":")
	builder.WriteString(port)

	// 添加数据库名
	builder.WriteString("/")
	builder.WriteString(url.PathEscape(dbname))

	// 添加查询参数（如 sslmode）
	queryParams := url.Values{}
	for key, value := range params {
		if key != "user" && key != "password" && key != "host" && key != "port" && key != "dbname" {
			queryParams.Add(key, value)
		}
	}
	if len(queryParams) > 0 {
		builder.WriteString("?")
		builder.WriteString(queryParams.Encode())
	}

	return builder.String(), nil
}

// Init 初始化数据
func Init() {
	if !configs.Config.Db.Migrate {
		return
	}

	ctx := &commoncontext.MantisContext{Context: context.Background()}
	if !configs.Config.Db.Migrate {
		return
	}
	err := gormx.GetDB(ctx).AutoMigrate(
		&models.UiCaseStep{},
		&models.UiCase{},
		&models.UiElement{},
		&models.UiPlan{},
		&models.UiReportCase{},
		&models.UiReportStepDetail{},
		&models.UiReport{},
	)
	if err != nil {
		panic(err)
	}

	// 从 embed.FS 创建迁移源
	migrationFS, err := fs.Sub(migrations, "postgres")
	if err != nil {
		panic(fmt.Errorf("无法获取嵌入的 migrations 文件夹: %w", err))
	}

	// 创建 iofs 驱动以支持 go-migrate
	sourceDriver, err := iofs.New(migrationFS, ".")
	if err != nil {
		panic(fmt.Errorf("无法创建迁移源驱动: %w", err))
	}

	// 初始化 migrate 实例
	dbUrl, err := convertDSNToURL(configs.Config.Db.Dsn)
	if err != nil {
		panic(dbUrl)
	}
	m, err := migrate.NewWithSourceInstance("iofs", sourceDriver, dbUrl)
	if err != nil {
		panic(fmt.Errorf("无法初始化迁移实例: %w", err))
	}
	defer m.Close()

	// 执行所有迁移（向上迁移）
	err = m.Up()
	if err != nil {
		if errors.Is(err, migrate.ErrNoChange) {
			// 没有需要应用的迁移
			return
		}
		panic(fmt.Errorf("迁移失败: %w", err))
	}

	return
}

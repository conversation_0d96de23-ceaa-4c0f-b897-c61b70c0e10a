-- ui_case 触发器
-- 创建设置order的触发器
DROP TRIGGER IF EXISTS ui_case_set_sibling_order ON ui_case;
CREATE TRIGGER ui_case_set_sibling_order
    BEFORE INSERT ON ui_case
    FOR EACH ROW
    EXECUTE FUNCTION set_sibling_order(); -- 传入表名参数

-- 移除节点触发器
DROP TRIGGER IF EXISTS ui_case_after_delete_trigger ON ui_case;
CREATE TRIGGER ui_case_after_delete_trigger
    AFTER UPDATE ON ui_case
    FOR EACH ROW
    WHEN (NEW.is_deleted = true)
    EXECUTE FUNCTION recursion_delete_children();

-- ui_case_step 触发器
-- 创建设置order的触发器
DROP TRIGGER IF EXISTS ui_case_step_set_sibling_order ON ui_case_step;
CREATE TRIGGER ui_case_step_set_sibling_order
    BEFORE INSERT ON ui_case_step
    FOR EACH ROW
    EXECUTE FUNCTION set_sibling_order(); -- 传入表名参数

-- 移除节点触发器
DROP TRIGGER IF EXISTS ui_case_step_after_delete_trigger ON ui_case_step;
CREATE TRIGGER ui_case_step_after_delete_trigger
    AFTER UPDATE ON ui_case_step
    FOR EACH ROW
    WHEN (NEW.is_deleted = true)
    EXECUTE FUNCTION recursion_delete_children();
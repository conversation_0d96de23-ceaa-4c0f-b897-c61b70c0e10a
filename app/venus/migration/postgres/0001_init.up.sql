-- 定义函数
-- 删除节点,descendants_total-1
CREATE OR REPLACE FUNCTION decrement_descendants()
    RETURNS TRIGGER AS $$
DECLARE
    table_name TEXT := TG_ARGV[0]; -- 从触发器参数获取表名
BEGIN
    EXECUTE format('
        WITH RECURSIVE ancestors AS (
            SELECT $1 AS id
            UNION ALL
            SELECT d.parent_id
            FROM %1$I d
            JOIN ancestors a ON d.id = a.id
            WHERE d.parent_id != %2$L
        )
        UPDATE %1$I
        SET descendants_total = descendants_total - 1
        WHERE id IN (SELECT id FROM ancestors)
    ', table_name, '')  -- %1$I 动态插入表名，%2$L 处理空字符串
        USING OLD.parent_id; -- 传递被删除行的父 ID

    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- 新增节点,descendants_total+1
CREATE OR REPLACE FUNCTION increment_descendants()
    RETURNS TRIGGER AS $$
DECLARE
    table_name TEXT := TG_ARGV[0]; -- 从触发器参数获取表名
BEGIN
    EXECUTE format('
        WITH RECURSIVE ancestors AS (
            SELECT $1 AS id
            UNION ALL
            SELECT d.parent_id
            FROM %1$I d
            JOIN ancestors a ON d.id = a.id
            WHERE d.parent_id != %2$L
        )
        UPDATE %1$I
        SET descendants_total = descendants_total + 1
        WHERE id IN (SELECT id FROM ancestors)
    ', table_name, '')  -- %1$I 动态插入表名，%2$L 处理空字符串
        USING NEW.parent_id; -- 传递 NEW.parent_id 到动态 SQL

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 设置节点 sibling_order
CREATE OR REPLACE FUNCTION set_sibling_order()
    RETURNS TRIGGER AS $$
DECLARE
    max_order NUMERIC;             -- 存储当前父节点的最大 sibling_order
BEGIN
    -- 如果 sibling_order 已经大于 0，则不做任何操作
    IF NEW.sibling_order > 0 THEN
        RETURN NEW;
    END IF;
    IF NEW.parent_id = '' THEN
        -- 如果 parent_id 为空（根节点），默认 sibling_order 为 1
        NEW.sibling_order := 0;
    ELSE
        -- 动态查询当前父节点下的最大 sibling_order
        EXECUTE format('
            SELECT COALESCE(MAX(sibling_order), 0) + 1
            FROM %I
            WHERE parent_id = $1
        ', TG_TABLE_NAME)
            INTO max_order
            USING NEW.parent_id; -- 传入父节点 ID

        -- 设置新节点的 sibling_order
        NEW.sibling_order := max_order;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 递归删除子节点
CREATE OR REPLACE FUNCTION recursion_delete_children()
    RETURNS TRIGGER AS $$
BEGIN
    -- 递归删除所有子节点
    EXECUTE format('
        WITH RECURSIVE children AS (
            SELECT id FROM %1$I WHERE parent_id = $1
            UNION ALL
            SELECT t.id FROM %1$I t
            INNER JOIN children c ON t.parent_id = c.id
        )
        update %1$I set is_deleted=true WHERE id IN (SELECT id FROM children)
    ', TG_TABLE_NAME)
        USING NEW.id; -- 传递被删除节点的 ID
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 递归删除子节点 & 更新descendants_total
CREATE OR REPLACE FUNCTION recursion_delete_children_x()
    RETURNS TRIGGER AS $$
DECLARE
    current_parent_id TEXT;
    delta NUMERIC;
    found BOOLEAN;
BEGIN
    -- 根据节点类型确定减少量
    IF NEW.is_dir = true THEN
        delta := NEW.descendants_total;
    ELSE
        delta := 1;
    END IF;

    -- 遍历父节点链并更新descendants_total
    current_parent_id := NEW.parent_id;
    WHILE current_parent_id != '' LOOP
            -- 动态检查父节点是否存在
            EXECUTE format('SELECT EXISTS (SELECT 1 FROM %I WHERE id = $1)', TG_TABLE_NAME)
                INTO found
                USING current_parent_id;

            IF NOT found THEN
                EXIT;
            END IF;

            -- 动态更新父节点的descendants_total
            EXECUTE format('
            UPDATE %I
            SET descendants_total = descendants_total - $1
            WHERE id = $2
        ', TG_TABLE_NAME)
                USING delta, current_parent_id;

            -- 动态获取上一级父节点
            EXECUTE format('
            SELECT parent_id
            FROM %I
            WHERE id = $1
        ', TG_TABLE_NAME)
                INTO current_parent_id
                USING current_parent_id;
        END LOOP;

    -- 如果是文件夹，递归删除所有子节点
    IF NEW.is_dir = true THEN
        EXECUTE format('
            WITH RECURSIVE descendants AS (
                SELECT id
                FROM %1$I
                WHERE parent_id = $1
                UNION ALL
                SELECT d.id
                FROM %1$I d
                INNER JOIN descendants de ON d.parent_id = de.id
            )
            update %1$I set is_deleted=true
            WHERE id IN (SELECT id FROM descendants)
        ', TG_TABLE_NAME)
            USING NEW.id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;


-- ui_element 触发器
-- 创建设置order的触发器
DROP TRIGGER IF EXISTS ui_element_set_sibling_order ON ui_element;
CREATE TRIGGER ui_element_set_sibling_order
    BEFORE INSERT ON ui_element
    FOR EACH ROW
EXECUTE FUNCTION set_sibling_order(); -- 传入表名参数

-- 移除节点触发器
DROP TRIGGER IF EXISTS ui_element_after_delete_trigger ON ui_element;
CREATE TRIGGER ui_element_after_delete_trigger
    AFTER UPDATE ON ui_element
    FOR EACH ROW
    WHEN (NEW.is_deleted = true)
EXECUTE FUNCTION recursion_delete_children();
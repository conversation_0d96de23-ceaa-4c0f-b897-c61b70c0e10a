package router

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/controller"
	. "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codegen/router/decl"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/auditlog"
)

const (
	accessLog            = "accessLog"
	recovery             = "recovery"
	trace                = "trace"
	authentication       = "authentication"
	authenticationX      = "authentication-"
	authorization        = "authorization"
	auditLog             = "auditLog"
	auditLogX            = "auditLog-"
	pocketBaseReqHandler = "pocketBaseReqHandler"
)

const (
	CommonAuditResourceLabelKey = auditlog.AuditResourceLabelKey + "=" + "COMMON 接口"
	CommonAuditId               = "auditlog.id=$resp.traceId"
)

var _ = Path("/common",
	Middlewares([]string{recovery, trace, accessLog, authentication, auditLog},
		Labels([]string{CommonAuditResourceLabelKey},
			Constructor(controller.InitializeUserController,
				Path("/v1/user/power", GET(controller.DefaultUserController.GetUserPower)),
				Path("/v1/user/app", GET(controller.DefaultUserController.GetApps)),
			),
			Constructor(controller.InitializeHubController,
				Path("/v1/hub/getAppsByHubUrl", GET(controller.DefaultHubController.GetAppsByHubUrl)),
			),
			Path("/api/v1",
				Path("/collections",
					Path("/env",
						Constructor(controller.InitializeEnvVariableController,
							Middleware(authorization,
								Path("/variable/records", POST(controller.DefaultEnvVariableController.Create, CommonAuditId, "auth.code=MANTIS-CONFIG-ENVVAR-CREATE")),
								Path("/variable/records/{id}", PUT(controller.DefaultEnvVariableController.Update, CommonAuditId, "auth.code=MANTIS-CONFIG-ENVVAR-EDIT")),
								Path("/variable/records/{id}", DELETE(controller.DefaultEnvVariableController.Delete, CommonAuditId, "auth.code=MANTIS-CONFIG-ENVVAR-DELETE")),
							),
							Path("/variable/records/{id}", GET(controller.DefaultEnvVariableController.Get)),
							Path("/variable/records", GET(controller.DefaultEnvVariableController.List)),
						),
					),
					Path("/global",
						Constructor(controller.InitializeGlobalVariableController,
							Middleware(authorization,
								Path("/variable/records", POST(controller.DefaultGlobalVariableController.Create, CommonAuditId, "auth.code=MANTIS-CONFIG-GLOBALVAR-CREATE")),
								Path("/variable/records/{id}", PUT(controller.DefaultGlobalVariableController.Update, CommonAuditId, "auth.code=MANTIS-CONFIG-GLOBALVAR-EDIT")),
								Path("/variable/records/{id}", DELETE(controller.DefaultGlobalVariableController.Delete, CommonAuditId, "auth.code=MANTIS-CONFIG-GLOBALVAR-DELETE")),
							),
							Path("/variable/records/{id}", GET(controller.DefaultGlobalVariableController.Get)),
							Path("/variable/records", GET(controller.DefaultGlobalVariableController.List)),
						),
					),
					Constructor(controller.InitializeDbConfController,
						Middleware(authorization,
							Path("/database_config/records", POST(controller.DefaultDatabaseConfigController.Create, CommonAuditId, "auth.code=MANTIS-CONFIG-DATABASE-CREATE")),
							Path("/database_config/records/{id}", PUT(controller.DefaultDatabaseConfigController.Update, CommonAuditId, "auth.code=MANTIS-CONFIG-DATABASE-EDIT")),
							Path("/database_config/records/{id}", DELETE(controller.DefaultDatabaseConfigController.Delete, CommonAuditId, "auth.code=MANTIS-CONFIG-DATABASE-DELETE")),
						),
						Path("/database_config/records/{id}", GET(controller.DefaultDatabaseConfigController.Get)),
						Path("/database_config/records", GET(controller.DefaultDatabaseConfigController.List)),
					),
					Constructor(controller.InitializeDataSourceController,
						Middleware(authorization,
							Path("/datasource/records", POST(controller.DefaultDataSourceController.Create, CommonAuditId, "auth.code=MANTIS-CONFIG-DATASOURCE-CREATE")),
							Path("/datasource/records/{id}", PUT(controller.DefaultDataSourceController.Update, CommonAuditId, "auth.code=MANTIS-CONFIG-DATASOURCE-EDIT")),
							Path("/datasource/records/{id}", DELETE(controller.DefaultDataSourceController.Delete, CommonAuditId, "auth.code=MANTIS-CONFIG-DATASOURCE-DELETE")),
						),
						Path("/datasource/records/{id}", GET(controller.DefaultDataSourceController.Get)),
						Path("/datasource/records", GET(controller.DefaultDataSourceController.List)),
					),
					Constructor(controller.InitializeMockTypeController,
						Path("/mock_type/records", POST(controller.DefaultMockTypeController.Create)),
						Path("/mock_type/records/{id}", GET(controller.DefaultMockTypeController.Get)),
						Path("/mock_type/records/{id}", PUT(controller.DefaultMockTypeController.Update)),
						Path("/mock_type/records/{id}", DELETE(controller.DefaultMockTypeController.Delete)),
						Path("/mock_type/records", GET(controller.DefaultMockTypeController.List)),
					),
				),
				Path("/biz",
					Path("/mock", Middleware(authenticationX,
						Constructor(controller.InitializeMockController,
							Path("/getTypes", GET(controller.DefaultMockController.GetMockParamDataTree)),
							Path("/getMockPreview", POST(controller.DefaultMockController.GetMockPreview))),
					)),
					Path("/datasource",
						Constructor(controller.InitializeDataSourceController,
							Path("/template", GET(controller.DefaultDataSourceController.GetTemplate)),
						),
					),
					Path("/db",
						Constructor(controller.InitializeDbConfController,
							Path("/test/conn", POST(controller.DefaultDatabaseConfigController.TestConn)),
							Path("/types", GET(controller.DefaultDatabaseConfigController.DbTypeList)),
						),
					),
					Path("/file",
						Constructor(controller.InitializeFileController,
							Middlewares([]string{auditLogX},
								Path("/upload", POST(controller.DefaultFileController.UploadFile)),
							),
							Middlewares([]string{authenticationX, auditLogX},
								Path("/download", GET(controller.DefaultFileController.Download)),
							),
						),
					),
					Path("/variable",
						Constructor(controller.InitializeVariableController,
							Middleware(authenticationX,
								Path("/getVars", GET(controller.DefaultVariableController.GetVars)),
							),
						),
					),
				),
			),
		),
	),
)

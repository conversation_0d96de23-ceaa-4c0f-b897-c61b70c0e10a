// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/schema"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/terrors"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// UploadFile 处理文件上传
// POST Path("/common/api/v1/biz/file/upload") -> controller.FileController.UploadFile
func controllerFileControllerUploadFileHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "UploadFile",
		Patten:            "/common/api/v1/biz/file/upload",
		Desc:              "UploadFile 处理文件上传",
		ControllerPkgName: "controller",
		ControllerName:    "FileController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeFileController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_fileDto, err1 := ctrl.UploadFile()
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call UploadFile failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

			opt.Codec.Encode(rw, _fileDto)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/biz/file/download") -> controller.FileController.Download
func controllerFileControllerDownloadHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "Download",
		Patten:            "/common/api/v1/biz/file/download",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "FileController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeFileController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		req1 := controller.FileReq{}
		if i, ok := decode.Implements(&req1); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := schema.Decode(req, &req1); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(req1); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			err1 := ctrl.Download(&req1)
			if err1 != nil {
				log.FromContext(req.Context()).Warn("call Download failed:", terrors.TraceError(err1))
				opt.Codec.EncodeError(rw, err1)
				return
			}

		}
	}
	return chain.ThenFunc(HandleFunc)
}

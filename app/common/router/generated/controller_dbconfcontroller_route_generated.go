// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"github.com/justinas/alice"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/controller"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode"

	"net/http"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/routerinfo"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
)

// POST Path("/common/api/v1/biz/db/test/conn") -> controller.DbConfController.TestConn
func controllerDbConfControllerTestConnHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "TestConn",
		Patten:            "/common/api/v1/biz/db/test/conn",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "DbConfController",
		HTTPMethod:        "POST",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeDbConfController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		dbCfg := models.DatabaseConfig{}
		if i, ok := decode.Implements(&dbCfg); ok {
			if err := i.Decode(req); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}
		} else {

			if err := json.Decode(req, &dbCfg); err != nil {
				opt.Codec.EncodeError(rw, err)
				return
			}

		}
		if err := opt.Validator.Struct(dbCfg); err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_testConnRes := ctrl.TestConn(&dbCfg)

			opt.Codec.Encode(rw, _testConnRes)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

// GET Path("/common/api/v1/biz/db/types") -> controller.DbConfController.DbTypeList
func controllerDbConfControllerDbTypeListHandleFunc(middleware alice.Chain, opt types.Option) http.Handler {

	rinfo := routerinfo.RouteInfo{
		Method:            "DbTypeList",
		Patten:            "/common/api/v1/biz/db/types",
		Desc:              "",
		ControllerPkgName: "controller",
		ControllerName:    "DbConfController",
		HTTPMethod:        "GET",
		Middleware:        []string{"recovery", "trace", "accessLog", "authentication", "auditLog"},
		Label: map[string]string{
			"auditlog.resource": "COMMON 接口",
		},
	}

	if opt.LogRouteFunc != nil {
		opt.LogRouteFunc(rinfo)
	}

	chain := alice.New(routerinfo.WrapRouteInfo(&rinfo)).Extend(middleware)
	HandleFunc := func(rw http.ResponseWriter, req *http.Request) {
		ctrl, err := controller.InitializeDbConfController(rw, req)
		if err != nil {
			opt.Codec.EncodeError(rw, err)
			return
		}

		{

			_dbTypeInfo := ctrl.DbTypeList()

			opt.Codec.Encode(rw, _dbTypeInfo)
		}
	}
	return chain.ThenFunc(HandleFunc)
}

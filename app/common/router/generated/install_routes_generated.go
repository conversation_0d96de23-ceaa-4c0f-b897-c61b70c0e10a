// Code Generated by framework-codegen. DO NOT EDIT
package generated

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/types"
	"github.com/gorilla/mux"
	"github.com/justinas/alice"
)

func InstallRoutes(mux *mux.Router, opt types.Option) {
	//generate from Pkg: controller

	//
	mux.Methods("POST").Path("/common/api/v1/collections/datasource/records").Handler(controllerDataSourceControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/common/api/v1/collections/datasource/records/{id}").Handler(controllerDataSourceControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/common/api/v1/collections/datasource/records/{id}").Handler(controllerDataSourceControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/datasource/records/{id}").Handler(controllerDataSourceControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/datasource/records").Handler(controllerDataSourceControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/biz/datasource/template").Handler(controllerDataSourceControllerGetTemplateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/common/api/v1/collections/database_config/records").Handler(controllerDatabaseConfigControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/common/api/v1/collections/database_config/records/{id}").Handler(controllerDatabaseConfigControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/common/api/v1/collections/database_config/records/{id}").Handler(controllerDatabaseConfigControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/database_config/records/{id}").Handler(controllerDatabaseConfigControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/database_config/records").Handler(controllerDatabaseConfigControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("POST").Path("/common/api/v1/biz/db/test/conn").Handler(controllerDatabaseConfigControllerTestConnHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/biz/db/types").Handler(controllerDatabaseConfigControllerDbTypeListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("POST").Path("/common/api/v1/collections/env/variable/records").Handler(controllerEnvVariableControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/common/api/v1/collections/env/variable/records/{id}").Handler(controllerEnvVariableControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/common/api/v1/collections/env/variable/records/{id}").Handler(controllerEnvVariableControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/env/variable/records/{id}").Handler(controllerEnvVariableControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/env/variable/records").Handler(controllerEnvVariableControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	// UploadFile 处理文件上传
	mux.Methods("POST").Path("/common/api/v1/biz/file/upload").Handler(controllerFileControllerUploadFileHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/biz/file/download").Handler(controllerFileControllerDownloadHandleFunc(alice.New(_recovery, _trace, _accessLog), opt))

	//
	mux.Methods("POST").Path("/common/api/v1/collections/global/variable/records").Handler(controllerGlobalVariableControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("PUT").Path("/common/api/v1/collections/global/variable/records/{id}").Handler(controllerGlobalVariableControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("DELETE").Path("/common/api/v1/collections/global/variable/records/{id}").Handler(controllerGlobalVariableControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog, _authorization), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/global/variable/records/{id}").Handler(controllerGlobalVariableControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/global/variable/records").Handler(controllerGlobalVariableControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/common/v1/hub/getAppsByHubUrl").Handler(controllerHubControllerGetAppsByHubUrlHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/common/api/v1/biz/mock/getTypes").Handler(controllerMockControllerGetMockParamDataTreeHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))
	//
	mux.Methods("POST").Path("/common/api/v1/biz/mock/getMockPreview").Handler(controllerMockControllerGetMockPreviewHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

	//
	mux.Methods("POST").Path("/common/api/v1/collections/mock_type/records").Handler(controllerMockTypeControllerCreateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/mock_type/records/{id}").Handler(controllerMockTypeControllerGetHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("PUT").Path("/common/api/v1/collections/mock_type/records/{id}").Handler(controllerMockTypeControllerUpdateHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("DELETE").Path("/common/api/v1/collections/mock_type/records/{id}").Handler(controllerMockTypeControllerDeleteHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/api/v1/collections/mock_type/records").Handler(controllerMockTypeControllerListHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/common/v1/user/power").Handler(controllerUserControllerGetUserPowerHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))
	//
	mux.Methods("GET").Path("/common/v1/user/app").Handler(controllerUserControllerGetAppsHandleFunc(alice.New(_recovery, _trace, _accessLog, _authentication, _auditLog), opt))

	//
	mux.Methods("GET").Path("/common/api/v1/biz/variable/getVars").Handler(controllerVariableControllerGetVarsHandleFunc(alice.New(_recovery, _trace, _accessLog, _auditLog), opt))

}

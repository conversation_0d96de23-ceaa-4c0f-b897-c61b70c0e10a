package controller

import (
	"net/http"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

func InitializeUserController(rw http.ResponseWriter, req *http.Request) (*UserController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	userController := &UserController{
		BaseController: baseController,
	}
	return userController, nil
}

func InitializeHubController(rw http.ResponseWriter, req *http.Request) (*HubController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &HubController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeMockController(rw http.ResponseWriter, req *http.Request) (*MockController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &MockController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeMockTypeController(rw http.ResponseWriter, req *http.Request) (*MockTypeController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &MockTypeController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeDbConfController(rw http.ResponseWriter, req *http.Request) (*DatabaseConfigController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &DatabaseConfigController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeDataSourceController(rw http.ResponseWriter, req *http.Request) (*DataSourceController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &DataSourceController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeEnvVariableController(rw http.ResponseWriter, req *http.Request) (*EnvVariableController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &EnvVariableController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeGlobalVariableController(rw http.ResponseWriter, req *http.Request) (*GlobalVariableController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &GlobalVariableController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeFileController(rw http.ResponseWriter, req *http.Request) (*FileController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &FileController{
		BaseController: baseController,
	}
	return c, nil
}

func InitializeVariableController(rw http.ResponseWriter, req *http.Request) (*VariableController, error) {
	baseController, err := controller.NewBaseController(rw, req)
	if err != nil {
		return nil, err
	}
	c := &VariableController{
		BaseController: baseController,
	}
	return c, nil
}

// CRUD methods generated by pb-api-generator.

package controller

import (
	"context"
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"gorm.io/gorm"
)

type VariableIdRequest struct {
	Id string `path:"id"`
}

type EnvVariableController struct {
	*controller.BaseController
}

var DefaultEnvVariableController EnvVariableController

func (c *EnvVariableController) List() (*commondto.PaginationRecord[models.Variable], error) {
	query := dto.VariableQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.Variable, 0)
	err = db.Model(&models.Variable{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.EnvExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Env)
		}
		f, ok := expand.GetExpandFunc("env")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["env"] = res[items[i].Env]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.Variable{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.Variable]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *EnvVariableController) Get(req *VariableIdRequest) (*models.Variable, error) {
	res := &models.Variable{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *EnvVariableController) Delete(req *VariableIdRequest) error {
	model := &models.Variable{}
	model.Id = req.Id
	err := gormx.SelectOneByCondition(c.MantisContext, &model)
	if err != nil {
		return err
	}
	model.IsDeleted = true
	_, err = gormx.UpdateOneByCondition(c.MantisContext, model)
	// 获取环境
	envs, err := cmdb.Client.GetBaseEnvList(context.Background(), c.User.CompanyId)
	if err != nil {
		return err
	}
	envIds := make([]string, 0)
	for _, env := range envs {
		envIds = append(envIds, string(env.InstanceBizID))
	}
	_, err = gormx.Exec(c.MantisContext, `update variable set is_deleted = 'true' where name = ? and env in ?`, model.Name, envIds)
	return err
}

func (c *EnvVariableController) Create(req *models.Variable) (*models.Variable, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	ctx := c.MantisContext
	if req.VarType != "global" {
		// 获取环境
		envs, err := cmdb.Client.GetBaseEnvList(context.Background(), c.MantisContext.User.CompanyID)
		if err != nil {
			return nil, err
		}
		for _, env := range envs {
			varT := models.Variable{
				VarType:     req.VarType,
				Env:         string(env.InstanceBizID),
				Name:        req.Name,
				Value:       req.Value,
				Description: req.Description,
				SpaceId:     req.SpaceId,
			}
			varT.Creator = req.Creator
			varT.Modifier = req.Modifier
			// 排除自己
			if varT.Env != req.Env {
				if _, err := gormx.InsertOne(ctx, &varT); err != nil {
					return nil, err
				}
			}
		}
	}
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *EnvVariableController) Update(req *models.Variable, idReq *VariableIdRequest) (*models.Variable, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	ctx := c.MantisContext
	if req.VarType != "global" {
		// 查询原始数据
		varOri := models.Variable{}
		varOri.Id = req.Id
		err := gormx.SelectOneByCondition(ctx, &varOri)
		if err != nil {
			return nil, err
		}
		// 名称未修改,其他环境不做变更
		if varOri.Name != req.Name {
			// 查询vars
			vars := make([]models.Variable, 0)
			err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.Variable{}).Eq("name", varOri.Name).Eq("is_deleted", false), &vars)
			if err != nil {
				return nil, err
			}
			ids := make([]string, 0)
			for _, vu := range vars {
				ids = append(ids, vu.Id)
			}
			_, err = gormx.Exec(ctx, `update variable set name = ? where id in ?`, req.Name, ids)
			if err != nil {
				return nil, err
			}
		}
	}
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

type GlobalVariableController struct {
	*controller.BaseController
}

var DefaultGlobalVariableController GlobalVariableController

func (c *GlobalVariableController) List() (*commondto.PaginationRecord[models.Variable], error) {
	query := dto.VariableQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.Variable, 0)
	err = db.Model(&models.Variable{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.EnvExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Env)
		}
		f, ok := expand.GetExpandFunc("env")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}

				items[i].Expand["env"] = res[items[i].Env]

			}
		}
	}

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.Variable{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.Variable]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *GlobalVariableController) Get(req *VariableIdRequest) (*models.Variable, error) {
	res := &models.Variable{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *GlobalVariableController) Delete(req *VariableIdRequest) error {
	model := &models.Variable{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *GlobalVariableController) Create(req *models.Variable) (*models.Variable, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	ctx := c.MantisContext
	if req.VarType != "global" {
		// 获取环境
		envs, err := cmdb.Client.GetBaseEnvList(context.Background(), c.MantisContext.User.CompanyID)
		if err != nil {
			return nil, err
		}
		for _, env := range envs {
			varT := models.Variable{
				VarType:     req.VarType,
				Env:         string(env.InstanceBizID),
				Name:        req.Name,
				Value:       req.Value,
				Description: req.Description,
				SpaceId:     req.SpaceId,
			}
			varT.Creator = req.Creator
			varT.Modifier = req.Modifier
			// 排除自己
			if varT.Env != req.Env {
				if _, err := gormx.InsertOne(ctx, &varT); err != nil {
					return nil, err
				}
			}
		}
	}
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *GlobalVariableController) Update(req *models.Variable, idReq *VariableIdRequest) (*models.Variable, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	ctx := c.MantisContext
	if req.VarType != "global" {
		// 查询原始数据
		varOri := models.Variable{}
		varOri.Id = req.Id
		err := gormx.SelectOneByCondition(ctx, &varOri)
		if err != nil {
			return nil, err
		}
		// 名称未修改,其他环境不做变更
		if varOri.Name != req.Name {
			// 查询vars
			vars := make([]models.Variable, 0)
			err = gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.Variable{}).Eq("name", varOri.Name).Eq("is_deleted", false), &vars)
			if err != nil {
				return nil, err
			}
			ids := make([]string, 0)
			for _, vu := range vars {
				ids = append(ids, vu.Id)
			}
			_, err = gormx.Exec(ctx, `update variable set name = ? where id in ?`, req.Name, ids)
			if err != nil {
				return nil, err
			}
		}
	}
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

type VariableController struct {
	*controller.BaseController
}

var DefaultVariableController VariableController

type GetVarsRequest struct {
	Env string `schema:"env"`
}

func (c *VariableController) GetVars(req *GetVarsRequest) ([]models.Variable, error) {
	vars := make([]models.Variable, 0)
	err := gormx.SelectByParamBuilder(c.MantisContext,
		gormx.NewParamBuilder().Model(&models.Variable{}).Or(gormx.NewParamBuilder().Eq("var_type", "global"), gormx.NewParamBuilder().Eq("var_type", "env").Eq("env", req.Env)).Eq("is_deleted", false),
		&vars,
	)
	if err != nil {
		return nil, err
	}
	return vars, nil
}

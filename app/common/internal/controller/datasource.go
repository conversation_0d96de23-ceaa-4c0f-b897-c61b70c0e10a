package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/resources"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type DataSourceIdRequest struct {
	Id string `path:"id"`
}

type DataSourceController struct {
	*controller.BaseController
}

var DefaultDataSourceController DataSourceController

func (c *DataSourceController) List() (*commondto.PaginationRecord[models.DataSource], error) {
	query := dto.DataSourceQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.DataSource, 0)
	err = db.Model(&models.DataSource{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.DataSource{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.DataSource]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *DataSourceController) Get(req *DataSourceIdRequest) (*models.DataSource, error) {
	res := &models.DataSource{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *DataSourceController) Delete(req *DataSourceIdRequest) error {
	model := &models.DataSource{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *DataSourceController) Create(req *models.DataSource) (*models.DataSource, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *DataSourceController) Update(req *models.DataSource, idReq *DataSourceIdRequest) (*models.DataSource, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

type TemplateType string

const (
	JSON  TemplateType = "json"
	Excel TemplateType = "excel"
)

const (
	DataSourceTemplatePath = "template/datasource/"
	ExcelTemplate          = "datasource.xlsx"
	JsonTemplate           = "datasource.json"
)

type TemplateTypeReq struct {
	Type TemplateType `schema:"type"`
}

func (c *DataSourceController) GetTemplate(req TemplateTypeReq) error {
	var fileName string
	switch req.Type {
	case Excel:
		fileName = ExcelTemplate
	case JSON:
		fileName = JsonTemplate
	default:
		return fmt.Errorf("不支持的模板类型: %s", req.Type)
	}

	file, err := resources.TemplateFS.ReadFile(DataSourceTemplatePath + fileName)
	if err != nil {
		return fmt.Errorf("获取文件失败 %s 文件: %v", fileName, err)
	}

	c.ResponseWriter.Header().Set("Content-Type", "application/octet-stream")
	c.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	c.ResponseWriter.Header().Set("Content-Disposition", "attachment; filename="+fileName)
	if _, err := c.ResponseWriter.Write(file); err != nil {
		return fmt.Errorf("写入response失败: %v", err)
	}

	return nil
}

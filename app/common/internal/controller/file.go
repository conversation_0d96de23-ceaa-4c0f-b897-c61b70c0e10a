package controller

import (
	"encoding/base64"
	"fmt"
	"path/filepath"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type FileController struct {
	*controller.BaseController
}
type FileReq struct {
	ObjKey string `schema:"obj_key"`
}

var (
	fileService           service.FileService
	DefaultFileController FileController
)

// 文件上传结构

// UploadFile 处理文件上传
func (c *FileController) UploadFile() (*dto.FileDto, error) {
	// 获取上传的文件
	_, handler, err := c.FormFile("file")
	if err != nil {
		return nil, err
	}
	ossKey, err := fileService.UploadFile(handler)
	if err != nil {
		return nil, err
	}
	return &dto.FileDto{
		Name:       handler.Filename,
		ObjKey:     base64.StdEncoding.EncodeToString([]byte(ossKey)),
		Size:       fmt.Sprintf("%d", handler.Size),
		CreateTime: time.Now().UnixMilli(),
		MimeType:   s3store.GetContentType(handler.Filename),
	}, nil
}

func (c *FileController) Download(req *FileReq) error {
	decode, err := base64.StdEncoding.DecodeString(req.ObjKey)
	if err != nil {
		return err
	}
	path := string(decode)
	logger.Logger.Infof("oss key : %s", path)
	fileBytes, err := fileService.DownloadFile(path)
	if err != nil {
		return err
	}
	c.ResponseWriter.Header().Set("Content-Type", "application/octet-stream")
	c.ResponseWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(path)))
	c.ResponseWriter.Header().Set("Content-Transfer-Encoding", "binary")
	if _, err = c.ResponseWriter.Write(fileBytes); err != nil {
		return err
	}
	return nil
}

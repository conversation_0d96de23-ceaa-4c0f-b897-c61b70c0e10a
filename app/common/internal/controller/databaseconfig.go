package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/openapi"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/expand"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
	"gorm.io/gorm"
)

type DatabaseConfigIdRequest struct {
	Id string `path:"id"`
}

type DatabaseConfigController struct {
	*controller.BaseController
}

var DefaultDatabaseConfigController DatabaseConfigController

func (c *DatabaseConfigController) List() (*commondto.PaginationRecord[models.DatabaseConfig], error) {
	query := dto.DatabaseConfigQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)
	db = db.Where("space_id = ?", c.Request.Header.Get("spaceid"))
	items := make([]models.DatabaseConfig, 0)
	err = db.Model(&models.DatabaseConfig{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	if query.EnvExpand {
		ids := make([]string, 0)
		for _, item := range items {
			ids = append(ids, item.Env)
		}
		f, ok := expand.GetExpandFunc("env")
		if ok {
			res, err := f(c.MantisContext, ids)
			if err != nil {
				return nil, err
			}
			for i := range items {
				if items[i].Expand == nil {
					items[i].Expand = make(map[string]any)
				}
				items[i].Expand["env"] = res[items[i].Env]
			}
		}
	}
	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.DatabaseConfig{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.DatabaseConfig]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *DatabaseConfigController) Get(req *DatabaseConfigIdRequest) (*models.DatabaseConfig, error) {
	res := &models.DatabaseConfig{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *DatabaseConfigController) Delete(req *DatabaseConfigIdRequest) error {
	model := &models.DatabaseConfig{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *DatabaseConfigController) Create(req *models.DatabaseConfig) (*models.DatabaseConfig, error) {
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.IsDeleted = false
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *DatabaseConfigController) Update(req *models.DatabaseConfig, idReq *DatabaseConfigIdRequest) (*models.DatabaseConfig, error) {
	req.Id = idReq.Id
	req.SpaceId = c.Request.Header.Get("spaceid")
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

type TestConnRes struct {
	Success bool   `json:"success"`
	Error   string `json:"error"`
}

var sqlExecutor openapi.SqlExecutor

func (c *DatabaseConfigController) TestConn(dbCfg *models.DatabaseConfig) TestConnRes {
	err := sqlExecutor.TestConn(dbCfg)
	errMsg := ""
	if err != nil {
		errMsg = err.Error()
	}
	return TestConnRes{
		Success: err == nil,
		Error:   errMsg,
	}
}

func (c *DatabaseConfigController) DbTypeList() []types.DbTypeInfo {
	return types.GetAllDbTypes()
}

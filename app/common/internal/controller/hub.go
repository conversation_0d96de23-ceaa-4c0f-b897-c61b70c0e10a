package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
)

type HubController struct {
	*controller.BaseController
}

var (
	DefaultHubController HubController
	shipRemoteApi        remote.ShipRemoteApi
)

func (c *HubController) GetAppsByHubUrl() {
	c.ResSuccessResult(shipRemoteApi.GetAppInfosByHubUrl(c.<PERSON>, c.Request.Header.Get(constants.GitRepoUrlKey)))
}

package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
)

var (
	DefaultMockController MockController
	mockService           service.MockServer
)

type MockPreviewRequest struct {
	Data string `json:"data"`
}

type MockController struct {
	*controller.BaseController
}

func (a *MockController) GetMockParamDataTree() (*dto.MockTree, error) {
	return mockService.GetMockParamDataTree()
}

func (a *MockController) GetMockPreview(req MockPreviewRequest) service.MockResponse {
	return mockService.GetMockPreview(req.Data)
}

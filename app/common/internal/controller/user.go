package controller

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/service"
	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
)

type UserSearchReq struct {
	Search string `schema:"search"`
}

type UserController struct {
	*controller.BaseController
}

var (
	DefaultUserController UserController
	usercenterRemoteApi   remote.UserCenterRemoteApi
	userService           service.UserService
	cubeBaseRemoteApi     remote.CubeBaseRemoteApi
)

func (c *UserController) GetUserPower() {
	c.ResSuccessResult(userService.GetChildResource(c.GetUser(), c.<PERSON>onte<PERSON>t))
}

func (c *UserController) GetApps() {
	var apps []cmdb.CmdbApp
	if c.Request.Header.Get(commonconstants.GitRepoUrlKey) != "" {
		apps = shipRemoteApi.GetAppInfosByHubUrl(c.MantisContext, c.Request.Header.Get(commonconstants.GitRepoUrlKey))
	} else {
		cmdbapps, err := cubeBaseRemoteApi.GetAppsByUser(c.MantisContext.User, "")
		if err != nil {
			c.ResFail(err)
		}
		apps = cmdbapps
	}
	res := make([]map[string]any, 0)
	for _, app := range apps {
		res = append(res, map[string]any{
			"id":   app.AppId,
			"name": app.Name,
		})
	}
	c.ResSuccessResult(res)
}

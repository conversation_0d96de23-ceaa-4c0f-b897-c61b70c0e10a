// CRUD methods generated by pb-api-generator.

package controller

import (
	"fmt"

	dto "git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto/generated"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	commondto "git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"gorm.io/gorm"
)

type MockTypeIdRequest struct {
	Id string `path:"id"`
}

type MockTypeController struct {
	*controller.BaseController
}

var DefaultMockTypeController MockTypeController

func (c *MockTypeController) List() (*commondto.PaginationRecord[models.MockType], error) {
	query := dto.MockTypeQueryDTO{}
	err := query.FromContext(c.Context(), c.Request)
	if err != nil {
		return nil, err
	}
	db, err := query.ToGORMQuery(gormx.GetDB(c.MantisContext))
	if err != nil {
		return nil, err
	}
	db = db.Where("is_deleted = ?", false)

	items := make([]models.MockType, 0)
	err = db.Model(&models.MockType{}).Find(&items).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	// Expand fields

	var total int64
	err = gormx.Raw(c.MantisContext, fmt.Sprintf(`select count(1) from "%s" where is_deleted = false`, models.MockType{}.TableName()), &total)
	if err != nil {
		return nil, err
	}
	totalPages := total / int64(query.PerPage)
	if total%int64(query.PerPage) > 0 {
		totalPages += 1
	}
	return &commondto.PaginationRecord[models.MockType]{
		Page:       query.Page,
		PerPage:    query.PerPage,
		Items:      items,
		TotalItems: total,
		TotalPages: totalPages,
	}, nil
}

func (c *MockTypeController) Get(req *MockTypeIdRequest) (*models.MockType, error) {
	res := &models.MockType{}
	res.Id = req.Id
	res.IsDeleted = false
	if err := gormx.SelectOneByCondition(c.MantisContext, res); err != nil {
		return nil, err
	}
	return res, nil
}

func (c *MockTypeController) Delete(req *MockTypeIdRequest) error {
	model := &models.MockType{}
	model.Id = req.Id
	model.IsDeleted = true
	_, err := gormx.UpdateOneByCondition(c.MantisContext, model)
	return err
}

func (c *MockTypeController) Create(req *models.MockType) (*models.MockType, error) {
	req.SetCreatorFromReq(c.Request)
	if _, err := gormx.InsertOne(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

func (c *MockTypeController) Update(req *models.MockType, idReq *MockTypeIdRequest) (*models.MockType, error) {
	req.Id = idReq.Id
	req.SetModifierFromReq(c.Request)
	if _, err := gormx.UpdateOneByCondition(c.MantisContext, req); err != nil {
		return nil, err
	}
	return req, nil
}

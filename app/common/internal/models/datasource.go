package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

type DataSource struct {
	models.Addons2
	Name    string         `json:"name" gorm:"column:name;type:text" `
	Data    DataSourceData `json:"data" gorm:"column:data;type:jsonb" `
	SpaceId string         `json:"space_id" gorm:"column:space_id;type:text"`
}

func (DataSource) TableName() string {
	return "datasource"
}

type DataSourceData []map[string]any

func (d DataSourceData) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *DataSourceData) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

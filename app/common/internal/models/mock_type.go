package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

type MockType struct {
	models.Addons2
	Type        string `json:"type" gorm:"column:type;type:text"`
	Name        string `json:"name" gorm:"column:name;type:text"`
	Description string `json:"description" gorm:"column:description;type:text"`
	Command     string `json:"command" gorm:"column:command;type:text"`
	// ExamplesStr string                `json:"-"`
	Examples MockDataTypeExamples `json:"examples" gorm:"column:examples;type:jsonb"`
}

func (MockType) TableName() string {
	return "mock_type"
}

type MockDataTypeExample struct {
	Example string `json:"example"`
	Result  []any  `json:"result"`
}

type MockDataTypeExamples []MockDataTypeExample

func (d MockDataTypeExamples) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *MockDataTypeExamples) Scan(value any) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

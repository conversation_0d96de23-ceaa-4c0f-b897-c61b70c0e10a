package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
)

type Variable struct {
	models.Addons2
	Name        string         `json:"name" gorm:"column:name;type:text"`
	Value       string         `json:"value" gorm:"column:value;type:text"`
	Description string         `json:"description" gorm:"column:description;type:text"`
	Env         string         `json:"env" gorm:"column:env;type:text" expand:"env"`
	VarType     string         `json:"var_type" gorm:"column:var_type;type:text"`
	SpaceId     string         `json:"space_id" gorm:"column:space_id;type:text"`
	Expand      map[string]any `json:"expand" gorm:"-"`
}

func (Variable) TableName() string {
	return "variable"
}

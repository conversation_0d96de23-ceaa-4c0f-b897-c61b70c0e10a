package models

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
)

type DatabaseConfig struct {
	models.Addons2
	Name    string         `json:"name" gorm:"column:name;type:text"`
	DbType  types.DbType   `json:"db_type" gorm:"column:db_type;type:text"`
	DbHost  string         `json:"db_host" gorm:"column:db_host;type:text"`
	Port    float64        `json:"port" gorm:"column:port;type:numeric"`
	DbName  string         `json:"db_name" gorm:"column:db_name;type:text"`
	DbUser  string         `json:"db_user" gorm:"column:db_user;type:text"`
	DbPw    string         `json:"db_pw" gorm:"column:db_pw;type:text"`
	Env     string         `json:"env" gorm:"column:env;type:text" expand:"env"`
	SpaceId string         `json:"space_id" gorm:"column:space_id;type:text"`
	Expand  map[string]any `json:"expand" gorm:"-"`
}

func (DatabaseConfig) TableName() string {
	return "database_config"
}

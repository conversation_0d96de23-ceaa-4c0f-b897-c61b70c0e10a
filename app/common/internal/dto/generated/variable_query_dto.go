// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// VariableQueryDTO represents query parameters for Variable
type VariableQueryDTO struct {
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	ValueEqual *string `json:"valueequal"`
	ValueLike *string `json:"valuelike"`
	ValueNotEqual *string `json:"valuenotequal"`
	ValueNotLike *string `json:"valuenotlike"`
	DescriptionEqual *string `json:"descriptionequal"`
	DescriptionLike *string `json:"descriptionlike"`
	DescriptionNotEqual *string `json:"descriptionnotequal"`
	DescriptionNotLike *string `json:"descriptionnotlike"`
	EnvEqual *string `json:"envequal"`
	EnvLike *string `json:"envlike"`
	EnvNotEqual *string `json:"envnotequal"`
	EnvNotLike *string `json:"envnotlike"`
	EnvExpand bool `json:"envexpand"`
	VarTypeEqual *string `json:"vartypeequal"`
	VarTypeLike *string `json:"vartypelike"`
	VarTypeNotEqual *string `json:"vartypenotequal"`
	VarTypeNotLike *string `json:"vartypenotlike"`
	SpaceIdEqual *string `json:"spaceidequal"`
	SpaceIdLike *string `json:"spaceidlike"`
	SpaceIdNotEqual *string `json:"spaceidnotequal"`
	SpaceIdNotLike *string `json:"spaceidnotlike"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *VariableQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "env":
				dto.EnvExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameEqual = &value
					
				case "value":
					dto.ValueEqual = &value
					
				case "description":
					dto.DescriptionEqual = &value
					
				case "env":
					dto.EnvEqual = &value
					
				case "var_type":
					dto.VarTypeEqual = &value
					
				case "space_id":
					dto.SpaceIdEqual = &value
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotEqual = &value
					
				case "value":
					dto.ValueNotEqual = &value
					
				case "description":
					dto.DescriptionNotEqual = &value
					
				case "env":
					dto.EnvNotEqual = &value
					
				case "var_type":
					dto.VarTypeNotEqual = &value
					
				case "space_id":
					dto.SpaceIdNotEqual = &value
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameLike = &value
				case "value":
					dto.ValueLike = &value
				case "description":
					dto.DescriptionLike = &value
				case "env":
					dto.EnvLike = &value
				case "var_type":
					dto.VarTypeLike = &value
				case "space_id":
					dto.SpaceIdLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotLike = &value
				case "value":
					dto.ValueNotLike = &value
				case "description":
					dto.DescriptionNotLike = &value
				case "env":
					dto.EnvNotLike = &value
				case "var_type":
					dto.VarTypeNotLike = &value
				case "space_id":
					dto.SpaceIdNotLike = &value
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *VariableQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.ValueEqual != nil {
		db = db.Where("value = ?", *dto.ValueEqual)
	}
	if dto.ValueLike != nil {
		db = db.Where("value ILIKE ?", "%"+*dto.ValueLike+"%")
	}
	if dto.ValueNotEqual != nil {
		db = db.Where("value != ?", *dto.ValueNotEqual)
	}
	if dto.ValueNotLike != nil {
		db = db.Where("value NOT ILIKE ?", "%"+*dto.ValueNotLike+"%")
	}
	
	
	
	if dto.DescriptionEqual != nil {
		db = db.Where("description = ?", *dto.DescriptionEqual)
	}
	if dto.DescriptionLike != nil {
		db = db.Where("description ILIKE ?", "%"+*dto.DescriptionLike+"%")
	}
	if dto.DescriptionNotEqual != nil {
		db = db.Where("description != ?", *dto.DescriptionNotEqual)
	}
	if dto.DescriptionNotLike != nil {
		db = db.Where("description NOT ILIKE ?", "%"+*dto.DescriptionNotLike+"%")
	}
	
	
	
	if dto.EnvEqual != nil {
		db = db.Where("env = ?", *dto.EnvEqual)
	}
	if dto.EnvLike != nil {
		db = db.Where("env ILIKE ?", "%"+*dto.EnvLike+"%")
	}
	if dto.EnvNotEqual != nil {
		db = db.Where("env != ?", *dto.EnvNotEqual)
	}
	if dto.EnvNotLike != nil {
		db = db.Where("env NOT ILIKE ?", "%"+*dto.EnvNotLike+"%")
	}
	
	
	
	if dto.VarTypeEqual != nil {
		db = db.Where("var_type = ?", *dto.VarTypeEqual)
	}
	if dto.VarTypeLike != nil {
		db = db.Where("var_type ILIKE ?", "%"+*dto.VarTypeLike+"%")
	}
	if dto.VarTypeNotEqual != nil {
		db = db.Where("var_type != ?", *dto.VarTypeNotEqual)
	}
	if dto.VarTypeNotLike != nil {
		db = db.Where("var_type NOT ILIKE ?", "%"+*dto.VarTypeNotLike+"%")
	}
	
	
	
	if dto.SpaceIdEqual != nil {
		db = db.Where("space_id = ?", *dto.SpaceIdEqual)
	}
	if dto.SpaceIdLike != nil {
		db = db.Where("space_id ILIKE ?", "%"+*dto.SpaceIdLike+"%")
	}
	if dto.SpaceIdNotEqual != nil {
		db = db.Where("space_id != ?", *dto.SpaceIdNotEqual)
	}
	if dto.SpaceIdNotLike != nil {
		db = db.Where("space_id NOT ILIKE ?", "%"+*dto.SpaceIdNotLike+"%")
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

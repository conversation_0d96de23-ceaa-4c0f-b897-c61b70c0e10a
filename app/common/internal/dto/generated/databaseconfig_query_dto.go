// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// DatabaseConfigQueryDTO represents query parameters for DatabaseConfig
type DatabaseConfigQueryDTO struct {
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	DbHostEqual *string `json:"dbhostequal"`
	DbHostLike *string `json:"dbhostlike"`
	DbHostNotEqual *string `json:"dbhostnotequal"`
	DbHostNotLike *string `json:"dbhostnotlike"`
	PortEqual *float64 `json:"portequal"`
	PortNotEqual *float64 `json:"portnotequal"`
	PortGreater *float64 `json:"portgreater"`
	PortLess *float64 `json:"portless"`
	PortGreaterEqual *float64 `json:"portgreaterequal"`
	PortLessEqual *float64 `json:"portlessequal"`
	DbNameEqual *string `json:"dbnameequal"`
	DbNameLike *string `json:"dbnamelike"`
	DbNameNotEqual *string `json:"dbnamenotequal"`
	DbNameNotLike *string `json:"dbnamenotlike"`
	DbUserEqual *string `json:"dbuserequal"`
	DbUserLike *string `json:"dbuserlike"`
	DbUserNotEqual *string `json:"dbusernotequal"`
	DbUserNotLike *string `json:"dbusernotlike"`
	DbPwEqual *string `json:"dbpwequal"`
	DbPwLike *string `json:"dbpwlike"`
	DbPwNotEqual *string `json:"dbpwnotequal"`
	DbPwNotLike *string `json:"dbpwnotlike"`
	EnvEqual *string `json:"envequal"`
	EnvLike *string `json:"envlike"`
	EnvNotEqual *string `json:"envnotequal"`
	EnvNotLike *string `json:"envnotlike"`
	EnvExpand bool `json:"envexpand"`
	SpaceIdEqual *string `json:"spaceidequal"`
	SpaceIdLike *string `json:"spaceidlike"`
	SpaceIdNotEqual *string `json:"spaceidnotequal"`
	SpaceIdNotLike *string `json:"spaceidnotlike"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *DatabaseConfigQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			case "env":
				dto.EnvExpand = true
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameEqual = &value
					
				case "db_host":
					dto.DbHostEqual = &value
					
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortEqual = &float64Val
					
				case "db_name":
					dto.DbNameEqual = &value
					
				case "db_user":
					dto.DbUserEqual = &value
					
				case "db_pw":
					dto.DbPwEqual = &value
					
				case "env":
					dto.EnvEqual = &value
					
				case "space_id":
					dto.SpaceIdEqual = &value
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotEqual = &value
					
				case "db_host":
					dto.DbHostNotEqual = &value
					
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortNotEqual = &float64Val
					
				case "db_name":
					dto.DbNameNotEqual = &value
					
				case "db_user":
					dto.DbUserNotEqual = &value
					
				case "db_pw":
					dto.DbPwNotEqual = &value
					
				case "env":
					dto.EnvNotEqual = &value
					
				case "space_id":
					dto.SpaceIdNotEqual = &value
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameLike = &value
				case "db_host":
					dto.DbHostLike = &value
				case "db_name":
					dto.DbNameLike = &value
				case "db_user":
					dto.DbUserLike = &value
				case "db_pw":
					dto.DbPwLike = &value
				case "env":
					dto.EnvLike = &value
				case "space_id":
					dto.SpaceIdLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "name":
					dto.NameNotLike = &value
				case "db_host":
					dto.DbHostNotLike = &value
				case "db_name":
					dto.DbNameNotLike = &value
				case "db_user":
					dto.DbUserNotLike = &value
				case "db_pw":
					dto.DbPwNotLike = &value
				case "env":
					dto.EnvNotLike = &value
				case "space_id":
					dto.SpaceIdNotLike = &value
				
				}
			}
			
			if strings.Contains(condition, ">=") {
				kv := strings.SplitN(condition, ">=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortGreaterEqual = &float64Val
					
				
				}
			} else if strings.Contains(condition, "<=") {
				kv := strings.SplitN(condition, "<=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortLessEqual = &float64Val
					
				
				}
			} else if strings.Contains(condition, ">") {
				kv := strings.SplitN(condition, ">", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortGreater = &float64Val
					
				
				}
			} else if strings.Contains(condition, "<") {
				kv := strings.SplitN(condition, "<", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "port":
					float64Val, err := strconv.ParseFloat(value, 64)
					if err != nil {
						return fmt.Errorf("invalid float64 value for port: %v", err)
					}
					dto.PortLess = &float64Val
					
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *DatabaseConfigQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.DbHostEqual != nil {
		db = db.Where("db_host = ?", *dto.DbHostEqual)
	}
	if dto.DbHostLike != nil {
		db = db.Where("db_host ILIKE ?", "%"+*dto.DbHostLike+"%")
	}
	if dto.DbHostNotEqual != nil {
		db = db.Where("db_host != ?", *dto.DbHostNotEqual)
	}
	if dto.DbHostNotLike != nil {
		db = db.Where("db_host NOT ILIKE ?", "%"+*dto.DbHostNotLike+"%")
	}
	
	
	
	if dto.PortEqual != nil {
		db = db.Where("port = ?", *dto.PortEqual)
	}
	if dto.PortNotEqual != nil {
		db = db.Where("port != ?", *dto.PortNotEqual)
	}
	
	if dto.PortGreater != nil {
		db = db.Where("port > ?", *dto.PortGreater)
	}
	if dto.PortLess != nil {
		db = db.Where("port < ?", *dto.PortLess)
	}
	if dto.PortGreaterEqual != nil {
		db = db.Where("port >= ?", *dto.PortGreaterEqual)
	}
	if dto.PortLessEqual != nil {
		db = db.Where("port <= ?", *dto.PortLessEqual)
	}
	
	
	
	
	if dto.DbNameEqual != nil {
		db = db.Where("db_name = ?", *dto.DbNameEqual)
	}
	if dto.DbNameLike != nil {
		db = db.Where("db_name ILIKE ?", "%"+*dto.DbNameLike+"%")
	}
	if dto.DbNameNotEqual != nil {
		db = db.Where("db_name != ?", *dto.DbNameNotEqual)
	}
	if dto.DbNameNotLike != nil {
		db = db.Where("db_name NOT ILIKE ?", "%"+*dto.DbNameNotLike+"%")
	}
	
	
	
	if dto.DbUserEqual != nil {
		db = db.Where("db_user = ?", *dto.DbUserEqual)
	}
	if dto.DbUserLike != nil {
		db = db.Where("db_user ILIKE ?", "%"+*dto.DbUserLike+"%")
	}
	if dto.DbUserNotEqual != nil {
		db = db.Where("db_user != ?", *dto.DbUserNotEqual)
	}
	if dto.DbUserNotLike != nil {
		db = db.Where("db_user NOT ILIKE ?", "%"+*dto.DbUserNotLike+"%")
	}
	
	
	
	if dto.DbPwEqual != nil {
		db = db.Where("db_pw = ?", *dto.DbPwEqual)
	}
	if dto.DbPwLike != nil {
		db = db.Where("db_pw ILIKE ?", "%"+*dto.DbPwLike+"%")
	}
	if dto.DbPwNotEqual != nil {
		db = db.Where("db_pw != ?", *dto.DbPwNotEqual)
	}
	if dto.DbPwNotLike != nil {
		db = db.Where("db_pw NOT ILIKE ?", "%"+*dto.DbPwNotLike+"%")
	}
	
	
	
	if dto.EnvEqual != nil {
		db = db.Where("env = ?", *dto.EnvEqual)
	}
	if dto.EnvLike != nil {
		db = db.Where("env ILIKE ?", "%"+*dto.EnvLike+"%")
	}
	if dto.EnvNotEqual != nil {
		db = db.Where("env != ?", *dto.EnvNotEqual)
	}
	if dto.EnvNotLike != nil {
		db = db.Where("env NOT ILIKE ?", "%"+*dto.EnvNotLike+"%")
	}
	
	
	
	if dto.SpaceIdEqual != nil {
		db = db.Where("space_id = ?", *dto.SpaceIdEqual)
	}
	if dto.SpaceIdLike != nil {
		db = db.Where("space_id ILIKE ?", "%"+*dto.SpaceIdLike+"%")
	}
	if dto.SpaceIdNotEqual != nil {
		db = db.Where("space_id != ?", *dto.SpaceIdNotEqual)
	}
	if dto.SpaceIdNotLike != nil {
		db = db.Where("space_id NOT ILIKE ?", "%"+*dto.SpaceIdNotLike+"%")
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

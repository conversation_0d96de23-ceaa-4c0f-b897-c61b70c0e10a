// Code generated by pb-api-generator; DO NOT EDIT.

package dto

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"gorm.io/gorm"
)

// MockTypeQueryDTO represents query parameters for MockType
type MockTypeQueryDTO struct {
	TypeEqual *string `json:"typeequal"`
	TypeLike *string `json:"typelike"`
	TypeNotEqual *string `json:"typenotequal"`
	TypeNotLike *string `json:"typenotlike"`
	NameEqual *string `json:"nameequal"`
	NameLike *string `json:"namelike"`
	NameNotEqual *string `json:"namenotequal"`
	NameNotLike *string `json:"namenotlike"`
	DescriptionEqual *string `json:"descriptionequal"`
	DescriptionLike *string `json:"descriptionlike"`
	DescriptionNotEqual *string `json:"descriptionnotequal"`
	DescriptionNotLike *string `json:"descriptionnotlike"`
	Page    int    `json:"page"`
	PerPage int    `json:"perPage"`
	SortBy  string `json:"sortBy"`
	SortOrder string `json:"sortOrder"`
}

// FromContext populates the DTO from PocketBase-style query parameters in the HTTP request context
func (dto *MockTypeQueryDTO) FromContext(ctx context.Context, r *http.Request) error {
	query := r.URL.Query()

	// Parse page
	if pageStr := query.Get("page"); pageStr != "" {
		page, err := strconv.Atoi(pageStr)
		if err != nil {
			return fmt.Errorf("invalid page parameter: %v", err)
		}
		dto.Page = page
	}
	if dto.Page == 0 {
		dto.Page = 1
	}

	// Parse perPage
	if perPageStr := query.Get("perPage"); perPageStr != "" {
		perPage, err := strconv.Atoi(perPageStr)
		if err != nil {
			return fmt.Errorf("invalid perPage parameter: %v", err)
		}
		dto.PerPage = perPage
	}
	if dto.PerPage == 0 {
		dto.PerPage = 30
	}

	// Parse sort
	if sortStr := query.Get("sort"); sortStr != "" {
		if strings.HasPrefix(sortStr, "-") {
			dto.SortBy = sortStr[1:]
			dto.SortOrder = "DESC"
		} else {
			dto.SortBy = sortStr
			dto.SortOrder = "ASC"
		}
	}

	// Parse expand
	if expandStr := query.Get("expand"); expandStr != "" {
		expandFields := strings.Split(expandStr, ",")
		for _, field := range expandFields {
			field = strings.TrimSpace(field)
			switch field {
			
			}
		}
	}

	// Parse filter
	if filterStr := query.Get("filter"); filterStr != "" {
		decodedFilter, err := url.QueryUnescape(filterStr)
		if err != nil {
			return fmt.Errorf("failed to decode filter: %v", err)
		}
		// Remove parentheses
		decodedFilter = strings.ReplaceAll(decodedFilter, "(", "")
		decodedFilter = strings.ReplaceAll(decodedFilter, ")", "")
		// Split by '&&' for AND conditions
		conditions := strings.Split(decodedFilter, "&&")
		for _, condition := range conditions {
			condition = strings.TrimSpace(condition)
			if strings.Contains(condition, "=") {
				kv := strings.SplitN(condition, "=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "type":
					dto.TypeEqual = &value
					
				case "name":
					dto.NameEqual = &value
					
				case "description":
					dto.DescriptionEqual = &value
					
				
				}
			} else if strings.Contains(condition, "!=") {
				kv := strings.SplitN(condition, "!=", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "type":
					dto.TypeNotEqual = &value
					
				case "name":
					dto.NameNotEqual = &value
					
				case "description":
					dto.DescriptionNotEqual = &value
					
				
				}
			} else if strings.Contains(condition, "~") {
				kv := strings.SplitN(condition, "~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "type":
					dto.TypeLike = &value
				case "name":
					dto.NameLike = &value
				case "description":
					dto.DescriptionLike = &value
				
				}
			} else if strings.Contains(condition, "!~") {
				kv := strings.SplitN(condition, "!~", 2)
				if len(kv) != 2 {
					continue
				}
				key := strings.TrimSpace(kv[0])
				value := strings.Trim(strings.TrimSpace(kv[1]), "'\"")
				switch key {
				case "type":
					dto.TypeNotLike = &value
				case "name":
					dto.NameNotLike = &value
				case "description":
					dto.DescriptionNotLike = &value
				
				}
			}
			
		}
	}

	return nil
}

// ToGORMQuery builds a GORM query based on the DTO fields
func (dto *MockTypeQueryDTO) ToGORMQuery(db *gorm.DB) (*gorm.DB, error) {
	
	
	if dto.TypeEqual != nil {
		db = db.Where("type = ?", *dto.TypeEqual)
	}
	if dto.TypeLike != nil {
		db = db.Where("type ILIKE ?", "%"+*dto.TypeLike+"%")
	}
	if dto.TypeNotEqual != nil {
		db = db.Where("type != ?", *dto.TypeNotEqual)
	}
	if dto.TypeNotLike != nil {
		db = db.Where("type NOT ILIKE ?", "%"+*dto.TypeNotLike+"%")
	}
	
	
	
	if dto.NameEqual != nil {
		db = db.Where("name = ?", *dto.NameEqual)
	}
	if dto.NameLike != nil {
		db = db.Where("name ILIKE ?", "%"+*dto.NameLike+"%")
	}
	if dto.NameNotEqual != nil {
		db = db.Where("name != ?", *dto.NameNotEqual)
	}
	if dto.NameNotLike != nil {
		db = db.Where("name NOT ILIKE ?", "%"+*dto.NameNotLike+"%")
	}
	
	
	
	if dto.DescriptionEqual != nil {
		db = db.Where("description = ?", *dto.DescriptionEqual)
	}
	if dto.DescriptionLike != nil {
		db = db.Where("description ILIKE ?", "%"+*dto.DescriptionLike+"%")
	}
	if dto.DescriptionNotEqual != nil {
		db = db.Where("description != ?", *dto.DescriptionNotEqual)
	}
	if dto.DescriptionNotLike != nil {
		db = db.Where("description NOT ILIKE ?", "%"+*dto.DescriptionNotLike+"%")
	}
	
	

	// Apply sorting
	if dto.SortBy != "" {
		if dto.SortOrder == "DESC" {
			db = db.Order(fmt.Sprintf("%s DESC", dto.SortBy))
		} else {
			db = db.Order(dto.SortBy)
		}
	}

	// Apply pagination
	if dto.PerPage > 0 {
		db = db.Limit(dto.PerPage)
		if dto.Page > 0 {
			offset := (dto.Page - 1) * dto.PerPage
			db = db.Offset(offset)
		}
	}

	return db, nil
}

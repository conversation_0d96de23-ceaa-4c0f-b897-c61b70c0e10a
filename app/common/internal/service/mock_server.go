package service

import (
	"context"
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/mock"
)

type MockServer struct{}

func (MockServer) GetMockPreview(input string) MockResponse {
	response, err := mock.Mock(input)
	if err != nil {
		return MockResponse{
			Success: false,
			Data:    "mock 错误",
		}
	}
	logger.Logger.Debugf("mock返回数据:%v", response)
	return MockResponse{
		Success: true,
		Data:    fmt.Sprint(response),
	}
}

func (MockServer) GetMockParamDataTree() (*dto.MockTree, error) {
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	list := make([]models.MockType, 0)
	err := gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MockType{}), &list)
	if err != nil {
		return nil, err
	}
	root := dto.MockTree{}
	types := make([]string, 0)
	typeNameMap := make(map[string][]string)
	nameMockTypesMap := make(map[string][]models.MockType)
	for _, mockType := range list {
		if _, ok := typeNameMap[mockType.Type]; !ok {
			types = append(types, mockType.Type)
			typeNameMap[mockType.Type] = make([]string, 0)
		}
		if len(typeNameMap[mockType.Type]) == 0 || typeNameMap[mockType.Type][len(typeNameMap[mockType.Type])-1] != mockType.Name {
			typeNameMap[mockType.Type] = append(typeNameMap[mockType.Type], mockType.Name)
		}
		if _, ok := nameMockTypesMap[mockType.Name]; !ok {
			nameMockTypesMap[mockType.Name] = make([]models.MockType, 0)
		}
		nameMockTypesMap[mockType.Name] = append(nameMockTypesMap[mockType.Name], mockType)
	}
	children := make([]dto.MockTree, 0)
	for _, t := range types {
		folder := dto.MockTree{Name: t}
		names := typeNameMap[t]
		ch := make([]dto.MockTree, 0)
		for _, name := range names {
			f := dto.MockTree{Name: name}
			f.Children = nameMockTypesMap[name]
			ch = append(ch, f)
		}
		folder.Children = ch
		children = append(children, folder)
	}
	root.Children = children
	return (&root), nil
}

type MockResponse struct {
	Success bool   `json:"success"`
	Data    string `json:"data"`
}

package service

import (
	"crypto/md5"
	"fmt"
	"mime/multipart"
	"path"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

type FileService struct{}

const rootPath = "mantis/upload"

func (s *FileService) UploadFile(fileHeader *multipart.FileHeader) (string, error) {
	return s.UploadFileBase(fileHeader, "")
}

func (s *FileService) UploadFileBase(fileHeader *multipart.FileHeader, prePath string) (string, error) {
	if prePath == "" {
		prePath = rootPath
	} else {
		prePath = path.Join(rootPath, prePath)
	}
	date := time.Now().Format("20060102")
	filename := fileHeader.Filename
	file, err := fileHeader.Open()
	if err != nil {
		logger.Logger.Errorf("文件打开失败!err=%v", err)
		return "", err
	}
	defer file.Close()
	bytes := make([]byte, fileHeader.Size)
	file.Read(bytes)

	fileMd5 := fmt.Sprintf("%x", md5.Sum(bytes))
	objectKey := path.Join(prePath, date, fileMd5, filename)
	// 文件指针回到原位
	file.Seek(0, 0)
	s3store.UploadFileWithReader(file, objectKey, "application/octet-stream")
	return objectKey, nil
}

func (s *FileService) DownloadFile(objectKey string) ([]byte, error) {
	content, err := s3store.GetFile(objectKey)
	if err != nil {
		logger.Logger.Errorf("文件下载失败!err=%v", err)
		return nil, err
	}
	return content, nil
}

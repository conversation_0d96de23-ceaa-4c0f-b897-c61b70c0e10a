package service

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/remote"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

type UserService struct{}

var cubeBaseRemoteApi remote.CubeBaseRemoteApi

func (UserService) GetChildResource(userId string, ctx *commoncontext.MantisContext) []dto.CubeResourceDTO {
	resource := make([]dto.CubeResourceDTO, 0)
	companyId := utils.IDString(ctx.User.CompanyID)
	resource = append(resource, cubeBaseRemoteApi.GetChildResource(userId, companyId, "lochness", "MANTIS")...)
	resource = append(resource, cubeBaseRemoteApi.GetChildResource(userId, companyId, "lochness", "codeup")...)
	return resource
}

package openapi

import (
	"encoding/base64"
	"fmt"
	"mime/multipart"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/service"
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

var fileService service.FileService

// UploadFile 上传文件,并返回访问连接
func UploadFile(file *multipart.FileHeader, prePath string) (string, error) {
	objkey, err := fileService.UploadFileBase(file, prePath)
	if err != nil {
		return "", err
	}
	base64Key := base64.StdEncoding.EncodeToString([]byte(objkey))
	url := fmt.Sprintf("%s/common/api/v1/biz/file/download?obj_key=%s", configs.Config.Domain.Cube, base64Key)
	logger.Logger.Infof("上传文件的访问连接: %s", url)
	return url, nil
}

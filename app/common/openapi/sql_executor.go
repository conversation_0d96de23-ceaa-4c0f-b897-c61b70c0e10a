package openapi

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/types"
	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
)

type SqlExecutor struct{}

func (s SqlExecutor) Execute(dbCfgId string, query string) (*sql.Rows, error) {
	dbcfg := &models.DatabaseConfig{}
	dbcfg.Id = dbCfgId
	dbcfg.IsDeleted = false
	err := gormx.SelectOneByCondition(&commoncontext.MantisContext{Context: context.Background()}, dbcfg)
	if err != nil {
		return nil, err
	}
	dsn, err := s.dealDsh(dbcfg)
	if err != nil {
		return nil, err
	}
	switch dbcfg.DbType {
	case types.MySQL:
		return s.executeMysql(dsn, query)
	case types.Postgres:
		return s.executePg(dsn, query)
	default:
		return nil, fmt.Errorf("不支持的数据库类型%s", dbcfg.DbType)
	}
}

func (s SqlExecutor) TestConn(dbCfg *models.DatabaseConfig) error {
	dsn, err := s.dealDsh(dbCfg)
	if err != nil {
		return err
	}
	db, err := sql.Open(string(dbCfg.DbType), dsn)
	if err != nil {
		return err
	}
	defer db.Close()
	err = db.Ping()
	if err != nil {
		return err
	}
	return nil
}

func (s SqlExecutor) dealDsh(dbCfg *models.DatabaseConfig) (string, error) {
	switch dbCfg.DbType {
	case types.MySQL:
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s", dbCfg.DbUser, dbCfg.DbPw, dbCfg.DbHost, int64(dbCfg.Port), dbCfg.DbName)
		return dsn, nil
	case types.Postgres:
		dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=disable", dbCfg.DbHost, int64(dbCfg.Port), dbCfg.DbUser, dbCfg.DbPw, dbCfg.DbName)
		return dsn, nil
	default:
		return "", fmt.Errorf("不支持的数据库类型%s", dbCfg.DbType)
	}
}

func (SqlExecutor) executeMysql(dsn string, query string) (*sql.Rows, error) {
	if !(strings.HasPrefix(query, "select") || strings.HasPrefix(query, "SELECT")) {
		return nil, errors.New("非查询语句, 不予执行")
	}
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}
	defer db.Close()
	err = db.Ping()
	if err != nil {
		return nil, err
	}
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	return rows, nil
}

func (SqlExecutor) executePg(dsn string, query string) (*sql.Rows, error) {
	if !(strings.HasPrefix(query, "select") || strings.HasPrefix(query, "SELECT")) {
		return nil, errors.New("非查询语句, 不予执行")
	}
	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}
	defer db.Close()
	err = db.Ping()
	if err != nil {
		return nil, err
	}
	rows, err := db.Query(query)
	if err != nil {
		return nil, err
	}
	return rows, nil
}

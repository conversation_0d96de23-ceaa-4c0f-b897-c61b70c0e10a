package openapi

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type DatasourceOpenapi struct{}

func (DatasourceOpenapi) GetData(id string) ([]map[string]any, error) {
	datasource := &models.DataSource{}
	datasource.Id = id
	datasource.IsDeleted = false
	err := gormx.SelectOneByCondition(&commoncontext.MantisContext{Context: context.Background()}, datasource)
	if err != nil {
		return nil, err
	}
	return datasource.Data, nil
}

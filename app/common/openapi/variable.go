package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type VariableOpenapi struct{}

func (VariableOpenapi) GetAllVar(ctx *commoncontext.MantisContext, spaceId, env string) (map[string]string, error) {
	var res []models.Variable
	sql := `select name,value from variable where (var_type ='global' and space_id=? and is_deleted=false) or 
                                      (env =? and space_id=? and is_deleted=false) order by env`
	err := gormx.Raw(ctx, sql, &res, spaceId, env, spaceId)
	if err != nil {
		return nil, err
	}
	m := make(map[string]string)
	for _, v := range res {
		m[v.Name] = v.Value
	}
	return m, nil
}

package openapi

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/app/common/internal/models"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
)

type MockTypeOpenapi struct{}

func (s MockTypeOpenapi) GetMockList(ctx *commoncontext.MantisContext) ([]models.MockType, error) {
	res := make([]models.MockType, 0)
	err := gormx.SelectByParamBuilder(ctx, gormx.NewParamBuilder().Model(&models.MockType{}).Eq("is_deleted", false), &res)
	return res, err
}

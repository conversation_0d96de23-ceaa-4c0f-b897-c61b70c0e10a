package web

import (
	"fmt"
	"net/url"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

func EncodeUrl(rawUrl string) string {
	parsedURL, err := url.Parse(rawUrl)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "url 解析错误"))
	}
	// 对路径进行编码
	encodedPath := ""
	split := strings.Split(parsedURL.Path, "/")
	for _, s := range split {
		encodedPath += url.PathEscape(s) + "/"
	}
	if len(encodedPath) != 0 {
		encodedPath = encodedPath[:len(encodedPath)-1]
	}
	// 对查询参数进行编码
	encodedQuery := ""
	for k, v := range parsedURL.Query() {
		val := ""
		if len(v) != 0 {
			for _, v1 := range v {
				val += url.QueryEscape(v1)
			}
		}
		encodedQuery += fmt.Sprintf("%s=%s", url.QueryEscape(k), val)
	}
	// 重新组合编码后的 URL
	encodedURL := fmt.Sprintf("%s://%s%s?%s", parsedURL.Scheme, parsedURL.Host, encodedPath, encodedQuery)
	return encodedURL
}

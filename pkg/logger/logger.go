package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/log"
	"github.com/sirupsen/logrus"
)

var (
	Logger              *logrus.Logger
	defaultRotateOption = log.DefaultRotateOption
)

func Init() {
	level := logrus.InfoLevel
	switch configs.Config.Log.Level {
	case "debug":
		level = logrus.DebugLevel
	case "info":
		level = logrus.InfoLevel
	case "warn":
		level = logrus.WarnLevel
	case "error":
		level = logrus.ErrorLevel
	case "panic":
		level = logrus.PanicLevel
	}
	Logger = logrus.StandardLogger()
	setup(configs.Config.Log.Path, configs.Config.App.Name, level, defaultRotateOption, Logger)
}

func setup(dir string, app string, level logrus.Level, opt log.RotateOption, logger *logrus.Logger) {
	logger.SetLevel(level)

	writers := []io.Writer{os.Stderr}
	levelSplitWriter := log.NewLevelSplitWriter()
	if dir != "" {
		seraphErrLogFile := log.SeraphErrLogFilename(dir, app)
		option := opt
		option.LinkName = seraphErrLogFile
		if out, err := log.NewRotateLogger(option); err != nil {
			fmt.Fprintf(os.Stderr, "NewRotateLogger %s failed:%v\n", option.LinkName, err)
		} else {
			writers = append(writers, out)
		}
	}
	levelSplitWriter.AddWriter(io.MultiWriter(writers...), logrus.ErrorLevel, logrus.FatalLevel, logrus.PanicLevel)

	writers = []io.Writer{os.Stdout}
	if dir != "" {
		n, _ := os.Hostname()
		option := opt
		option.LinkName = filepath.Join(dir, fmt.Sprintf("%s-%s.log", app, n))
		if out, err := log.NewRotateLogger(option); err != nil {
			fmt.Fprintf(os.Stderr, "NewRotateLogger %s failed:%v\n", option.LinkName, err)
		} else {
			writers = append(writers, out)
		}
	}
	levelSplitWriter.AddWriter(io.MultiWriter(writers...), logrus.WarnLevel, logrus.InfoLevel, logrus.DebugLevel)

	logger.SetReportCaller(true)
	logger.AddHook(levelSplitWriter)
	formatter := newSeraphFormatter(&logrus.TextFormatter{})
	logger.SetFormatter(formatter)
}

package logger

import (
	"bytes"
	"fmt"
	"path/filepath"
	"strings"
	"sync"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/tracing"
	"github.com/sirupsen/logrus"
)

type SeraphFormatter struct {
	pool      sync.Pool
	formatter logrus.Formatter
}

var (
	levelMap = map[logrus.Level]string{
		logrus.PanicLevel: "PANIC",
		logrus.FatalLevel: "FATAL",
		logrus.ErrorLevel: "ERROR",
		logrus.WarnLevel:  "WARN",
		logrus.InfoLevel:  "INFO",
		logrus.DebugLevel: "DEBUG",
	}
	pkgs = []string{"mercury", "neptune", "mars", "common", "pkg"}
)

func newSeraphFormatter(formatter logrus.Formatter) *SeraphFormatter {
	if formatter == nil {
		formatter = &logrus.JSONFormatter{
			DisableTimestamp: true,
		}
	}

	return &SeraphFormatter{
		pool: sync.Pool{
			New: func() interface{} {
				return &bytes.Buffer{}
			},
		},
		formatter: formatter,
	}
}

func shortFuncName(f string) string {
	if f == "" {
		return "'"
	}
	i := strings.LastIndex(f, ".")
	return f[i+1:]
}

func (s *SeraphFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	sb := s.pool.Get().(*bytes.Buffer)
	defer func() {
		sb.Reset()
		s.pool.Put(sb)
	}()
	sb.WriteString(entry.Time.Format("2006-01-02 15:04:05,999"))
	for _, s := range pkgs {
		if strings.Contains(entry.Caller.File, s) {
			sb.WriteString(fmt.Sprintf(" [%s]", s))
			break
		}
	}
	sb.WriteString(fmt.Sprintf(" %s", levelMap[entry.Level]))
	if entry.Caller != nil {
		sb.WriteString(fmt.Sprintf(" [%s]", shortFuncName(entry.Caller.Function)))
		_, f := filepath.Split(entry.Caller.File)
		sb.WriteString(fmt.Sprintf(" [%s:%d]", f, entry.Caller.Line))
		entry.Caller = nil
	}

	if entry.Context != nil {
		if traceID := tracing.UberTraceID(entry.Context); traceID != "" {
			sb.WriteString(fmt.Sprintf(" [trace=%s,span=,parent=]", traceID))
		}
	}
	sb.WriteString(" - ")
	sb.WriteString(entry.Message + "\n")
	return sb.Bytes(), nil
}

package xmlx

import (
	"encoding/xml"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/antchfx/xmlquery"
	"github.com/pkg/errors"
)

func Marshal(data any) []byte {
	bytes, err := xml.Marshal(data)
	if err != nil {
		logger.Logger.Panicf("error in marshal %v", data)
	}
	return bytes
}

func UnMarshal(data []byte, v any) {
	err := xml.Unmarshal(data, v)
	if err != nil {
		logger.Logger.Panicf("error in unmarshal %v", data)
	}
}

func GetDataByXpath(xmlRaw string, xpath string) (any, error) {
	parse, err := xmlquery.Parse(strings.NewReader(xmlRaw))
	if err != nil {
		return nil, err
	}
	nodes, err := xmlquery.QueryAll(parse, xpath)
	if err != nil || nodes == nil {
		return nil, errors.New("error path")
	}
	res := ""
	for _, node := range nodes {
		split := strings.Split(node.InnerText(), "\n")
		for _, s := range split {
			res += strings.TrimSpace(s)
		}
	}
	return res, nil
}

package goroutine

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"golang.org/x/sync/semaphore"
)

var w *semaphore.Weighted

// Init 初始化全局协程限制
func Init() {
	logger.Logger.Info("初始化全局协程控制")
	maxRoutines := 50 // 默认值
	if configs.Config.App.MaxRoutine >= 1 {
		maxRoutines = configs.Config.App.MaxRoutine
	}
	w = semaphore.NewWeighted(int64(maxRoutines))
}

// 通用的协程恢复函数
func safeRecover(f func()) {
	if e := recover(); e != nil {
		logger.Logger.Errorf("%v", e)
	}
	f()
}

// Run 运行任务，带全局协程数量控制，不会同步等待
// 如果panic，会进行recover但不返回错误。如果需要感知协程内部是否panic，请自行在task里进行recover
func Run(task func()) {
	_ = w.Acquire(context.TODO(), 1)
	go func() {
		defer safeRecover(func() {
			w.Release(1)
		})
		task()
	}()
}

// RunX 运行可返回错误的任务，带全局协程数量控制，不会同步等待
// 如果panic，会进行recover但不返回错误。如果需要感知协程内部是否panic，请自行在task里进行recover
func RunX(task func() error) {
	_ = w.Acquire(context.TODO(), 1)
	go func() {
		defer safeRecover(func() {
			w.Release(1)
		})
		if err := task(); err != nil {
			logger.Logger.Error(err)
		}
	}()
}

// RunWithoutLimit 运行任务，没有全局协程数量控制，不会同步等待
// 如果panic，会进行recover但不返回错误
func RunWithoutLimit(task func()) {
	go func() {
		defer safeRecover(func() {})
		task()
	}()
}

// runTasksWithSemaphore 运行任务列表的内部实现，支持不同信号量
func runTasksWithSemaphore(tasks []func(), sem *semaphore.Weighted) error {
	logger.Logger.Debugf("共%d个任务需要执行", len(tasks))
	wg := sync.WaitGroup{}
	wg.Add(len(tasks))
	success := true

	for i, task := range tasks {
		taskIndex := i
		taskFunc := task

		go func() {
			defer func() {
				if e := recover(); e != nil {
					logger.Logger.Error(xerror.Wrap(fmt.Errorf("%v", e), "error in async task"))
					success = false
				}
				wg.Done()
				if sem != nil {
					sem.Release(1)
				}
				logger.Logger.Debugf("第%d个任务结束执行", taskIndex)
			}()

			if sem != nil {
				_ = sem.Acquire(context.TODO(), 1)
				logger.Logger.Debugf("第%d个任务开始执行", taskIndex)
			}

			taskFunc()
		}()
	}

	wg.Wait()
	if !success {
		return errors.New("error in async tasks")
	}
	return nil
}

// RunTasks 运行任务列表，有全局协程数量控制，会同步等待
// 如果任务中的某一个panic，会返回一个不为空的err
func RunTasks(tasks []func()) error {
	return runTasksWithSemaphore(tasks, w)
}

// RunTasksX 运行可返回错误的任务列表，有全局协程数量控制，会同步等待
// 如果任务中的某一个panic或返回错误，会返回一个不为空的err
func RunTasksX(tasks []func() error) error {
	wrappedTasks := make([]func(), len(tasks))
	success := true

	for i, task := range tasks {
		taskFunc := task
		wrappedTasks[i] = func() {
			if err := taskFunc(); err != nil {
				logger.Logger.Errorf("error in async task, err=%s", err.Error())
				success = false
			}
		}
	}

	err := RunTasks(wrappedTasks)
	if err != nil || !success {
		return errors.New("error in async tasks")
	}
	return nil
}

// RunTasksWithoutLimit 运行任务列表，无全局协程数量控制，会同步等待
// 如果任务中的某一个panic，会返回一个不为空的err
func RunTasksWithoutLimit(tasks []func()) error {
	return runTasksWithSemaphore(tasks, nil)
}

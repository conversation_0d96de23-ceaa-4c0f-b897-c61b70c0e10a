package goroutine

import (
	"context"
	"fmt"
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"golang.org/x/sync/semaphore"
)

var (
	limitCount = 0
	mux        = sync.Mutex{}
)

// Limit
/*
在全局协程限制下继续对某一个模块的任务进行限制。应该在应用初始化时进行初始化，而不是在运行中动态初始化。
原因是因为协程数不足而初始化可能报错，如果自行实现了初始化报错后等待协程数量放开的逻辑则可以在运行时使用。
*/
type Limit struct {
	limit    int
	wg       sync.WaitGroup
	weighted *semaphore.Weighted
}

// NewLimit 创建新的协程限制器
func NewLimit(limit int) (*Limit, error) {
	mux.Lock()
	defer mux.Unlock()
	if configs.Config.App.MaxRoutine-limitCount < limit {
		return nil, xerror.New("没有足够的协程数")
	}
	limitCount += limit
	return &Limit{
		limit:    limit,
		wg:       sync.WaitGroup{},
		weighted: semaphore.NewWeighted(int64(limit)),
	}, nil
}

// RunTasks 运行任务列表，有协程数量控制，会同步等待。
// 如果panic，会进行recover并且打印。如果任务中的某一个panic，会返回一个不为空的err。
// 如果有不可估计的大量任务的情况下，会构建一个大数组，占用大量内存，不建议使用
func (l *Limit) RunTasks(tasks []func()) error {
	logger.Logger.Debugf("共%d个任务需要执行", len(tasks))
	l.wg.Add(len(tasks))
	success := true
	for i, task := range tasks {
		taskIndex := i
		taskFunc := task
		go func() {
			defer func() {
				if e := recover(); e != nil {
					// 某个服务调用协程报错，可以在这里打印一些错误日志
					logger.Logger.Error(xerror.New(fmt.Sprintf("%v", e)), "error in async task")
					success = false
				}
				l.wg.Done()
				// 解放信号量
				l.weighted.Release(1)
				logger.Logger.Debugf("第%d个任务结束执行", taskIndex)
			}()
			_ = l.weighted.Acquire(context.TODO(), 1) // 从总协程数量令牌池中获取，到极限就阻塞
			logger.Logger.Debugf("第%d个任务开始执行", taskIndex)
			taskFunc()
		}()
	}
	l.wg.Wait()
	if !success {
		return xerror.New("error in async tasks")
	}
	return nil
}

// Destroy 释放协程限制器占用的资源
// 手动初始化的limit必须手动destroy
func (l *Limit) Destroy() {
	mux.Lock()
	defer mux.Unlock()
	limitCount -= l.limit
}

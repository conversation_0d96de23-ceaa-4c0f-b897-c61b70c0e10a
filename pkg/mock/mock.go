package mock

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/ddosify/go-faker/faker"
)

func Mock(input string) (string, error) {
	keys := extractRandomStrings(input)
	client := faker.NewFaker()
	output := input
	for _, k := range keys {
		v, err := getFakeData(client, k[3:len(k)-2])
		if err != nil {
			return "", err
		}
		output = strings.ReplaceAll(output, k, fmt.Sprintf("%v", v))
	}
	return output, nil
}

func extractRandomStrings(input string) []string {
	// 匹配{{_任意字符}}格式
	re := regexp.MustCompile(`{{_\w+}}`)
	matches := re.FindAllString(input, -1)

	return matches
}

func getFakeData(client faker.Faker, key string) (any, error) {
	var res any
	switch key {
	// Common
	case "guid":
		res = client.RandomGuid()
	case "timestamp":
		res = client.CurrentTimestamp()
	case "isoTimestamp":
		res = client.CurrentISOTimestamp()
	case "randomUUID":
		res = client.RandomUUID()
	// Text() numbers() and colors
	case "randomAlphaNumeric":
		res = client.RandomAlphanumeric()
	case "randomBoolean":
		res = client.RandomBoolean()
	case "randomInt":
		res = client.RandomInt()
	case "randomColor":
		res = client.RandomSafeColorName()
	case "randomHexColor":
		res = client.RandomSafeColorHex()
	case "randomAbbreviation":
		res = client.RandomAbbreviation()
	// Internet and IP addresses
	case "randomIP":
		res = client.RandomIP()
	case "randomIPV6":
		res = client.RandomIpv6()
	case "randomMACAddress":
		res = client.RandomMACAddress()
	case "randomPassword":
		res = client.RandomPassword()
	case "randomLocale":
		res = client.RandomLocale()
	case "randomUserAgent":
		res = client.RandomUserAgent()
	case "randomProtocol":
		res = client.RandomProtocol()
	case "randomSemver":
		res = client.RandomSemver()
	// Names
	case "randomFirstName":
		res = client.RandomPersonFirstName()
	case "randomLastName":
		res = client.RandomPersonLastName()
	case "randomFullName":
		res = client.RandomPersonFullName()
	case "randomNamePrefix":
		res = client.RandomPersonNamePrefix()
	case "randomNameSuffix":
		res = client.RandomPersonNameSuffix()
	// Profession
	case "randomJobArea":
		res = client.RandomJobArea()
	case "randomJobDescriptor":
		res = client.RandomJobDescriptor()
	case "randomJobTitle":
		res = client.RandomJobTitle()
	case "randomJobType":
		res = client.RandomJobType()
	// Phone() address() and location
	case "randomPhoneNumber":
		res = client.RandomPhoneNumber()
	case "randomPhoneNumberExt":
		res = client.RandomPhoneNumberExt()
	case "randomCity":
		res = client.RandomAddressCity()
	case "randomStreetName":
		res = client.RandomAddresStreetName()
	case "randomStreetAddress":
		res = client.RandomAddressStreetAddress()
	case "randomCountry":
		res = client.RandomAddressCountry()
	case "randomCountryCode":
		res = client.RandomCountryCode()
	case "randomLatitude":
		res = client.RandomAddressLatitude()
	case "randomLongitude":
		res = client.RandomAddressLongitude()
	// Images
	case "randomAvatarImage":
		res = client.RandomAvatarImage()
	case "randomImageUrl":
		res = client.RandomImageURL()
	case "randomAbstractImage":
		res = client.RandomAbstractImage()
	case "randomAnimalsImage":
		res = client.RandomAnimalsImage()
	case "randomBusinessImage":
		res = client.RandomBusinessImage()
	case "randomCatsImage":
		res = client.RandomCatsImage()
	case "randomCityImage":
		res = client.RandomCityImage()
	case "randomFoodImage":
		res = client.RandomFoodImage()
	case "randomNightlifeImage":
		res = client.RandomNightlifeImage()
	case "randomFashionImage":
		res = client.RandomFashionImage()
	case "randomPeopleImage":
		res = client.RandomPeopleImage()
	case "randomNatureImage":
		res = client.RandomNatureImage()
	case "randomSportsImage":
		res = client.RandomSportsImage()
	case "randomTransportImage":
		res = client.RandomTransportImage()
	case "randomImageDataUri":
		res = client.RandomDataImageUri()
	// Finance
	case "randomBankAccount":
		res = client.RandomBankAccount()
	case "randomBankAccountName":
		res = client.RandomBankAccountName()
	case "randomCreditCardMask":
		res = client.RandomCreditCardMask()
	case "randomBankAccountBic":
		res = client.RandomBankAccountBic()
	case "randomBankAccountIban":
		res = client.RandomBankAccountIban()
	case "randomTransactionType":
		res = client.RandomTransactionType()
	case "randomCurrencyCode":
		res = client.RandomCurrencyCode()
	case "randomCurrencyName":
		res = client.RandomCurrencyName()
	case "randomCurrencySymbol":
		res = client.RandomCurrencySymbol()
	case "randomBitcoin":
		res = client.RandomBitcoin()
	// Business
	case "randomCompanyName":
		res = client.RandomCompanyName()
	case "randomCompanySuffix":
		res = client.RandomCompanySuffix()
	case "randomBs":
		res = client.RandomBs()
	case "randomBsAdjective":
		res = client.RandomBsAdjective()
	case "randomBsBuzz":
		res = client.RandomBsBuzzWord()
	case "randomBsNoun":
		res = client.RandomBsNoun()
	// Catchphrases
	case "randomCatchPhrase":
		res = client.RandomCatchPhrase()
	case "randomCatchPhraseAdjective":
		res = client.RandomCatchPhraseAdjective()
	case "randomCatchPhraseDescriptor":
		res = client.RandomCatchPhraseDescriptor()
	case "randomCatchPhraseNoun":
		res = client.RandomCatchPhraseNoun()
	// Databases
	case "randomDatabaseColumn":
		res = client.RandomDatabaseColumn()
	case "randomDatabaseType":
		res = client.RandomDatabaseType()
	case "randomDatabaseCollation":
		res = client.RandomDatabaseCollation()
	case "randomDatabaseEngine":
		res = client.RandomDatabaseEngine()
	// Dates
	case "randomDateFuture":
		res = client.RandomDateFuture()
	case "randomDatePast":
		res = client.RandomDatePast()
	case "randomDateRecent":
		res = client.RandomDateRecent()
	case "randomWeekday":
		res = client.RandomWeekday()
	case "randomMonth":
		res = client.RandomMonth()
	// Domains() emails() and usernames
	case "randomDomainName":
		res = client.RandomDomainName()
	case "randomDomainSuffix":
		res = client.RandomDomainSuffix()
	case "randomDomainWord":
		res = client.RandomDomainWord()
	case "randomEmail":
		res = client.RandomEmail()
	case "randomExampleEmail":
		res = client.RandomExampleEmail()
	case "randomUserName":
		res = client.RandomUsername()
	case "randomUrl":
		res = client.RandomUrl()
	// Files and directories
	case "randomFileName":
		res = client.RandomFileName()
	case "randomFileType":
		res = client.RandomFileType()
	case "randomFileExt":
		res = client.RandomFileExtension()
	case "randomCommonFileName":
		res = client.RandomCommonFileName()
	case "randomCommonFileType":
		res = client.RandomCommonFileType()
	case "randomCommonFileExt":
		res = client.RandomCommonFileExtension()
	case "randomFilePath":
		res = client.RandomFilePath()
	case "randomDirectoryPath":
		res = client.RandomDirectoryPath()
	case "randomMimeType":
		res = client.RandomMimeType()
	// Stores
	case "randomPrice":
		res = client.RandomPrice()
	case "randomProduct":
		res = client.RandomProduct()
	case "randomProductAdjective":
		res = client.RandomProductAdjective()
	case "randomProductMaterial":
		res = client.RandomProductMaterial()
	case "randomProductName":
		res = client.RandomProductName()
	case "randomDepartment":
		res = client.RandomDepartment()
	// Grammar
	case "randomNoun":
		res = client.RandomNoun()
	case "randomVerb":
		res = client.RandomVerb()
	case "randomIngverb":
		res = client.RandomIngVerb()
	case "randomAdjective":
		res = client.RandomAdjective()
	case "randomWord":
		res = client.RandomWord()
	case "randomWords":
		res = client.RandomWords()
	case "randomPhrase":
		res = client.RandomPhrase()
	// Lorem ipsum
	case "randomLoremWord":
		res = client.RandomLoremWord()
	case "randomLoremWords":
		res = client.RandomLoremWords()
	case "randomLoremSentence":
		res = client.RandomLoremSentence()
	case "randomLoremSentences":
		res = client.RandomLoremSentences()
	case "randomLoremParagraph":
		res = client.RandomLoremParagraph()
	case "randomLoremParagraphs":
		res = client.RandomLoremParagraphs()
	case "randomLoremText":
		res = client.RandomLoremText()
	case "randomLoremSlug":
		res = client.RandomLoremSlug()
	case "randomLoremLines":
		res = client.RandomLoremLines()
	/*
	* Specific to us.
	 */
	case "randomFloat":
		res = client.RandomFloat()
	case "randomString":
		res = client.RandomString()
	default:
		return nil, fmt.Errorf("unkown key: %s", key)
	}
	return res, nil
}

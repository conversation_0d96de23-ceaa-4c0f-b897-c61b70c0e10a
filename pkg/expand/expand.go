package expand

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/services/client"
)

var expandMap = make(map[string]func(ctx context.Context, ids []string) (map[string]any, error))

func GetExpandFunc(key string) (func(ctx context.Context, ids []string) (map[string]any, error), bool) {
	res, ok := expandMap[key]
	return res, ok
}

func SetExpandFunc(key string, val func(ctx context.Context, ids []string) (map[string]any, error)) {
	expandMap[key] = val
}

func Init() {
	// 写入查询env的Expand
	SetExpandFunc("env", func(ctx context.Context, ids []string) (map[string]any, error) {
		mantisCtx, ok := ctx.(*commoncontext.MantisContext)
		if !ok {
			return nil, fmt.Errorf("error transfer context to mantis context")
		}
		startTime := time.Now()
		attrList := []string{}
		param := client.NewParam().WithQueries("top", fmt.Sprintf("%v", cmdb.DefaultInstance9999Top))
		if len(attrList) > 0 {
			param.Query().Add("attrs", strings.Join(attrList, "|"))
		}
		list, err := cmdb.GetDefaultCMDB().GetBaseEnvList(ctx, mantisCtx.User.CompanyID)
		if err != nil {
			return nil, err
		}
		elapsedTime := time.Since(startTime).Milliseconds() // 计算耗时
		logger.Logger.Infof("get envs length: %v,cost: %d ms", len(list), elapsedTime)
		res := make(map[string]any)
		for _, e := range list {
			res[string(e.InstanceBizID)] = e
		}
		return res, nil
	})

	// 写入查询app的Expand
	SetExpandFunc("app", func(ctx context.Context, appIds []string) (map[string]any, error) {
		if len(appIds) == 0 {
			logger.Logger.Infof("appIds is empty! return empty map")
			return map[string]any{}, nil
		}
		startTime := time.Now()
		logger.Logger.Infof("start get app by ids: %v", appIds)
		models := make(map[string]any)
		filter := ""
		for _, appId := range appIds {
			filter += fmt.Sprintf("id='%s'||", appId)
		}
		filter = filter[:len(filter)-2]
		records, err := cmdb.GetDefaultCMDB().GetAppList(ctx, url.Values{
			"filter": []string{filter},
		})
		if err != nil {
			return nil, err
		}
		for _, record := range records {
			models[utils.IDString(record.AppId)] = map[string]any{
				"id":           record.AppId,
				"chinese_name": record.ChineseName,
				"company_code": record.CompanyCode,
				"company_id":   record.CompanyId,
				"company_name": record.CompanyName,
				"code":         record.Name,
			}
		}
		elapsedTime := time.Since(startTime).Milliseconds() // 计算耗时
		logger.Logger.Infof("end get app length: %v,cost: %d ms", len(models), elapsedTime)
		return models, nil
	})
}

package common_util

import (
	"container/list"
	"fmt"
	"io"
	"mime/multipart"
	"strconv"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/models"

	"github.com/duke-git/lancet/v2/xerror"
)

func StringToInt64List(s string, sep string) []int64 {
	intList := make([]int64, 0)
	split := strings.Split(s, sep)
	for _, s := range split {
		i, err := strconv.ParseInt(s, 10, 64)
		if err != nil {
			logger.Logger.Panic("error in convert string to int64 list")
		}
		intList = append(intList, i)
	}
	return intList
}

func ReadFileToString(file *multipart.FileHeader) string {
	f, err := file.Open()
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in open file"))
	}
	defer f.Close()
	var dataBlock []byte
	buf := make([]byte, 102400)
	for {
		// 从file读取到buf中
		n, err := f.Read(buf)
		if err != nil {
			if err == io.EOF {
				// 判断文件读取结束
				break
			}
			logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in read file"))
		}
		dataBlock = append(dataBlock, buf[:n]...)
	}
	return string(dataBlock)
}

func ListContain(li *list.List, value any) bool {
	if li.Len() == 0 {
		return false
	}
	for i := li.Front(); i != nil; i = i.Next() {
		if i.Value == value {
			return true
		}
	}
	return false
}

// StrRemoveDuplicates 字符串数据去重
func StrRemoveDuplicates(slice []string) []string {
	if len(slice) < 2 {
		fmt.Println("不处理")
		return slice
	}
	// 获取映射类型以存储已经看到的元素
	seen := make(map[string]bool)
	var result []string
	for _, str := range slice {
		// 检查元素是否已经出现过
		if _, ok := seen[str]; !ok {
			// 如果没有出现过，则添加到结果切片和映射中
			seen[str] = true
			result = append(result, str)
		}
	}
	// 返回结果切片
	return result
}

// ListToMap 函数. 只要继承 Addons.go 对象，都可以直接使用
func ListToMap[T models.HasId](objects []T) map[int64]T {
	m := make(map[int64]T)
	for _, obj := range objects {
		id := obj.GetId()
		m[id] = obj
	}
	return m
}

package k8s

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/cache"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/ioutil"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/redis"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/services/client"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/tracing"
	tekton "github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	"github.com/tektoncd/pipeline/pkg/client/informers/externalversions"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

var (
	initOnce               sync.Once
	defaultClientsetManger *ClientsetManger
)

const (
	clusterCertificatesCacheTTL = 1 * time.Millisecond
)

func Init() {
	initOnce.Do(func() {
		defaultClientsetManger = NewClientsetManger(ClientsetProvider)
	})
}

func getKubernetesClusterModelinstance(cluster string) (*cmdb.K8sClusterModel, error) {
	var instances []struct {
		Meta cmdb.K8sClusterModel `json:"meta"`
	}
	param := client.NewParam().WithQueries("filter", fmt.Sprintf("id='%v'", cluster))
	if err := cmdb.GetDefaultCMDB().GetModelInstances(context.Background(), constants.KubernetesClusterModelType, &instances, param); err != nil {
		return nil, err
	}
	if len(instances) != 1 {
		return nil, fmt.Errorf("get kubernetes clusters failed")
	}
	return &instances[0].Meta, nil
}

func ClientsetProvider(cluster string) (*rest.Config, string, error) {
	k8sConf := configs.Config.K8S
	if err := os.MkdirAll(k8sConf.ConfigPath, os.ModePerm); err != nil {
		return nil, "", err
	}

	var cert string
	var k8sConfFullPath string
	if cluster != "" && cluster != "default" {
		k8sConfFullPath = path.Join(k8sConf.ConfigPath, cluster)
		instance, err := getKubernetesClusterModelinstance(cluster)
		if err != nil {
			return nil, "", err
		}
		cert = instance.K8sClusterMeta().Certificate
		if err := ioutil.SafeWriteFile(k8sConfFullPath, []byte(cert)); err != nil {
			logger.Logger.WithField("filename", k8sConfFullPath).Warnf("write config file failed: %v", err)
		}
	} else {
		k8sConfFullPath = path.Join(k8sConf.ConfigPath, "k8sconfig")
	}

	conf, err := clientcmd.BuildConfigFromFlags("", k8sConfFullPath)
	if err != nil {
		return nil, "", err
	}
	conf.QPS = float32(k8sConf.KubeQPS)
	conf.Burst = k8sConf.KubeBurst

	conf.WrapTransport = func(rt http.RoundTripper) http.RoundTripper {
		return tracing.NewTransport(false, "k8s", rt)
	}

	return conf, cert, err
}

type ClientsetManger struct {
	sync.RWMutex
	configGetter func(cluster string) (*rest.Config, string, error)
	cache        redis.Client
	certificates map[string]string
	configs      map[string]*rest.Config
	kubernetes   map[string]kubernetes.Interface
	tekton       map[string]tekton.Interface
}

func NewClientsetManger(configGetter func(cluster string) (*rest.Config, string, error)) *ClientsetManger {
	return &ClientsetManger{
		RWMutex:      sync.RWMutex{},
		configGetter: configGetter,
		cache:        cache.GetRedisClient(),
		certificates: map[string]string{},
		configs:      map[string]*rest.Config{},
		kubernetes:   map[string]kubernetes.Interface{},
		tekton:       map[string]tekton.Interface{},
	}
}

func (c *ClientsetManger) GetKubernetes(cluster string) (client kubernetes.Interface, err error) {
	if err := c.clearOldCache(cluster); err != nil {
		return nil, err
	}
	c.RLock()
	i := c.kubernetes[cluster]
	c.RUnlock()
	if i != nil {
		return i, nil
	}

	conf, err := c.getConfig(cluster)
	if err != nil {
		return nil, err
	}

	clientSet, err := kubernetes.NewForConfig(conf)
	if err != nil {
		return nil, err
	}

	c.Lock()
	defer c.Unlock()
	c.kubernetes[cluster] = clientSet
	return clientSet, nil
}

func (c *ClientsetManger) GetPipeline(cluster string) (tekton.Interface, error) {
	if err := c.clearOldCache(cluster); err != nil {
		return nil, err
	}
	c.RLock()
	i := c.tekton[cluster]
	c.RUnlock()
	if i != nil {
		return i, nil
	}

	conf, err := c.getConfig(cluster)
	if err != nil {
		return nil, err
	}

	clientSet, err := tekton.NewForConfig(conf)
	if err != nil {
		return nil, err
	}

	c.Lock()
	defer c.Unlock()
	c.tekton[cluster] = clientSet

	return clientSet, nil
}

func (c *ClientsetManger) getConfig(cluster string) (*rest.Config, error) {
	c.RLock()
	conf := c.configs[cluster]
	c.RUnlock()
	if conf != nil {
		return conf, nil
	}

	conf, cert, err := c.configGetter(cluster)
	if err != nil {
		return nil, err
	}
	c.Lock()
	defer c.Unlock()
	c.configs[cluster] = conf
	c.certificates[cluster] = cert
	return conf, nil
}

func (c *ClientsetManger) clearOldCache(cluster string) error {
	c.RLock()
	cert := c.certificates[cluster]
	c.RUnlock()
	if cert == "" {
		return nil
	}
	newCert, err := c.getCertificate(cluster)
	if err != nil {
		return err
	}
	if cert != newCert {
		c.delete(cluster)
	}

	return nil
}

func (c *ClientsetManger) getCertificate(cluster string) (string, error) {
	key := fmt.Sprintf("%s-certificates", cluster)
	cert, err := c.cache.Get(key).Result()
	if err == nil {
		return cert, nil
	}

	instance, err := getKubernetesClusterModelinstance(cluster)
	if err != nil {
		return "", err
	}
	cert = instance.K8sClusterMeta().Certificate

	if err := c.cache.Set(key, cert, clusterCertificatesCacheTTL).Err(); err != nil {
		logger.Logger.Warnf("set cluster certificates cache %s failed: %v", key, err)
	}
	return cert, nil
}

func (c *ClientsetManger) delete(cluster string) {
	c.Lock()
	defer c.Unlock()
	delete(c.configs, cluster)
	delete(c.certificates, cluster)
	delete(c.kubernetes, cluster)
	delete(c.tekton, cluster)
}

func GetKubeRestConfig(cluster string) (*rest.Config, error) {
	return defaultClientsetManger.getConfig(cluster)
}

func GetClientset(cluster string) (kubernetes.Interface, error) {
	return defaultClientsetManger.GetKubernetes(cluster)
}

func CleanWholeCache(cluster string) {
	defaultClientsetManger.delete(cluster)
}

func ClusterIsDeleted(cluster string) bool {
	return defaultClientsetManger.configs[cluster] == nil
}

func GetTektonClientset(cluster string) (client tekton.Interface, err error) {
	return defaultClientsetManger.GetPipeline(cluster)
}

// NewSharedInformerFactory constructs a new instance of sharedInformerFactory for namespace.
func NewSharedInformerFactory(client kubernetes.Interface, defaultResync time.Duration, namespace string) informers.SharedInformerFactory {
	return informers.NewSharedInformerFactoryWithOptions(client, defaultResync, informers.WithNamespace(namespace))
}

// NewTektonSharedInformerFactory constructs a new instance of sharedInformerFactory for namespace.
func NewTektonSharedInformerFactory(client tekton.Interface, defaultResync time.Duration, namespace string) externalversions.SharedInformerFactory {
	return externalversions.NewSharedInformerFactoryWithOptions(client, defaultResync, externalversions.WithNamespace(namespace))
}

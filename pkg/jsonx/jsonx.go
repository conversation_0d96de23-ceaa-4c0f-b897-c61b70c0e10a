package jsonx

import (
	"encoding/json"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/PaesslerAG/jsonpath"
	"github.com/duke-git/lancet/v2/xerror"
)

// deprecated: 使用原生
func Marshal(data any) []byte {
	bytes, err := json.Marshal(data)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in marshal json"))
	}
	return bytes
}

// deprecated: 使用原生
func UnMarshal(data []byte, v any) {
	err := json.Unmarshal(data, v)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in unmarshal json"))
	}
}

func GetDataByJsonPath(jsonStr string, jsonPathStr string) (any, error) {
	var jsonObject any
	err := json.Unmarshal([]byte(jsonStr), &jsonObject)
	if err != nil {
		return nil, err
	}
	res, err := jsonpath.Get(jsonPathStr, jsonObject)
	return res, err
}

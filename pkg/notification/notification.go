package notification

import (
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/message"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/message/cube"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
)

// Send sender可以为空，若为空则默认DevCube研发一体化平台
func Send(to []string, subject string, msg string, templateCodes []string, sender string, companyId any) error {
	if sender == "" {
		sender = "DevCube研发一体化平台"
	}
	err := cube.GetDefaultSender().Send(to, "",
		"",
		message.Param{
			TemplatesCode: templateCodes,
			Variables: map[string]string{
				"title":      subject,
				"content":    msg,
				"senderName": sender,
			},
			CompanyId: utils.IDString(companyId),
		})
	if err != nil {
		return err
	}
	return nil
}

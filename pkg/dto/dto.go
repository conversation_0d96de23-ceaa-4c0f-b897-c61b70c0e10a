package dto

import (
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

type CodeEnumDTO struct {
	Value     any    `json:"value"`
	Label     string `json:"label"`
	Extend    string `json:"extend"`
	IsSubject int32  `json:"isSubject"`
}

type AddonTimeDTO struct {
	GmtCreated           string `schema:"gmtCreated"` // example: 2017-01-02,2017-01-02
	GmtModified          string `schema:"gmtModified"`
	LastScanTestExecTime string `schema:"lastScanTestExecTime"`
}

const (
	comma  string = ","
	start  int32  = 0
	end    int32  = 1
	layout string = "2006-01-02 15:04:05"
)

func (t AddonTimeDTO) getData(dateStr string, index int32) *times.Time {
	split := strings.Split(dateStr, comma)
	if index >= int32(len(split)) {
		return nil
	}
	str := split[index]
	if index == start {
		str = str + " 00:00:00"
	} else {
		str = str + " 23:59:59"
	}
	parseTime, err := time.Parse(layout, str)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in parsing string to time"))
	}
	pTime := times.Time(parseTime)
	return &pTime
}

func (t AddonTimeDTO) GetCreatTimeStart() *times.Time {
	return t.getData(t.GmtCreated, start)
}

func (t AddonTimeDTO) GetCreatTimeEnd() *times.Time {
	return t.getData(t.GmtCreated, end)
}

func (t AddonTimeDTO) GetModifyTimeStart() *times.Time {
	return t.getData(t.GmtModified, start)
}

func (t AddonTimeDTO) GetModifyTimeEnd() *times.Time {
	return t.getData(t.GmtModified, end)
}

func (t AddonTimeDTO) GetExecuteTimeStart() *times.Time {
	return t.getData(t.LastScanTestExecTime, start)
}

func (t AddonTimeDTO) GetExecuteTimeEnd() *times.Time {
	return t.getData(t.LastScanTestExecTime, end)
}

type BaseTagDTO struct {
	Id      int64  `json:"id,omitempty"`
	Name    string `json:"name,omitempty"`
	SpaceId string `json:"spaceId,omitempty"`
}

type MagicResponseDTO struct {
	Code    string `json:"code"`
	Data    any    `json:"data"`
	Message string `json:"message"`
}

type QueryDTO struct {
	PageSize  int64  `json:"pageSize,omitempty" schema:"pageSize"`
	Page      int64  `json:"page,omitempty"  schema:"page"`
	Q         string `json:"q,omitempty" schema:"q"`
	RequestId string `json:"X-Request-Id,omitempty" schema:"requestId"`
	SearchKey string `json:"searchKey,omitempty" schema:"searchKey"`
	QMap      map[string]interface{}
}

// CheckPageInfo 校验请求中的page信息,如果不符合规范,则设置默认值 1 99
func (dto *QueryDTO) CheckPageInfo() {
	if dto.Page < 1 {
		dto.Page = 1
	}
	if dto.PageSize < 1 || dto.PageSize > 1000 {
		dto.PageSize = 99
	}
}

func (dto *QueryDTO) qToMap() {
	qMap := make(map[string]interface{})
	dto.QMap = qMap
	if dto.Q == "" {
		return
	}
	// 分割字符串，以逗号分隔
	pairs := strings.Split(dto.Q, ",")
	for _, pair := range pairs {
		// 分割键和值，以等号分隔
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) == 2 {
			// 清除键和值两端的空白字符
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			switch {
			case strings.HasPrefix(value, "[") && strings.HasSuffix(value, "]"):
				// 范围匹配
				rangeArr := strings.SplitN(strings.Trim(value, "[]"), "~", 2)
				qMap[key+"@start"] = rangeArr[0]
				qMap[key+"@end"] = rangeArr[0]
				break
			case strings.HasPrefix(value, "(") && strings.HasSuffix(value, ")"):
				// 数组处理
				rangeArr := strings.Split(strings.Trim(value, "()"), " ")
				qMap[key] = rangeArr
				break
			default:
				// 精确匹配
				qMap[key] = value
			}
		}
	}
}

// GetQueryParam 根据key获取请求中的参数
func (dto *QueryDTO) GetQueryParam(key string) interface{} {
	if dto.QMap == nil {
		dto.qToMap()
	}
	return dto.QMap[key]
}

// QueryWithOrderDTO 查询对象
type QueryWithOrderDTO struct {
	QueryDTO
	OrderField string `json:"orderField,omitempty" form:"orderField"`
	// 1 正序 其他都是倒序
	OrderType int8 `json:"orderType,omitempty" form:"orderType"`
}

func (s QueryWithOrderDTO) OrderTypeFormat() string {
	if s.OrderType == 1 {
		return "asc"
	} else {
		return "desc"
	}
}

type BatchDeleteReq struct {
	Ids []string `json:"ids"`
}

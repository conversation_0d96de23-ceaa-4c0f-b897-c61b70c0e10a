package dto

type MoveDto struct {
	Id       string `json:"id"`
	ParentId string `json:"parent_id"`
}

type MoveTreeNodeItem struct {
	Id           string `json:"id" validate:"required"`
	NewParentId  string `json:"newParentId" validate:"required"`
	NewSiblingId string `json:"newSiblingId" validate:"required"`
}

type MoveTreeNodeRequest struct {
	Nodes []MoveTreeNodeItem `json:"nodes" validate:"required"`
}

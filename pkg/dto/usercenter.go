package dto

type usercenterCommonResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Code    int64  `json:"code"`
}

type UsercenterUserListResp struct {
	usercenterCommonResp
	Result []UserInfo `json:"result"`
}

type UsercenterUserInfoResp struct {
	usercenterCommonResp
	Result UserInfo `json:"result"`
}

type UserInfo struct {
	AdAccount string `json:"adAccount"`
	CompanyID any    `json:"companyId"`
	Name      string `json:"name"`
	Username  string `json:"username"`
	Id        string `json:"-"`
}

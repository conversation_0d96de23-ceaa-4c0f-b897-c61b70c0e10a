package dto

// CubeResourceDTO 资源对象
type CubeResourceDTO struct {
	Id       int64             `json:"id"`
	Name     string            `json:"name"`
	Code     string            `json:"code"`
	Type     string            `json:"type"`
	Pid      int64             `json:"pid"`
	Props    string            `json:"props"`
	Children []CubeResourceDTO `json:"children"`
}

// CubeResourceResp 查询资源相应对象
type CubeResourceResp struct {
	Result []CubeResourceDTO `json:"result"`
	Code   int               `json:"code"`
}

// cube 通用返回结果
type cubeBaseCommonResp struct {
	ReturnCode string `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
}

const CubeBaseRespOkCode = "000000"

type CubeTagResp struct {
	cubeBaseCommonResp
	Data struct {
		List []struct {
			Colour  string `json:"colour"`
			Id      int64  `json:"id"`
			TagName string `json:"tagName"`
		} `json:"list"`
	} `json:"data"`
}

type CubeTagDTO struct {
	Colour string `json:"colour"`
	Value  int64  `json:"value"`
	Label  string `json:"label"`
}

type CubeProjectResp struct {
	cubeBaseCommonResp
	Data CubeProjectDTO `json:"data"`
}

type CubeProjectDTO struct {
	Name      string `json:"name"`
	Id        int64  `json:"id"`
	CompanyId string `json:"companyId"`
	System    []struct {
		Name string `json:"name"`
		Id   any    `json:"id"`
	} `json:"system"`
}

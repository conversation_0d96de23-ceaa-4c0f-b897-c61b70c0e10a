package driver

import (
	"context"
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/jenkins"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/tekton"
)

const (
	TektonDriver  string = "tekton"
	JenkinsDriver string = "jenkins"
)

type DriverProvider interface {
	Run(ctx context.Context, req request.RunRequest) error
	Cancel(ctx context.Context, taskId string) error
	GetLog(ctx context.Context, taskId string) (string, error)
	Delete(ctx context.Context, taskId string) error
}

var Driver DriverProvider

func NewDriverProvider() (DriverProvider, error) {
	switch configs.Config.Pipeline.Driver {
	case TektonDriver:
		dp, err := tekton.NewTekton()
		if err != nil {
			return nil, err
		}
		return dp, nil
	case JenkinsDriver:
		return &jenkins.<PERSON>{}, nil
	default:
		return nil, fmt.Errorf("driver %s not support", configs.Config.Pipeline.Driver)
	}
}

package tasks

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func sonarTask() v1beta1.Task {
	task := v1beta1.Task{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "sonar-scanner",
			Namespace: configs.Config.Pipeline.Namespace,
		},
		Spec: v1beta1.TaskSpec{
			Params: []v1beta1.ParamSpec{
				{
					Name:        "call-back",
					Type:        v1beta1.ParamTypeString,
					Description: "pod内部直接回调主服务地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "type",
					Type:        v1beta1.ParamTypeString,
					Description: "任务类型: unit(单测)/scan(扫描)",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "mode",
					Type:        v1beta1.ParamTypeString,
					Description: "模式: increment(增量)/total(全量)",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "language",
					Type:        v1beta1.ParamTypeString,
					Description: "语言: java/go/...",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "java-version",
					Type:        v1beta1.ParamTypeString,
					Description: "java版本: Java8/Java11/Java17",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "module-path",
					Type:        v1beta1.ParamTypeString,
					Description: "模块路径: cube-magic-base",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "app-type",
					Type:        v1beta1.ParamTypeString,
					Description: "应用类型: front/backend",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "code-url",
					Type:        v1beta1.ParamTypeString,
					Description: "仓库地址: http://xx.xx.git",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "git-user",
					Type:        v1beta1.ParamTypeString,
					Description: "仓库用户名: root",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "git-token",
					Type:        v1beta1.ParamTypeString,
					Description: "仓库用token: xxx",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "branch",
					Type:        v1beta1.ParamTypeString,
					Description: "分支: master",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "base-commit-id",
					Type:        v1beta1.ParamTypeString,
					Description: "基础提交id: xx",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "compare-commit-id",
					Type:        v1beta1.ParamTypeString,
					Description: "比较提交id: xx",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-url",
					Type:        v1beta1.ParamTypeString,
					Description: "sonar地址: https://sonar.com.cn",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-token",
					Type:        v1beta1.ParamTypeString,
					Description: "sonar-token: xxx",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-pdf",
					Type:        v1beta1.ParamTypeString,
					Description: "是否生成pds报告: true/false",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-profile",
					Type:        v1beta1.ParamTypeString,
					Description: "sonar规则集名称: default",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-callback",
					Type:        v1beta1.ParamTypeString,
					Description: "sonar回调地址: https://cube.com.cn/api/magic/xxx",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-exclusion",
					Type:        v1beta1.ParamTypeString,
					Description: "排除的路径: **/x.java",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "sonar-inclusion",
					Type:        v1beta1.ParamTypeString,
					Description: "包含的路径: **/x.java",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "mvn-repo",
					Type:        v1beta1.ParamTypeString,
					Description: "maven仓库地址: https://maven.aliyun.com/repository/public",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "mvn-user",
					Type:        v1beta1.ParamTypeString,
					Description: "maven用户名: ",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "mvn-pw",
					Type:        v1beta1.ParamTypeString,
					Description: "maven密码: ",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
			},
			Steps: []v1beta1.Step{
				{
					Name:    "sonar-scanner",
					Image:   configs.Config.Modules.Neptune.SonarTask.Image,
					Command: []string{"esonar-scanner"},
					Args: []string{
						"--call-back=$(params.call-back)",
						"--type=$(params.type)",
						"--mode=$(params.mode)",
						"--language=$(params.language)",
						"--java-version=$(params.java-version)",
						"--module-path=$(params.module-path)",
						"--app-type=$(params.app-type)",
						"--code-url=$(params.code-url)",
						"--git-user=$(params.git-user)",
						"--git-token=$(params.git-token)",
						"--branch=$(params.branch)",
						"--base-commit-id=$(params.base-commit-id)",
						"--compare-commit-id=$(params.compare-commit-id)",
						"--sonar-url=$(params.sonar-url)",
						"--sonar-token=$(params.sonar-token)",
						"--sonar-pdf=$(params.sonar-pdf)",
						"--sonar-profile=$(params.sonar-profile)",
						"--sonar-callback=$(params.sonar-callback)",
						"--sonar-exclusion=$(params.sonar-exclusion)",
						"--sonar-inclusion=$(params.sonar-inclusion)",
						"--mvn-repo=$(params.mvn-repo)",
						"--mvn-user=$(params.mvn-user)",
						"--mvn-pw=$(params.mvn-pw)",
					},
					Env: []corev1.EnvVar{
						{
							Name: "TASK_HOST_IP",
							ValueFrom: &corev1.EnvVarSource{
								FieldRef: &corev1.ObjectFieldSelector{
									APIVersion: "v1",
									FieldPath:  "status.hostIP",
								},
							},
						},
					},
					ImagePullPolicy: corev1.PullAlways,
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.SonarTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.SonarTask.Memory)),
						},
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.SonarTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.SonarTask.Memory)),
						},
					},
				},
			},
		},
	}
	return task
}

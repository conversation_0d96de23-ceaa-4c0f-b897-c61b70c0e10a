package tasks

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func uiExecutorTask() v1beta1.Task {
	task := v1beta1.Task{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "ui-executor",
			Namespace: configs.Config.Pipeline.Namespace,
		},
		Spec: v1beta1.TaskSpec{
			Params: []v1beta1.ParamSpec{
				{
					Name:        "callback",
					Type:        v1beta1.ParamTypeString,
					Description: "pod内部直接回调主服务地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "input",
					Type:        v1beta1.ParamTypeString,
					Description: "执行用例的json",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "reportid",
					Type:        v1beta1.ParamTypeString,
					Description: "报告id",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "filedownloadurl",
					Type:        v1beta1.ParamTypeString,
					Description: "文件下载地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "variableurl",
					Type:        v1beta1.ParamTypeString,
					Description: "变量请求地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "env",
					Type:        v1beta1.ParamTypeString,
					Description: "环境",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
			},
			Steps: []v1beta1.Step{
				{
					Name:    "ui-executor",
					Image:   configs.Config.Modules.Venus.ExecuteTask.Image,
					Command: []string{"ui-executor"},
					Args: []string{
						"--callback=$(params.callback)",
						"--input=$(params.input)",
						"--reportid=$(params.reportid)",
						"--filedownloadurl=$(params.filedownloadurl)",
						"--variableurl=$(params.variableurl)",
						"--env=$(params.env)",
					},
					Env: []corev1.EnvVar{
						{
							Name: "TASK_HOST_IP",
							ValueFrom: &corev1.EnvVarSource{
								FieldRef: &corev1.ObjectFieldSelector{
									APIVersion: "v1",
									FieldPath:  "status.hostIP",
								},
							},
						},
					},
					ImagePullPolicy: corev1.PullIfNotPresent,
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Venus.ExecuteTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Venus.ExecuteTask.Memory)),
						},
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Venus.ExecuteTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Venus.ExecuteTask.Memory)),
						},
					},
				},
			},
		},
	}
	return task
}

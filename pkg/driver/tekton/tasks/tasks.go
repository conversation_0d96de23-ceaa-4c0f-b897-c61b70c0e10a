package tasks

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/k8s"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"

	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Init() {
	tasks := map[string]v1beta1.Task{
		"trivy-scanner":   trivyScanTask(),
		"sonar-scanner":   sonarTask(),
		"jacoco-coverage": jacocoCoverageTask(),
		"ui-executor":     uiExecutorTask(),
	}

	var clusterId string
	var items []struct {
		Meta cmdb.K8sClusterModel `json:"meta"`
	}
	if err := cmdb.GetDefaultCMDB().GetModelInstances(context.Background(), constants.KubernetesClusterModelType, &items, nil); err != nil {
		logger.Logger.Errorf("get kubernetes clusters failed: %v", err)
	}
	for _, item := range items {
		if item.Meta.K8sClusterMeta().IsSystemCluster {
			clusterId = item.Meta.K8sClusterMeta().ClusterId
			break
		}
	}

	tektonClient, err := k8s.GetTektonClientset(clusterId)
	if err != nil {
		logger.Logger.Errorf("get kubernetes cluster client failed: %v", err)
		return
	}
	for name, task := range tasks {
		oldTask, err := tektonClient.TektonV1beta1().Tasks(configs.Config.Pipeline.Namespace).Get(context.Background(), task.Name, metav1.GetOptions{})
		if err != nil {
			_, err = tektonClient.TektonV1beta1().Tasks(configs.Config.Pipeline.Namespace).Create(context.Background(), &task, metav1.CreateOptions{})
		} else {
			oldTask.Labels = task.Labels
			oldTask.Annotations = task.Annotations
			oldTask.Spec = task.Spec
			_, err = tektonClient.TektonV1beta1().Tasks(configs.Config.Pipeline.Namespace).Update(context.Background(), oldTask, metav1.UpdateOptions{})
		}
		if err != nil {
			logger.Logger.Errorf("init task %s failed: %v", name, err)
			continue
		}
	}

	loopDeal(tektonClient)
}

package tasks

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func jacocoCoverageTask() v1beta1.Task {
	task := v1beta1.Task{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "jacoco-coverage",
			Namespace: configs.Config.Pipeline.Namespace,
		},
		Spec: v1beta1.TaskSpec{
			Params: []v1beta1.ParamSpec{
				{
					Name:        "mode",
					Type:        v1beta1.ParamTypeString,
					Description: "模式: increment(增量)/total(全量)",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "app-name",
					Type:        v1beta1.ParamTypeString,
					Description: "应用名",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "ip",
					Type:        v1beta1.ParamTypeString,
					Description: "ip",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "port",
					Type:        v1beta1.ParamTypeString,
					Description: "port",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "packages",
					Type:        v1beta1.ParamTypeString,
					Description: "comma separated packages, such as com.zhongan.a.*,com.zhongan.b.*",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "git-url",
					Type:        v1beta1.ParamTypeString,
					Description: "git url",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "git-user",
					Type:        v1beta1.ParamTypeString,
					Description: "git user",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "git-token",
					Type:        v1beta1.ParamTypeString,
					Description: "git token",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "exclude",
					Type:        v1beta1.ParamTypeString,
					Description: "exclude",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "module-name",
					Type:        v1beta1.ParamTypeString,
					Description: "module name",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "branch",
					Type:        v1beta1.ParamTypeString,
					Description: "branch",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-path",
					Type:        v1beta1.ParamTypeString,
					Description: "oss path",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-endpoint",
					Type:        v1beta1.ParamTypeString,
					Description: "oss endpoint",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-bucket-name",
					Type:        v1beta1.ParamTypeString,
					Description: "oss bucket name",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-access-id",
					Type:        v1beta1.ParamTypeString,
					Description: "oss access id",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-access-key",
					Type:        v1beta1.ParamTypeString,
					Description: "oss access key",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "task-no",
					Type:        v1beta1.ParamTypeString,
					Description: "task no",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "base-commit-id",
					Type:        v1beta1.ParamTypeString,
					Description: "base commit id",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "compare-commit-id",
					Type:        v1beta1.ParamTypeString,
					Description: "compare commit id",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "mantis-call-back",
					Type:        v1beta1.ParamTypeString,
					Description: "mantis call back url",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "oss-path-style",
					Type:        v1beta1.ParamTypeString,
					Description: "oss path style",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "false",
					},
				},
				{
					Name:        "old-exec-path",
					Type:        v1beta1.ParamTypeString,
					Description: "上次执行的exec文件在oss的地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "false",
					},
				},
			},
			Steps: []v1beta1.Step{
				{
					Name:    "jacoco-coverage",
					Image:   configs.Config.Modules.Neptune.JacocoTask.Image,
					Command: []string{"jacoco-coverage"},
					Args: []string{
						"--mode=$(params.mode)",
						"--app-name=$(params.app-name)",
						"--ip=$(params.ip)",
						"--port=$(params.port)",
						"--packages=$(params.packages)",
						"--git-url=$(params.git-url)",
						"--git-user=$(params.git-user)",
						"--git-token=$(params.git-token)",
						"--exclude=$(params.exclude)",
						"--module-name=$(params.module-name)",
						"--oss-path=$(params.oss-path)",
						"--branch=$(params.branch)",
						"--oss-endpoint=$(params.oss-endpoint)",
						"--oss-bucket-name=$(params.oss-bucket-name)",
						"--oss-access-id=$(params.oss-access-id)",
						"--oss-access-key=$(params.oss-access-key)",
						"--oss-path-style=$(params.oss-path-style)",
						"--task-no=$(params.task-no)",
						"--base-commit-id=$(params.base-commit-id)",
						"--compare-commit-id=$(params.compare-commit-id)",
						"--mantis-call-back=$(params.mantis-call-back)",
						"--old-exec-path=$(params.old-exec-path)",
					},
					Env: []corev1.EnvVar{
						{
							Name: "TASK_HOST_IP",
							ValueFrom: &corev1.EnvVarSource{
								FieldRef: &corev1.ObjectFieldSelector{
									APIVersion: "v1",
									FieldPath:  "status.hostIP",
								},
							},
						},
					},
					ImagePullPolicy: corev1.PullAlways,
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.JacocoTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.JacocoTask.Memory)),
						},
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.JacocoTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.JacocoTask.Memory)),
						},
					},
				},
			},
		},
	}
	return task
}

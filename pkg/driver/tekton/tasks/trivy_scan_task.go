package tasks

import (
	"fmt"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func trivyScanTask() v1beta1.Task {
	task := v1beta1.Task{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "trivy-scanner",
			Namespace: configs.Config.Pipeline.Namespace,
		},
		Spec: v1beta1.TaskSpec{
			Params: []v1beta1.ParamSpec{
				{
					Name:        "type",
					Type:        v1beta1.ParamTypeString,
					Description: "制品类型可选: Generic、Docker",
				},
				{
					Name:        "cube-mantis-url",
					Type:        v1beta1.ParamTypeString,
					Description: "cube-mantis服务地址",
				},
				{
					Name:        "upload-result-url",
					Type:        v1beta1.ParamTypeString,
					Description: "扫描报告上传接口URL",
				},
				{
					Name:        "image",
					Type:        v1beta1.ParamTypeString,
					Description: "镜像名称",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "docker-addr",
					Type:        v1beta1.ParamTypeString,
					Description: "镜像仓库地址",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "docker-user",
					Type:        v1beta1.ParamTypeString,
					Description: "镜像仓库账号",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "docker-password",
					Type:        v1beta1.ParamTypeString,
					Description: "镜像仓库密码",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "filename",
					Type:        v1beta1.ParamTypeString,
					Description: "制品文件名称",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "artifact-download-url",
					Type:        v1beta1.ParamTypeString,
					Description: "制品下载URL",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "artifact-download-token",
					Type:        v1beta1.ParamTypeString,
					Description: "制品下载Token",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "white-cve",
					Type:        v1beta1.ParamTypeString,
					Description: "CVE白名单",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "",
					},
				},
				{
					Name:        "non-ssl",
					Type:        v1beta1.ParamTypeString,
					Description: "镜像仓库是否为http",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "false",
					},
				},
				{
					Name:        "artifact-download-authorization",
					Type:        v1beta1.ParamTypeString,
					Description: "制品的用户信息",
					Default: &v1beta1.ParamValue{
						Type:      v1beta1.ParamTypeString,
						StringVal: "false",
					},
				},
			},
			Steps: []v1beta1.Step{
				{
					Name:    "trivy-scanner",
					Image:   configs.Config.Modules.Neptune.TrivyTask.Image,
					Command: []string{"trivy-scanner"},
					Args: []string{
						"--type=$(params.type)",
						"--cube-mantis-url=$(params.cube-mantis-url)",
						"--image=$(params.image)",
						"--docker-addr=$(params.docker-addr)",
						"--docker-user=$(params.docker-user)",
						"--docker-password=$(params.docker-password)",
						"--upload-result-url=$(params.upload-result-url)",
						"--filename=$(params.filename)",
						"--artifact-download-url=$(params.artifact-download-url)",
						"--artifact-download-token=$(params.artifact-download-token)",
						"--white-cve=$(params.white-cve)",
						"--non-ssl=$(params.non-ssl)",
						"--artifact-download-authorization=$(params.artifact-download-authorization)",
					},
					Env: []corev1.EnvVar{
						{
							Name: "TASK_HOST_IP",
							ValueFrom: &corev1.EnvVarSource{
								FieldRef: &corev1.ObjectFieldSelector{
									APIVersion: "v1",
									FieldPath:  "status.hostIP",
								},
							},
						},
					},
					ImagePullPolicy: corev1.PullAlways,
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.TrivyTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.TrivyTask.Memory)),
						},
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse(fmt.Sprintf("%dm", configs.Config.Modules.Neptune.TrivyTask.Cpu)),
							corev1.ResourceMemory: resource.MustParse(fmt.Sprintf("%dMi", configs.Config.Modules.Neptune.TrivyTask.Memory)),
						},
					},
					VolumeMounts: []corev1.VolumeMount{
						{
							Name:      "var-run",
							MountPath: "/var/run",
						},
					},
				},
			},
			Volumes: []corev1.Volume{
				{
					Name: "var-run",
					VolumeSource: corev1.VolumeSource{
						HostPath: &corev1.HostPathVolumeSource{
							Path: "/var/run",
						},
					},
				},
			},
		},
	}
	return task
}

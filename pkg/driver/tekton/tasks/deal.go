package tasks

import (
	"context"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"knative.dev/pkg/apis"
)

var taskRunDealers = make(map[string]func(condition apis.Condition, labels map[string]string) error)

func AddTaskRunDealers(d map[string]func(condition apis.Condition, labels map[string]string) error) {
	for k, dealer := range d {
		taskRunDealers[k] = dealer
	}
}

func dealTaskRun(ctx context.Context, taskRun v1beta1.TaskRun) error {
	condition := taskRun.Status.GetCondition(apis.ConditionSucceeded)
	if condition == nil {
		return nil
	}

	// 执行业务处理
	if dealer, ok := taskRunDealers[taskRun.Spec.TaskRef.Name]; ok {
		if err := dealer(*condition, taskRun.GetObjectMeta().GetLabels()); err != nil {
			return err
		}
	}

	// 任务尚未完成，无需处理日志和清理
	if condition.Status == corev1.ConditionUnknown {
		return nil
	}

	// 获取执行日志
	dp, err := driver.NewDriverProvider()
	if err != nil {
		return err
	}

	log, err := dp.GetLog(ctx, taskRun.Name)
	if err != nil {
		return err
	}

	// 处理日志文件
	fileName := fmt.Sprintf("%s.txt", taskRun.Name)
	filePath := path.Join("/tmp", fileName)
	objectKey := path.Join(constants.TaskRunLogFilePath, fileName)

	// 创建临时文件并写入日志
	file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o666)
	if err != nil {
		return err
	}
	defer func() {
		file.Close()
		os.Remove(filePath)
	}()

	if _, err = file.WriteString(log); err != nil {
		return err
	}

	// 上传日志文件
	if !s3store.ObjectExists(objectKey) {
		if _, err = s3store.UploadFile(filePath, objectKey); err != nil {
			return err
		}
	}

	// 删除已完成的任务
	return dp.Delete(ctx, taskRun.Name)
}

func doDealTaskRun(ctx context.Context, tektonClient versioned.Interface, namespace, prefix string) error {
	selector := labels.SelectorFromSet(labels.Set{
		constants.PlatformLabel: "cube-mantis",
	})
	// 列出指定命名空间下的所有 TaskRun
	taskRuns, err := tektonClient.TektonV1beta1().TaskRuns(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		return fmt.Errorf("failed to list TaskRuns: %v", err)
	}

	// 遍历并找到所有匹配前缀的 TaskRun
	for _, tr := range taskRuns.Items {
		if len(prefix) > 0 && len(tr.Name) >= len(prefix) && strings.HasPrefix(tr.Name, prefix) {
			if err := dealTaskRun(ctx, tr); err != nil {
				return err
			}
		}
	}
	return nil
}

func loopDeal(client versioned.Interface) {
	do := func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Logger.Errorf("error deal taskRun, err=%v", err)
			}
		}()
		if err := doDealTaskRun(context.Background(), client, configs.Config.Pipeline.Namespace, "cube-mantis"); err != nil {
			logger.Logger.Errorf("error deal taskRun, err=%s", err.Error())
		}
		time.Sleep(10 * time.Second)
	}
	go func() {
		for {
			do()
		}
	}()
}

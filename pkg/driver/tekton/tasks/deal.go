package tasks

import (
	"context"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	"github.com/tektoncd/pipeline/pkg/client/clientset/versioned"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"knative.dev/pkg/apis"
)

var taskRunDealers = make(map[string]func(condition apis.Condition, labels map[string]string) error)

func AddTaskRunDealers(d map[string]func(condition apis.Condition, labels map[string]string) error) {
	for k, dealer := range d {
		taskRunDealers[k] = dealer
	}
}

func dealTaskRun(ctx context.Context, taskRun v1beta1.TaskRun) error {
	if condition := taskRun.Status.GetCondition(apis.ConditionSucceeded); condition != nil {
		// 业务处理
		if dealer, ok := taskRunDealers[taskRun.Spec.TaskRef.Name]; ok {
			if err := dealer(*condition, taskRun.GetObjectMeta().GetLabels()); err != nil {
				return err
			}
		}
		if condition.Status != corev1.ConditionUnknown { // "True" 表示成功，"False" 表示失败
			// upload logs
			dp, err := driver.NewDriverProvider()
			if err != nil {
				return err
			}
			log, err := dp.GetLog(ctx, taskRun.Name)
			if err != nil {
				return err
			}

			filePath := path.Join("/tmp", fmt.Sprintf("%s.txt", taskRun.Name))
			file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o666)
			if err != nil {
				return err
			}

			defer func() {
				file.Close()
				os.Remove(filePath)
			}()

			_, err = file.WriteString(log)
			if err != nil {
				return err
			}
			objectKey := path.Join(constants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", taskRun.Name))
			if s3store.ObjectExists(objectKey) {
				s3store.UploadFile(filePath, objectKey)
			}

			// 删除执行完成的taskRun
			err = dp.Delete(ctx, taskRun.Name)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func doDealTaskRun(ctx context.Context, tektonClient versioned.Interface, namespace, prefix string) error {
	selector := labels.SelectorFromSet(labels.Set{
		constants.PlatformLabel: "cube-mantis",
	})
	// 列出指定命名空间下的所有 TaskRun
	taskRuns, err := tektonClient.TektonV1beta1().TaskRuns(namespace).List(ctx, metav1.ListOptions{
		LabelSelector: selector.String(),
	})
	if err != nil {
		return fmt.Errorf("failed to list TaskRuns: %v", err)
	}

	// 遍历并找到所有匹配前缀的 TaskRun
	for _, tr := range taskRuns.Items {
		if len(prefix) > 0 && len(tr.Name) >= len(prefix) && strings.HasPrefix(tr.Name, prefix) {
			if err := dealTaskRun(ctx, tr); err != nil {
				return err
			}
		}
	}
	return nil
}

func loopDeal(client versioned.Interface) {
	do := func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Logger.Errorf("error deal taskRun, err=%v", err)
			}
		}()
		if err := doDealTaskRun(context.Background(), client, configs.Config.Pipeline.Namespace, "cube-mantis"); err != nil {
			logger.Logger.Errorf("error deal taskRun, err=%s", err.Error())
		}
		time.Sleep(10 * time.Second)
	}
	go func() {
		for {
			do()
		}
	}()
}

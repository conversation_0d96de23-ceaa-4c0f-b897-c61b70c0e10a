package jenkins

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver/request"
)

type Jenkins struct{}

func (jk *<PERSON>) Run(ctx context.Context, req request.RunRequest) error {
	// TODO
	return nil
}

func (jk *<PERSON>) Cancel(ctx context.Context, taskId string) error {
	// TODO
	return nil
}

func (jk *<PERSON>) GetLog(ctx context.Context, taskNo string) (string, error) {
	return "", nil
}

func (jk *<PERSON>) Delete(ctx context.Context, taskId string) error {
	return nil
}

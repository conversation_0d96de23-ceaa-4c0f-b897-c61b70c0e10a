package pubsub

import (
	"context"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub"
)

// SendToRedisQueue 发送消息到redis队列
// msg 消息体
// tag 消息的任务类型 需要与消费端对应起来
// queue 队列名称 -- 需要与消费端对应起来
func SendToRedisQueue(msg pubsub.Payload, queue string) {
	// 生产客户端
	if Client == nil {
		logger.Logger.Panic("客户端为nil!")
	}
	_, err := Client.Pub(context.Background(), msg, pubsub.WithMaxRetry(0),
		pubsub.WithTimeout(3*time.Minute), pubsub.WithDeadline(time.Now().Add(3*time.Minute)), pubsub.WithQueue(queue))
	if err != nil {
		logger.Logger.Panicf("消息发送失败!tag=%v,msg=%+v,err: %v", msg.Kind(), msg, err)
	}
	logger.Logger.Debugf("消息发送成功!tag=%v,msg=%+v", msg.Kind(), msg)
}

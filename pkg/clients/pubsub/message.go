package pubsub

import (
	"context"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	cubeasynq "git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/asynq"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/pubsub/factory"
)

var Client *factory.Client

const (
	PipelineQueue = "pipeline"
	WebQueue      = "web"
)

func Init() {
	// 生产客户端
	client, err := factory.NewClient(factory.Config{
		Driver: factory.DriverAsynq,
		Asynq: cubeasynq.Config{
			Redis:       configs.Config.Redis,
			Concurrency: 1,
			Queues: map[string]int{
				"pipeline": 6, // 流水线队列中的任务将被处理 60% 的时间
				"web":      3, //  web队列中的任务将被处理 30% 的时间
				"default":  1, // 默认队列中的任务将被处理 10% 的时间
			},
			ShutdownTimeoutSecond: 10,
			Logger:                logger.Logger,
		},
	})
	if err != nil {
		logger.Logger.Panicf("初始化消息队列错误, err=%s", err.Error())
	}
	Client = client
	logger.Logger.Info("启动生产者客户端")
	go func() {
		if err = Client.Start(context.Background()); err != nil {
			logger.Logger.Panicf("初始化消息队列错误, err=%s", err.Error())
		}
	}()
}

package rest

import (
	"fmt"
	"testing"

	"github.com/avast/retry-go"
	"github.com/duke-git/lancet/v2/xerror"
)

func TestGet(t *testing.T) {
	type IPResult struct {
		ErrorCode int    `json:"error_code"`
		Reason    string `json:"reason"`
		Result    struct {
			City     string `json:"City"`
			Country  string `json:"Country"`
			Isp      string `json:"-"`
			Province string `json:"Province"`
		} `json:"result"`
		// 添加一个字段用于存储结果码
		ResultCode string `json:"resultcode"`
	}

	var resBody IPResult
	err := retry.Do(func() error {
		resp := Get("https://apis.juhe.cn/ip/ipNew?ip=*************&key=6f929d31ee461e43280ec7ec71292886")
		if resp.Err != nil {
			return resp.Err
		}
		err := resp.FillUp(&resBody)
		if err != nil {
			return err
		}
		return nil
	}, retry.Attempts(3), retry.LastErrorOn<PERSON>(true), retry.OnRetry(func(n uint, err error) {
		t.Logf("第%d次重试：%v", n, err)
	}))
	if err != nil {
		t.Errorf("%v", xerror.Wrap(err, "调用接口失败"))
	}
	fmt.Printf("%+v\n", resBody)
}

package rest

var dfltBuilder = RequestBuilder{}

// Get use the DefaultBuilder
func Get(url string) *Response {
	return dfltBuilder.Get(url)
}

// Post use the DefaultBuilder
func Post(url string, body interface{}) *Response {
	return dfltBuilder.Post(url, body)
}

// Put use the DefaultBuilder
func Put(url string, body interface{}) *Response {
	return dfltBuilder.Put(url, body)
}

// Patch use the DefaultBuilder
func Patch(url string, body interface{}) *Response {
	return dfltBuilder.Patch(url, body)
}

// Delete use the DefaultBuilder
func Delete(url string) *Response {
	return dfltBuilder.Delete(url)
}

// Head uses the DefaultBuilder
func Head(url string) *Response {
	return dfltBuilder.Head(url)
}

// Options uses the DefaultBuilder
func Options(url string) *Response {
	return dfltBuilder.Options(url)
}

// AsyncGet uses the DefaultBuilder
func AsyncGet(url string, f func(*Response)) {
	dfltBuilder.AsyncGet(url, f)
}

// AsyncPost uses the DefaultBuilder
func AsyncPost(url string, body interface{}, f func(*Response)) {
	dfltBuilder.AsyncPost(url, body, f)
}

// AsyncPut is the *asynchronous* option for PUT
// The go routine calling AsyncPut(), will not be blocked
// Whenever the Response is ready, the *f* function will be called back
// AsyncPut uses the DefaultBuilder
func AsyncPut(url string, body interface{}, f func(*Response)) {
	dfltBuilder.AsyncPut(url, body, f)
}

// AsyncPatch is the *asynchronous* option for PATCH
// The go routine calling AsyncPatch(), will not be blocked
// Whenever the Response is ready, the *f* function will be called back
// AsyncPatch uses the DefaultBuilder
func AsyncPatch(url string, body interface{}, f func(*Response)) {
	dfltBuilder.AsyncPatch(url, body, f)
}

// AsyncDelete is the *asynchronous* option for DELETE
// The go routine calling AsyncDelete(), will not be blocked
// Whenever the Response is ready, the *f* function will be called back
// AsyncDelete uses the DefaultBuilder
func AsyncDelete(url string, f func(*Response)) {
	dfltBuilder.AsyncDelete(url, f)
}

// AsyncHead is the *asynchronous* option for HEAD
// The go routine calling AsyncHead(), will not be blocked
// Whenever the Response is ready, the *f* function will be called back
// AsyncHead uses the DefaultBuilder
func AsyncHead(url string, f func(*Response)) {
	dfltBuilder.AsyncHead(url, f)
}

// AsyncOptions is the *asynchronous* option for OPTIONS
// The go routine calling AsyncOptions(), will not be blocked
// Whenever the Response is ready, the *f* function will be called back
// AsyncOptions uses the DefaultBuilder
func AsyncOptions(url string, f func(*Response)) {
	dfltBuilder.AsyncOptions(url, f)
}

// ForkJoin let you *fork* requests, and *wait* until all of them have return
//
// Concurrent has methods for Get, Post, Put, Patch, Delete, Head & Options,
// with almost the same API as the synchronous methods
// The difference is that these methods return a FutureResponse, which holds a pointer to
// Response. Response inside FutureResponse is nil until request has finished
//
//	var futureA, futureB *rest.FutureResponse
//
//	rest.ForkJoin(func(c *rest.Concurrent){
//		futureA = c.Get("/url/1")
//		futureB = c.Get("/url/2")
//	})
//
//	fmt.Println(futureA.Response())
//	fmt.Println(futureB.Response())
//
// AsyncOptions use the DefaultBuilder
func ForkJoin(f func(*Concurrent)) {
	dfltBuilder.ForkJoin(f)
}

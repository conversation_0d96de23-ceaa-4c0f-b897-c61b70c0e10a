package nexus

import (
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"github.com/datadrivers/go-nexus-client/nexus3/pkg/client"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"github.com/datadrivers/go-nexus-client/nexus3"
)

var (
	clientSet *nexus3.NexusClient
	once      sync.Once
)

func Init() {
	once.Do(func() {
		if configs.Config.Nexus.Enable {
			logger.Logger.Info("初始化Nexus客户端")
			nexusClient := nexus3.NewClient(client.Config{
				URL:      configs.Config.Nexus.Url,
				Username: configs.Config.Nexus.Username,
				Password: configs.Config.Nexus.Password.String(),
			})
			clientSet = nexusClient
		}
	})
}

func GetClientSet() *nexus3.NexusClient {
	if clientSet == nil {
		panic("Nexus客户端未初始化")
	}
	return clientSet
}

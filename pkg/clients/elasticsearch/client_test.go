package elasticsearch

import (
	"context"
	"fmt"
	"testing"
)

func TestGetClientSet(t *testing.T) {
	Init()
	client := GetClientSet()
	clusterhealth, _ := client.ClusterHealth().Do(context.TODO())
	fmt.Printf("Cluster status: %s\n", clusterhealth.Status)
	createIndex, err := client.CreateIndex("testindex").Do(context.TODO())
	if err != nil {
		t.Fatal(err)
	}
	if !createIndex.Acknowledged {
		t.<PERSON><PERSON><PERSON>("expected IndicesCreateResult.Acknowledged %v; got %v", true, createIndex.Acknowledged)
	}
	names, _ := client.IndexNames()
	fmt.Printf("Index names: %s\n", names)
	client.DeleteIndex("testindex")
}

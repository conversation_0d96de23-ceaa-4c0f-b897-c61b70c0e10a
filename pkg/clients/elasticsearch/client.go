package elasticsearch

import (
	"sync"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/olivere/elastic/v7"
)

var (
	clientSet *elastic.Client
	once      sync.Once
)

func Init() {
	once.Do(func() {
		if configs.Config.ElasticSearch.Enable {
			logger.Logger.Info("初始化ElasticSearch客户端")
			esClient, err := elastic.NewClient(elastic.SetURL(configs.Config.ElasticSearch.Url...),
				elastic.SetBasicAuth(configs.Config.ElasticSearch.Username, configs.Config.ElasticSearch.Password.String()),
				elastic.SetSniff(false))
			if err != nil {
				logger.Logger.Panicf("%+v", xerror.Wrap(err, "初始化ElasticSearch客户端失败"))
			}
			clientSet = esClient
		}
	})
}

func GetClientSet() *elastic.Client {
	if clientSet == nil {
		logger.Logger.Panic("ElasticSearch客户端未初始化")
	}
	return clientSet
}

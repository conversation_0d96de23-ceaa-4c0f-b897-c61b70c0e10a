package cache

import (
	"context"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/redis"
)

var (
	client redis.Client
	once   sync.Once
	ctx    = context.Background()
)

func NewRedisClient(config redis.Config) (redis.Client, error) {
	return redis.New(config)
}

func TryLock(timeOut time.Duration, key string, waitTime int64) (*redis.Lock, error) {
	return redis.ObtainLock(client, key, timeOut)
}

func Unlock(mutex *redis.Lock) error {
	if mutex == nil {
		return nil
	}
	return mutex.Release()
}

func GetObject(key string) (any, error) {
	return client.Get(key).Result()
}

// GetList 获取整个列表元素
func GetList(key string) ([]string, error) {
	lLen, err := client.LLen(key).Result()
	if err != nil {
		return nil, err
	}
	result, err := client.LRange(key, 0, lLen-1).Result()
	if err != nil {
		return nil, err
	}
	return result, nil
}

// ListRPop 删除list尾部的节点
func ListRPop(key string) error {
	return client.RPop(key).Err()
}

// ListRPush 将数据添加到列表尾部
func ListRPush(key string, value ...string) error {
	err := client.RPush(key, value).Err()
	if err != nil {
		return err
	}
	return nil
}

// ListChange 更换整个list
func ListChange(key string, value any, expire time.Duration) error {
	client.Del(key)
	err := client.RPush(key, value).Err()
	if err != nil {
		return err
	}
	err = client.Expire(key, expire).Err()
	if err != nil {
		return err
	}
	return nil
}

// ListRefresh 刷新list为空值
func ListRefresh(key string, expire time.Duration) error {
	client.Del(key)
	err := client.RPush(key, "").Err()
	if err != nil {
		return err
	}
	err = client.Expire(key, expire).Err()
	if err != nil {
		return err
	}
	return nil
}

func ExpireKey(key string, expire time.Duration) error {
	return client.Expire(key, expire).Err()
}

func Init() {
	once.Do(func() {
		logger.Logger.Info("初始化Cache客户端")
		redisClient, err := NewRedisClient(configs.Config.Redis)
		if err != nil {
			panic(err)
		}
		client = redisClient
	})
}

func GetRedisClient() redis.Client {
	if client == nil {
		logger.Logger.Panic("RedisCache客户端未初始化")
	}
	return client
}

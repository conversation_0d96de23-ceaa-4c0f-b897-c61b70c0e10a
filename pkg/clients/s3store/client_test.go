package s3store

import (
	"fmt"
	"testing"
)

func TestUploadFile(t *testing.T) {
	file, _ := UploadFile("D://test.doc", "/test测试.doc")
	fmt.Println(file)
}

func TestDownloadFile(t *testing.T) {
	DownloadFile("/test测试.doc", "D://test.doc")
}

func TestDeleteFile(t *testing.T) {
	DeleteFile("/test测试.doc")
}

func TestGetUrl(t *testing.T) {
	key := GetURLByKeyWithPanic("tekton/0bd73a8f-f53a-46d5-8f7e-a7fd11272df0/log")
	fmt.Println(key)
}

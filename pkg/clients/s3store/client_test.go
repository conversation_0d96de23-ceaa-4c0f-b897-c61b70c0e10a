package s3store

import (
	"fmt"
	"os"
	"testing"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/sirupsen/logrus"
)

func setup() {
	// 初始化logger，避免空指针引用
	logger.Logger = logrus.New()

	// 初始化S3客户端，使用测试用的参数
	InitWithoutConfig(
		"http://172.28.38.22:9006", // 测试环境的endpoint
		"admin",                    // 测试访问ID
		"admin1234",                // 测试访问密钥
		"s3-test",                  // 测试桶名称
	)
}

func TestUploadFile(t *testing.T) {
	setup()

	// 创建临时测试文件
	testFilePath := "test-temp.txt"
	err := os.WriteFile(testFilePath, []byte("这是测试内容"), 0644)
	if err != nil {
		t.Fatalf("无法创建测试文件: %v", err)
	}
	defer os.Remove(testFilePath) // 测试后清理

	file, err := UploadFile(testFilePath, "/test测试.doc")
	if err != nil {
		t.Fatalf("上传文件失败: %v", err)
	}
	fmt.Println(file)
}

func TestDownloadFile(t *testing.T) {
	setup()
	DownloadFile("/test测试.doc", "logs/mantis-hualaosi.local.log")
}

func TestDeleteFile(t *testing.T) {
	setup()
	DeleteFile("/test测试.doc")
}

func TestGetUrl(t *testing.T) {
	setup()
	key, _ := GetURLByKey("tekton/0bd73a8f-f53a-46d5-8f7e-a7fd11272df0/log")
	fmt.Println(key)
}

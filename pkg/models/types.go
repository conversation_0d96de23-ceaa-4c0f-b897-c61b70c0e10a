package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
)

type StringSlice []string

func (d StringSlice) Value() (driver.Value, error) {
	return json.Marshal(d)
}

func (d *StringSlice) Scan(value any) error {
	if value == nil || value == "" {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("invalid JSON data")
	}
	return json.Unmarshal(bytes, d)
}

type JSONAny struct {
	Data any
}

func (j JSONAny) Value() (driver.Value, error) {
	if j.Data == nil {
		return nil, nil
	}
	bytes, err := json.Marshal(j.Data)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (j *JSONAny) Scan(value interface{}) error {
	if value == nil {
		j.Data = nil
		return nil
	}
	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return errors.New("unsupported scan type for JSONAny")
	}
	if len(bytes) == 0 {
		j.Data = nil
		return nil
	}
	return json.Unmarshal(bytes, &j.Data)
}

func (j *JSONAny) UnmarshalJSON(data []byte) (err error) {
	var d any
	err = json.Unmarshal(data, &d)
	if err != nil {
		return err
	}
	j.Data = d
	return nil
}

func (j JSONAny) MarshalJSON() ([]byte, error) {
	d := j.Data
	data, err := json.Marshal(d)
	return data, err
}

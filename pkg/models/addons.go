package models

import (
	"net/http"
	"strconv"
	"time"

	commonconstants "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/snowflake"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/times"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authentication"
	"gorm.io/gorm"
)

type Addons struct {
	Id          int64       `gorm:"column:id;primary_key;type:int8" json:"id"`
	Creator     string      `gorm:"column:creator;type:varchar(100)" json:"creator"`
	Modifier    string      `gorm:"column:modifier;type:varchar(100)" json:"modifier"`
	GmtCreated  *times.Time `gorm:"column:gmt_created;type:timestamp" json:"gmtCreated"`
	GmtModified *times.Time `gorm:"column:gmt_modified;type:timestamp" json:"gmtModified"`
	IsDeleted   string      `gorm:"column:is_deleted;type:varchar(1);default:N" json:"isDeleted"`
}

// SetTimeNowAndUser 设置 GmtCreated GmtModified 和 Creator Modifier
func (a *Addons) SetTimeNowAndUser(user string) {
	a.GmtCreated = times.Now()
	a.GmtModified = times.Now()
	a.Creator = user
	a.Modifier = user
	a.IsDeleted = "N"
}

func (a *Addons) BeforeCreate(tx *gorm.DB) (err error) {
	if a.Id == 0 {
		a.Id = snowflake.GenSnowFlakeId()
	}
	a.GmtCreated = times.Now()
	a.GmtModified = times.Now()
	return
}

func (a *Addons) BeforeUpdate(tx *gorm.DB) (err error) {
	a.GmtModified = times.Now()
	return
}

func (a *Addons) BeforeSave(tx *gorm.DB) (err error) {
	if a.Id == 0 {
		a.Id = snowflake.GenSnowFlakeId()
		a.GmtCreated = times.Now()
	}
	a.GmtModified = times.Now()
	return
}

func NewAddons() Addons {
	return Addons{
		Creator:     "admin",
		Modifier:    "admin",
		GmtCreated:  times.Now(),
		GmtModified: times.Now(),
	}
}

func NewAddonsWithId(id int64) Addons {
	return Addons{
		Id:          id,
		Creator:     "admin",
		Modifier:    "admin",
		GmtCreated:  times.Now(),
		GmtModified: times.Now(),
		IsDeleted:   commonconstants.DeleteNo,
	}
}

type HasId interface {
	GetId() int64
}

func (a *Addons) GetId() int64 {
	return a.Id
}

type Addons2 struct {
	Id        string `gorm:"column:id;primary_key;type:varchar(32)" json:"id"`
	Creator   string `gorm:"column:creator;type:varchar(128)" json:"creator"`
	Modifier  string `gorm:"column:modifier;type:varchar(128)" json:"modifier"`
	Created   int64  `gorm:"column:created;type:int8;default:(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)" json:"created"`
	Updated   int64  `gorm:"column:updated;type:int8;default:(EXTRACT(EPOCH FROM CURRENT_TIMESTAMP) * 1000)" json:"updated"`
	IsDeleted bool   `gorm:"column:is_deleted;type:bool;default:false" json:"is_deleted"`
}

func (a *Addons2) SetNewId() {
	if a.Id == "" {
		a.Id = strconv.FormatInt(snowflake.GenSnowFlakeId(), 10)
	}
}

func (a *Addons2) BeforeCreate(tx *gorm.DB) (err error) {
	a.SetNewId()
	return
}

func (a *Addons2) BeforeUpdate(tx *gorm.DB) (err error) {
	a.Updated = time.Now().UnixMilli()
	return
}

func (a *Addons2) SetCreatorFromReq(req *http.Request) error {
	acc, err := authentication.FromContext(req.Context())
	if err != nil {
		return err
	}
	a.Creator = acc.Name
	a.Modifier = acc.Name
	return nil
}

func (a *Addons2) SetModifierFromReq(req *http.Request) error {
	acc, err := authentication.FromContext(req.Context())
	if err != nil {
		return err
	}
	a.Modifier = acc.Name
	return nil
}

func NewAddons2() Addons2 {
	return Addons2{
		Creator:  "admin",
		Modifier: "admin",
		Created:  time.Now().UnixMilli(),
		Updated:  time.Now().UnixMilli(),
	}
}

package controllermanager

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"strings"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/k8s"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"

	"github.com/sirupsen/logrus"
	"github.com/tektoncd/pipeline/pkg/client/informers/externalversions"
	core "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	coordv1 "k8s.io/client-go/kubernetes/typed/coordination/v1"
	v1core "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/client-go/tools/record"
	"k8s.io/component-base/config"
)

const (
	defaultResyncPeriod    = 12 * time.Hour
	defaultLeaseDuration   = 15 * time.Second
	defaultRenewDeadline   = 10 * time.Second
	defaultRetryPeriod     = 2 * time.Second
	defaultWaitJitterRange = 200 // 毫秒
	defaultWaitCycle       = 10  // 秒
)

var once sync.Once

// ControllerOption 定义控制器的配置选项
type ControllerOption struct {
	NormalConcurrentSyncs   int
	Namespace               string
	TektonNamespace         string
	MinInformerResyncPeriod time.Duration
	LeaderElection          config.LeaderElectionConfiguration
}

// ControllerContext 定义控制器的运行上下文
type ControllerContext struct {
	Cluster               string
	Client                kubernetes.Interface
	InformerFactory       informers.SharedInformerFactory
	TektonInformerFactory externalversions.SharedInformerFactory
	Option                ControllerOption
	Stop                  <-chan struct{}
}

// Init 初始化控制器管理器
func Init() {
	once.Do(func() {
		go runSystemClusterControllers(context.Background())
	})
}

// newDefaultControllerOption 创建默认的控制器配置
func newDefaultControllerOption(namespace string) ControllerOption {
	syncNamespace := namespace
	if configs.Config.K8S.SyncResourceNamespace != "" {
		syncNamespace = configs.Config.K8S.SyncResourceNamespace
	}

	return ControllerOption{
		NormalConcurrentSyncs:   configs.Config.K8S.SyncWorkersNum,
		MinInformerResyncPeriod: defaultResyncPeriod,
		Namespace:               syncNamespace,
		TektonNamespace:         configs.Config.Pipeline.Namespace,
		LeaderElection: config.LeaderElectionConfiguration{
			LeaseDuration: metav1.Duration{Duration: defaultLeaseDuration},
			RenewDeadline: metav1.Duration{Duration: defaultRenewDeadline},
			RetryPeriod:   metav1.Duration{Duration: defaultRetryPeriod},
			ResourceLock:  resourcelock.LeasesResourceLock,
			LeaderElect:   true,
		},
	}
}

// newControllerContext 创建控制器上下文
func newControllerContext(cluster string, option ControllerOption) (*ControllerContext, error) {
	client, err := k8s.GetClientset(cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get k8s clientset: %w", err)
	}

	tektonClient, err := k8s.GetTektonClientset(cluster)
	if err != nil {
		return nil, fmt.Errorf("failed to get tekton clientset: %w", err)
	}

	sharedInformers := k8s.NewSharedInformerFactory(
		client,
		generateResyncPeriod(option.MinInformerResyncPeriod),
		option.Namespace,
	)

	tektonInformerFactory := k8s.NewTektonSharedInformerFactory(
		tektonClient,
		generateResyncPeriod(option.MinInformerResyncPeriod),
		option.TektonNamespace,
	)

	return &ControllerContext{
		Cluster:               cluster,
		Client:                client,
		InformerFactory:       sharedInformers,
		TektonInformerFactory: tektonInformerFactory,
		Option:                option,
	}, nil
}

// generateResyncPeriod 生成随机的重新同步周期
func generateResyncPeriod(period time.Duration) time.Duration {
	factor := rand.Float64() + 1
	return time.Duration(float64(period.Nanoseconds()) * factor)
}

// newEventRecorder 创建事件记录器
func newEventRecorder(kubeClient kubernetes.Interface) record.EventRecorder {
	eventBroadcaster := record.NewBroadcaster()
	eventBroadcaster.StartLogging(logger.Logger.Infof)
	eventBroadcaster.StartRecordingToSink(
		&v1core.EventSinkImpl{
			Interface: v1core.New(kubeClient.CoreV1().RESTClient()).Events(""),
		},
	)
	return eventBroadcaster.NewRecorder(scheme.Scheme, core.EventSource{Component: "cube-mantis"})
}

// getControllerLockName 生成控制器锁名称
func getControllerLockName(cluster, name string) string {
	return strings.ToLower(fmt.Sprintf("%s-cube-mantis-%s-controller-manager", cluster, name))
}

// getLockerIdentity 获取锁定标识
func getLockerIdentity() (string, error) {
	hostname, err := os.Hostname()
	if err != nil {
		return "", fmt.Errorf("failed to get hostname: %w", err)
	}
	return hostname + "-" + strings.TrimPrefix(configs.Config.App.Port, ":"), nil
}

// waitRandomTime 等待随机时间以避免竞争
func waitRandomTime() {
	nextTenSeconds := defaultWaitCycle - time.Now().Second()%defaultWaitCycle
	jitter := time.Millisecond * time.Duration(rand.Intn(defaultWaitJitterRange))
	time.Sleep(time.Duration(nextTenSeconds)*time.Second + jitter)
}

// startController 启动单个控制器
func startController(ctx context.Context, ctrlCtx ControllerContext, ctrl controller) error {
	logEntry := logger.Logger.WithFields(logrus.Fields{
		"cluster":    ctrlCtx.Cluster,
		"controller": ctrl.Name,
	})

	lockerIdentity, err := getLockerIdentity()
	if err != nil {
		return err
	}

	lockNamespace := getLockNamespace(ctrlCtx)
	resourceLock, err := createResourceLock(ctrlCtx, lockNamespace, ctrl.Name, lockerIdentity)
	if err != nil {
		return err
	}

	leaderConfig := leaderelection.LeaderElectionConfig{
		Lock:          resourceLock,
		LeaseDuration: ctrlCtx.Option.LeaderElection.LeaseDuration.Duration,
		RenewDeadline: ctrlCtx.Option.LeaderElection.RenewDeadline.Duration,
		RetryPeriod:   ctrlCtx.Option.LeaderElection.RetryPeriod.Duration,
		Callbacks:     createLeaderCallbacks(ctx, &ctrlCtx, ctrl, logEntry),
	}

	leaderelection.RunOrDie(ctx, leaderConfig)

	if shouldContinueRunning(ctrlCtx) {
		return nil
	}

	return nil
}

// getLockNamespace 获取锁命名空间
func getLockNamespace(ctrlCtx ControllerContext) string {
	if ctrlCtx.Option.Namespace != "" {
		return ctrlCtx.Option.Namespace
	}
	return configs.Config.K8S.SyncResourceNamespace
}

// createResourceLock 创建资源锁
func createResourceLock(ctrlCtx ControllerContext, namespace, controllerName, identity string) (resourcelock.Interface, error) {
	return resourcelock.New(
		ctrlCtx.Option.LeaderElection.ResourceLock,
		namespace,
		getControllerLockName(ctrlCtx.Cluster, controllerName),
		ctrlCtx.Client.CoreV1(),
		coordv1.New(ctrlCtx.Client.CoordinationV1().RESTClient()),
		resourcelock.ResourceLockConfig{
			Identity:      identity,
			EventRecorder: newEventRecorder(ctrlCtx.Client),
		},
	)
}

// createLeaderCallbacks 创建领导者选举回调
func createLeaderCallbacks(ctx context.Context, ctrlCtx *ControllerContext, ctrl controller, logger *logrus.Entry) leaderelection.LeaderCallbacks {
	return leaderelection.LeaderCallbacks{
		OnStartedLeading: func(ctx context.Context) {
			logger.Info("starting controller")
			ctrlCtx.Stop = ctx.Done()
			if err := ctrl.StartFunc(*ctrlCtx); err != nil {
				logger.WithError(err).Warn("failed to run controller")
			}
			ctrlCtx.InformerFactory.Start(ctx.Done())
			ctrlCtx.TektonInformerFactory.Start(ctx.Done())
		},
		OnStoppedLeading: func() {
			logger.Info("stopping controller")
		},
	}
}

// shouldContinueRunning 检查是否应该继续运行
func shouldContinueRunning(ctrlCtx ControllerContext) bool {
	select {
	case <-ctrlCtx.Stop:
		return !k8s.ClusterIsDeleted(ctrlCtx.Cluster)
	default:
		return true
	}
}

// startClusterControllers 启动指定集群的所有控制器
func startClusterControllers(ctx context.Context, cluster, namespace string) {
	ctrlCtx, err := newControllerContext(cluster, newDefaultControllerOption(namespace))
	if err != nil {
		logger.Logger.WithFields(logrus.Fields{
			"cluster": cluster,
			"error":   err,
		}).Error("failed to create controller context")
		return
	}

	startRegisteredControllers(ctx, ctrlCtx)
}

// startRegisteredControllers 启动所有注册的控制器
func startRegisteredControllers(ctx context.Context, ctrlCtx *ControllerContext) {
	for _, ctrl := range getRegisterControllers() {
		go startSingleController(ctx, ctrlCtx, ctrl)
	}
}

// startSingleController 启动单个控制器的包装函数
func startSingleController(ctx context.Context, ctrlCtx *ControllerContext, ctrl controller) {
	waitRandomTime()

	logEntry := logger.Logger.WithFields(logrus.Fields{
		"cluster":    ctrlCtx.Cluster,
		"controller": ctrl.Name,
	})

	if err := startController(ctx, *ctrlCtx, ctrl); err != nil {
		logEntry.WithError(err).Warn("failed to start controller")
		return
	}

	logEntry.Info("controller started successfully")
}

// runSystemClusterControllers 运行系统集群控制器
func runSystemClusterControllers(ctx context.Context) {
	if configs.Config.K8S.SyncResourceDisable {
		return
	}

	clusterId, err := getSystemClusterId(ctx)
	if err != nil {
		logger.Logger.Error("failed to get system cluster ID: ", err)
		return
	}

	logger.Logger.WithField("cluster", clusterId).Info("creating new cluster controller")
	startClusterControllers(ctx, clusterId, configs.Config.K8S.SyncResourceNamespace)
}

// getSystemClusterId 获取系统集群ID
func getSystemClusterId(ctx context.Context) (string, error) {
	var clusters []struct {
		Meta cmdb.K8sClusterModel `json:"meta"`
	}
	if err := cmdb.GetDefaultCMDB().GetModelInstances(ctx, constants.KubernetesClusterModelType, &clusters, nil); err != nil {
		return "", fmt.Errorf("获取kubernetes集群失败: %w", err)
	}

	for _, item := range clusters {
		if item.Meta.K8sClusterMeta().IsSystemCluster {
			return item.Meta.K8sClusterMeta().ClusterId, nil
		}
	}
	return "", fmt.Errorf("未找到系统默认集群")
}

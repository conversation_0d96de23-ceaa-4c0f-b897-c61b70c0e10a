package controllermanager

import (
	"sync"
	"sync/atomic"

	duckv1beta1 "knative.dev/pkg/apis/duck/v1beta1"
)

type controller struct {
	Name      string
	StartFunc func(ctx ControllerContext) error
}

var (
	controllerMu sync.Mutex
	controllers  atomic.Value
)

func RegisterController(name string, startFunc func(ctx ControllerContext) error) {
	controllerMu.Lock()
	defer controllerMu.Unlock()

	cs, _ := controllers.Load().([]controller)
	controllers.Store(append(cs, controller{Name: name, StartFunc: startFunc}))
}

func getRegisterControllers() []controller {
	cs, _ := controllers.Load().([]controller)
	return cs
}

var dealTaskRun = make(map[string]func(conditions duckv1beta1.Conditions, labels map[string]string) error)

func AddDealTaskRunFunc(add map[string]func(conditions duckv1beta1.Conditions, labels map[string]string) error) {
	for k, v := range add {
		dealTaskRun[k] = v
	}
}

func GetDealTaskRunFunc(name string) (func(conditions duckv1beta1.Conditions, labels map[string]string) error, bool) {
	res, ok := dealTaskRun[name]
	return res, ok
}

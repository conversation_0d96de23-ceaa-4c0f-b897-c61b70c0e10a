package base

import (
	"context"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"github.com/sirupsen/logrus"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"
)

const maxRetries = 10

type resource interface {
	GetResourceVersion() string
}

type namespaceGetter interface {
	GetNamespace() string
}

type ObjectController interface {
	ControllerName() string
	SyncHandler(namespace, name string) (bool, error)
	cache.ResourceEventHandler
}

type Controller interface {
	Context() context.Context
	Run(workers int, stopCh <-chan struct{})
	cache.ResourceEventHandler
	L() *logrus.Entry
	Client() kubernetes.Interface
	Cluster() string
}

type Option struct {
	Cluster         string
	Client          kubernetes.Interface
	FilterNamespace func(cluster string, namespace string) bool
	FilterObject    func(obj interface{}) bool
}

type controller struct {
	ctx            context.Context
	opt            Option
	ctrl           ObjectController
	informerSynced cache.InformerSynced
	queue          workqueue.RateLimitingInterface
	logger         *logrus.Entry
}

func New(opt Option, objCtrl ObjectController, informer cache.SharedIndexInformer) Controller {
	c := &controller{
		ctx:            context.Background(),
		opt:            opt,
		logger:         logger.Logger.WithFields(logrus.Fields{"controller": objCtrl.ControllerName(), "cluster": opt.Cluster}),
		informerSynced: informer.HasSynced,
		ctrl:           objCtrl,
		queue:          workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(), objCtrl.ControllerName()),
	}

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.onAdd,
		UpdateFunc: c.onUpdate,
		DeleteFunc: c.onDelete,
	})
	return c
}

func (c *controller) Context() context.Context     { return c.ctx }
func (c *controller) L() *logrus.Entry             { return c.logger }
func (c *controller) Client() kubernetes.Interface { return c.opt.Client }
func (c *controller) Cluster() string              { return c.opt.Cluster }

func (c *controller) Run(workers int, stopCh <-chan struct{}) {
	defer c.queue.ShutDown()

	if !cache.WaitForCacheSync(stopCh, c.informerSynced) {
		c.logger.Error("cache sync failed")
		return
	}

	for i := 0; i < workers; i++ {
		go wait.Until(c.worker, time.Second, stopCh)
	}
	<-stopCh
	c.logger.WithField("name", c.ctrl.ControllerName()).Debug("controller stopped")
}

func (c *controller) enqueue(obj interface{}) {
	key, err := cache.DeletionHandlingMetaNamespaceKeyFunc(obj)
	if err != nil {
		c.logger.Warnf("failed to get key for object %#v: %v", obj, err)
		return
	}
	c.queue.Add(key)
}

func (c *controller) omit(obj interface{}) bool {
	if c.opt.FilterNamespace != nil {
		if ng, ok := obj.(namespaceGetter); ok {
			if c.opt.FilterNamespace(c.opt.Cluster, ng.GetNamespace()) {
				return true
			}
		}
	}
	return c.opt.FilterObject != nil && c.opt.FilterObject(obj)
}

func (c *controller) OnAdd(obj interface{})         {}
func (c *controller) OnUpdate(old, new interface{}) {}
func (c *controller) OnDelete(obj interface{})      {}
func (c *controller) Namespaced() bool              { return false }

func (c *controller) onAdd(obj interface{}) {
	if c.omit(obj) {
		return
	}
	c.ctrl.OnAdd(obj)
	c.enqueue(obj)
}

func (c *controller) onUpdate(oldObj, newObj interface{}) {
	if c.omit(newObj) {
		return
	}

	oldResource, newResource := oldObj.(resource), newObj.(resource)
	if oldResource.GetResourceVersion() == newResource.GetResourceVersion() {
		return
	}

	c.ctrl.OnUpdate(oldObj, newObj)
	c.enqueue(newObj)
}

func (c *controller) onDelete(obj interface{}) {
	if c.omit(obj) {
		return
	}
	c.ctrl.OnDelete(obj)
	c.enqueue(obj)
}

func (c *controller) worker() {
	for c.processNextWorkItem() {
	}
}

func (c *controller) processNextWorkItem() bool {
	key, quit := c.queue.Get()
	if quit {
		return false
	}
	defer c.queue.Done(key)

	forget, err := c.syncHandler(key.(string))
	if err == nil && forget {
		c.queue.Forget(key)
		return true
	}

	c.handleError(err, key)
	return true
}

func (c *controller) syncHandler(key string) (bool, error) {
	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		c.logger.WithField("key", key).Warn("invalid resource key")
		return true, nil
	}
	return c.ctrl.SyncHandler(namespace, name)
}

func (c *controller) handleError(err error, key interface{}) {
	if err == nil {
		c.queue.Forget(key)
		return
	}

	if c.queue.NumRequeues(key) < maxRetries {
		c.queue.AddRateLimited(key)
		return
	}

	c.logger.WithField("key", key).Warnf("dropping item from queue: %v", err)
	c.queue.Forget(key)
}

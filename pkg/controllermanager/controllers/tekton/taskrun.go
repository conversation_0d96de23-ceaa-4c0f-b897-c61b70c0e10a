package tekton

import (
	"fmt"
	"os"
	"path"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/controllermanager"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/clients/s3store"

	cm "git.zhonganinfo.com/zainfo/cube-mantis/pkg/controllermanager"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/controllermanager/controllers/base"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/driver"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/apispec/errors"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	"github.com/tektoncd/pipeline/pkg/apis/pipeline/v1beta1"
	listers "github.com/tektoncd/pipeline/pkg/client/listers/pipeline/v1beta1"
	corev1 "k8s.io/api/core/v1"
)

// TaskRunController 管理TaskRun资源的控制器
type TaskRunController struct {
	taskRunList listers.TaskRunLister
	base.Controller
}

// New 创建一个新的TaskRunController实例
func New(ctx cm.ControllerContext) *TaskRunController {
	ctrl := &TaskRunController{}
	opt := base.Option{
		Cluster: ctx.Cluster,
		Client:  ctx.Client,
	}
	informer := ctx.TektonInformerFactory.Tekton().V1beta1().TaskRuns()
	ctrl.Controller = base.New(opt, ctrl, informer.Informer())
	ctrl.taskRunList = informer.Lister()

	return ctrl
}

func (trc *TaskRunController) ControllerName() string {
	return "taskRun"
}

// SyncHandler 处理TaskRun资源的同步
func (trc *TaskRunController) SyncHandler(ns, name string) (bool, error) {
	taskRun, err := trc.getTaskRun(ns, name)
	if err != nil {
		return true, err
	}

	if !trc.shouldProcessTaskRun(taskRun) {
		return true, nil
	}

	return trc.syncTaskRunRecord(taskRun)
}

// getTaskRun 获取TaskRun资源
func (trc *TaskRunController) getTaskRun(ns, name string) (*v1beta1.TaskRun, error) {
	taskRun, err := trc.taskRunList.TaskRuns(ns).Get(name)
	if err != nil {
		if errors.IsNotFound(err) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get TaskRun %s/%s: %v", ns, name, err)
	}
	return taskRun, nil
}

// shouldProcessTaskRun 判断是否需要处理该TaskRun
func (trc *TaskRunController) shouldProcessTaskRun(taskRun *v1beta1.TaskRun) bool {
	if taskRun == nil {
		return false
	}
	return taskRun.ObjectMeta.Labels[constants.PlatformLabel] == "cube-mantis"
}

func (prc *TaskRunController) checkFinished(taskRun *v1beta1.TaskRun) bool {
	var status string
	conditions := taskRun.Status.Conditions
	if len(conditions) == 0 {
		return false
	}
	switch conditions[0].Status {
	case corev1.ConditionTrue:
		// succuss
		status = "success"
	case corev1.ConditionFalse:
		if conditions[0].Reason == v1beta1.TaskRunSpecStatusCancelled {
			status = "fail"
		} else {
			status = "fail"
		}
	case corev1.ConditionUnknown:
		if conditions[0].Reason == "Running" || conditions[0].Reason == "Pending" {
			status = "running"
		}
		if conditions[0].Reason == v1beta1.TaskRunReasonTimedOut.String() {
			status = "fail"
		}
	default:
		return false
	}
	return status == "success" || status == "fail"
}

// syncTaskRunRecord 同步TaskRun记录
func (prc *TaskRunController) syncTaskRunRecord(taskRun *v1beta1.TaskRun) (bool, error) {
	logger.Logger.Infof("tekton task status callback...")

	if deal, ok := controllermanager.GetDealTaskRunFunc(taskRun.Spec.TaskRef.Name); ok {
		err := deal(taskRun.Status.Conditions, taskRun.ObjectMeta.Labels)
		if err != nil {
			return false, err
		}
	}

	// 当taskRun执行结束时上传日志文件并清理takRun
	if prc.checkFinished(taskRun) {
		dp, err := driver.NewDriverProvider()
		if err != nil {
			return false, err
		}
		log, err := dp.GetLog(prc.Context(), taskRun.Name)
		if err != nil {
			return false, err
		}

		filePath := path.Join("/tmp", fmt.Sprintf("%s.txt", taskRun.Name))
		file, err := os.OpenFile(filePath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0o666)
		if err != nil {
			return false, err
		}

		defer func() {
			file.Close()
			os.Remove(filePath)
		}()

		_, err = file.WriteString(log)
		if err != nil {
			return false, err
		}

		s3store.UploadFile(filePath, path.Join(constants.TaskRunLogFilePath, fmt.Sprintf("%s.txt", taskRun.Name)))

		// 删除执行完成的taskRun
		err = dp.Delete(prc.Context(), taskRun.Name)
		if err != nil {
			return false, err
		}
	}

	return true, nil
}

func startTaskRunController(ctx cm.ControllerContext) error {
	trc := New(ctx)
	go trc.Run(ctx.Option.NormalConcurrentSyncs, ctx.Stop)
	return nil
}

func Init() {
	cm.RegisterController("taskRun", startTaskRunController)
}

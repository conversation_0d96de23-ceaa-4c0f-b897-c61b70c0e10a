package gormx

import (
	"context"
	"errors"
	"fmt"
	"time"

	mantislog "git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/sirupsen/logrus"
	gormLog "gorm.io/gorm/logger"
	"gorm.io/gorm/utils"
)

type customGormLogger struct {
	logger *logrus.Logger
	gormLog.Config
	traceStr     string
	traceWarnStr string
	traceErrStr  string
}

func newGormLogger(level gormLog.LogLevel, ignoreRecordNotFoundError bool) *customGormLogger {
	return &customGormLogger{
		Config: gormLog.Config{
			LogLevel:                  level,
			IgnoreRecordNotFoundError: ignoreRecordNotFoundError,
		},
		logger:       mantislog.Logger,
		traceStr:     "%s\t[%.3fms] [rows:%v]\n%s",
		traceWarnStr: "%s %s\t[%.3fms] [rows:%v]\n%s",
		traceErrStr:  "%s %s\t[%.3fms] [rows:%v]\n%s",
	}
}

// LogMode log mode
func (gl customGormLogger) LogMode(level gormLog.LogLevel) gormLog.Interface {
	newlogger := gl
	newlogger.LogLevel = level
	return &newlogger
}

// Info print info
func (gl customGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if gl.LogLevel >= gormLog.Info {
		gl.logger.Info(fmt.Sprintf(msg, append([]interface{}{utils.FileWithLineNum()}, data...)...))
	}
}

// Warn print warn messages
func (gl customGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if gl.LogLevel >= gormLog.Warn {
		gl.logger.Warn(fmt.Sprintf(msg, append([]interface{}{utils.FileWithLineNum()}, data...)...))
	}
}

// Error print error messages
func (gl customGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if gl.LogLevel >= gormLog.Error {
		gl.logger.Error(fmt.Sprintf(msg, append([]interface{}{utils.FileWithLineNum()}, data...)...))
	}
}

// Trace print sql message
func (gl customGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if gl.LogLevel <= gormLog.Silent {
		return
	}

	elapsed := time.Since(begin)
	switch {
	case err != nil && gl.LogLevel >= gormLog.Error && (!errors.Is(err, gormLog.ErrRecordNotFound) || !gl.IgnoreRecordNotFoundError):
		sql, rows := fc()
		if rows == -1 {
			gl.logger.Error(fmt.Sprintf(gl.traceErrStr, utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			gl.logger.Error(fmt.Sprintf(gl.traceErrStr, utils.FileWithLineNum(), err, float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	case elapsed > gl.SlowThreshold && gl.SlowThreshold != 0 && gl.LogLevel >= gormLog.Warn:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %v", gl.SlowThreshold)
		if rows == -1 {
			gl.logger.Warn(fmt.Sprintf(gl.traceWarnStr, utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			gl.logger.Warn(fmt.Sprintf(gl.traceWarnStr, utils.FileWithLineNum(), slowLog, float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	case gl.LogLevel == gormLog.Info:
		sql, rows := fc()
		if rows == -1 {
			gl.logger.Info(fmt.Sprintf(gl.traceStr, utils.FileWithLineNum(), float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			gl.logger.Info(fmt.Sprintf(gl.traceStr, utils.FileWithLineNum(), float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	}
}

package gormx

import (
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
)

func AutoMigrate(ctx *commoncontext.MantisContext, models ...any) {
	db := GetDB(ctx)
	err := db.AutoMigrate(models...)
	if err != nil {
		logger.Logger.Panicf("%v", xerror.Wrap(err, "error in auto migrate table"))
	}
}

package gormx

import (
	"fmt"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	gormLog "gorm.io/gorm/logger"
)

var (
	db *gorm.DB
)

func Init() {
	InitNormal()
}

func InitNormal() {
	logLevel := gormLog.Error
	if configs.Config.Log.Level == "debug" {
		logLevel = gormLog.Info
	}
	gormLogger := newGormLogger(logLevel, false)

	dbConn, err := gorm.Open(postgres.Open(fmt.Sprintf("%v", configs.Config.Db.Dsn)), &gorm.Config{
		SkipDefaultTransaction: false,
		PrepareStmt:            true,
		Logger:                 gormLogger,
	})
	if err != nil {
		panic("failed to connect database, error=" + err.Error())
	}
	db = dbConn
	sqldb, _ := db.DB()
	sqldb.SetMaxIdleConns(configs.Config.Db.MaxIdle)
	sqldb.SetMaxOpenConns(configs.Config.Db.MaxOpen)
	sqldb.SetConnMaxLifetime(time.Duration(configs.Config.Db.MaxLifeTime) * time.Second)
}

func GetDB(ctx *commoncontext.MantisContext) *gorm.DB {
	if ctx.DB == nil {
		return db
	} else {
		return ctx.DB
	}
}

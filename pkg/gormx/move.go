package gormx

import (
	"errors"
	"fmt"

	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"gorm.io/gorm"
)

func Move(ctx *commoncontext.MantisContext, tableName, id, newParentId, newSiblingId string) error {
	return move(GetDB(ctx), tableName, id, newParentId, newSiblingId)
}

// move relocates a node in the specified table to a new parent with a given sibling order.
func move(db *gorm.DB, tableName, id, newParentId, newSiblingId string) error {
	// Validate inputs
	if tableName == "" || id == "" {
		return errors.New("tableName and id cannot be empty")
	}

	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Quote table name to prevent SQL injection
	quotedTableName := tx.Statement.Quote(tableName)

	// 1. Get current node's parent_id and sibling_order
	var currentParentId string
	var currentOrder int
	query := fmt.Sprintf("SELECT parent_id, sibling_order FROM %s WHERE id = ?", quotedTableName)
	result := struct {
		ParentID     string
		SiblingOrder int
	}{}
	err := tx.Raw(query, id).Scan(&result).Error
	if err != nil {
		return fmt.Errorf("failed to get current node: %w", err)
	}
	currentParentId = result.ParentID
	currentOrder = result.SiblingOrder

	// 2. Determine new sibling order
	var newOrder int
	if newSiblingId == "-1" {
		// Get max sibling_order for new parent
		query = fmt.Sprintf("SELECT COALESCE(MAX(sibling_order), 0) FROM %s WHERE parent_id = ?", quotedTableName)
		err = tx.Raw(query, newParentId).Scan(&newOrder).Error
		if err != nil {
			return fmt.Errorf("failed to get max sibling order: %w", err)
		}
		newOrder++ // Increment for new node's order
	} else {
		// Get sibling_order of newSiblingId
		query = fmt.Sprintf("SELECT sibling_order FROM %s WHERE id = ?", quotedTableName)
		err = tx.Raw(query, newSiblingId).Scan(&newOrder).Error
		if err != nil {
			return fmt.Errorf("failed to get sibling order: %w", err)
		}
	}

	// 3. Update current node's parent_id and sibling_order
	query = fmt.Sprintf("UPDATE %s SET parent_id = ?, sibling_order = ? WHERE id = ?", quotedTableName)
	err = tx.Exec(query, newParentId, newOrder, id).Error
	if err != nil {
		return fmt.Errorf("failed to update node: %w", err)
	}

	// 4. Update sibling_order for nodes under old parent (decrement orders > currentOrder)
	if currentParentId != "" {
		query = fmt.Sprintf("UPDATE %s SET sibling_order = sibling_order - 1 WHERE parent_id = ? AND sibling_order > ?", quotedTableName)
		err = tx.Exec(query, currentParentId, currentOrder).Error
		if err != nil {
			return fmt.Errorf("failed to update old parent's siblings: %w", err)
		}
	}

	// 5. Update sibling_order for nodes under new parent (increment orders >= newOrder)
	query = fmt.Sprintf("UPDATE %s SET sibling_order = sibling_order + 1 WHERE parent_id = ? AND sibling_order >= ? AND id != ?", quotedTableName)
	err = tx.Exec(query, newParentId, newOrder, id).Error
	if err != nil {
		return fmt.Errorf("failed to update new parent's siblings: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// moveX relocates a node and updates descendants_total for affected parents.
func moveX(db *gorm.DB, tableName, id, newParentId, newSiblingId string) error {
	// Validate inputs
	if tableName == "" || id == "" {
		return errors.New("tableName and id cannot be empty")
	}

	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer tx.Rollback()

	// Quote table name to prevent SQL injection
	quotedTableName := tx.Statement.Quote(tableName)

	// 1. Get old parent_id, sibling_order, is_dir, and descendants_total
	var oldParentId string
	var oldSiblingOrder float64
	var isDir bool
	var descendantsCount float64
	query := fmt.Sprintf("SELECT parent_id, sibling_order, is_dir, descendants_total FROM %s WHERE id = ?", quotedTableName)
	result := struct {
		ParentID         string
		SiblingOrder     float64
		IsDir            bool
		DescendantsTotal float64
	}{}
	err := tx.Raw(query, id).Scan(&result).Error
	if err != nil {
		return fmt.Errorf("failed to get current node: %w", err)
	}
	oldParentId = result.ParentID
	oldSiblingOrder = result.SiblingOrder
	isDir = result.IsDir
	descendantsCount = result.DescendantsTotal

	// 2. Determine new sibling order
	var newSiblingOrder float64
	if newSiblingId == "-1" {
		query = fmt.Sprintf("SELECT COALESCE(MAX(sibling_order), 0) FROM %s WHERE parent_id = ?", quotedTableName)
		err = tx.Raw(query, newParentId).Scan(&newSiblingOrder).Error
		if err != nil {
			return fmt.Errorf("failed to get max sibling order: %w", err)
		}
	} else {
		query = fmt.Sprintf("SELECT sibling_order FROM %s WHERE id = ?", quotedTableName)
		err = tx.Raw(query, newSiblingId).Scan(&newSiblingOrder).Error
		if err != nil {
			return fmt.Errorf("failed to get sibling order: %w", err)
		}
	}

	// 3. Update node's sibling_order
	query = fmt.Sprintf("UPDATE %s SET sibling_order = ? WHERE id = ?", quotedTableName)
	err = tx.Exec(query, newSiblingOrder, id).Error
	if err != nil {
		return fmt.Errorf("failed to update sibling order: %w", err)
	}

	// 4. Update sibling_order for new parent's children
	query = fmt.Sprintf("UPDATE %s SET sibling_order = sibling_order + 1 WHERE parent_id = ? AND sibling_order >= ? AND id != ?", quotedTableName)
	err = tx.Exec(query, newParentId, newSiblingOrder, id).Error
	if err != nil {
		return fmt.Errorf("failed to update new parent's siblings: %w", err)
	}

	// 5. Update sibling_order for old parent's children
	query = fmt.Sprintf("UPDATE %s SET sibling_order = sibling_order - 1 WHERE parent_id = ? AND sibling_order > ?", quotedTableName)
	err = tx.Exec(query, oldParentId, oldSiblingOrder).Error
	if err != nil {
		return fmt.Errorf("failed to update old parent's siblings: %w", err)
	}

	// 6. Update node's parent_id
	query = fmt.Sprintf("UPDATE %s SET parent_id = ? WHERE id = ?", quotedTableName)
	err = tx.Exec(query, newParentId, id).Error
	if err != nil {
		return fmt.Errorf("failed to update parent_id: %w", err)
	}

	// 7. Update descendants_total for new parent chain
	currentParentId := newParentId
	for currentParentId != "" && currentParentId != "null" {
		var updateValue float64
		if isDir {
			updateValue = descendantsCount
		} else {
			updateValue = 1
		}
		query = fmt.Sprintf("UPDATE %s SET descendants_total = descendants_total + ? WHERE id = ?", quotedTableName)
		err = tx.Exec(query, updateValue, currentParentId).Error
		if err != nil {
			return fmt.Errorf("failed to update new parent's descendants_total: %w", err)
		}

		// Get next parent
		var nextParentId string
		query = fmt.Sprintf("SELECT parent_id FROM %s WHERE id = ?", quotedTableName)
		err = tx.Raw(query, currentParentId).Scan(&nextParentId).Error
		if err != nil {
			return fmt.Errorf("failed to get next parent: %w", err)
		}
		currentParentId = nextParentId
	}

	// 8. Update descendants_total for old parent chain
	currentParentId = oldParentId
	for currentParentId != "" && currentParentId != "null" {
		var updateValue float64
		if isDir {
			updateValue = descendantsCount
		} else {
			updateValue = 1
		}
		query = fmt.Sprintf("UPDATE %s SET descendants_total = descendants_total - ? WHERE id = ?", quotedTableName)
		err = tx.Exec(query, updateValue, currentParentId).Error
		if err != nil {
			return fmt.Errorf("failed to update old parent's descendants_total: %w", err)
		}

		// Get next parent
		var nextParentId string
		query = fmt.Sprintf("SELECT parent_id FROM %s WHERE id = ?", quotedTableName)
		err = tx.Raw(query, currentParentId).Scan(&nextParentId).Error
		if err != nil {
			return fmt.Errorf("failed to get next parent: %w", err)
		}
		currentParentId = nextParentId
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

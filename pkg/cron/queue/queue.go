package queue

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron/trigger"
	"github.com/reugn/go-quartz/quartz"
	"github.com/sirupsen/logrus"
)

const jobTABLEName = "mantis_schedule_job"

var unmarshalMap = make(map[string]func(description string) (quartz.Job, error))

func AddUnmarshalMap(key string, deal func(description string) (quartz.Job, error)) {
	unmarshalMap[key] = deal
}

// marshal returns the JSON encoding of the job.
func marshal(job quartz.ScheduledJob) ([]byte, error) {
	var serialized serializedJob
	serialized.Job = job.JobDetail().Job().Description()
	serialized.JobKeyName = job.JobDetail().JobKey().Name()
	serialized.JobKeyGroup = job.JobDetail().JobKey().Group()
	serialized.Options = job.JobDetail().Options()
	serialized.Trigger = job.Trigger().Description()
	serialized.NextRunTime = job.NextRunTime()
	return json.Marshal(serialized)
}

// unmarshal parses the JSON-encoded job.
func unmarshal(encoded []byte) (quartz.ScheduledJob, error) {
	var serialized serializedJob
	if err := json.Unmarshal(encoded, &serialized); err != nil {
		return nil, err
	}

	triggerOpts := strings.Split(serialized.Trigger, quartz.Sep)
	if len(triggerOpts) == 0 {
		return nil, fmt.Errorf("error trigger, %s", serialized.Trigger)
	}
	var trig quartz.Trigger
	switch triggerOpts[0] {
	case "SimpleTrigger":
		if len(triggerOpts) < 2 {
			return nil, fmt.Errorf("error trigger, %s", serialized.Trigger)
		}
		interval, err := time.ParseDuration(triggerOpts[1])
		if err != nil {
			return nil, err
		}
		trig = quartz.NewSimpleTrigger(interval)
	case "RunOnceTrigger":
		if len(triggerOpts) < 3 {
			return nil, fmt.Errorf("error trigger, %s", serialized.Trigger)
		}
		delay, err := time.ParseDuration(triggerOpts[1])
		if err != nil {
			return nil, err
		}
		onceTrigger := quartz.NewRunOnceTrigger(delay)
		if triggerOpts[2] == "expired" {
			onceTrigger.Expired = true
		}
		trig = onceTrigger
	case "CronTrigger":
		if len(triggerOpts) < 3 {
			return nil, fmt.Errorf("error trigger, %s", serialized.Trigger)
		}
		loc, err := time.LoadLocation(triggerOpts[2])
		if err != nil {
			return nil, err
		}
		c, err := quartz.NewCronTriggerWithLoc(triggerOpts[1], loc)
		if err != nil {
			return nil, err
		}
		trig = c
	case "TimeRangeCronTrigger":
		if len(triggerOpts) < 6 {
			return nil, fmt.Errorf("error trigger, %s", serialized.Trigger)
		}
		loc, err := time.LoadLocation(triggerOpts[3])
		if err != nil {
			return nil, err
		}
		startTime, err := strconv.ParseInt(triggerOpts[4], 10, 64)
		if err != nil {
			return nil, err
		}
		endTime, err := strconv.ParseInt(triggerOpts[5], 10, 64)
		if err != nil {
			return nil, err
		}
		t, err := trigger.NewTimeRangeCronTriggerWithLoc(triggerOpts[2], loc, startTime, endTime)
		if err != nil {
			return nil, err
		}
		trig = t
	}
	if trig == nil {
		return nil, fmt.Errorf("trigger is nil, trigger string: %s", serialized.Trigger)
	}
	job, err := unmarshalMap[serialized.JobKeyGroup](serialized.Job)
	if err != nil {
		return nil, err
	}
	jobKey := quartz.NewJobKeyWithGroup(serialized.JobKeyName, serialized.JobKeyGroup)
	jobDetail := quartz.NewJobDetailWithOptions(job, jobKey, serialized.Options)

	return &scheduledMantisJob{
		jobDetail:   jobDetail,
		trigger:     trig,
		nextRunTime: serialized.NextRunTime,
	}, nil
}

// DbJobQueue implements the quartz.JobQueue interface, using the file system
// as the persistence layer.
type DbJobQueue struct {
	logger *logrus.Logger
	db     *sql.DB
}

var _ quartz.JobQueue = (*DbJobQueue)(nil)

// NewJobQueue initializes and returns an empty jobQueue.
func NewDbJobQueue(logger *logrus.Logger, db *sql.DB) (*DbJobQueue, error) {
	// create table if not exists
	sql := `CREATE TABLE IF NOT EXISTS $TABLE (
		"key" varchar PRIMARY KEY,
		job json NOT NULL,
		next_run_time numeric NULL
	)`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	_, err := db.Exec(safeSql)
	if err != nil {
		return nil, err
	}
	return &DbJobQueue{logger: logger, db: db}, nil
}

// Push inserts a new scheduled job to the queue.
// This method is also used by the Scheduler to reschedule existing jobs that
// have been dequeued for execution.
func (jq *DbJobQueue) Push(job quartz.ScheduledJob) error {
	jq.logger.Trace("Push job", "key", job.JobDetail().JobKey().String())
	serialized, err := marshal(job)
	if err != nil {
		return err
	}
	sql := `insert into "$TABLE" (key, job, next_run_time) values ($1, $2, $3)`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	_, err = jq.db.Exec(safeSql, job.JobDetail().JobKey().String(), string(serialized), job.NextRunTime())
	if err != nil {
		jq.logger.Error("Failed to write job", "error", err)
		return err
	}
	return nil
}

// Pop removes and returns the next scheduled job from the queue.
func (jq *DbJobQueue) Pop() (job quartz.ScheduledJob, err error) {
	jq.logger.Trace("Pop")
	tx, err := jq.db.Begin()
	if err != nil {
		return nil, err
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()
	sql := `select job from $TABLE order by next_run_time asc limit 1`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := tx.Query(safeSql)
	if err != nil {
		return nil, err
	}
	var j string
	if rows.Next() {
		rows.Scan(&j)
		job, err = unmarshal([]byte(j))
		if err != nil {
			return nil, err
		}
	} else {
		return nil, nil
	}
	rows.Close()
	if err == nil {
		sql := `delete from $TABLE where key = $1`
		safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
		res, err := tx.Exec(safeSql, job.JobDetail().JobKey().String())
		if err != nil {
			return nil, err
		}
		rowsAffected, _ := res.RowsAffected()
		if rowsAffected == 0 {
			// 有其他人抢先删除了
			return nil, fmt.Errorf("delete failed, another service took it")
		}
		return job, nil
	}
	return nil, err
}

// Head returns the first scheduled job without removing it from the queue.
func (jq *DbJobQueue) Head() (quartz.ScheduledJob, error) {
	jq.logger.Trace("Head")
	job, err := jq.findHead()
	if err != nil {
		jq.logger.Error("Failed to find job", "error", err)
	}
	return job, err
}

func (jq *DbJobQueue) findHead() (quartz.ScheduledJob, error) {
	sql := `select job from "$TABLE" order by next_run_time asc limit 1`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := jq.db.Query(safeSql)
	defer func() {
		rows.Close()
	}()
	if err != nil {
		return nil, err
	}
	if rows.Next() {
		var j string
		rows.Scan(&j)
		return unmarshal([]byte(j))
	} else {
		return nil, nil
	}
}

// Get returns the scheduled job with the specified key without removing it
// from the queue.
func (jq *DbJobQueue) Get(jobKey *quartz.JobKey) (quartz.ScheduledJob, error) {
	jq.logger.Trace("Get")
	sql := `select job from "$TABLE" where key = $1`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := jq.db.Query(safeSql, jobKey.String())
	defer func() {
		rows.Close()
	}()
	if err != nil {
		return nil, err
	}
	if rows.Next() {
		var j string
		rows.Scan(&j)
		return unmarshal([]byte(j))
	} else {
		return nil, nil
	}
}

// Remove removes and returns the scheduled job with the specified key.
func (jq *DbJobQueue) Remove(jobKey *quartz.JobKey) (quartz.ScheduledJob, error) {
	jq.logger.Trace("Remove")
	sql := `select job from "$TABLE" where key = $1`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := jq.db.Query(safeSql, jobKey.String())
	defer func() {
		rows.Close()
	}()
	if err != nil {
		return nil, err
	}
	if rows.Next() {
		var j string
		rows.Scan(&j)
		sql := `delete from "$TABLE" where key = $1`
		safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
		if _, err := jq.db.Exec(safeSql, jobKey.String()); err != nil {
			return nil, err
		}
		return unmarshal([]byte(j))
	} else {
		return nil, nil
	}
}

// ScheduledJobs returns the slice of all scheduled jobs in the queue.
func (jq *DbJobQueue) ScheduledJobs(
	matchers []quartz.Matcher[quartz.ScheduledJob],
) ([]quartz.ScheduledJob, error) {
	jq.logger.Trace("ScheduledJobs")
	var jobs []quartz.ScheduledJob
	sql := `select job from "$TABLE"`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := jq.db.Query(safeSql)
	defer func() {
		rows.Close()
	}()
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		var j string
		err = rows.Scan(&j)
		if err != nil {
			return nil, err
		}
		job, err := unmarshal([]byte(j))
		if err != nil {
			return nil, err
		}
		if isMatch(job, matchers) {
			jobs = append(jobs, job)
		}
	}
	return jobs, nil
}

func isMatch(job quartz.ScheduledJob, matchers []quartz.Matcher[quartz.ScheduledJob]) bool {
	for _, matcher := range matchers {
		// require all matchers to match the job
		if !matcher.IsMatch(job) {
			return false
		}
	}
	return true
}

// Size returns the size of the job queue.
func (jq *DbJobQueue) Size() (int, error) {
	jq.logger.Trace("Size")
	var res int
	sql := `select count(*) from "$TABLE"`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	rows, err := jq.db.Query(safeSql)
	defer func() {
		rows.Close()
	}()
	if err != nil {
		return 0, err
	}
	if rows.Next() {
		err = rows.Scan(&res)
		if err != nil {
			return 0, err
		}
	}
	return res, nil
}

// Clear clears the job queue.
func (jq *DbJobQueue) Clear() error {
	jq.logger.Trace("Clear")
	sql := `delete from "$TABLE"`
	safeSql := strings.Replace(sql, "$TABLE", jobTABLEName, 1)
	_, err := jq.db.Exec(safeSql)
	return err
}

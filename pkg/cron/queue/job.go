package queue

import (
	"github.com/reugn/go-quartz/quartz"
)

// scheduledMantisJob
type scheduledMantisJob struct {
	jobDetail   *quartz.JobDetail
	trigger     quartz.Trigger
	nextRunTime int64
}

// serializedJob
type serializedJob struct {
	Job         string                   `json:"job"`
	JobKeyName  string                   `json:"job_key_name"`
	JobKeyGroup string                   `json:"job_key_group"`
	Options     *quartz.JobDetailOptions `json:"job_options"`
	Trigger     string                   `json:"trigger"`
	NextRunTime int64                    `json:"next_run_time"`
}

var _ quartz.ScheduledJob = (*scheduledMantisJob)(nil)

func (job *scheduledMantisJob) JobDetail() *quartz.JobDetail { return job.jobDetail }
func (job *scheduledMantisJob) Trigger() quartz.Trigger      { return job.trigger }
func (job *scheduledMantisJob) NextRunTime() int64           { return job.nextRunTime }

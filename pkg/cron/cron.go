package cron

import (
	"context"
	"fmt"
	"sync"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/constants"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron/queue"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/cron/trigger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/gormx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/reugn/go-quartz/quartz"
)

var scheduler quartz.Scheduler

func Init() {
	ctx := &commoncontext.MantisContext{Context: context.Background()}
	db, err := gormx.GetDB(ctx).DB()
	if err != nil {
		panic(err)
	}
	// 初始化job表
	jobQueue, err := queue.NewDbJobQueue(logger.Logger, db)
	if err != nil {
		panic(err)
	}
	sch, err := quartz.NewStdScheduler(
		quartz.WithOutdatedThreshold(time.Second), // considering file system I/O latency
		quartz.WithQueue(jobQueue, &sync.Mutex{}),
	)
	if err != nil {
		panic(err)
	}
	scheduler = sch
	scheduler.Start(ctx)
}

func ShutDown() {
	scheduler.Stop()
}

type Task struct {
	StartTime   *time.Time
	EndTime     *time.Time
	Cron        string
	JobName     string // 任务类型
	Key         string
	Type        int64
	ExecuteTime *time.Time
	Job         quartz.Job
}

func (t *Task) RegisterJob() error {
	if t.Type == constants.TaskTypeManual {
		return nil
	}
	key := quartz.NewJobKeyWithGroup(t.Key, t.JobName)
	scheduler.DeleteJob(key)
	jobDetail := quartz.NewJobDetail(t.Job, key)
	var trig quartz.Trigger
	switch t.Type {
	case constants.TaskTypeScheduled:
		if t.ExecuteTime.Before(time.Now()) {
			return fmt.Errorf("不能在 %s 执行定时任务", t.ExecuteTime.Format("2006-01-02 15:04:05"))
		}
		trig = quartz.NewRunOnceTrigger(time.Until(*t.ExecuteTime))
	case constants.TaskTypeInterval, constants.TaskTypePeriod:
		shanghaiZone, err := time.LoadLocation("Asia/Shanghai")
		if err != nil {
			logger.Logger.Error("加载上海时区失败:", err)
			return err
		}
		var startTime int64 = 0
		if t.StartTime != nil {
			startTime = t.StartTime.UnixMilli()
		}
		var endTime int64 = 0
		if t.EndTime != nil {
			endTime = t.EndTime.UnixMilli()
		}
		tri, err := trigger.NewTimeRangeCronTriggerWithLoc(t.Cron, shanghaiZone, startTime, endTime)
		if err != nil {
			logger.Logger.Errorf("error in registerJob, %s", err.Error())
			return err
		}
		trig = tri
	}
	err := scheduler.ScheduleJob(jobDetail, trig)
	if err != nil {
		logger.Logger.Errorf("error in registerJob, %s", err.Error())
		return err
	}
	return nil
}

func RemoveJob(key string) error {
	jobKey := quartz.NewJobKey(key)
	return scheduler.DeleteJob(jobKey)
}

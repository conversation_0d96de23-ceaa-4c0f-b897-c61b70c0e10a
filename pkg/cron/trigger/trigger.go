package trigger

import (
	"fmt"
	"time"

	"github.com/reugn/go-quartz/quartz"
)

// TimeRangeCronTrigger 自定义触发器，支持 StartTime 和 EndTime
type TimeRangeCronTrigger struct {
	innerTrigger *quartz.CronTrigger // 内部标准 CronTrigger
	startTime    int64               // 开始时间ms
	endTime      int64               // 结束时间ms
}

// NewTimeRangeCronTrigger 创建新的触发器
func NewTimeRangeCronTrigger(expression string, startTime int64, endTime int64) (*TimeRangeCronTrigger, error) {
	// 创建标准 CronTrigger 作为内部触发器
	inner, err := quartz.NewCronTrigger(expression)
	if err != nil {
		return nil, err
	}
	return &TimeRangeCronTrigger{
		innerTrigger: inner,
		startTime:    startTime,
		endTime:      endTime,
	}, nil
}

func NewTimeRangeCronTriggerWithLoc(expression string, loc *time.Location, startTime int64, endTime int64) (*TimeRangeCronTrigger, error) {
	inner, err := quartz.NewCronTriggerWithLoc(expression, loc)
	if err != nil {
		return nil, err
	}
	return &TimeRangeCronTrigger{
		innerTrigger: inner,
		startTime:    startTime,
		endTime:      endTime,
	}, nil
}

func (t *TimeRangeCronTrigger) Description() string {
	return fmt.Sprintf("TimeRangeCronTrigger%s%s%s%d%s%d", quartz.Sep, t.innerTrigger.Description(), quartz.Sep, t.startTime, quartz.Sep, t.endTime)
}

// NextFireTime 计算下一次触发时间
func (t *TimeRangeCronTrigger) NextFireTime(prev int64) (int64, error) {
	// 获取当前时间
	now := time.Now().UnixMilli()

	// 如果设置了开始时间且尚未达到，返回开始时间
	if t.startTime != 0 && now < t.startTime {
		return t.startTime, nil
	}

	// 如果设置了结束时间且当前时间已超过，停止触发
	if t.endTime != 0 && now >= t.endTime {
		return 0, quartz.ErrTriggerExpired
	}

	// 否则，委托给内部 CronTrigger 计算
	return t.innerTrigger.NextFireTime(prev)
}

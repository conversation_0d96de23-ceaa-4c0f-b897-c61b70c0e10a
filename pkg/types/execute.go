package types

import "sort"

// ExecuteType 定义用例执行类型，用于标识是由何种方式触发
type ExecuteType string

const (
	Debug      ExecuteType = "debug"       // 调试执行
	Cloud      ExecuteType = "cloud"       // 云端执行
	Manual     ExecuteType = "manual"      // 手动执行
	Cron       ExecuteType = "cron"        // 定时执行
	ThirdParty ExecuteType = "third_party" // 流水线执行
)

// ExecuteTypeInfo 定义执行类型信息
type ExecuteTypeInfo struct {
	Value ExecuteType `json:"value"`
	Label string      `json:"label"`
}

// 创建执行类型枚举映射
var executeTypeMap = map[ExecuteType]ExecuteTypeInfo{
	Debug:      {Value: Debug, Label: "调试执行"},
	Cloud:      {Value: Cloud, Label: "云端执行"},
	Manual:     {Value: Manual, Label: "手动执行"},
	Cron:       {Value: Cron, Label: "定时执行"},
	ThirdParty: {Value: ThirdParty, Label: "外部调用"},
}

// GetAllExecTypes 获取所有执行类型信息列表
func GetAllExecTypes() ExecuteTypeInfoSlice {
	results := make(ExecuteTypeInfoSlice, 0, len(executeTypeMap))
	for _, v := range executeTypeMap {
		results = append(results, v)
	}
	sort.Sort(results)
	return results
}

type ExecuteTypeInfoSlice []ExecuteTypeInfo

// 实现 sort.Interface 接口的三个方法
func (s ExecuteTypeInfoSlice) Len() int {
	return len(s)
}

func (s ExecuteTypeInfoSlice) Less(i, j int) bool {
	// 按 Age 升序排序
	return s[i].Value < s[j].Value
}

func (s ExecuteTypeInfoSlice) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// IsValidExecType 验证是否为有效执行类型信息
func IsValidExecType(code ExecuteType) bool {
	_, exists := executeTypeMap[code]
	return exists
}

package types

// DbType 定义用例执行类型，用于标识是由何种方式触发
type DbType string

const (
	MySQL    DbType = "mysql"    // mysql
	Postgres DbType = "postgres" // postgres
)

// DbTypeInfo 定义执行类型信息
type DbTypeInfo struct {
	Value DbType `json:"value"`
	Label string `json:"label"`
}

// 创建数据库类型枚举映射
var dbTypeMap = map[DbType]DbTypeInfo{
	MySQL:    {Value: MySQL, Label: "mysql"},
	Postgres: {Value: Postgres, Label: "postgres"},
}

// GetAllDbTypes 获取所有数据库类型信息列表
func GetAllDbTypes() []DbTypeInfo {
	results := make([]DbTypeInfo, 0, len(dbTypeMap))
	for _, v := range dbTypeMap {
		results = append(results, v)
	}
	return results
}

// IsValidDbType 验证是否为有效数据库类型信息
func IsValidDbType(code DbType) bool {
	_, exists := dbTypeMap[code]
	return exists
}

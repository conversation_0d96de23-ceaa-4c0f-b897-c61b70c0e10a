package constants

const (
	DeleteYes                    = "Y"
	DeleteNo                     = "N"
	RequestSplitChar             = ","
	JsonContentType              = "application/json"
	XmlContentType               = "application/xmlx"
	UrlencodedContentType        = "application/x-www-form-urlencoded"
	FormContentType              = "multipart/form-data"
	ResponseErrorCode            = "1001"
	ResponseSuccessCode          = "1000"
	NotificationIm               = "DEFAULT_IM" // 企微通知方式
	NotificationEmail            = "DEFAULT_EMAIL"
	NotificationEnterpriseWechat = "DEFAULT_ENTERPRISE_WECHAT"
	NotificationDingDing         = "DEFAULT_DING_DING"
	NotificationLark             = "DEFAULT_LARK"

	TaskTypeScheduled = 1 // 定时任务
	TaskTypePeriod    = 2 // 周期任务
	TaskTypeManual    = 3 // 手动任务
	TaskTypeInterval  = 4 // 间隔任务

	// header中仓库全路径的key
	HubUrlKey = "hubUrl"

	GitRepoUrlKey = "repoUrl"

	KubernetesClusterModelType = "cluster_model"

	TaskRunLogFilePath = "cube-mantis/logs/"

	TrivyTaskType  = "trivy"
	SonarTaskType  = "sonar"
	JacocoTaskType = "jacoco"
	UiTaskType     = "ui"

	PlatformLabel = "cube.zhonganinfo.com/platform"
	TaskIdLabel   = "cube.zhonganinfo.com/task-id"
	ExecIdLabel   = "cube.zhonganinfo.com/exec-id"
	DetailIdLabel = "cube.zhonganinfo.com/detail-id"
	IsFirstLabel  = "cube.zhonganinfo.com/is-first"
	TaskFromLabel = "cube.zhonganinfo.com/task-from"
	ReportIdLabel = "cube.zhonganinfo.com/report-id"
)

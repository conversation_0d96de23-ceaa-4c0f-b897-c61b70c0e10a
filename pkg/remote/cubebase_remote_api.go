package remote

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	shipcmdb "git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/utils"
	"github.com/duke-git/lancet/v2/netutil"
	"github.com/duke-git/lancet/v2/xerror"
)

type CubeBaseRemoteApi struct{}

func (c CubeBaseRemoteApi) GetChildResource(loginName, companyId, service, pcode string) []dto.CubeResourceDTO {
	m := make(map[string]any)
	m["loginName"] = loginName
	m["service"] = service
	m["pcode"] = pcode
	m["companyId"] = companyId
	request := &netutil.HttpRequest{
		RawURL: configs.Config.Auth.AuthUrl + "/base/open/getChildResource?" + netutil.ConvertMapToQueryString(m),
		Method: "GET",
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to base checkPermission, err=%+v, controller=%v", err, resp)
	}
	var rs dto.CubeResourceResp
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		logger.Logger.Panicf("error in decode resource, err=%+v", err)
	}
	if rs.Code != 0 {
		logger.Logger.Panicf("cude base return error, code=%v", rs.Code)
	}
	res := make([]dto.CubeResourceDTO, 0)
	c.convertTree2Slice(rs.Result, &res)
	return res
}

func (c CubeBaseRemoteApi) convertTree2Slice(tree []dto.CubeResourceDTO, res *[]dto.CubeResourceDTO) {
	if len(tree) != 0 {
		for _, v := range tree {
			// 过滤非mantis的code
			if !(strings.HasPrefix(v.Code, "MANTIS") || strings.HasPrefix(v.Code, "MAGIC")) {
				continue
			}
			*res = append(*res, dto.CubeResourceDTO{
				Id:    v.Id,
				Name:  v.Name,
				Code:  v.Code,
				Type:  v.Type,
				Pid:   v.Pid,
				Props: v.Props,
			})
			c.convertTree2Slice(v.Children, res)
		}
	}
}

func (c CubeBaseRemoteApi) GetTagsByIds(ctx *commoncontext.MantisContext, tagIds string) []dto.CubeTagDTO {
	startTime := time.Now()
	httpClient := netutil.NewHttpClient()
	params := make(map[string]string)
	params["pageSize"] = "999"
	if tagIds != "" {
		params["tagIdList"] = tagIds
	}
	if projectNo := ctx.Header.Get("spaceid"); projectNo != "" {
		params["projectId"] = projectNo
	}
	uri := "/base/tags/findTagsByElement?"
	for k, v := range params {
		uri += fmt.Sprintf("%s=%s&", url.QueryEscape(k), url.QueryEscape(v))
	}
	uri = uri[:len(uri)-1]
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Auth.AuthUrl + uri,
		Method:  "GET",
		Headers: ctx.Header,
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		logger.Logger.Panicf("error in send request to cube getCubeTags, err=%+v, resp=%v", err, resp)
	}
	var rs dto.CubeTagResp
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		logger.Logger.Panicf("error in decode tags, err=%+v", err)
	}
	if rs.ReturnCode != dto.CubeBaseRespOkCode {
		logger.Logger.Panicf("%v", xerror.New("error calling cube, msg = %s", rs.ReturnMsg))
	}
	logger.Logger.Info("Func -> GetTagsByIds执行耗时：", time.Since(startTime))
	res := make([]dto.CubeTagDTO, 0, len(rs.Data.List))
	for _, data := range rs.Data.List {
		res = append(res, dto.CubeTagDTO{
			Label:  data.TagName,
			Value:  data.Id,
			Colour: data.Colour,
		})
	}
	return res
}

func (c CubeBaseRemoteApi) GetProjectInfo(projectNo string) (*dto.CubeProjectDTO, error) {
	startTime := time.Now()
	httpClient := netutil.NewHttpClient()
	uri := "/base/open/v1/getProjectInfo?projectId=" + projectNo
	request := &netutil.HttpRequest{
		RawURL: configs.Config.Auth.AuthUrl + uri,
		Method: "GET",
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		logger.Logger.Panicf("error in send request to base getCubeTags, err=%+v, controller=%v", err, resp)
	}
	var rs dto.CubeProjectResp
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		return nil, err
	}
	if rs.ReturnCode != dto.CubeBaseRespOkCode {
		return nil, fmt.Errorf("error calling cube, msg = %s", rs.ReturnMsg)
	}
	logger.Logger.Info("Func -> GetProjectInfo执行耗时：", time.Since(startTime))
	return &(rs.Data), nil
}

// GetAppsByUser 查询用户可见的应用列表
func (c CubeBaseRemoteApi) GetAppsByUser(user dto.UserInfo, projectNo string) ([]shipcmdb.CmdbApp, error) {
	userId := user.Id
	companyId := utils.IDString(user.CompanyID)
	if projectNo != "" {
		// 项目内权限
		projectInfo, err := c.GetProjectInfo(projectNo)
		if err != nil {
			return nil, err
		}
		projectFilter := ""
		for _, system := range projectInfo.System {
			projectFilter += fmt.Sprintf("project_id='%v'||", system.Id)
		}
		cmdbApps, err := cmdb.Client.GetAppList(context.Background(), url.Values{
			"page":    []string{"1"},
			"perPage": []string{"999"},
			"filter":  []string{fmt.Sprintf("(%s)&&(owner_list~'%s'||contractor_list~'%s')", projectFilter, userId, userId)},
		})
		if err != nil {
			return nil, err
		}
		return cmdbApps, nil
	} else {
		cmdbApps, err := cmdb.Client.GetAppList(context.Background(), url.Values{
			"page":    []string{"1"},
			"perPage": []string{"999"},
			"filter":  []string{fmt.Sprintf("(owner_list~'%s'||contractor_list~'%s') && company_id='%s'", userId, userId, companyId)},
		})
		if err != nil {
			return nil, err
		}
		return cmdbApps, nil
	}
}

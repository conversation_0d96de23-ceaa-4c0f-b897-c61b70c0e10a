package remote

import (
	"net/http"
	"net/url"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	commoncontext "git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/context"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	shipcmdb "git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/cmdb"
	"github.com/duke-git/lancet/v2/netutil"
)

type ShipRemoteApi struct{}

func (ShipRemoteApi) GetAppInfosByHubUrl(ctx *commoncontext.MantisContext, hubUrl string) []shipcmdb.CmdbApp {
	startTime := time.Now()
	httpClient := netutil.NewHttpClient()
	uri := "/ship/api/v1/scm/app_info"
	hub, err := url.QueryUnescape(hubUrl)
	if err != nil {
		logger.Logger.Panicf("error in decode hub url, hub url = %s, err=%s", hubUrl, err.Error())
	}
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Domain.Deckjob + uri,
		Method:  http.MethodPost,
		Headers: ctx.Header,
		Body: jsonx.Marshal(map[string]any{
			"repo_url": hub,
		}),
	}
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != http.StatusOK {
		logger.Logger.Panicf("error in send request to ship get appinfo by huburl, err=%+v, controller=%v", err, resp)
	}
	rs := make([]dto.ShipAppDto, 0)
	err = httpClient.DecodeResponse(resp, &rs)
	if err != nil {
		logger.Logger.Panicf("error in decode ship appinfos, err=%+v", err)
	}
	logger.Logger.Info("Func -> GetAppInfosByHubUrl执行耗时：", time.Since(startTime))
	list := make([]shipcmdb.CmdbApp, 0, len(rs))
	for _, a := range rs {
		list = append(list, shipcmdb.CmdbApp{
			AppId:     a.AppId,
			Name:      a.AppName,
			ProjectId: a.ProjectId,
		})
	}
	return list
}

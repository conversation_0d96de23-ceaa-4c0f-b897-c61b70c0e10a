package remote

import (
	"fmt"
	"net/http"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/dto"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/jsonx"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/netutil"
)

type UserCenterRemoteApi struct{}

func (u UserCenterRemoteApi) GetUserInfoList(ids []string, companyId string) ([]dto.UserInfo, error) {
	startTime := time.Now()
	header := http.Header{}
	header.Add("content-type", "application/json")
	request := &netutil.HttpRequest{
		RawURL:  configs.Config.Auth.AuthUrl + "/base/open/getUserListByAdAccount",
		Method:  http.MethodPost,
		Headers: header,
		Body: jsonx.Marshal(map[string]any{
			"adAccounts": ids,
			"companyId":  companyId,
		}),
	}
	httpClient := netutil.NewHttpClient()
	resp, err := httpClient.SendRequest(request)
	if err != nil || resp.StatusCode != 200 {
		logger.Logger.Panicf("error in send request to usercenter, err=%+v", err)
	}
	responseDTO := dto.UsercenterUserListResp{}
	err = httpClient.DecodeResponse(resp, &responseDTO)
	if err != nil {
		return nil, fmt.Errorf("error in send request to usercenter, err=%+v", err)
	}
	if responseDTO.Code != 0 {
		return nil, fmt.Errorf("error in send request to usercenter")
	}
	logger.Logger.Info("Func -> GetUserInfoList执行耗时：", time.Since(startTime))
	return responseDTO.Result, nil
}

func (u UserCenterRemoteApi) GetUserAdAccountNameMap(res map[string]string, companyId string) error {
	_, ok := res[""]
	if len(res) == 0 || ok {
		return nil
	}
	userIds := make([]string, 0, len(res))
	for k := range res {
		userIds = append(userIds, k)
	}
	list, err := u.GetUserInfoList(userIds, companyId)
	if err != nil {
		return err
	}
	for _, userInfo := range list {
		res[userInfo.AdAccount] = userInfo.Name
	}
	return nil
}

func (u UserCenterRemoteApi) GetUserInfo(id, companyId string) (dto.UserInfo, error) {
	res, err := u.GetUserInfoList([]string{id}, companyId)
	if err != nil {
		return dto.UserInfo{}, err
	}
	if len(res) == 0 {
		return dto.UserInfo{}, fmt.Errorf("公司%s下不存在用户%s", companyId, id)
	}
	return res[0], nil
}

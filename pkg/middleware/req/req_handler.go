package req

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"slices"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	cubelibJson "git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/json"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/codec/decode/path"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/framework/middleware/authentication"
)

// PocketBaseReqHandler @description 处理pocketbase请求,在filter中加入条件
// @param tablesWithNoSpaceId : 没有space_id字段的表名
func PocketBaseReqHandler(tablesWithNoSpaceId []string) func(h http.Handler) http.Handler {
	return func(h http.Handler) http.Handler {
		return reqHandler{
			handler:             h,
			tablesWithNoSpaceId: tablesWithNoSpaceId,
		}
	}
}

type reqHandler struct {
	handler             http.Handler
	tablesWithNoSpaceId []string
}

func (h reqHandler) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	acc, err := authentication.FromContext(req.Context())
	if err != nil {
		logger.Logger.Warnf("从请求中获取用户失败! %v", err)
		return
	}
	body := map[string]any{}
	if req.Header.Get("Content-Type") == "application/json" &&
		(req.Method == http.MethodPost || req.Method == http.MethodPut) {
		if err := cubelibJson.Decode(req, &body); err != nil {
			logger.Logger.Warnf("解析请求体失败! %v", err)
			return
		}
	}
	spaceid := req.Header.Get("spaceid")
	switch req.Method {
	case http.MethodPost:
		body["space_id"] = spaceid
		body["creator"] = acc.Name
		body["modifier"] = acc.Name
		body["is_deleted"] = false
		bodyByte, _ := json.Marshal(body)
		req.Body = io.NopCloser(bytes.NewBuffer(bodyByte))
		break
	case http.MethodPut:
		body["modifier"] = acc.Name
		bodyByte, _ := json.Marshal(body)
		req.Body = io.NopCloser(bytes.NewBuffer(bodyByte))
		break
	case http.MethodGet:
		collection := path.DecodeString(req, "collection")
		defaultStr := "(is_deleted='false'" + " && " + "space_id='" + spaceid + "')"
		if slices.Contains(h.tablesWithNoSpaceId, collection) {
			defaultStr = "(is_deleted='false')"
		}
		query := req.URL.Query()
		// url 上的参数是 filter=(is_dir='false' && parent_id='')&page=1&perPage=10
		// 获取字符串 (is_dir='false' && parent_id='')
		f := query.Get("filter")
		if f == "" {
			query.Set("filter", fmt.Sprintf("(%s)", defaultStr))
		} else {
			// 存在filter
			// 移除最外层的()
			filterStr := strings.TrimPrefix(strings.TrimSuffix(f, ")"), "(")
			if strings.Contains(filterStr, "parent_id='-1'") {
				filterStr = strings.Replace(filterStr, "parent_id='-1'", "1='1'", 1)
			}
			query.Set("filter", fmt.Sprintf("(%s && %s)", defaultStr, filterStr))
		}
		logger.Logger.Infof("真实filter请求参数: %v", query.Get("filter"))
		// 将修改后的查询参数重新赋值给 req.URL.RawQuery
		req.URL.RawQuery = query.Encode()
	}
	h.handler.ServeHTTP(w, req)
}

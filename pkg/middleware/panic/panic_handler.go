package panic

import (
	"encoding/json"
	"net/http"
	"strings"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/biz/controller"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/sirupsen/logrus"
)

func RecoveryHandler() func(h http.Handler) http.Handler {
	return func(h http.Handler) http.Handler {
		return recoveryHandler{handler: h}
	}
}

type recoveryHandler struct {
	handler http.Handler
}

func (h recoveryHandler) ServeHTTP(w http.ResponseWriter, req *http.Request) {
	defer func() {
		if err := recover(); err != nil {
			msg := ""
			if logrusEntry, ok := err.(*logrus.Entry); ok {
				msg = strings.Split(logrusEntry.Message, "\n")[0]
			} else if err1, ok := err.(error); ok {
				logger.Logger.Errorf("%+v", xerror.Wrap(err1))
				msg = err1.Error()
			}
			res := controller.Response{
				Code:    "1001",
				Message: msg,
				Data:    nil,
				TraceId: req.Header.Get("Traceid"),
			}
			body, _ := json.Marshal(&res)
			// 返回一个错误响应给客户端
			w.Header().Set("Content-Type", "application/json; charset=utf-8")
			w.Write(body)
		}
	}()

	h.handler.ServeHTTP(w, req)
}

package third_party

import (
	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/third_party/cmdb"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/adaptor"
	"git.zhonganinfo.com/zainfo/shiplib/pkgs/manager/authority/ua"
)

func Init() {
	// 初始化第三方服务
	adaptor.InitCmdbAdaptor(configs.Config.Cmdb)
	cmdb.Init()
	ua.Init(ua.Config{
		Type:    ua.AuthType(configs.Config.Auth.AuthType),
		UaURL:   configs.Config.Auth.UaUrl + "/" + configs.Config.Auth.UaSuffix,
		AuthURL: configs.Config.Auth.AuthUrl,
		Token:   configs.Config.Auth.Token,
		Service: configs.Config.Auth.Service,
	}, adaptor.GetCmdbAdaptor())
}

package snowflake

import (
	"os"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
	"github.com/duke-git/lancet/v2/xerror"
	"github.com/yitter/idgenerator-go/idgen"
)

var (
	snowTime   = "2024-01-01 00:00:00"
	snowWorkId uint16
)

func Init() {
	// workId使用服务进程pid%64
	snowWorkId = uint16(os.Getpid() % 64)
	options := idgen.NewIdGeneratorOptions(snowWorkId)
	st, err := time.Parse("2006-01-02 15:04:05", snowTime)
	if err != nil {
		logger.Logger.Panicf("%+v", xerror.Wrap(err, "error in init snowflake"))
	}
	options.BaseTime = st.UnixMilli()
	idgen.SetIdGenerator(options)
}

func GenSnowFlakeId() int64 {
	return idgen.NextId()
}

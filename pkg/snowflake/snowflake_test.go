package snowflake

import (
	"testing"
	"time"

	"git.zhonganinfo.com/zainfo/cube-mantis/configs"
	"git.zhonganinfo.com/zainfo/cube-mantis/pkg/logger"
)

func TestSnowflakeNode_Generate(t *testing.T) {
	// 初始化配置文件
	configs.Init()
	// 初始化日志
	logger.Init()
	Init()
	for i := 0; i < 100; i++ {
		time.Sleep(100 * time.Nanosecond)
		generate := GenSnowFlakeId()
		logger.Logger.Infof("generate id : %d", generate)
	}
}

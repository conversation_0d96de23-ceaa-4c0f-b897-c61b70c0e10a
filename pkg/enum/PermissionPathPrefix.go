package enum

import "strings"

func GetPermissionPathAdapter() *PermissionPathAdapter {
	adapter := &PermissionPathAdapter{}
	adapter.initMap()
	return adapter
}

type PermissionPathAdapter struct {
	codeMap map[string]string
}

func (p *PermissionPathAdapter) initMap() {
	ppc := map[string]string{
		"neptune": "MANTIS",
		"rick":    "代码扫描的code",
		"mercury": "MANTIS",
	}
	p.codeMap = ppc
}

func (p *PermissionPathAdapter) GetPCodeByUrlPath(urlPath string) string {
	trimPath := strings.Trim(urlPath, "/")
	n := strings.SplitN(trimPath, "/", 2)
	key := n[0]
	return p.codeMap[key]
}

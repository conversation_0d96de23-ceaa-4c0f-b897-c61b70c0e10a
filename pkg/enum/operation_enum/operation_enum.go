package operation_enum

import (
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
)

type OperationEnumInterface interface {
	Operate(actual string, expect string) bool
}

type EqualOperation struct{}

func (o EqualOperation) Operate(value1 string, value2 string) bool {
	return value1 == value2
}

type NotEqualOperation struct{}

func (o NotEqualOperation) Operate(value1 string, value2 string) bool {
	return value1 != value2
}

type GtOperation struct{}

func (o GtOperation) Operate(value1 string, value2 string) bool {
	v1, err := strconv.ParseFloat(value1, 64)
	if err != nil {
		return false
	}
	v2, err := strconv.ParseFloat(value2, 64)
	if err != nil {
		return false
	}
	return v1 > v2
}

type GteOperation struct{}

func (o GteOperation) Operate(value1 string, value2 string) bool {
	v1, err := strconv.ParseFloat(value1, 64)
	if err != nil {
		return false
	}
	v2, err := strconv.ParseFloat(value2, 64)
	if err != nil {
		return false
	}
	return v1 >= v2
}

type LtOperation struct{}

func (o LtOperation) Operate(value1 string, value2 string) bool {
	v1, err := strconv.ParseFloat(value1, 64)
	if err != nil {
		return false
	}
	v2, err := strconv.ParseFloat(value2, 64)
	if err != nil {
		return false
	}
	return v1 < v2
}

type LteOperation struct{}

func (o LteOperation) Operate(value1 string, value2 string) bool {
	v1, err := strconv.ParseFloat(value1, 64)
	if err != nil {
		return false
	}
	v2, err := strconv.ParseFloat(value2, 64)
	if err != nil {
		return false
	}
	return v1 <= v2
}

type ContainOperation struct{}

func (o ContainOperation) Operate(value1 string, value2 string) bool {
	vals := make([]string, 0)
	err := json.Unmarshal([]byte(value1), &vals)
	if err == nil {
		for _, val := range vals {
			if strings.Contains(val, value2) {
				return true
			}
		}
		return false
	} else {
		return strings.Contains(value1, value2)
	}
}

type NotContainOperation struct{}

func (o NotContainOperation) Operate(value1 string, value2 string) bool {
	vals := make([]string, 0)
	err := json.Unmarshal([]byte(value1), &vals)
	if err == nil {
		for _, val := range vals {
			if strings.Contains(val, value2) {
				return false
			}
		}
		return true
	} else {
		return !strings.Contains(value1, value2)
	}
}

type RegexpOperation struct{}

func (o RegexpOperation) Operate(value1 string, value2 string) bool {
	matched, err := regexp.MatchString(value2, value1)
	if matched && err == nil {
		return true
	}
	return false
}

type ExistOperation struct{}

func (o ExistOperation) Operate(value1 string, value2 string) bool {
	return value1 != ""
}

type NotExistOperation struct{}

func (o NotExistOperation) Operate(value1 string, value2 string) bool {
	if value1 == "" {
		return true
	}
	return false
}

func GetOperationByName(name string) OperationEnumInterface {
	switch name {
	case "equal":
		return EqualOperation{}
	case "notEqual":
		return NotEqualOperation{}
	case "exist":
		return ExistOperation{}
	case "notExist":
		return NotExistOperation{}
	case "lessThan":
		return LtOperation{}
	case "lessThanEqual":
		return LteOperation{}
	case "greaterThan":
		return GtOperation{}
	case "greaterThanEqual":
		return GteOperation{}
	case "contain":
		return ContainOperation{}
	case "notContain":
		return NotContainOperation{}
	case "regularExpression":
		return RegexpOperation{}
	}
	return OperationEnumInterface(nil)
}

func GetOperationNames() []string {
	return []string{
		"equal",
		"notEqual",
		"exist",
		"notExist",
		"lessThan",
		"lessThanEqual",
		"greaterThan",
		"greaterThanEqual",
		"contain",
		"notContain",
		"regularExpression",
	}
}
